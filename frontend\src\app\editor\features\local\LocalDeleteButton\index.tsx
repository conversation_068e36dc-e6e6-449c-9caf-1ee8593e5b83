import React from "react";
import { But<PERSON> } from "kf-bui";
import { FormattedMessage } from "react-intl";
import { usePrefixedTranslation } from "@/libs/i18n";
import { LocalDeleteModal } from "../LocalDeleteModal";
import { BooleanToggler } from "@/components/BooleanToggler";

export type LocalDeleteItemType =
  | "LOCAL_HANDBOOK"
  | "LOCAL_CHAPTER"
  | "LOCAL_SECTION";

export interface LocalDeleteItem {
  id: string;
  title: string;
  type: LocalDeleteItemType;
}

interface LocalDeleteButtonProps {
  toDelete: LocalDeleteItem;
  onDelete: (keepLocal?: boolean) => void;
  readlinkExists?: boolean;
}

/**
 * LocalDeleteButton - Delete button component exclusively for Local Editor
 * This component handles deletion operations within the local editor.
 * Used exclusively in the Local Editor flow (/editor/)
 */
export const LocalDeleteButton: React.FC<LocalDeleteButtonProps> = ({
  onDelete,
  toDelete,
  readlinkExists = false,
}) => {
  const t = usePrefixedTranslation("editor.containers.ChapterSelection");

  return (
    <BooleanToggler>
      {(toggle, isVisible) => (
        <span>
          <Button
            key="button"
            onClick={toggle}
            icon="trash"
            control
            outlined
            color="danger"
            size="small"
          >
            {t("deleteButton")}
          </Button>

          {isVisible && (
            <LocalDeleteModal
              isOpen={isVisible}
              key="modal"
              onHide={toggle}
              onDelete={onDelete}
              title={t("deleteTitle")}
              text={
                <div>
                  <p>
                    {t("deleteQuestion", {
                      title: toDelete.title,
                    })}
                  </p>
                  {toDelete.type !== "LOCAL_SECTION" && <p>{t("deleteWarning")}</p>}
                  {toDelete.type === "LOCAL_HANDBOOK" && (
                    <p>
                      <b>MERK! </b>
                      <FormattedMessage id="editor.containers.DeleteButton.deleteLocalWarning" />
                    </p>
                  )}
                  {readlinkExists && (
                    <p>
                      <b>OBS!</b>
                      <FormattedMessage id="editor.containers.DeleteButton.readLinkDeleteWarning" />
                    </p>
                  )}
                </div>
              }
            />
          )}
        </span>
      )}
    </BooleanToggler>
  );
};