package no.kf.handboker.rest

import no.kf.handboker.model.local.{Link, LinkCollection}
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.support.JsonSupport
import org.scalatra.ScalatraServlet
import no.kf.rest.ScalatraExceptions

class HandbookLinkServlet extends ScalatraServlet with JsonSupport with SessionSupport {

  private lazy val service= componentRegistry.handbookLinkService

  get("/:handbookId/?") {
    val handbookId = extractRequiredParam("handbookId")
    service.retrieveLinkCollectionsForHandbook(handbookId)
  }

  post("/link-collection/?") {
    val linkCollection = parsedBody.extract[LinkCollection]
    val handbookId = linkCollection.handbookId
    val handbookOpt = componentRegistry.localHandbookService.retrieveHandbook(handbookId)
    handbookOpt match {
      case None =>
        ScalatraExceptions.forbiddenException("A link collection must refert to an existing handbook")
      case Some(handbook) if handbook.externalOrgId != currentExternalOrganizationId =>
        ScalatraExceptions.forbiddenException("It is not possible to create a link collection on other organizations than what the user is currently logged in to")
      case Some(handbook) if handbook.externalOrgId == currentExternalOrganizationId =>
        service.persistLinkCollection(linkCollection)
    }
  }

  post("/link/:linkCollectionId/?") {
    val linkCollectionId = extractRequiredParam("linkCollectionId")
    val link = parsedBody.extract[Link]

    service.persistLink(link, linkCollectionId)
  }

  delete("/link/:linkId/?") {
    val linkId = extractRequiredParam("linkId")

    service.deleteLink(linkId)
  }

  delete("/link-collection/:linkCollectionId") {
    val linkCollectionId = extractRequiredParam("linkCollectionId")

    service.deleteLinkCollection(linkCollectionId)
  }
}