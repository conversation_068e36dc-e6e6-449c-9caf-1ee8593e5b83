import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import './styles/index.css';
import App from './App.tsx';

// The App component now handles I18nProvider internally for each app
// since each app needs its own provider with the correct store context

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
);
