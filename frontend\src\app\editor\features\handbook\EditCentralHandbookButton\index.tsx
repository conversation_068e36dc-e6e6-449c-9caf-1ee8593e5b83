import { <PERSON><PERSON>, <PERSON> } from "kf-bui";
import { CentralHandbookModal } from "../CentralHandbookModal";
import { BooleanToggler } from "@/components/BooleanToggler";
import type { CentralHandbook } from "@/types";
import { usePrefixedTranslation } from "@/libs/i18n";

interface EditCentralHandbookButtonProps {
  centralHandbook: CentralHandbook;
}

export const EditCentralHandbookButton = ({
  centralHandbook,
}: EditCentralHandbookButtonProps) => {
  const t = usePrefixedTranslation("editor.containers.HandbookSelection");

  return (
    <BooleanToggler>
      {(toggle, value) => (
        <>
          <Control>
            <Button
              onClick={toggle}
              title="Rediger denne Håndboken"
              size="small"
              icon="pencil"
            >
              {t("editButton")}
            </Button>
          </Control>
          {value && (
            <CentralHandbookModal
              isOpen={value}
              type="edit"
              onHide={() => toggle()}
              centralHandbook={centralHandbook}
            />
          )}
        </>
      )}
    </BooleanToggler>
  );
};
