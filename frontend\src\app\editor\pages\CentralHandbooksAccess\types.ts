import type { CentralHandbook } from '@/types';

// Page props
export interface CentralHandbooksAccessPageProps {
  // Page-level props if needed
}

// Component props
export interface SelectCentralHandbooksProps {
  handbooks: CentralHandbook[];
  access: string[];
  isSaving: boolean;
  isLoading: boolean;
  onSave: (ids: string[]) => void;
}

// Local state types
export interface CentralHandbooksAccessState {
  externalOrganization: string;
  access: string[];
  isSaving: boolean;
}

// API types
export interface CentralHandbookAccessRequest {
  organizationId: string;
  handbookIds: string[];
}

export interface CentralHandbookAccessResponse {
  organizationId: string;
  handbookIds: string[];
}