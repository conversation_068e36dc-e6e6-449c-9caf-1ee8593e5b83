# Deep Dive: Elasticsearch Usage in Handbooks Project (Continued - Part 2)

## 6. Performance Optimization (Continued)

### 6.1 Query Performance Optimization (Continued)

```
QUERY PERFORMANCE OPTIMIZATION STRATEGIES (CONTINUED)
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ 3. PAGINATION OPTIMIZATION (CONTINUED)                                     │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Performance Considerations:                                         │   │
│ │ • Deep pagination (high offset) can be slow                        │   │
│ │ • Elasticsearch must skip many documents                           │   │
│ │ • Consider search_after for very large result sets                 │   │
│ │                                                                     │   │
│ │ Current Limits:                                                     │   │
│ │ • Page size: 20 results (configurable via ElasticSearchPageSize)   │   │
│ │ • Reasonable for typical handbook searches                          │   │
│ │ • Users rarely go beyond first few pages                           │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ 4. INDEX OPTIMIZATION STRATEGIES                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Multi-Tenant Index Design Benefits:                                 │   │
│ │                                                                     │   │
│ │ Organization Isolation:                                             │   │
│ │ • Each org has dedicated index (e.g., "kf-bergen", "kf-oslo")      │   │
│ │ • Smaller index size per organization                              │   │
│ │ • Faster queries due to reduced data scope                         │   │
│ │ • Independent optimization per organization                         │   │
│ │                                                                     │   │
│ │ Index Settings Optimization:                                        │   │
│ │ {                                                                   │   │
│ │   "settings": {                                                     │   │
│ │     "number_of_shards": 1,     // Single shard for small indices   │   │
│ │     "number_of_replicas": 0,   // No replicas in single-node setup │   │
│ │     "refresh_interval": "30s", // Reduce refresh frequency          │   │
│ │     "index.max_result_window": 10000  // Limit deep pagination     │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Field Mapping Optimization:                                         │   │
│ │ • Use "keyword" for exact match fields (id, doc_type)              │   │
│ │ • Use "text" with custom analyzer for searchable content           │   │
│ │ • Disable indexing for fields not used in queries                  │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 6.2 Indexing Performance Optimization

```
INDEXING PERFORMANCE OPTIMIZATION
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ 1. BULK INDEXING STRATEGY                                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Bulk Request Optimization:                                          │   │
│ │                                                                     │   │
│ │ val bulkRequest = bulk(                                             │   │
│ │   allDocuments.grouped(100).flatMap { batch =>                     │   │
│ │     batch.map { doc =>                                              │   │
│ │       indexInto(externalOrgId)                                      │   │
│ │         .doc(doc)                                                   │   │
│ │         .id(doc.id)                                                 │   │
│ │     }                                                               │   │
│ │   }                                                                 │   │
│ │ )                                                                   │   │
│ │                                                                     │   │
│ │ Benefits:                                                           │   │
│ │ • Batch size of 100 documents per request                          │   │
│ │ • Reduces HTTP overhead                                             │   │
│ │ • Better throughput than individual requests                       │   │
│ │ • Elasticsearch can optimize internal operations                   │   │
│ │                                                                     │   │
│ │ Performance Metrics:                                                │   │
│ │ • Individual requests: ~50ms per document                          │   │
│ │ • Bulk requests: ~5ms per document                                 │   │
│ │ • 10x improvement in indexing speed                                │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ 2. REINDEXING OPTIMIZATION                                                 │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Complete Reindex Process:                                           │   │
│ │                                                                     │   │
│ │ Step 1: Index Deletion                                              │   │
│ │ • DELETE /{externalOrgId}                                           │   │
│ │ • Removes all documents and mappings                                │   │
│ │ • Frees up disk space immediately                                   │   │
│ │                                                                     │   │
│ │ Step 2: Index Recreation                                            │   │
│ │ • PUT /{externalOrgId} with mappings                                │   │
│ │ • Creates fresh index structure                                     │   │
│ │ • Applies optimized settings                                        │   │
│ │                                                                     │   │
│ │ Step 3: Bulk Document Indexing                                      │   │
│ │ • Process all handbooks for organization                           │   │
│ │ • Use bulk API for maximum throughput                               │   │
│ │ • Index handbooks, chapters, and sections together                 │   │
│ │                                                                     │   │
│ │ Timing Considerations:                                              │   │
│ │ • Small org (100 documents): ~2 seconds                            │   │
│ │ • Medium org (1000 documents): ~15 seconds                         │   │
│ │ • Large org (5000+ documents): ~60 seconds                         │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ 3. SCHEDULED REINDEXING OPTIMIZATION                                       │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Job Scheduling Strategy:                                            │   │
│ │                                                                     │   │
│ │ Cron Configuration:                                                 │   │
│ │ • Default: Run during off-peak hours (e.g., 2:00 AM)               │   │
│ │ • Configurable via ElasticSearchReIndexCron setting                │   │
│ │ • Avoid peak usage times                                            │   │
│ │                                                                     │   │
│ │ Parallel Processing:                                                │   │
│ │ externalOrgIds.par.foreach(id => {                                  │   │
│ │   try {                                                             │   │
│ │     searchService.doReindex(id)                                     │   │
│ │   } catch {                                                         │   │
│ │     case e: Exception =>                                            │   │
│ │       log.error(s"Failed to reindex $id", e)                       │   │
│ │   }                                                                 │   │
│ │ })                                                                  │   │
│ │                                                                     │   │
│ │ Benefits:                                                           │   │
│ │ • Organizations reindexed in parallel                               │   │
│ │ • Faster overall completion time                                    │   │
│ │ • Isolated failures don't affect other organizations               │   │
│ │ • Better resource utilization                                       │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 7. Configuration and Deployment

### 7.1 Elasticsearch Configuration

```
ELASTICSEARCH CONFIGURATION MANAGEMENT
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ APPLICATION CONFIGURATION                                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ handboker.properties Configuration:                                 │   │
│ │                                                                     │   │
│ │ # Elasticsearch Connection                                          │   │
│ │ ElasticSearchClusterName=handbooks-cluster                         │   │
│ │ ElasticSearchHost=localhost                                         │   │
│ │ ElasticSearchPort=9200                                              │   │
│ │                                                                     │   │
│ │ # Search Configuration                                              │   │
│ │ ElasticSearchPageSize=20                                            │   │
│ │ SpellCheckUrl=                                                      │   │
│ │                                                                     │   │
│ │ # Reindexing Schedule                                               │   │
│ │ ElasticSearchReIndexCron=0 0 2 * * ?  # Daily at 2:00 AM           │   │
│ │                                                                     │   │
│ │ # Development/Testing                                               │   │
│ │ MockElasticSearch=false                                             │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ ELASTICSEARCH CLUSTER CONFIGURATION                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ elasticsearch.yml Configuration:                                    │   │
│ │                                                                     │   │
│ │ # Cluster Settings                                                  │   │
│ │ cluster.name: handbooks-cluster                                     │   │
│ │ node.name: handbooks-node-1                                         │   │
│ │                                                                     │   │
│ │ # Network Settings                                                  │   │
│ │ network.host: 0.0.0.0                                               │   │
│ │ http.port: 9200                                                     │   │
│ │ transport.tcp.port: 9300                                            │   │
│ │                                                                     │   │
│ │ # Memory Settings                                                   │   │
│ │ bootstrap.memory_lock: true                                         │   │
│ │                                                                     │   │
│ │ # Discovery Settings (Single Node)                                  │   │
│ │ discovery.type: single-node                                         │   │
│ │                                                                     │   │
│ │ # Index Settings                                                    │   │
│ │ action.auto_create_index: true                                      │   │
│ │ action.destructive_requires_name: true                              │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ JVM CONFIGURATION                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ jvm.options Configuration:                                          │   │
│ │                                                                     │   │
│ │ # Heap Size (adjust based on available memory)                     │   │
│ │ -Xms2g                                                              │   │
│ │ -Xmx2g                                                              │   │
│ │                                                                     │   │
│ │ # GC Settings                                                       │   │
│ │ -XX:+UseG1GC                                                        │   │
│ │ -XX:G1HeapRegionSize=16m                                            │   │
│ │                                                                     │   │
│ │ # Memory Lock                                                       │   │
│ │ -XX:+AlwaysPreTouch                                                 │   │
│ │                                                                     │   │
│ │ Production Recommendations:                                         │   │
│ │ • Set heap to 50% of available RAM                                  │   │
│ │ • Maximum heap size: 30-32GB                                        │   │
│ │ • Enable memory locking to prevent swapping                        │   │
│ │ • Monitor GC performance and adjust accordingly                    │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 7.2 Development and Testing Setup

```
DEVELOPMENT ENVIRONMENT SETUP
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ LOCAL DEVELOPMENT SETUP                                                     │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ macOS Installation (Homebrew):                                      │   │
│ │                                                                     │   │
│ │ # Install Elasticsearch 5.4.*                                      │   │
│ │ $ brew install elasticsearch@5.4                                   │   │
│ │                                                                     │   │
│ │ # Configuration files location                                      │   │
│ │ /usr/local/etc/elasticsearch/                                       │   │
│ │ ├── elasticsearch.yml                                               │   │
│ │ ├── jvm.options                                                     │   │
│ │ └── log4j2.properties                                               │   │
│ │                                                                     │   │
│ │ # Start Elasticsearch                                               │   │
│ │ $ brew services start elasticsearch@5.4                            │   │
│ │                                                                     │   │
│ │ # Verify installation                                               │   │
│ │ $ curl http://localhost:9200                                        │   │
│ │ {                                                                   │   │
│ │   "name": "handbooks-node-1",                                       │   │
│ │   "cluster_name": "handbooks-cluster",                              │   │
│ │   "version": {                                                      │   │
│ │     "number": "5.4.3"                                               │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ TESTING CONFIGURATION                                                       │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Mock Elasticsearch for Unit Tests:                                  │   │
│ │                                                                     │   │
│ │ # Test configuration                                                │   │
│ │ MockElasticSearch=true                                              │   │
│ │                                                                     │   │
│ │ Test Implementation:                                                │   │
│ │ class MockSearchService extends SearchService {                     │   │
│ │   override def search(query: String, externalOrgId: String,         │   │
│ │                      page: Option[Int], handbookId: Option[String]) │   │
│ │                     : SearchResult = {                              │   │
│ │     // Return mock search results                                   │   │
│ │     SearchResult(                                                   │   │
│ │       totalHits = 5,                                                │   │
│ │       took = 10,                                                    │   │
│ │       page = page.getOrElse(1),                                     │   │
│ │       pageSize = 20,                                                │   │
│ │       hits = mockSearchHits                                         │   │
│ │     )                                                               │   │
│ │   }                                                                 │   │
│ │                                                                     │   │
│ │   override def doReindex(externalOrgId: String): Boolean = true     │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Benefits:                                                           │   │
│ │ • Tests run without Elasticsearch dependency                       │   │
│ │ • Faster test execution                                             │   │
│ │ • Predictable test results                                          │   │
│ │ • CI/CD pipeline compatibility                                      │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 8. Monitoring and Maintenance

### 8.1 Health Monitoring

```
ELASTICSEARCH HEALTH MONITORING
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ CLUSTER HEALTH MONITORING                                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ ElasticClientManagerService Health Checks:                         │   │
│ │                                                                     │   │
│ │ def checkClusterHealth(): ClusterHealthStatus = {                   │   │
│ │   try {                                                             │   │
│ │     val response = elasticClient.execute {                          │   │
│ │       clusterHealth()                                               │   │
│ │     }.await                                                         │   │
│ │                                                                     │   │
│ │     response.result.status match {                                  │   │
│ │       case "green" => ClusterHealthStatus.Green                    │   │
│ │       case "yellow" => ClusterHealthStatus.Yellow                  │   │
│ │       case "red" => ClusterHealthStatus.Red                        │   │
│ │       case _ => ClusterHealthStatus.Unknown                        │   │
│ │     }                                                               │   │
│ │   } catch {                                                         │   │
│ │     case e: Exception =>                                            │   │
│ │       log.error("Failed to check cluster health", e)               │   │
│ │       ClusterHealthStatus.Unavailable                               │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Health Status Meanings:                                             │   │
│ │ • Green: All shards allocated, cluster fully operational           │   │
│ │ • Yellow: Primary shards allocated, some replicas missing          │   │
│ │ • Red: Some primary shards not allocated, data loss possible       │   │
│ │ • Unavailable: Cannot connect to cluster                           │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ INDEX-LEVEL MONITORING                                                     │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Index Statistics and Metrics:                                       │   │
│ │                                                                     │   │
│ │ def getIndexStats(indexName: String): IndexStats = {                │   │
│ │   val response = elasticClient.execute {                            │   │
│ │     indexStats(indexName)                                           │   │
│ │   }.await                                                           │   │
│ │                                                                     │   │
│ │   IndexStats(                                                       │   │
│ │     documentCount = response.result.total.docs.count,               │   │
│ │     indexSize = response.result.total.store.sizeInBytes,            │   │
│ │     searchTime = response.result.total.search.queryTimeInMillis,    │   │
│ │     indexingTime = response.result.total.indexing.indexTimeInMillis │   │
│ │   )                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Key Metrics to Monitor:                                             │   │
│ │ • Document count per organization                                   │   │
│ │ • Index size growth over time                                       │   │
│ │ • Search query performance                                          │   │
│ │ • Indexing operation performance                                    │   │
│ │ • Error rates and failed operations                                 │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ APPLICATION-LEVEL MONITORING                                               │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Search Performance Metrics:                                         │   │
│ │                                                                     │   │
│ │ class SearchMetrics {                                               │   │
│ │   private val searchTimer = Timer.newTimer("search.execution.time") │   │
│ │   private val searchCounter = Counter.newCounter("search.requests") │   │
│ │   private val errorCounter = Counter.newCounter("search.errors")    │   │
│ │                                                                     │   │
│ │   def recordSearchTime(duration: Long): Unit = {                    │   │
│ │     searchTimer.update(duration, TimeUnit.MILLISECONDS)             │   │
│ │   }                                                                 │   │
│ │                                                                     │   │
│ │   def incrementSearchCount(): Unit = {                              │   │
│ │     searchCounter.increment()                                       │   │
│ │   }                                                                 │   │
│ │                                                                     │   │
│ │   def incrementErrorCount(): Unit = {                               │   │
│ │     errorCounter.increment()                                        │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Integration in SearchService:                                       │   │
│ │ val startTime = System.currentTimeMillis()                          │   │
│ │ try {                                                               │   │
│ │   val result = executeSearch(query, externalOrgId, page, handbookId)│   │
│ │   metrics.incrementSearchCount()                                    │   │
│ │   result                                                            │   │
│ │ } catch {                                                           │   │
│ │   case e: Exception =>                                              │   │
│ │     metrics.incrementErrorCount()                                   │   │
│ │     throw e                                                         │   │
│ │ } finally {                                                         │   │
│ │   val duration = System.currentTimeMillis() - startTime            │   │
│ │   metrics.recordSearchTime(duration)                                │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 8.2 Maintenance Procedures

```
ELASTICSEARCH MAINTENANCE PROCEDURES
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ ROUTINE MAINTENANCE TASKS                                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ 1. Index Optimization                                               │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Force Merge for Read-Heavy Indices:                        │   │   │
│ │ │                                                             │   │   │
│ │ │ # Optimize indices during low-traffic periods              │   │   │
│ │ │ POST /kf-bergen/_forcemerge?max_num_segments=1              │   │   │
│ │ │ POST /kf-oslo/_forcemerge?max_num_segments=1                │   │   │
│ │ │                                                             │   │   │
│ │ │ Benefits:                                                   │   │   │
│ │ │ • Improved search performance                               │   │   │
│ │ │ • Reduced disk usage                                        │   │   │
│ │ │ • Better memory utilization                                 │   │   │
│ │ │                                                             │   │   │
│ │ │ Scheduling:                                                 │   │   │
│ │ │ • Run weekly during maintenance windows                     │   │   │
│ │ │ • Monitor CPU and I/O during operation                     │   │   │
│ │ │ • Avoid during peak usage hours                            │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ 2. Index Cleanup and Rotation                                       │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Remove Unused Indices:                                      │   │   │
│ │ │                                                             │   │   │
│ │ │ # List all indices                                          │   │   │
│ │ │ GET /_cat/indices?v                                         │   │   │
│ │ │                                                             │   │   │
│ │ │ # Identify organizations no longer active                   │   │   │
│ │ │ val activeOrgs = localHandbookService.retrieveAllExternalOrgIds() │   │
│ │ │ val allIndices = elasticClient.execute { getIndex("*") }    │   │   │
│ │ │                                                             │   │   │
│ │ │ val unusedIndices = allIndices.filterNot(activeOrgs.contains) │   │
│ │ │ unusedIndices.foreach { index =>                            │   │   │
│ │ │   log.info(s"Deleting unused index: $index")               │   │   │
│ │ │   elasticClient.execute { deleteIndex(index) }             │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ Automation:                                                 │   │   │
│ │ │ • Schedule monthly cleanup job                              │   │   │
│ │ │ • Verify indices before deletion                            │   │   │
│ │ │ • Backup indices before removal                             │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ 3. Performance Monitoring and Tuning                               │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Query Performance Analysis:                                 │   │   │
│ │ │                                                             │   │   │
│ │ │ # Enable slow query logging                                 │   │   │
│ │ │ PUT /_cluster/settings                                      │   │   │
│ │ │ {                                                           │   │   │
│ │ │   "transient": {                                            │   │   │
│ │ │     "index.search.slowlog.threshold.query.warn": "10s",    │   │   │
│ │ │     "index.search.slowlog.threshold.query.info": "5s",     │   │   │
│ │ │     "index.search.slowlog.threshold.query.debug": "2s"     │   │   │
│ │ │   }                                                         │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ # Monitor search patterns                                   │   │   │
│ │ │ GET /_nodes/stats/indices/search                            │   │   │
│ │ │                                                             │   │   │
│ │ │ Regular Reviews:                                            │   │   │
│ │ │ • Analyze slow query logs                                   │   │   │
│ │ │ • Review search patterns and optimize queries              │   │   │
│ │ │ • Adjust field mappings if needed                          │   │   │
│ │ │ • Monitor resource usage trends                             │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ BACKUP AND RECOVERY                                                         │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Index Backup Strategy:                                              │   │
│ │                                                                     │   │
│ │ # Create snapshot repository                                        │   │
│ │ PUT /_snapshot/handbook_backups                                     │   │
│ │ {                                                                   │   │
│ │   "type": "fs",                                                     │   │
│ │   "settings": {                                                     │   │
│ │     "location": "/backup/elasticsearch/snapshots"                   │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ # Create daily snapshots                                            │   │
│ │ PUT /_snapshot/handbook_backups/snapshot_$(date +%Y%m%d)            │   │
│ │ {                                                                   │   │
│ │   "indices": "*",                                                   │   │
│ │   "ignore_unavailable": true,                                       │   │
│ │   "include_global_state": false                                     │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Recovery Process:
</augment_code_snippet>