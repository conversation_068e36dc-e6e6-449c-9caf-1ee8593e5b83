import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import {
  Modal,
  Field,
  Label,
  Button,
  Checkbox,
  DatePicker,
  Icon,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import type { CentralHandbook } from "@/types";
import { usePublishCentralHandbookMutation } from "@/store/services/handbook/centralHandbookApi";

interface PubliseringsModalProps {
  handbook: CentralHandbook;
  isOpen: boolean;
  toggleHide: () => void;
  handlePublish: (publicationDate: string) => void;
}

interface FormData {
  publiseringsdato?: Date;
}

export const PubliseringsModal: React.FC<PubliseringsModalProps> = ({
  handbook,
  isOpen,
  toggleHide,
  handlePublish,
}) => {
  const [publiciseNow, setPubliciseNow] = useState<boolean>(false);
  const [publishCentralHandbook, { isLoading }] =
    usePublishCentralHandbookMutation();

  const {
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<FormData>();

  const onSubmit = async (data: FormData) => {
    const dato = data.publiseringsdato;

    if (publiciseNow || dato) {
      try {
        const publicationDate = publiciseNow
          ? new Date().toISOString()
          : dato!.toISOString();

        await publishCentralHandbook({
          handbookId: handbook.id!,
          publicationDate,
        }).unwrap();

        handlePublish(publicationDate);
      } catch (error) {
        console.error("Failed to publish handbook:", error);
        toast.error("Feil ved publisering av håndbok");
      }
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={toggleHide} autoFocus={false}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Modal.Header>
          <Modal.Title>Publiser håndbok</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Field>
            <Label htmlFor="title">Publisering av {handbook.title}</Label>
          </Field>

          <Field>
            <Checkbox
              checked={publiciseNow}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                setPubliciseNow(e.target.checked);
              }}
            >
              Publiser nå
            </Checkbox>
          </Field>

          {!publiciseNow && (
            <Field>
              <Label htmlFor="publiseringsdato">Dato for publisering</Label>
              <Controller
                name="publiseringsdato"
                control={control}
                rules={{
                  required: "Du må sette en dato for publisering",
                  validate: (value) => {
                    if (!value) return true;
                    return (
                      new Date(value) > new Date() || "Dato må være i fremtiden"
                    );
                  },
                }}
                render={({ field }) => (
                  <DatePicker
                    id="publiseringsdato"
                    placeholder="Velg dato for publisering"
                    value={field.value}
                    onChange={(selectedDates: Date[]) => {
                      field.onChange(selectedDates[0] || undefined);
                    }}
                    minDate={new Date()}
                    showIcon
                    icon={<Icon icon="CalendarDays" />}
                  />
                )}
              />
              {errors.publiseringsdato && (
                <span
                  style={{ color: "red", display: "block", marginTop: "4px" }}
                >
                  {errors.publiseringsdato.message}
                </span>
              )}
            </Field>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button type="button" onClick={toggleHide}>
            Avbryt
          </Button>
          <Button type="submit" loading={isLoading}>
            Publiser
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};
