-- KFHB-125: Store central content in db
DROP TABLE centralhandbookupdated
DROP TABLE centralchapterupdated
DROP TABLE centralsectionupdated

CREATE SCHEMA central_handbooks
CREATE TABLE central_handbooks.handbook(central_id VARCHAR(100), title VARCHAR(2000) NOT NULL, updated_date BIGINT NOT NULL, PRIMARY KEY(central_id))
CREATE TABLE central_handbooks.chapter(central_id VARCHAR(100), title VARCHAR(2000) NOT NULL, central_parent_id VARCHAR(100), central_handbook_id VARCHAR(100) NOT NULL, updated_date BIGINT NOT NULL, PRIMARY KEY(central_id))
CREATE TABLE central_handbooks.section(id VARCHAR(100), central_id VARCHAR(37), title VARCHAR(2000) NOT NULL, central_handbook_id VARCHAR(100) NOT NULL, central_parent_id VARCHAR(100) NOT NULL, html CLOB NOT NULL, status VARCHAR(200) NOT NULL, issuer VARCHAR(200), document_type VARCHAR(200), created_date BIGINT NOT NULL, registered_date BIGINT NOT NULL, updated_date BIGINT NOT NULL, PRIMARY KEY(id))
CREATE TABLE central_handbooks.handbook_structure(central_handbook_id VARCHAR(100), structure_json CLOB NOT NULL, PRIMARY KEY(central_handbook_id))

ALTER TABLE central_handbooks.chapter ADD CONSTRAINT fk_central_handbooks_chapter_central_parent_id FOREIGN KEY (central_parent_id) REFERENCES central_handbooks.chapter(central_id)
ALTER TABLE central_handbooks.chapter ADD CONSTRAINT fk_central_handbooks_chapter_central_handbook_id FOREIGN KEY (central_handbook_id) REFERENCES central_handbooks.handbook(central_id)
ALTER TABLE central_handbooks.section ADD CONSTRAINT fk_central_handbooks_section_central_parent_id FOREIGN KEY (central_parent_id) REFERENCES central_handbooks.chapter(central_id)
ALTER TABLE central_handbooks.section ADD CONSTRAINT fk_central_handbooks_section_central_handbook_id FOREIGN KEY (central_handbook_id) REFERENCES central_handbooks.handbook(central_id)
ALTER TABLE central_handbooks.handbook_structure ADD CONSTRAINT fk_central_handbooks_handbook_structure_central_handbook_id FOREIGN KEY (central_handbook_id) REFERENCES central_handbooks.handbook(central_id)

CREATE TABLE central_handbooks.instance(instance_id VARCHAR(37), PRIMARY KEY(instance_id))

-- HMS-håndbok (bokmål) 4465 -> KFHBKB
UPDATE handbooksection SET importedhandbook_id = 'KFHBKB' WHERE importedhandbook_id = '4465'
UPDATE handbookchapter SET importedhandbook_id = 'KFHBKB' WHERE importedhandbook_id = '4465'
UPDATE handbook SET importedhandbook_id = 'KFHBKB' WHERE importedhandbook_id = '4465'
UPDATE centralcontentaccess SET importedhandbook_id = 'KFHBKB' where importedhandbook_id = '4465'

-- HMT-handbok (nynorsk) 4466 -> KFHBK
UPDATE handbooksection SET importedhandbook_id = 'KFHBK' WHERE importedhandbook_id = '4466'
UPDATE handbookchapter SET importedhandbook_id = 'KFHBK' WHERE importedhandbook_id = '4466'
UPDATE handbook SET importedhandbook_id = 'KFHBK' WHERE importedhandbook_id = '4466'
UPDATE centralcontentaccess SET importedhandbook_id = 'KFHBK' where importedhandbook_id = '4466'

-- Personalhåndbok (nynorsk) 4445 -> KFPHN
UPDATE handbooksection SET importedhandbook_id = 'KFPHN' WHERE importedhandbook_id = '4445'
UPDATE handbookchapter SET importedhandbook_id = 'KFPHN' WHERE importedhandbook_id = '4445'
UPDATE handbook SET importedhandbook_id = 'KFPHN' WHERE importedhandbook_id = '4445'
UPDATE centralcontentaccess SET importedhandbook_id = 'KFPHN' where importedhandbook_id = '4445'

-- Personalhåndbok (bokmål) 4425 -> KFPHB
UPDATE handbooksection SET importedhandbook_id = 'KFPHB' WHERE importedhandbook_id = '4425'
UPDATE handbookchapter SET importedhandbook_id = 'KFPHB' WHERE importedhandbook_id = '4425'
UPDATE handbook SET importedhandbook_id = 'KFPHB' WHERE importedhandbook_id = '4425'
UPDATE centralcontentaccess SET importedhandbook_id = 'KFPHB' where importedhandbook_id = '4425'

-- Personalhåndbok (bokmål) 4485 -> KFLTH
UPDATE handbooksection SET importedhandbook_id = 'KFLTH' WHERE importedhandbook_id = '4485'
UPDATE handbookchapter SET importedhandbook_id = 'KFLTH' WHERE importedhandbook_id = '4485'
UPDATE handbook SET importedhandbook_id = 'KFLTH' WHERE importedhandbook_id = '4485'
UPDATE centralcontentaccess SET importedhandbook_id = 'KFLTH' where importedhandbook_id = '4485'
