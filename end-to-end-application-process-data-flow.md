# End-to-End Application Process Data Flow

## Overview
This document explains the complete flow of how API requests are processed in the Håndbøker application, from the initial HTTP request to the final response, including all middleware, servlets, and backend components involved.

## 1. Application Bootstrap & Initialization

### 1.1 Application Startup Flow
```mermaid
graph TD
    A[Jetty Server Start] --> B[Web.xml Processing]
    B --> C[ScalatraBootstrap.init]
    C --> D[Mount Servlets]
    D --> E[Initialize Services]
    E --> F[Start Batch Jobs]
    F --> G[Index ElasticSearch]
    G --> H[Application Ready]
    
    D --> D1[MainServlet /*]
    D --> D2[SessionServlet /session/*]
    D --> D3[LocalHandbookServlet /handbooks/local/*]
    D --> D4[CentralHandbookServlet /handbooks/central/*]
    D --> D5[HandbookApiServlet /api/*]
    D --> D6[SearchServlet /search/]
    D --> D7[PublicIpFilteredHandbookServlet /public/*]
    D --> D8[Other Servlets...]
```

### 1.2 ScalatraBootstrap Configuration
```scala
// ScalatraBootstrap.scala - Application Entry Point
override def init(context: ServletContext) {
    // Set environment
    context.initParameters("org.scalatra.environment") = "production"
    
    // Configure CORS
    context.initParameters("org.scalatra.cors.allowedOrigins") = swaggerUiPath
    context.initParameters("org.scalatra.cors.allowedMethods") = "GET"
    
    // Mount all servlets with their URL patterns
    context mount(new MainServlet, "/*")
    context mount(new SessionServlet, "/session/*")
    context mount(new LocalHandbookServlet, "/handbooks/local/*")
    context mount(new CentralHandbookServlet, "/handbooks/central/*")
    context mount(new HandbookApiServlet, "/api/*", "handbook")
    context mount(new SearchServlet, "/search/")
    context mount(new PublicIpFilteredHandbookServlet, "/public/*")
    // ... other servlets
    
    // Initialize background services
    wireUpBatchJobs()
    indexElasticSearch()
}
```

## 2. Request Processing Flow

### 2.1 High-Level Request Flow
```mermaid
sequenceDiagram
    participant Client
    participant Jetty
    participant Filter
    participant Servlet
    participant Service
    participant DAO
    participant DB
    
    Client->>Jetty: HTTP Request
    Jetty->>Filter: Apply Filters (CORS, Auth, etc.)
    Filter->>Servlet: Route to Appropriate Servlet
    Servlet->>Servlet: Extract Parameters
    Servlet->>Servlet: Validate Session/Auth
    Servlet->>Service: Call Business Logic
    Service->>DAO: Data Access
    DAO->>DB: SQL Query
    DB-->>DAO: Result Set
    DAO-->>Service: Domain Objects
    Service-->>Servlet: Processed Data
    Servlet-->>Filter: JSON Response
    Filter-->>Jetty: HTTP Response
    Jetty-->>Client: Final Response
```

### 2.2 URL Routing Decision Tree
```mermaid
graph TD
    A[Incoming Request] --> B{URL Pattern Match}
    
    B -->|"/*"| C[MainServlet]
    B -->|"/session/*"| D[SessionServlet]
    B -->|"/handbooks/local/*"| E[LocalHandbookServlet]
    B -->|"/handbooks/central/*"| F[CentralHandbookServlet]
    B -->|"/api/*"| G[HandbookApiServlet]
    B -->|"/search/"| H[SearchServlet]
    B -->|"/public/*"| I[PublicIpFilteredHandbookServlet]
    B -->|"/images/*"| J[ImageServlet]
    B -->|"/files/*"| K[FileServlet]
    B -->|"/helsesjekk/*"| L[HealthCheckServlet]
    
    C --> C1[Static Content/SPA]
    D --> D1[Session Management]
    E --> E1[Local Handbook CRUD]
    F --> F1[Central Handbook Access]
    G --> G1[API with Auth]
    H --> H1[Search Operations]
    I --> I1[Public Access with IP Filter]
```

## 3. Detailed Request Processing Examples

### 3.1 API Request Example: GET /api/handbooks
```mermaid
sequenceDiagram
    participant Client
    participant HandbookApiServlet
    participant ComponentRegistry
    participant HandbookApiService
    participant LocalHandbookService
    participant Database
    
    Client->>HandbookApiServlet: GET /api/handbooks
    Note over HandbookApiServlet: URL matches /api/* pattern
    
    HandbookApiServlet->>HandbookApiServlet: before() - Set JSON content type
    HandbookApiServlet->>HandbookApiServlet: externalOrgIdOrThrowIfMissingKeyOrNoAccess()
    HandbookApiServlet->>HandbookApiServlet: Extract API Key from headers
    HandbookApiServlet->>HandbookApiServlet: Validate API Key against organizations map
    
    HandbookApiServlet->>ComponentRegistry: handbookApiService
    ComponentRegistry-->>HandbookApiServlet: HandbookApiService instance
    
    HandbookApiServlet->>HandbookApiService: retrieveHandbooksWithMetadata(externalOrgId)
    HandbookApiService->>LocalHandbookService: Get handbooks for org
    LocalHandbookService->>Database: SQL Query
    Database-->>LocalHandbookService: Handbook data
    LocalHandbookService-->>HandbookApiService: Handbook objects
    HandbookApiService-->>HandbookApiServlet: Processed handbooks
    
    HandbookApiServlet->>HandbookApiServlet: Map("handbooks" -> handbooks)
    HandbookApiServlet-->>Client: JSON Response
```

### 3.2 Local Handbook Request: GET /handbooks/local/
```mermaid
sequenceDiagram
    participant Client
    participant LocalHandbookServlet
    participant SessionSupport
    participant LocalHandbookService
    participant Database
    
    Client->>LocalHandbookServlet: GET /handbooks/local/
    Note over LocalHandbookServlet: URL matches /handbooks/local/* pattern
    
    LocalHandbookServlet->>SessionSupport: currentExternalOrganizationId
    SessionSupport->>SessionSupport: Extract from session
    SessionSupport-->>LocalHandbookServlet: externalOrgId
    
    LocalHandbookServlet->>SessionSupport: currentUser
    SessionSupport-->>LocalHandbookServlet: User object
    
    LocalHandbookServlet->>LocalHandbookService: retrieveHandbooksWithChaptersAndSectionsWithoutTextForExternalOrganization
    LocalHandbookService->>Database: Complex JOIN query
    Database-->>LocalHandbookService: Handbooks, Chapters, Sections
    LocalHandbookService-->>LocalHandbookServlet: Tuple(handbooks, chapters, sections)
    
    LocalHandbookServlet->>LocalHandbookServlet: Map response structure
    LocalHandbookServlet-->>Client: JSON with handbooks, chapters, sections
```

### 3.3 Search Request: GET /search/?query=safety
```mermaid
sequenceDiagram
    participant Client
    participant SearchServlet
    participant SessionSupport
    participant SearchService
    participant ElasticSearch
    
    Client->>SearchServlet: GET /search/?query=safety&page=1&handbookId=123
    
    SearchServlet->>SearchServlet: extractRequiredParam("query")
    SearchServlet->>SearchServlet: extractOptionalInt("page")
    SearchServlet->>SearchServlet: params.get("handbookId")
    
    SearchServlet->>SessionSupport: currentExternalOrganizationId
    SessionSupport-->>SearchServlet: externalOrgId
    
    SearchServlet->>SearchService: doSearch(externalOrgId, query, handbookId, page)
    SearchService->>ElasticSearch: Search query with filters
    ElasticSearch-->>SearchService: Search results
    SearchService-->>SearchServlet: Formatted results
    
    SearchServlet-->>Client: JSON search results
```

## 4. Authentication & Authorization Flow

### 4.1 Session-Based Authentication
```mermaid
graph TD
    A[Request with Session Cookie] --> B[SessionSupport.currentUser]
    B --> C{Session Valid?}
    C -->|Yes| D[Extract User Info]
    C -->|No| E[Redirect to Login/Error]
    
    D --> F[Check User Permissions]
    F --> G{Has Required Access?}
    G -->|Yes| H[Continue Processing]
    G -->|No| I[Return 403 Forbidden]
    
    H --> J[Extract External Org ID]
    J --> K[Validate Org Access]
    K --> L[Process Request]
```

### 4.2 API Key Authentication (HandbookApiServlet)
```mermaid
graph TD
    A[API Request] --> B[Extract API Key from Headers]
    B --> C{API Key Present?}
    C -->|No| D[Return 401 Unauthorized]
    C -->|Yes| E[Lookup in Organizations Map]
    
    E --> F{Key Valid?}
    F -->|No| G[Return 401 Unauthorized]
    F -->|Yes| H[Get External Org ID]
    
    H --> I[Set Organization Context]
    I --> J[Continue Processing]
```

## 5. Component Registry & Dependency Injection

### 5.1 Service Resolution Flow
```mermaid
graph TD
    A[Servlet Needs Service] --> B[componentRegistry.serviceX]
    B --> C[ComponentRegistry Lookup]
    C --> D{Service Cached?}
    D -->|Yes| E[Return Cached Instance]
    D -->|No| F[Create New Instance]
    
    F --> G[Inject Dependencies]
    G --> H[Initialize Service]
    H --> I[Cache Instance]
    I --> J[Return Service]
    
    E --> K[Use Service]
    J --> K
```

### 5.2 Typical Service Dependencies
```mermaid
graph LR
    A[Servlet] --> B[ComponentRegistry]
    B --> C[HandbookService]
    B --> D[SearchService]
    B --> E[UserService]
    
    C --> F[HandbookDAO]
    C --> G[ChapterDAO]
    C --> H[SectionDAO]
    
    D --> I[ElasticSearchClient]
    E --> J[LDAPService]
    
    F --> K[Database Connection]
    G --> K
    H --> K
    I --> L[ElasticSearch Cluster]
    J --> M[LDAP Server]
```

## 6. Error Handling Flow

### 6.1 Exception Processing
```mermaid
graph TD
    A[Exception Thrown] --> B{Exception Type}
    
    B -->|ScalatraException| C[Extract HTTP Status]
    B -->|UserFriendlyException| D[Extract User Message]
    B -->|General Exception| E[Log Error & Return 500]
    
    C --> F[Return Specific Status Code]
    D --> G[Return 400 with Message]
    E --> H[Return Generic Error]
    
    F --> I[JSON Error Response]
    G --> I
    H --> I
```

## 7. Response Processing

### 7.1 JSON Response Generation
```mermaid
sequenceDiagram
    participant Servlet
    participant JsonSupport
    participant Json4s
    participant Client
    
    Servlet->>Servlet: Process business logic
    Servlet->>Servlet: Create response Map/Object
    Servlet->>JsonSupport: Implicit JSON conversion
    JsonSupport->>Json4s: Serialize to JSON
    Json4s-->>JsonSupport: JSON String
    JsonSupport-->>Servlet: JSON Response
    Servlet->>Servlet: Set Content-Type: application/json
    Servlet-->>Client: HTTP Response with JSON body
```

## 8. Database Transaction Flow

### 8.1 Typical Database Operation
```mermaid
sequenceDiagram
    participant Service
    participant DAO
    participant ConnectionPool
    participant Database
    
    Service->>DAO: Call data method
    DAO->>ConnectionPool: Get connection
    ConnectionPool-->>DAO: Database connection
    
    DAO->>Database: BEGIN TRANSACTION
    DAO->>Database: Execute SQL
    Database-->>DAO: Result set
    
    alt Success
        DAO->>Database: COMMIT
        DAO-->>Service: Success result
    else Error
        DAO->>Database: ROLLBACK
        DAO-->>Service: Throw exception
    end
    
    DAO->>ConnectionPool: Return connection
```

## 9. Complete Request Lifecycle Summary

### 9.1 Full End-to-End Flow
```mermaid
graph TD
    A[HTTP Request] --> B[Jetty Server]
    B --> C[Filter Chain]
    C --> D[URL Pattern Matching]
    D --> E[Servlet Selection]
    
    E --> F[Authentication Check]
    F --> G[Parameter Extraction]
    G --> H[Business Logic]
    H --> I[Service Layer]
    I --> J[Data Access Layer]
    J --> K[Database Query]
    
    K --> L[Result Processing]
    L --> M[Response Formatting]
    M --> N[JSON Serialization]
    N --> O[HTTP Response]
    O --> P[Client Receives Response]
    
    style A fill:#e1f5fe
    style P fill:#e8f5e8
    style F fill:#fff3e0
    style K fill:#fce4ec
```

This comprehensive flow shows how every API request travels through the Håndbøker application, from initial HTTP request to final JSON response, including all the middleware, authentication, business logic, and data persistence layers involved.