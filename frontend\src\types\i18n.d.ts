// TypeScript declarations for i18n
declare module '*.json' {
  const value: Record<string, string>;
  export default value;
}

// Message keys type (can be extended with specific keys for better type safety)
export type MessageKey = string;

// Translation values type
export type TranslationValues = Record<string, string | number | boolean | Date | null | undefined>;

// Intl formatting options
export interface IntlFormatOptions {
  defaultMessage?: string;
  description?: string;
}
