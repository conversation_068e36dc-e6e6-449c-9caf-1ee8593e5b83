import { createSelector } from "@reduxjs/toolkit";
import type { LocalHandbooksGetResponse } from "./localHandbookApi";


// Selects the count of pending changes across all handbooks, chapters, and sections
export const selectPendingCount = createSelector(
  [(data: LocalHandbooksGetResponse | undefined) => data],
  (data) => {
    if (!data) return 0;

    const { handbooks, chapters, sections } = data;

    const handbookCount = handbooks.filter((h) => h.pendingChange).length;
    const chapterCount = chapters.filter((c) => c.pendingChange || c.pendingDeletion).length;
    const sectionCount = sections.filter(
      (s) => s.pendingTitleChange || s.pendingTextChange || s.pendingDeletion
    ).length;

    return handbookCount + chapterCount + sectionCount;
  }
);

// Selects items with pending changes, grouped by handbook
export const selectPendingItems = createSelector(
  [(data: LocalHandbooksGetResponse | undefined) => data],
  (data) => {
    if (!data) return [];

    const { handbooks, chapters, sections } = data;

    // Group sections and chapters by handbook
    const groupedSections = sections
      .filter((s) => s.pendingTitleChange || s.pendingTextChange || s.pendingDeletion)
      .reduce((acc, section) => {
        if (!acc[section.handbookId]) acc[section.handbookId] = [];
        acc[section.handbookId].push(section);
        return acc;
      }, {} as Record<string, typeof sections>);

    const groupedChapters = chapters
      .filter((c) => c.pendingChange || c.pendingDeletion)
      .reduce((acc, chapter) => {
        if (!acc[chapter.handbookId]) acc[chapter.handbookId] = [];
        acc[chapter.handbookId].push(chapter);
        return acc;
      }, {} as Record<string, typeof chapters>);

    // Create grouped handbooks
    const groupedHandbooks = handbooks
      .map((handbook) => ({
        ...handbook,
        chapters: groupedChapters[handbook.id!] || [],
        sections: groupedSections[handbook.id!] || [],
      }))
      .filter((h) => h.pendingChange || h.chapters.length > 0 || h.sections.length > 0)
      .sort((a, b) => a.title.localeCompare(b.title));

    return groupedHandbooks;
  }
);

//
// Selects just the handbooks array from the main local handbooks query.
// Replaces the removed getLocalHandbooksOnly query.
export const selectLocalHandbooks = createSelector(
  [(data: LocalHandbooksGetResponse | undefined) => data],
  (data) => data?.handbooks || []
);

//
// Selects just the chapters array from the main local handbooks query.
// Replaces the removed getLocalChapters query.
export const selectLocalChapters = createSelector(
  [(data: LocalHandbooksGetResponse | undefined) => data],
  (data) => data?.chapters || []
);

//
// Selects just the sections array from the main local handbooks query.
// Replaces the removed getLocalSections query.
export const selectLocalSections = createSelector(
  [(data: LocalHandbooksGetResponse | undefined) => data],
  (data) => data?.sections || []
);