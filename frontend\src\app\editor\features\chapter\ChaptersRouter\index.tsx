import React from "react";
import { Routes, Route } from "react-router-dom";
import { CentralChapterForm } from "../CentralChapterForm";
import { MoveCentralChapterOrSection } from "../../shared/MoveCentralChapterOrSection";
import { CentralChapterScreen } from "../CentralChapterScreen";

export const ChaptersRouter: React.FC = () => {
  return (
    <Routes>
      <Route path=":chapterId" element={<CentralChapterScreen />} />
      <Route path="add-new" element={<CentralChapterForm />} />
      <Route path=":chapterId/edit" element={<CentralChapterForm />} />
      <Route path=":chapterId/move" element={<MoveCentralChapterOrSection />} />
    </Routes>
  );
};
