import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig({
  plugins: [react()],
  base: "/",
  define: {
    "process.env.NODE_ENV": JSON.stringify(
      process.env.NODE_ENV || "development"
    ),
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@/app": path.resolve(__dirname, "./src/app"),
      "@/shared": path.resolve(__dirname, "./src/shared"),
      "@/libs": path.resolve(__dirname, "./src/libs"),
      "@/assets": path.resolve(__dirname, "./src/assets"),
      "@/workers": path.resolve(__dirname, "./src/workers"),
      "@/hooks": path.resolve(__dirname, "./src/hooks"),
      "@/components": path.resolve(__dirname, "./src/components"),
      "@/services": path.resolve(__dirname, "./src/services"),
    },
  },
  server: {
    port: 3000,
    proxy: {
      "^/public": {
        target: "http://localhost:5600",
        changeOrigin: true,
        secure: false,
        rewrite: (path) => `/handboker${path}`,
        configure: (proxy) => {
          proxy.on("proxyRes", (proxyRes, _req, _res) => {
            const cookies = proxyRes.headers["set-cookie"];
            if (cookies) {
              proxyRes.headers["set-cookie"] = cookies.map((cookie) => {
                return cookie
                  .replace(/Domain=[^;]+;?\s*/i, "")
                  .replace(/Path=\/handboker/i, "Path=/")
                  .replace(/SameSite=None/i, "SameSite=Lax");
              });
            }
          });
        },
      },

      // Handle requests that include /handboker prefix (like legacy app)
      "^/handboker/(api|handbooks|session|search|files|images|image-upload|linkdata|helsesjekk)": {
        target: "http://localhost:5600",
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path, // Keep the path as is since it already includes /handboker
        configure: (proxy) => {
          proxy.on("proxyRes", (proxyRes, _req, _res) => {
            const cookies = proxyRes.headers["set-cookie"];
            if (cookies) {
              proxyRes.headers["set-cookie"] = cookies.map((cookie) => {
                return cookie
                  .replace(/Domain=[^;]+;?\s*/i, "")
                  .replace(/Path=\/handboker/i, "Path=/")
                  .replace(/SameSite=None/i, "SameSite=Lax");
              });
            }
          });
        },
      },

      // Handle requests without /handboker prefix  
      "^/(api|handbooks|session|search|files|images|image-upload|linkdata|helsesjekk)": {
        target: "http://localhost:5600",
        changeOrigin: true,
        secure: false,
        rewrite: (path) => `/handboker${path}`,
        configure: (proxy) => {
          proxy.on("proxyRes", (proxyRes, _req, _res) => {
            const cookies = proxyRes.headers["set-cookie"];
            if (cookies) {
              proxyRes.headers["set-cookie"] = cookies.map((cookie) => {
                return cookie
                  .replace(/Domain=[^;]+;?\s*/i, "")
                  .replace(/Path=\/handboker/i, "Path=/")
                  .replace(/SameSite=None/i, "SameSite=Lax");
              });
            }
          });
        },
      },
    },
  },
  build: {
    outDir: "build",
    rollupOptions: {
      output: {
        manualChunks: {
          "editor-app": ["./src/app/editor/index"],
          "public-app": ["./src/app/public/index"],
          "vendor-react": ["react", "react-dom"],
          "vendor-redux": ["@reduxjs/toolkit", "react-redux"],
          "vendor-router": ["react-router-dom"],
        },
      },
    },
  },
});
