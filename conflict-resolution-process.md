# Conflict Resolution Process

When central and local changes conflict, the system requires manual resolution:

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  CONFLICT RESOLUTION WORKFLOW                                               │
│                                                                             │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐│
│  │ User Views  │     │ System      │     │ User Selects│     │ System      ││
│  │ Pending     │────►│ Shows       │────►│ Resolution  │────►│ Applies     ││
│  │ Changes     │     │ Diff View   │     │ Option      │     │ Changes     ││
│  └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘│
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Merge Options

When conflicts occur, users have three options:

1. **Keep Central Changes**
   - Discard local modifications
   - Apply central content completely
   - Reset `localChange` flag

2. **Keep Local Changes**
   - Preserve local modifications
   - Mark as synchronized but still locally modified
   - Maintain `localChange` flag, clear `pendingChange` flag

3. **Custom Merge**
   - Manually edit content to combine changes
   - System marks as locally modified
   - Sets `localChange` flag, clears `pendingChange` flag

## Implementation in MergePage Component

The frontend implements this in the `MergePage` component:

```jsx
onSave = event => {
  const { match } = this.props;
  const { centralElement, title } = this.state;
  event.preventDefault();
  this.setState({ isSaving: true });

  // Merge the title based on user selection
  const merged = mergeTitle(getElementToMerge(this.props), centralElement, title);

  // Track whether changes are from central or local
  const centralChange = centralElement.title === title ? 'KF' : 'local';
  
  if (match.params.handbookId) {
    // Merging a handbook
    this.props.saveLocalHandbook({
      ...merged,
      title: `${merged.title}`
    });
  } else {
    // Merging a chapter
    this.props.saveLocalChapter({ chapter: merged, centralChange });
  }
};
```

## Tracking Merge Status

The system tracks merge status using flags:

- `localChange`: Indicates local modifications exist
- `pendingChange`: Indicates unresolved central changes
- `importedHandbookId`: References the central source

These flags help the system determine which content needs attention during future synchronizations.