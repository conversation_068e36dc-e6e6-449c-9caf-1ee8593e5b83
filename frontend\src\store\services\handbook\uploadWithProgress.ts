import { useState, useCallback } from 'react';
import type { AttachmentUploadResponse } from '@/types';

// Custom hook for file upload with progress tracking
export function useFileUploadWithProgress() {
  const [isUploading, setIsUploading] = useState(false);
  
  const uploadFile = useCallback(async (
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<AttachmentUploadResponse> => {
    setIsUploading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/files', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: {
          // Don't set Content-Type for FormData - browser will set it with boundary
          // Include CSRF token for security
          ...(document.cookie.includes('XSRF-TOKEN') && {
            'X-XSRF-TOKEN': decodeURIComponent(
              document.cookie
                .split('; ')
                .find(row => row.startsWith('XSRF-TOKEN='))
                ?.split('=')[1] || ''
            )
          })
        },
      });
      
      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
      }
      
      // Simulate progress completion since fetch doesn't support upload progress
      onProgress?.(100);
      
      const result = await response.json();
      return result;
    } finally {
      setIsUploading(false);
    }
  }, []);
  
  return {
    uploadFile,
    isUploading,
  };
}

// This function can be used as a drop-in replacement for legacy upload code.
export async function uploadFileWithProgress(
  file: File, 
  onProgress?: (progress: number) => void
): Promise<AttachmentUploadResponse> {
  const formData = new FormData();
  formData.append('file', file);
  
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    
    // Track upload progress
    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const percentComplete = Math.round((event.loaded / event.total) * 100);
        onProgress(percentComplete);
      }
    });
    
    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch {
          reject(new Error('Invalid JSON response'));
        }
      } else {
        reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
      }
    });
    
    xhr.addEventListener('error', () => {
      reject(new Error('Upload failed due to network error'));
    });
    
    xhr.addEventListener('abort', () => {
      reject(new Error('Upload was aborted'));
    });
    
    // Include CSRF token
    const xsrfCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('XSRF-TOKEN='));
    
    xhr.open('POST', '/files');
    xhr.withCredentials = true;
    
    if (xsrfCookie) {
      const xsrfToken = xsrfCookie.split('=')[1];
      xhr.setRequestHeader('X-XSRF-TOKEN', decodeURIComponent(xsrfToken));
    }
    
    xhr.send(formData);
  });
}