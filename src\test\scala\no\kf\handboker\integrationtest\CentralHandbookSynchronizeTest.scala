package no.kf.handboker.integrationtest

import java.io.File

import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.scalatest.{FunSuite, PrivateMethodTester}

@RunWith(classOf[JUnitRunner])
class CentralHandbookSynchronizeTest extends FunSuite with FullStack with PrivateMethodTester {

  override def handbookConfigDir = new File("src/test/resources/integrationtest")

  val persistCentralHandbookVersion = PrivateMethod[(CentralHandbook, List[CentralChapter], List[CentralSection])]('persistCentralHandbookVersion)
  val synchronizeLocalHandbook = PrivateMethod[Unit]('synchronizeLocalHandbook)
  lazy val centralNotificationRepository = componentRegistry.centralNotificationRepository
  lazy val localHanbookRepository = componentRegistry.handbookRepository
  lazy val localHandbookService = componentRegistry.localHandbookService
  lazy val versionService = componentRegistry.localHandbookVersionService
  lazy val organizationDataService = componentRegistry.organizationDataService

  test("That we can synchronize central changes to a local handbook") {
    testInTransaction {
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")
      val ch21 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch21", ch2.id, centralHandbook.id.get), "user")
      val ch211 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch211", ch21.id, centralHandbook.id.get), "user")

      val s12 = centralHbRepo.persistCentralSection(CentralSection(None, "s12", ch1.id.get, centralHandbook.id.get, Some("s12html")), "user")
      val s21 = centralHbRepo.persistCentralSection(CentralSection(None, "s21", ch2.id.get, centralHandbook.id.get, Some("s21html")), "user")
      val s22 = centralHbRepo.persistCentralSection(CentralSection(None, "s22", ch2.id.get, centralHandbook.id.get, Some("s22html")), "user")
      val s211 = centralHbRepo.persistCentralSection(CentralSection(None, "s211", ch21.id.get, centralHandbook.id.get, Some("s211html")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      assert(hbVersion1._2.size == 4)
      assert(hbVersion1._3.size == 4)

      // Give organization access
      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create central based local handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))

      // Local handbook sanity check
      val storedLocalHandbook = localHandbookService.retrieveHandbook(localHandbook.id.get)
      assert(storedLocalHandbook.isDefined)
      assert(storedLocalHandbook.get.importedHandbookId == centralHandbook.id)
      assert(storedLocalHandbook.get.id == localHandbook.id)

      val storedLocalChapters = localHandbookService.retrieveChaptersForHandbook(storedLocalHandbook.get.id.get)
      assert(storedLocalChapters.nonEmpty)
      assert(storedLocalChapters.map(c => c.title).toSet.subsetOf(hbVersion1._2.map(ch => ch.title).toSet))
      assert(storedLocalChapters.forall(c => c.importedHandbookId == centralHandbook.id))
      assert(storedLocalChapters.map(c => c.importedHandbookChapterId).toSet.subsetOf(hbVersion1._2.map(ch => ch.versionOf).toSet))
      assert(storedLocalChapters.size == 4)

      val localSections1 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(localSections1.size == 4)
      assert(localSections1.map(c => c.text).forall(t => t.isDefined))

      // Make a change in central handbook chapter
      centralHbRepo.persistCentralChapter(ch1.copy(title = "ch1 ny tittel"), "current-user_id")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      // Synchronize
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      // Check synchronization result
      val storedLocalHandbookSync = localHandbookService.retrieveHandbook(localHandbook.id.get)
      assert(storedLocalHandbookSync.isDefined)
      assert(storedLocalHandbookSync.get.importedHandbookId == centralHandbook.id)
      val storedLocalChaptersSync = localHandbookService.retrieveChaptersForHandbook(storedLocalHandbook.get.id.get)
      assert(storedLocalChaptersSync.nonEmpty)
      assert(storedLocalChaptersSync.size == 4)
      assert(storedLocalChaptersSync.exists(c => c.title == "ch1 ny tittel" && storedLocalChapters.exists(lc => lc.id == c.id && lc.title == "ch1")))
      val lch1Versions = versionService.retrieveChapterVersions(storedLocalChaptersSync.filter(c => c.title == "ch1 ny tittel").head.id.get)
      assert(lch1Versions.nonEmpty )
      assert(lch1Versions.size == 1)
      assert(lch1Versions.head.title == "ch1")
      val localSections22 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(localSections22.size == 4)

      // Add a central chapter, check that it is synchronized to local handbook
      val ch3 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch3", None, centralHandbook.id.get, createdDate = Some(DateTime.now), sortOrder = 3), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localChapters3 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters3.exists(c => c.title == "ch3"))
      assert(localChapters3.size == 5)

      // Delete a central chapter. Check that it's deleted locally
      centralHbRepo.deleteCentralChapter(ch3.id.get)
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localChapters4 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters4.size == 4)
      assert(!localChapters4.exists(c => c.title == "ch3"))
      val localChapters4deleted = componentRegistry.handbookRepository.retrieveDeletedAndNotDeletedChaptersForHandbook(localHandbook.id.get)
      val deletedChapter = localChapters4deleted.find(c => c.title == "ch3").get
      assert(deletedChapter.isDeleted.get)
      assert(localChapters4deleted.size == 5)
      val deletedChapterVersions = versionService.retrieveChapterVersions(deletedChapter.id.get)
      assert(deletedChapterVersions.nonEmpty)
      assert(deletedChapterVersions.size == 1)

      // Change a central section. Check that change is synchronized to local handbook
      centralHbRepo.persistCentralSection(s21.copy(title = "s21 ny tittel", html = Some("s21html new text")), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localSections5 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(localSections5.exists(c => c.text.contains("s21html new text")))
      assert(localSections5.exists(c => c.title == "s21 ny tittel"))
      val ls21 = localSections5.find(c => c.title == "s21 ny tittel").get
      val ls21Versions = versionService.retrieveSectionVersions(ls21.id.get)
      assert(ls21Versions.nonEmpty)
      assert(ls21Versions.size == 1)
      assert(ls21Versions.head.title == "s21")
      val ls21Version = versionService.retrieveSectionVersion(ls21Versions.head.id.get).get
      assert(ls21Version.text.get == "s21html")

      // Add a central section, check that it's synchronized
      val s23 = centralHbRepo.persistCentralSection(CentralSection(None, "s23", ch2.id.get, centralHandbook.id.get, Some("s22html"), sortOrder = 4), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localSections6 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(localSections6.size == 5)
      assert(localSections6.exists(s => s.title == "s23" && s.handbookId == localHandbook.id.get && localChapters4.exists(ch => ch.id.get == s.parentId && ch.title == "ch2")))

      // Delete a central section, check that it's deleted locally
      centralHbRepo.deleteCentralSection(s23.id.get)
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localSections7 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(!localSections7.exists(s => s.title == "s23"))
      assert(localSections7.size == 4)
      val deletedS23 = localHanbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook(localHandbook.id.get).filter(s => s.title == "s23")
      assert(deletedS23.nonEmpty && deletedS23.size == 1 && deletedS23.head.isDeleted.getOrElse(false))

      // Delete a chapter with subitems, check that local chapter & subitems are deleted locally
      centralHbRepo.deleteCentralChapter(ch21.id.get)
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localSections8 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(!localSections8.exists(s => s.title == "s21"))
      assert(localSections8.size == 3)
      val localChapters5 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters5.size == 2)

      // Make both local and central chapter changes, check that central change is not automatically applied
      val localChapters = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters.nonEmpty)
      assert(localChapters.exists(c => c.importedHandbookChapterId == ch2.id))
      val localCh2 = localChapters.find(c => c.importedHandbookChapterId == ch2.id).get
      val localSections9 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      val localS1 = localSections9.find(s => s.importedHandbookSectionId == s22.id)
      localHandbookService.persistSection(localS1.get.copy(title = "s22 lokal"), Some("user"))
      centralHbRepo.persistCentralSection(s22.copy(title = "s22 sentral"), "someuser")
      val notifications1 = centralNotificationRepository.retrieveNotifications(localHandbook.id.get)
      assert(!notifications1.exists(n => n.chapterId.isDefined && n.chapterId == localCh2.id))
      localHandbookService.persistChapter(localCh2.copy(title = "ch2 lokal"), Some("9900"))
      centralHbRepo.persistCentralChapter(ch2.copy(title = "ch2 sentral"), "user")

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      val localChapters6 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(!localChapters6.exists(c => c.title == "ch2 sentral" && c.pendingChange && !c.pendingDeletion))
      val notifications2 = centralNotificationRepository.retrieveNotifications(localHandbook.id.get)
      assert(notifications2.nonEmpty)
      assert(notifications2.exists(n => n.chapterId.isDefined && n.chapterId == localCh2.id))

      val localSections10 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(!localSections10.exists(s => s.title == "ch2 sentral"))

      // Change local chapter, then delete central. Check that local chapter is not deleted and gets pending deletion flag
      val localchapters7 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      val localCh1 = localchapters7.find(c => c.importedHandbookChapterId == ch1.id).get
      localHandbookService.persistChapter(localCh1.copy(title = "ch1 ny lokal tittel", localChange = true))
      centralHbRepo.deleteCentralChapter(ch1.id.get)
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localchapters8 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localchapters8.exists(c => c.importedHandbookChapterId == ch1.id && c.pendingDeletion && !c.pendingChange))

      // Delete local section, then change central section. Check that local section is not undeleted and that we don't get a notification
      val localSections11 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      val localS21 = localSections11.find(s => s.importedHandbookSectionId == s21.id).get
      localHandbookService.deleteSection(localS21.id.get)
      centralHbRepo.persistCentralSection(s21.copy(title = "s21 ny tittel 2"), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localSections12 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(!centralNotificationRepository.retrieveNotifications(localHandbook.id.get).exists(n => n.handbookId == localHandbook.id.get && n.chapterId.isDefined && n.chapterId == localS21.id))
      val localSectionsAfterSync = componentRegistry.handbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook(localHandbook.id.get)
      assert(localSectionsAfterSync.exists(c => c.id == localS21.id && c.isDeleted.getOrElse(false) && !c.pendingTitleChange))

      // Change central handbook title, check that it's synchronized to local handbook
      val changedCentralHandbook = centralHbRepo.persistCentralHandbook(centralHandbook.copy(title = "handbook new"), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(changedCentralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val changedHb = localHandbookService.retrieveHandbook(localHandbook.id.get).get
      assert(!changedHb.pendingChange && changedHb.title == "handbook new")
      assert(centralNotificationRepository.retrieveNotifications(localHandbook.id.get).exists(n => n.handbookId == localHandbook.id.get && n.chapterId.isEmpty && n.sectionId.isEmpty))

      // Change local & central handbook title, check that local handbook title is not overwritten, and that we get a notifocation
      val changedCentralHandbook2 = centralHbRepo.persistCentralHandbook(centralHandbook.copy(title = "handbook central"), "user")
      val changedLocalHandbook = localHandbookService.persistHandbook(localHandbook.copy(title = "handbook local"))
      assert(changedLocalHandbook.localChange)
      synchronizationService invokePrivate persistCentralHandbookVersion(changedCentralHandbook2, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(changedLocalHandbook.id.get, centralHandbook.id.get)
      val changedHb2 = localHandbookService.retrieveHandbook(localHandbook.id.get).get
      assert(changedHb2.pendingChange && changedHb2.title != "handbook central")
      assert(centralNotificationRepository.retrieveNotifications(localHandbook.id.get).exists(n => n.handbookId == localHandbook.id.get && n.chapterId.isEmpty && n.sectionId.isEmpty))

      // add a central sub-chapter, check that it is synchronized
      val ch4 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch4", ch2.id, centralHandbook.id.get, createdDate = Some(DateTime.now), sortOrder = 3), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localChapters9 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters9.exists(c => c.title == ch4.title))
      assert(localChapters9.size == 3)

      // Add an unimported section to the subchapter, then delete the parent chapter centrally. Check that local chapter is not deleted, and that we get a pending deletion notification
      val lc4 = localChapters9.find(c => c.title == "ch4").get
      localHandbookService.persistSection(Section(None, "s41", Some("text"), None, None, localHandbook.id.get, lc4.id.get, None), Some("user"))
      centralHbRepo.deleteCentralChapter(ch4.id.get)
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localChapters10 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters10.exists(c => c.title == ch4.title && !c.isDeleted.getOrElse(false) && !c.pendingChange && c.pendingDeletion))
      assert(localChapters9.size == 3)

      // Create pure local handbook, add central based chapter and section. Check that they are synchronized
      val pureLocal = localHandbookService.persistHandbook(Handbook(None, "helt lokal", None, "9900"))
      val ch5 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch5", None, centralHandbook.id.get, createdDate = Some(DateTime.now), sortOrder = 3), "user")
      val s5 = centralHbRepo.persistCentralSection(CentralSection(None, "s5", ch5.id.get, centralHandbook.id.get, Some("s5html")), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      localHandbookService.persistChapterWithChildren(Chapter(None, "ch5 local", ch5.id, centralHandbook.id, pureLocal.id.get, None, None), Some("<EMAIL>"), Some("9900"))
      centralHbRepo.persistCentralChapter(ch5.copy(title = "ch5 central"), "user")
      centralHbRepo.persistCentralSection(s5.copy(title = "s5 central"), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(pureLocal.id.get, centralHandbook.id.get)
      assert(localHandbookService.retrieveChaptersForHandbook(pureLocal.id.get).exists(c => c.title == "ch5 central"))
      assert(localHandbookService.retrieveSectionsForHandbook(pureLocal.id.get).exists(s => s.title == "s5 central"))
    }
  }

  test("That new chapter/section is put last in local chapter or root") {
    testInTransaction {

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")
      val s21 = centralHbRepo.persistCentralSection(CentralSection(None, "s21", ch2.id.get, centralHandbook.id.get, Some("s21")), "user")
      val ch21 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch21", ch2.id, centralHandbook.id.get), "user")
      val ch22 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch22", ch2.id, centralHandbook.id.get), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      // Give organization access
      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create central based local handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))

      //create new central root- and sub-shapters
      val ch3 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch3", None, centralHandbook.id.get), "user")
      // sort c3 first in central chapter
      centralHbRepo.persistSortOrder(List(ch3.id.get, ch1.id.get, ch2.id.get))
      val ch23 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch23", ch2.id, centralHandbook.id.get), "user")
      centralHbRepo.persistSortOrder(List(ch23.id.get, ch21.id.get, ch22.id.get))

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      val localChapters = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters.find(c => c.title == "ch1").get.sortOrder.get == 0)
      assert(localChapters.find(c => c.title == "ch2").get.sortOrder.get == 1)
      assert(localChapters.find(c => c.title == "ch3").get.sortOrder.get == 2)

      assert(localChapters.find(c => c.title == "ch21").get.sortOrder.get == 1)
      assert(localChapters.find(c => c.title == "ch22").get.sortOrder.get == 2)
      assert(localChapters.find(c => c.title == "ch23").get.sortOrder.get == 3)
    }
  }

  test("That new chapter is put last in local chapter that contains sections") {
    testInTransaction {

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")
      val s21 = centralHbRepo.persistCentralSection(CentralSection(None, "s21", ch2.id.get, centralHandbook.id.get, Some("s21")), "user")
      val s22 = centralHbRepo.persistCentralSection(CentralSection(None, "s22", ch2.id.get, centralHandbook.id.get, Some("s22")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      // Give organization access
      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create central based local handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))

      //create new central chapter
      val ch23 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch23", ch2.id, centralHandbook.id.get), "user")

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      val localChapters = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters.find(c => c.title == ch23.title).get.sortOrder.get == 2)
    }
  }

  test("That content of new chapter is maintained when synchronized to local handbook") {
    testInTransaction {

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      // Give organization access
      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create local based central handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))


      //add new central chapter with content
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")
      val s21 = centralHbRepo.persistCentralSection(CentralSection(None, "s21", ch2.id.get, centralHandbook.id.get, Some("s21")), "user")
      val s22 = centralHbRepo.persistCentralSection(CentralSection(None, "s22", ch2.id.get, centralHandbook.id.get, Some("s22")), "user")
      val ch3 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch3", ch2.id, centralHandbook.id.get), "user")

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      val localChapters = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      val localSections = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(localSections.find(s => s.title == s21.title).get.sortOrder.get == 0)
      assert(localSections.find(s => s.title == s22.title).get.sortOrder.get == 1)
      assert(localChapters.find(c => c.title == ch3.title).get.sortOrder.get == 2)
    }
  }

  test("That we don't generate conflict warning more than once per central change") {
    testInTransaction {

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create local handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))

      // Central changes
      val changedCentralHb = centralHbRepo.persistCentralHandbook(centralHandbook.copy(title = "handbook central"), "user")
      centralHbRepo.persistCentralChapter(ch1.copy(title = "ch1 central"), "user")
      centralHbRepo.persistCentralSection(s11.copy(title = "s11 central", html = Some("s11 central")), "user")

      // Local change
      localHandbookService.persistHandbook(localHandbook.copy(title = "handbook local"))
      localHandbookService.persistChapter(localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).head.copy(title = "ch1 local", localChange = true))
      localHandbookService.persistSection(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.copy(title = "s11 local", text = Some("s11 local"), localTextChange = true, localTitleChange = true), Some("user"))
      synchronizationService invokePrivate persistCentralHandbookVersion(changedCentralHb, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      val lhbSYnched = localHandbookService.retrieveHandbook(localHandbook.id.get).get
      assert(lhbSYnched.pendingChange)
      val lch1Synched = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).head
      assert(lch1Synched.title == "ch1 local")
      assert(lch1Synched.pendingChange)
      val ls11Synched = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head
      assert(ls11Synched.title == "s11 local")
      assert(ls11Synched.pendingTitleChange)
      assert(ls11Synched.pendingTextChange)

      localHandbookService.persistHandbook(lhbSYnched.copy(pendingChange = false))
      localHandbookService.persistChapter(localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).head.copy(pendingChange = false))
      localHandbookService.persistSection(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.copy(pendingTitleChange = false, pendingTextChange = false), Some("user"))

      synchronizationService invokePrivate persistCentralHandbookVersion(changedCentralHb, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      assert(!localHandbookService.retrieveHandbook(localHandbook.id.get).get.pendingChange)
      assert(!localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).head.pendingChange)
      assert(!localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.pendingTitleChange)
      assert(!localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.pendingTextChange)
    }
  }

  test("That section title is not overwritten when just text is changed") {
    testInTransaction {

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create local handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))

      // Local and central changes
      localHandbookService.persistSection(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.copy(title = "s11 local"), Some("user"))
      centralHbRepo.persistCentralSection(s11.copy(html = Some("s11 central")), "user")

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      // Local title should not be overwritten, since there is no central change
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.title == "s11 local")
      // Local text should be overwritten, since there is no local change
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.text.get == "s11 central")

    }
  }

  test("That section text is not overwritten when just title is changed") {
    testInTransaction {

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create local handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))

      // Local and central changes
      val section = localHandbookService.persistSection(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.copy(title = "s11 local", text = Some("s11 local"), localTextChange = true, localTitleChange = true), Some("user"))
      val updatedCentralS11 = centralHbRepo.persistCentralSection(s11.copy(html = Some("s11 central"), title = "s11 central"), "user")

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      // Local title should not be overwritten, since there is local change
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.title == "s11 local")
      // Local text should not be overwritten, since there is local change
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.text.get == "s11 local")

      // Keep local changes, discard central
      val section2 = localHandbookService.persistSection(section, Some("user"))
      assert(!section.pendingTextChange)
      assert(!section.pendingTitleChange)
      assert(!localHandbookService.retrieveSection(section.id.get).get.pendingTitleChange)
      assert(!localHandbookService.retrieveSection(section.id.get).get.pendingTextChange)

      // New central title change
      centralHbRepo.persistCentralSection(updatedCentralS11.copy(title = "s11 central 2"), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      val section3 = localHandbookService.persistSection(section2.copy(title = "s11 sentral 2"), Some("user"))

      assert(localHandbookService.retrieveSection(section.id.get).get.text.contains("s11 local"))
    }
  }

  test("That section title is not overwritten when text and title is changed") {
    testInTransaction {

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create local handbook
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))

      // Local and central changes
      localHandbookService.persistSection(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.copy(title = "s11 local", text = Some("s11 local"), localTextChange = true, localTitleChange = true), Some("user"))
      centralHbRepo.persistCentralSection(s11.copy(title = "s11 central", html = Some("s11 central")), "user")

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      // Local title should not be overwritten, since there is local change
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.title == "s11 local")
      // Local text should not be overwritten, since there is local change
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head.text.get == "s11 local")

    }
  }

  test("That sync of pure local handbook with isolated central base chapter does not insert other central chapters") {
    testInTransaction {
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")
      val ch3 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      val pureLocal = localHandbookService.persistHandbook(Handbook(None, "helt lokal", None, "9900"))
      localHandbookService.persistChapterWithChildren(Chapter(None, ch1.title, ch1.id, centralHandbook.id, pureLocal.id.get, None, None), Option("<EMAIL>"), Option("9900"))

      synchronizationService invokePrivate synchronizeLocalHandbook(pureLocal.id.get, centralHandbook.id.get)

      assert(localHandbookService.retrieveChaptersForHandbook(pureLocal.id.get).size == 1)
      assert(localHandbookService.retrieveChaptersForHandbook(pureLocal.id.get).head.title == ch1.title)

    }
  }

  test("That sync centrally deleted chapter containg a sction does not fail") {
    testInTransaction {
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")
      val ch3 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      val pureLocal = localHandbookService.persistHandbook(Handbook(None, "helt lokal", None, "9900"))
      localHandbookService.persistChapterWithChildren(Chapter(None, ch1.title, ch1.id, centralHandbook.id, pureLocal.id.get, None, None), Option("<EMAIL>"), Option("9900"))
      localHandbookService.persistChapterWithChildren(Chapter(None, ch2.title, ch2.id, centralHandbook.id, pureLocal.id.get, None, None), Option("<EMAIL>"), Option("9900"))

      componentRegistry.centralHandbookRepository.deleteCentralChapter(ch1.id.get)
      val hbVersion2 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      synchronizationService invokePrivate synchronizeLocalHandbook(pureLocal.id.get, centralHandbook.id.get)

      assert(localHandbookService.retrieveChaptersForHandbook(pureLocal.id.get).size == 1)
      assert(localHandbookService.retrieveChaptersForHandbook(pureLocal.id.get).head.title == ch2.title)

    }
  }

  test("That sync central based section in pure local chapter does not fail") {
    testInTransaction {
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")
      val ch3 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      val pureLocal = localHandbookService.persistHandbook(Handbook(None, "helt lokal", None, "9900"))
      val lch1 = localHandbookService.persistChapter(Chapter(None, "ch1 local", None, None, pureLocal.id.get, None, None), Option("9900"))
      val lsec = localHandbookService.persistSection(Section(None, s11.title, s11.html, s11.id, centralHandbook.id, pureLocal.id.get, lch1.id.get, None), Some("user"))
      centralHbRepo.deleteCentralSection(s11.id.get)

      val hbVersion2 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(pureLocal.id.get, centralHandbook.id.get)

      assert(localHandbookService.retrieveChaptersForHandbook(pureLocal.id.get).size == 1)
      assert(localHandbookService.retrieveChaptersForHandbook(pureLocal.id.get).head.title == "ch1 local")
      assert(localHanbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook(pureLocal.id.get).nonEmpty)
      assert(localHanbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook(pureLocal.id.get).head.isDeleted.getOrElse(false))
    }
  }

  test("That a locally changed section is not deleted when it is deleted centrally") {
    testInTransaction {
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      // Create local hb
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))
      val ls11 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head

      // Local change & central deletion
      localHandbookService.persistSection(ls11.copy(title = "s11 local", text = Some("s11 local"), localTitleChange = true, localTextChange = true), Some("user"))
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTitleChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTextChange)
      centralHbRepo.deleteCentralSection(s11.id.get)

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      // Keep local version
      localHandbookService.persistSection(ls11.copy(title = "s11 local", text = Some("s11 local"), localTitleChange = true, localTextChange = true), Some("user"))
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTitleChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTextChange)

      // Another sync
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      assert(localHandbookService.retrieveSection(ls11.id.get).isDefined)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTitleChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTextChange)
    }
  }

  test("That sections and chapters don't get deletion warnings twice") {
    testInTransaction {
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")

      val synchronizationService = componentRegistry.handbookSynchronizationService
      val hbVersion1 = synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hbVersion1._1, Option("9900"), Option("<EMAIL>"))
      val ls11 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head
      val lch1 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).head

      // Local change & central delete
      localHandbookService.persistChapter(lch1.copy(title = "ch1 local", localChange = true))
      localHandbookService.persistSection(ls11.copy(title = "s11 local", text = Some("s11 local"), localTextChange = true, localTitleChange = true), Some("user"))
      assert(localHandbookService.retrieveChapter(lch1.id.get).get.localChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTitleChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTextChange)
      centralHbRepo.deleteCentralChapter(ch1.id.get)
      centralHbRepo.deleteCentralSection(s11.id.get)

      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)

      // Keeping local version
      assert(localHandbookService.retrieveChapter(lch1.id.get).get.localChange)
      assert(localHandbookService.retrieveChapter(lch1.id.get).get.pendingDeletion)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.pendingDeletion)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTitleChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTextChange)
      localHandbookService.persistSection(ls11.copy(title = "s11 local", text = Some("s11 local"), localTitleChange = true, localTextChange = true), Some("user"))
      localHandbookService.persistChapter(lch1.copy(title = "ch1 local", localChange = true))
      assert(localHandbookService.retrieveChapter(lch1.id.get).get.localChange)
      assert(!localHandbookService.retrieveChapter(lch1.id.get).get.pendingDeletion)
      assert(!localHandbookService.retrieveSection(ls11.id.get).get.pendingDeletion)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTitleChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTextChange)

      // New sync
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "user")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      assert(localHandbookService.retrieveSection(ls11.id.get).isDefined)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTitleChange)
      assert(localHandbookService.retrieveSection(ls11.id.get).get.localTextChange)
      assert(!localHandbookService.retrieveSection(ls11.id.get).get.pendingDeletion)
      assert(localHandbookService.retrieveChapter(lch1.id.get).isDefined)
      assert(localHandbookService.retrieveChapter(lch1.id.get).get.localChange)
      assert(!localHandbookService.retrieveChapter(lch1.id.get).get.pendingDeletion)
    }
  }

  test("That sections and chapters moved to handbooks based on different central handbooks are synced") {
    testInTransaction {
      // Create central handbooks
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook1 = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook1", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook1.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook1.id.get, Some("s11")), "user")
      val centralHandbook2 = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook2", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")

      // Publicize handbooks
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook1.id.get, DateTime.now(), "user")
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook2.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()
      val hb1Version = componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(centralHandbook1.id.get).get
      componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(centralHandbook1.id.get).get

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook1.id.get, centralHandbook2.id.get))

      // Create local handbooks
      val localHandbook1 = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook1.id, "9900"))
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook1, hb1Version, Option("9900"), Option("<EMAIL>"))
      val ls11 = localHandbookService.retrieveSectionsForHandbook(localHandbook1.id.get).head
      val lch1 = localHandbookService.retrieveChaptersForHandbook(localHandbook1.id.get).head
      val localHandbook2 = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook2.id, "9900"))

      // Move chapter
      localHandbookService.persistChapter(lch1.copy(handbookId = localHandbook2.id.get))
      localHandbookService.persistSection(ls11.copy(handbookId = localHandbook2.id.get), Some("user"))

      // Make central change
      centralHbRepo.persistCentralChapter(ch1.copy(title = "ch1 central"), "user")
      centralHbRepo.persistCentralSection(s11.copy(title = "s11 central", html = Some("s11 central")), "user")

      // Synchronize
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook1.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      // Check result
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook2.id.get).head.title == "s11 central")
      assert(localHandbookService.retrieveSectionsForHandbook(localHandbook2.id.get).head.text.get == "s11 central")
      assert(localHandbookService.retrieveChaptersForHandbook(localHandbook2.id.get).head.title == "ch1 central")

    }
  }

  test("That pure local handbooks don't get title notifications") {
    testInTransaction {
      // Create central handbooks
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook1 = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook1", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook1.id.get), "user")

      // Publicize handbooks
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook1.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()
      val hb1Version = componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(centralHandbook1.id.get).get

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook1.id.get))

      // Pure local handbook, with central based chapter
      val pureLocal = componentRegistry.localHandbookService.persistHandbook(Handbook(None, "helt lokal", None, "9900"))
      componentRegistry.localHandbookService.persistChapterWithChildren(Chapter(None, ch1.title, ch1.id, centralHandbook1.id, pureLocal.id.get, None, None), Option("<EMAIL>"), Option("9900"))
      centralHbRepo.persistCentralHandbook(centralHandbook1.copy(title = "new title"), "user")

      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook1.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      assert(componentRegistry.localHandbookService.retrieveHandbook(pureLocal.id.get).get.title == "helt lokal")
    }
  }

  test("That central based handbooks that contain items based on other handbook don't get title notifications from those handbooks") {
    testInTransaction {
      // Create central handbooks
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook1 = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook1", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val centralHandbook2 = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook2", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook1.id.get), "user")

      // Publicize
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook1.id.get, DateTime.now(), "user")
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook2.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()
      val hb1Version = componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(centralHandbook1.id.get).get

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook1.id.get, centralHandbook2.id.get))

      // Create local, change central title
      val localHandbook2 = componentRegistry.localHandbookService.persistHandbook(Handbook(None, "handbook local", centralHandbook2.id, "9900"))
      componentRegistry.localHandbookService.persistChapterWithChildren(Chapter(None, ch1.title, ch1.id, centralHandbook1.id, localHandbook2.id.get, None, None), Option("<EMAIL>"), Option("9900"))
      centralHbRepo.persistCentralHandbook(centralHandbook1.copy(title = "new title"), "user")

      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook1.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      assert(componentRegistry.localHandbookService.retrieveHandbook(localHandbook2.id.get).get.title == "handbook local")
    }
  }

  test("That a deleted local chapter or section is not re-inserted in next sync") {
    testInTransaction {
      // Create central handbook
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook1", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s11 = centralHbRepo.persistCentralSection(CentralSection(None, "s11", ch1.id.get, centralHandbook.id.get, Some("s11")), "user")
      val ch2 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch2", None, centralHandbook.id.get), "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))

      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      // Create local handbooks
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      val hb1Version = componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(centralHandbook.id.get).get
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hb1Version, Option("9900"), Option("<EMAIL>"))
      val ls11 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).head
      val lch1 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).find(c => c.title == "ch1").get
      val lch2 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).find(c => c.title == "ch2").get
      localHandbookService.deleteChapter(lch2.id.get)
      localHandbookService.deleteSection(ls11.id.get)
      assert(localHanbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook(localHandbook.id.get).exists(s => s.id == ls11.id && s.isDeleted.getOrElse(false)))
      assert(localHanbookRepository.retrieveDeletedAndNotDeletedChaptersForHandbook(localHandbook.id.get).exists(c => c.id == lch2.id && c.isDeleted.getOrElse(false)))

      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      assert(!localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).exists(c => c.title == "ch2"))
      assert(!localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).exists(c => c.title == "s11"))
    }
  }

  test("That central title change does not generate notification when ony local section text is not changed and vice versa") {
    testInTransaction {
      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook1", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")
      val s1 = centralHbRepo.persistCentralSection(CentralSection(None, "s1 title", ch1.id.get, centralHandbook.id.get, Some("s1 text")), "user")
      val s2 = centralHbRepo.persistCentralSection(CentralSection(None, "s2 title", ch1.id.get, centralHandbook.id.get, Some("s2 text")), "user")
      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))
      val hb1Version = componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(centralHandbook.id.get).get
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hb1Version, Option("9900"), Option("<EMAIL>"))
      val ls1 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.title == "s1 title").get
      val ls2 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.title == "s2 title").get
      assert(ls1.title == "s1 title")
      localHandbookService.persistSection(ls1.copy(text = Some("s1 text local")), Some("user"))
      localHandbookService.persistSection(ls2.copy(title = "s1 title local"), Some("user"))
      componentRegistry.centralHandbookService.persistCentralSection(s1.copy(title = "s1 title central"), "user")
      componentRegistry.centralHandbookService.persistCentralSection(s2.copy(html = Some("s1 text central")), "user")

      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      val synchedS1 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.id == ls1.id).get
      val synchedS2 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.id == ls2.id).get
      assert(!synchedS1.pendingTextChange)
      assert(!synchedS1.pendingTitleChange)
      assert(!synchedS2.pendingTextChange)
      assert(!synchedS2.pendingTitleChange)
    }
  }

  test("Test that converting links does not generate notifications") {
    testInTransaction {
      val htmlLocalSection2 = "<p><a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" target=\"_parent\" rel=\"noreffer\">test with target and rel</a> and <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" rel=\"noopener\">test only rel</a></p>"
      val hmtlCentralSection1 = htmlLocalSection2
      val hmtlCentralSection2 = "<p>test <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\">https://www.vg.no/</a></p>"
      val hmtlCentralSection3 = "<p>test <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" target=\"_blank\" rel=\"noopener\">https://www.vg.no/</a></p>"

      val centralHbRepo = componentRegistry.centralHandbookRepository
      val centralHandbook = centralHbRepo.persistCentralHandbook(CentralHandbook(None, "handbook1", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
      val ch1 = centralHbRepo.persistCentralChapter(CentralChapter(None, "ch1", None, centralHandbook.id.get), "user")

      val s1 = centralHbRepo.persistCentralSection(CentralSection(None, "centralSection1", ch1.id.get, centralHandbook.id.get, Some(hmtlCentralSection1)), "user")
      val s2 = centralHbRepo.persistCentralSection(CentralSection(None, "centralSection2", ch1.id.get, centralHandbook.id.get, Some(hmtlCentralSection2)), "user")
      val s3 = centralHbRepo.persistCentralSection(CentralSection(None, "centralSection3", ch1.id.get, centralHandbook.id.get, Some(hmtlCentralSection3)), "user")

      componentRegistry.centralAccessService.updateOrgAccesses("9900", List(centralHandbook.id.get))
      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook.id.get, DateTime.now(), "user")

      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "handbook", centralHandbook.id, "9900"))

      val hb1Version = componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(centralHandbook.id.get).get
      localHandbookService.persistChapterAndSectionsFromCentralHandbook(localHandbook, hb1Version, Option("9900"), Option("<EMAIL>"))

      val chLhb1 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).head
      val chapterLhb1 = localHandbookService.persistChapter(chLhb1.copy(title = "local chapter title", localChange = true))

      val ls1 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.title == "centralSection1").get
      val ls2 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.title == "centralSection2").get
      assert(ls1.title == "centralSection1")
      localHandbookService.persistSection(ls1.copy(title = "s1 title local1", localTitleChange = true), Some("user"))
      localHandbookService.persistSection(ls2.copy(title = "s1 title local2", text = Some("Some new text"), localTitleChange = true, localTextChange = true), Some("user"))

      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      componentRegistry.centralHandbookService.persistCentralSection(s1.copy(title = "s1 title central"), "user")
      componentRegistry.centralHandbookService.persistCentralSection(s2.copy(title = "s2 title central"), "user")

      componentRegistry.centralHandbookService.persistCentralSection(s1.copy(title = "s1 title central"), "user")

      componentRegistry.centralHandbookService.persistCentralSection(s2.copy(title = "s1 text central"), "user")
      val chapterChb1 = componentRegistry.centralHandbookService.persistCentralChapter(ch1.copy(title = "central chapter title"),"user")

      organizationDataService.replaceTargetForLinksInHandbooks("user")

      componentRegistry.centralHandbookPublicationService.createPublication(centralHandbook.id.get, DateTime.now(), "user")
      componentRegistry.handbookSynchronizationService.synchronizeHandbooks()

      val synchedS1 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.id == ls1.id).get
      val synchedS2 = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get).find(s => s.id == ls2.id).get
      val synchedCh = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get).find(c => c.id == chapterLhb1.id).get


      assert(!synchedS1.pendingTextChange)
      assert(synchedS1.pendingTitleChange)
      assert(!synchedS2.pendingTextChange)
      assert(synchedS2.pendingTitleChange)
      assert(synchedCh.pendingChange)
    }
  }

}
