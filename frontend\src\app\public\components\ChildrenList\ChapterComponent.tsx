import React from "react";
import { Title, Icon } from "kf-bui";

import { SectionComponent } from "./SectionComponent";
import type { Chapter, Section } from "@/types";

interface ChapterComponentProps {
  chapter: Chapter;
  chapters: Chapter[];
  sections: Section[];
  onSectionEnter?: (section: Section) => void;
  onSectionLeave?: (section: Section) => void;
  waypointsEnabled?: boolean;
}

export const ChapterComponent: React.FC<ChapterComponentProps> = ({
  chapter,
  chapters,
  sections,
  onSectionEnter,
  onSectionLeave,
  waypointsEnabled = true,
}) => {
  const subChapters = chapters
    .filter((c) => c.parentId === chapter.id)
    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const subSections = sections
    .filter((s) => s.parentId === chapter.id)
    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const chaptersAndSections = [
    ...subChapters.map((c) => ({ ...c, type: "LOCAL_CHAPTER" as const })),
    ...subSections.map((s) => ({ ...s, type: "SECTION" as const })),
  ].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  return (
    <div className="public-chapter-component">
      <hr />
      <Title id={`content-${chapter.id}`} size="4" className="chapter-title">
        <Icon icon="RegBookmark" className="chapter-icon" /> {chapter.title}
      </Title>

      {chaptersAndSections.map((child) =>
        child.type === "LOCAL_CHAPTER" ? (
          <ChapterComponent
            key={child.id}
            chapter={{ ...child, type: "CHAPTER" } as Chapter}
            chapters={chapters}
            sections={sections}
            onSectionEnter={onSectionEnter}
            onSectionLeave={onSectionLeave}
            waypointsEnabled={waypointsEnabled}
          />
        ) : (
          <SectionComponent
            key={child.id}
            section={child as Section}
            onSectionEnter={onSectionEnter}
            onSectionLeave={onSectionLeave}
            waypointsEnabled={waypointsEnabled}
          />
        )
      )}
    </div>
  );
};
