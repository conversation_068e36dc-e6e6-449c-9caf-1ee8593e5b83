import type { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { toast } from '@/shared/components/Toast';

export interface ApiError {
  status: number;
  data?: any;
  message?: string;
}

export const getErrorMessage = (error: unknown): string => {
  if (!error) return 'Ukjent feil';

  if (typeof error === 'object' && error !== null) {
    const fetchError = error as FetchBaseQueryError;
    
    if ('status' in fetchError) {
      switch (fetchError.status) {
        case 400:
          return 'Ugyldig forespørsel. Sjekk inndataene.';
        case 401:
          return 'Du er ikke autorisert. Logg inn på nytt.';
        case 403:
          return 'Du har ikke tilgang til denne ressursen.';
        case 404:
          return 'Ressursen ble ikke funnet.';
        case 409:
          return 'Konflikt. Ressursen er allerede endret.';
        case 500:
          return 'Serverfeil. Prøv igjen senere.';
        case 503:
          return 'Tjenesten er ikke tilgjengelig. Prøv igjen senere.';
        default:
          if (fetchError.data && typeof fetchError.data === 'string') {
            return fetchError.data;
          }
          if (fetchError.data && typeof fetchError.data === 'object' && 'message' in fetchError.data) {
            return fetchError.data.message as string;
          }
          return `HTTP feil ${fetchError.status}`;
      }
    }
    
    if ('error' in fetchError) {
      return 'Nettverksfeil. Sjekk internetttilkoblingen.';
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'En uventet feil oppstod';
};

export const handleOptimisticError = (
  error: unknown,
  operation: string,
  options?: {
    customMessage?: string;
    showToast?: boolean;
    logToConsole?: boolean;
    onRetry?: () => void;
  }
): void => {
  const errorMessage = options?.customMessage || getErrorMessage(error);
  
  if (options?.logToConsole !== false) {
    console.error(`Optimistic update failed for ${operation}:`, error);
  }
  
  if (options?.showToast !== false) {
    toast.error(errorMessage);
  }
};