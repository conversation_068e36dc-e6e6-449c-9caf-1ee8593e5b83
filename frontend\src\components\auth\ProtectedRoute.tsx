import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useSession } from "@/store/services/session/hooks";
import type { SessionInfo } from "@/types";
import { Spinner } from "@/shared/components/Spinner";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  requireOrganization?: boolean;
  session?: SessionInfo | null;
  redirectTo?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requireAdmin = false,
  requireOrganization = false,
  session: propSession,
  redirectTo,
}) => {
  const location = useLocation();
  const { session: hookSession, isAuthenticated } = useSession();

  const session = propSession || hookSession;

  if (requireAuth && !isAuthenticated) {
    const redirect = redirectTo || "/select";
    return <Navigate to={redirect} state={{ from: location }} replace />;
  }

  if (requireAuth && !session) {
    return (
      <div className="loading-container">
        <Spinner text="Loading session..." />
      </div>
    );
  }

  if (requireOrganization && !session?.organization) {
    const redirect = redirectTo || "/select";
    return <Navigate to={redirect} state={{ from: location }} replace />;
  }

  if (requireAdmin && session && !session.isKfAdmin) {
    const redirect = redirectTo || "/forbidden";
    return <Navigate to={redirect} replace />;
  }

  return <>{children}</>;
};

export const useAuthPermissions = () => {
  const { session, isAuthenticated } = useSession();

  return {
    isAuthenticated,
    isAdmin: session?.isKfAdmin || false,
    hasOrganization: !!session?.organization,
    isLocalUser: session?.user?.localUser || false,
    isLocalAdmin: session?.user?.localAdmin || false,
    isGlobalAdmin: session?.user?.globalAdmin || false,
    userOrganizations: session?.user?.organizations || [],
    availableOrganizations: session?.userOrgsWithAccess || [],
  };
};

export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, "children"> = {}
) => {
  return (props: P) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
};
