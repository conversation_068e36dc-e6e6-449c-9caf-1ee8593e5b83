.app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.app h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.app p {
  font-size: 1.2rem;
  color: #666;
}

a.navbar-item:hover,
a.navbar-item.is-active,
.navbar-link:hover,
.navbar-link.is-active {
  color: #050037 !important;
  font-weight: 500;
  background-color: rgba(245, 245, 245, 0.7) !important;
}

.navbar-item.is-tab.is-active {
  color: #050037 !important;
  border-bottom-color: #050037 !important;
}

.navbar-item.is-tab:hover {
  background-color: transparent !important;
  border-bottom-color: #050037 !important;
  color: #050037 !important;
}

.metadata .heading {
  display: block;
  font-size: 11px;
  letter-spacing: 1px;
  margin-bottom: 5px;
  text-transform: uppercase;
}

/* Session Expiry Modal Styles */
.session-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.session-modal {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.session-modal-expired {
  border: 2px solid #dc3545;
}

.session-modal-warning {
  border: 2px solid #ffc107;
}

.session-modal-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: bold;
}

.session-expired-message-header {
  margin-bottom: 12px;
}

.session-modal-header-expired {
  color: #dc3545;
}

.session-modal-header-warning {
  color: #856404;
}

.session-modal-content {
  margin-bottom: 20px;
  line-height: 1.5;
}

.session-modal-countdown {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  margin: 16px 0;
  padding: 12px;
  background-color: #fff3cd;
  border-radius: 4px;
  color: #856404;
}

.session-modal-actions {
  text-align: right;
}

.session-modal-button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  margin: 0 8px;
}

.session-modal-button-close {
  background-color: #6c757d;
  color: white;
}

.session-modal-button-close:hover {
  background-color: #545b62;
}

.session-modal-button-keep-alive {
  background-color: #28a745;
  color: white;
}

.session-modal-button-keep-alive:hover {
  background-color: #218838;
}

.session-modal-button-keep-alive:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.session-modal-button-keep-alive:disabled:hover {
  background-color: #28a745;
}

.central-export-modal .modal-content {
  overflow: unset;
}

.pending-warning .tag {
  height: 100%;
}

.merge-column.column .right-radio {
  margin: 8px 0 12px 0;
}

.no-chapters {
  margin-top: 12px;
  font-style: italic;
}

.menu-list a.is-active {
  background-color: #050037;
}

.menu-list a.is-active:hover {
  background-color: #070057;
}
