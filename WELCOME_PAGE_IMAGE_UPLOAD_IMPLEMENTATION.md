# Welcome Page Image Upload Implementation

## Overview

This document summarizes the implementation of proper image upload and storage functionality for the Welcome Page feature. The changes ensure that images are stored correctly in the database and file system, supporting both draft and publish states with a maximum file size of 5MB.

## Changes Made

### 1. Database Schema Updates

**Files Modified:**
- `src/main/resources/migrate-db-26.sql`
- `src/main/resources/migrate-db-26-mssql.sql`

**Changes:**
- Updated `welcome_image` column from `VARCHAR(255)` to `VARCHAR(500)` to accommodate longer image file paths
- Added documentation explaining that the column stores image file references (paths) returned by ImageService
- Changes integrated into existing migration scripts to avoid additional database version

### 2. REST API Enhancements

**File Modified:** `src/main/scala/no/kf/handboker/rest/WelcomePageServlet.scala`

**Changes:**
- Added `FileUploadSupport` trait for handling multipart file uploads
- Added `imageService` dependency injection
- Implemented new endpoints:
  - `POST /api/welcome-page/draft/:handbookId/image` - Upload image to draft
  - `DELETE /api/welcome-page/draft/:handbookId/image` - Remove image from draft
- Added comprehensive image validation:
  - File size validation (5MB maximum)
  - Content type validation (images only)
  - Supported format validation (jpg, jpeg, png, gif, webp)
- Added `getUploadedImage()` helper method for image extraction and validation

### 3. Service Layer Updates

**File Modified:** `src/main/scala/no/kf/handboker/service/welcomepage/WelcomePageService.scala`

**Changes:**
- Added `ImageServiceComponent` dependency
- Implemented new service methods:
  - `updateWelcomeImage()` - Handles image upload, persistence, and customization update
  - `removeWelcomeImage()` - Removes image and clears related fields
- Enhanced `discardDraft()` to clean up associated images when drafts are deleted
- Added proper image cleanup logic to prevent orphaned files
- Updated audit fields (imageUpdatedBy, imageUpdatedAt) when images are modified

### 4. Image Storage Integration

**Integration with Existing ImageService:**
- Leverages the existing `ImageService` for consistent image storage patterns
- Images are stored in the file system using the established folder structure
- Image references (URLs) are stored in the database, not binary data
- Follows the same naming and storage conventions as other image uploads in the system

### 5. Testing

**File Created:** `src/test/scala/no/kf/handboker/service/WelcomePageImageServiceTest.scala`

**Test Coverage:**
- Image upload and persistence verification
- Image removal functionality
- Image replacement scenarios
- Draft-only operation validation
- Proper audit field updates

## API Usage

### Create Draft with Optional Image
```http
POST /api/welcome-page/draft/{handbookId}
Content-Type: multipart/form-data

file: [optional image file, max 5MB]
```

### Update Draft with Optional Image and Data
```http
PUT /api/welcome-page/draft/{handbookId}
Content-Type: multipart/form-data

file: [optional image file, max 5MB]
data: [optional JSON string with WelcomePageDto]
```

**OR for JSON-only updates:**
```http
PUT /api/welcome-page/draft/{handbookId}
Content-Type: application/json

{
  "versionId": "...",
  "customization": { ... },
  "linkCollections": [ ... ],
  "shortcutCollections": [ ... ]
}
```

### Standalone Image Operations (Alternative)
```http
POST /api/welcome-page/draft/{handbookId}/image
Content-Type: multipart/form-data

file: [image file, max 5MB]
```

```http
DELETE /api/welcome-page/draft/{handbookId}/image
```

**Response:**
```json
{
  "imageUrl": "/handboker/images/generated-filename.jpg"
}
```

## Validation Rules

1. **File Size:** Maximum 5MB (5,242,880 bytes)
2. **File Types:** Images only (content-type must start with "image/")
3. **Supported Formats:** jpg, jpeg, png, gif, webp
4. **Draft Only:** Image operations only work on draft versions
5. **Authentication:** User must be authenticated (uses currentUser.email)

## Error Handling

- **File too large:** Returns user-friendly error with actual vs. maximum size
- **Invalid file type:** Returns error for non-image files
- **Unsupported format:** Lists supported formats in error message
- **No draft found:** Returns 404 when trying to modify non-existent draft
- **No file provided:** Returns 400 when upload request has no file

## Database Impact

- The `welcome_image` field now stores file paths/URLs instead of text content
- Existing data remains compatible (VARCHAR field can store both text and paths)
- New migration scripts handle the column size increase
- Audit fields properly track image modifications

## Architecture Compliance

- Follows existing patterns established by `ImageUploadServlet` and `ImageService`
- Maintains consistency with other file upload functionality
- Respects the draft/publish workflow
- Integrates seamlessly with existing transaction management
- Uses established dependency injection patterns

## Future Considerations

1. **Image Optimization:** Consider adding image resizing/compression for large uploads
2. **Format Conversion:** Potential automatic conversion to optimized formats
3. **CDN Integration:** Future enhancement for serving images from CDN
4. **Bulk Operations:** Support for multiple image uploads if needed
5. **Image Metadata:** Enhanced support for alt text, captions, and credits through the UI

## Migration Notes

- The `welcome_image` column size increase is included in the existing `migrate-db-26.sql` and `migrate-db-26-mssql.sql` scripts
- No additional database migration required - changes are integrated into the current version
- No data migration required - existing text content in `welcome_image` field remains valid
- New image uploads will use the proper file storage approach
- Old text-based content can be gradually replaced through normal editing workflows