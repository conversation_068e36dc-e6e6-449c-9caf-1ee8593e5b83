import { useCallback } from "react";
import { toast } from "@/shared/components/Toast";

export const useOptimisticMutation = () => {
  const handleOptimisticUpdate = useCallback(
    async <TResult, TArgs>(
      mutationFn: (args: TArgs) => any,
      args: TArgs,
      options?: {
        successMessage?: string;
        errorMessage?: string;
        onSuccess?: (result: TResult) => void;
        onError?: (error: unknown) => void;
      }
    ): Promise<TResult | null> => {
      try {
        const result = await mutationFn(args).unwrap();

        if (options?.successMessage) {
          toast.success(options.successMessage);
        }

        if (options?.onSuccess && result) {
          options.onSuccess(result);
        }

        return result;
      } catch (error) {
        console.error("Optimistic update failed:", error);

        const errorMessage =
          options?.errorMessage || "En feil oppstod under lagring";
        toast.error(errorMessage);

        if (options?.onError) {
          options.onError(error);
        }

        return null;
      }
    },
    []
  );

  return { handleOptimisticUpdate };
};

export const generateTempId = (prefix: string = "temp"): string => {
  return `temp-${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

export const isTempId = (id: string): boolean => {
  return id.startsWith("temp-");
};
