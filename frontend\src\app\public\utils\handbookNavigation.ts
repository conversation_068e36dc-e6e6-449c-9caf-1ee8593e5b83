import type { Chapter, Section } from "@/types";

export const getRootChapterId = (
  chapterId: string,
  chapters: Chapter[]
): string => {
  const chapter = chapters.find((c) => c.id === chapterId);
  if (!chapter || !chapter.parentId) {
    return chapterId;
  }
  return getRootChapterId(chapter.parentId, chapters);
};

export const isRootChapter = (
  chapterId: string,
  chapters: Chapter[]
): boolean => {
  const chapter = chapters.find((c) => c.id === chapterId);
  return chapter ? !chapter.parentId : false;
};

export const getParentChapterIds = (
  sectionId: string,
  chapters: Chapter[],
  sections: Section[]
): string[] => {
  const section = sections.find((s) => s.id === sectionId);
  if (!section || !section.parentId) return [];

  const parents: string[] = [];
  let currentParentId: string | undefined = section.parentId;

  while (currentParentId) {
    const parentChapter = chapters.find((c) => c.id === currentParentId);
    if (!parentChapter) break;

    parents.push(currentParentId);
    currentParentId = parentChapter.parentId;
  }

  return parents;
};

export const getFirstSectionOfChapter = (
  chapterId: string | null,
  sections: Section[],
  chapters?: Chapter[]
): Section | null => {
  if (!chapterId) return null;

  const directSections = sections
    .filter((s) => s.parentId === chapterId)
    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const childChapters = chapters
    ? chapters
        .filter((c) => c.parentId === chapterId)
        .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
    : [];

  const firstDirectSection = directSections[0];
  const firstChildChapter = childChapters[0];

  if (!firstChildChapter) {
    return firstDirectSection || null;
  }

  if (!firstDirectSection) {
    return getFirstSectionOfChapter(firstChildChapter.id!, sections, chapters);
  }

  const childChapterSortOrder = firstChildChapter.sortOrder || 0;
  const directSectionSortOrder = firstDirectSection.sortOrder || 0;

  if (childChapterSortOrder <= directSectionSortOrder) {
    return getFirstSectionOfChapter(firstChildChapter.id!, sections, chapters);
  } else {
    return firstDirectSection;
  }
};
