import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import type { CentralTreeNode } from "@/types";

interface CentralTreeState {
  selectedCentralItem: CentralTreeNode | null;
}

const initialState: CentralTreeState = {
  selectedCentralItem: null,
};

export const centralTreeSlice = createSlice({
  name: "centralTree",
  initialState,
  reducers: {
    setSelectedCentralItem: (state, action: PayloadAction<CentralTreeNode | null>) => {
      state.selectedCentralItem = action.payload;
    },
    clearSelectedCentralItem: (state) => {
      state.selectedCentralItem = null;
    },
  },
});

export const { setSelectedCentralItem, clearSelectedCentralItem } = centralTreeSlice.actions;

export default centralTreeSlice.reducer;

// Selectors
export const selectSelectedCentralItem = (state: { centralTree: CentralTreeState }) =>
  state.centralTree.selectedCentralItem;