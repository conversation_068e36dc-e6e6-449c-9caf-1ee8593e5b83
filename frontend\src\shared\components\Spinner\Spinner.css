@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-container--below {
  flex-direction: column;
  gap: 0.5rem;
}

.spinner-container--inline {
  flex-direction: row;
  gap: 0.75rem;
}

.spinner-icon {
  border-radius: 50%;
  animation: spin 1s linear infinite;
  border-style: solid;
  border-color: #f3f3f3;
  border-top-color: #3498db;
}

.spinner-icon--small {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.spinner-icon--medium {
  width: 24px;
  height: 24px;
  border-width: 3px;
}

.spinner-icon--large {
  width: 32px;
  height: 32px;
  border-width: 4px;
}

.spinner-text {
  color: #666;
}

.spinner-text--small {
  font-size: 0.875rem;
}

.spinner-text--medium {
  font-size: 1rem;
}

.spinner-text--large {
  font-size: 1.125rem;
}

.loading-container {
  display: flex;
  justify-content: center;
}