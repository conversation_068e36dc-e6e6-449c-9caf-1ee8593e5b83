import React, { useState } from "react";
import {
  Button,
  Field,
  Input,
  Label,
  Help,
  Addons,
  Form,
  SrOnly,
} from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { LinkCollection } from "@/types";

interface LinkCollectionFormProps {
  handbookId: string;
  sortOrder: number;
  onSave: (linkCollection: LinkCollection) => void;
  linkCollection?: LinkCollection;
  onCancel?: () => void;
}

export const LinkCollectionForm = ({
  handbookId,
  sortOrder,
  onSave,
  linkCollection,
  onCancel,
}: LinkCollectionFormProps) => {
  const [title, setTitle] = useState(linkCollection?.title || "");
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<{ title?: string }>({});

  const t = usePrefixedTranslation(
    "editor.containers.LinkPage.components.LinkCollectionForm"
  );
  const isEditing = !!linkCollection;

  const validateForm = () => {
    const newErrors: { title?: string } = {};

    if (!title || !title.trim()) {
      newErrors.title = "Lenkesamlingen må ha en tittel";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    const linkCollectionData: LinkCollection = {
      id: linkCollection?.id || crypto.randomUUID(),
      title: title?.trim() || "",
      handbookId,
      sortOrder: linkCollection?.sortOrder ?? sortOrder,
      links: linkCollection?.links ?? [],
    };

    try {
      await onSave(linkCollectionData);
      if (!isEditing) {
        setTitle("");
        setErrors({});
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setTitle(linkCollection?.title || "");
    setErrors({});
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Addons>
        <Field>
          <SrOnly>
            <Label htmlFor="link-collection-title">
              {t("titleInputPlaceholder")}
            </Label>
          </SrOnly>
          <Input
            id="link-collection-title"
            type="text"
            value={title}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setTitle(e.target.value)
            }
            placeholder={t("titleInputPlaceholder")}
            required
            size="small"
            color={errors.title ? "danger" : undefined}
          />
          {errors.title && <Help color="danger">{errors.title}</Help>}
        </Field>

        {isEditing && onCancel && (
          <Button
            control
            color="danger"
            size="small"
            type="button"
            onClick={handleCancel}
            disabled={isSaving}
          >
            {t("cancelButtonText")}
          </Button>
        )}

        <Button
          control
          size="small"
          color="primary"
          type="submit"
          disabled={!title || !title.trim() || isSaving}
          loading={isSaving}
        >
          {isEditing ? "Lagre" : t("addButtonText")}
        </Button>
      </Addons>
    </Form>
  );
};
