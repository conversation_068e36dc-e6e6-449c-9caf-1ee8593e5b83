package no.kf.handboker.rest

import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.support.JsonSupport
import org.scalatra.ScalatraServlet

class SessionServlet extends ScalatraServlet with SessionSupport with JsonSupport {

  get("/?") {
    log.debug("Getting current session")
    currentSession
  }

  post("/:externalOrgId/?") {
    val externalOrgId = extractRequiredParam("externalOrgId")
    log.debug(s"Setting external organization in session to <$externalOrgId>")
    setExternalOrganization(extractRequiredParam("externalOrgId"))
    currentSession
  }

  get("/editors/:externalOrgId/?") {
//    if (!userHasSystemAccess(currentUser)) {
//      ScalatraExceptions.forbiddenException("You do not have sufficient privileges")
//    }
    val externalOrgId = extractRequiredParam("externalOrgId")
    log.debug(s"Getting editors for external organization <$externalOrgId>")
    getEditorsForOrganization(externalOrgId)
  }
}
