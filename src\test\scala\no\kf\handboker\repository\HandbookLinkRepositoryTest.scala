package no.kf.handboker.repository

import no.kf.handboker.model.local.{Handbook, Link, LinkCollection}
import org.junit.runner.RunWith
import org.scalatest.FunSuite
import org.scalatest.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class HandbookLinkRepositoryTest extends FunSuite with DbTestHandler {

  private val repo = componentRegistry.handbookLinkRepository

  transactedTest("That we can insert and retrieve an empty handbook link collection") {
    val handbook = componentRegistry.handbookRepository.persistHandbook(Handbook(None, "H title", None, "9900"))
    val linkCollection = LinkCollection(None, "title", handbook.id.get, 0, Nil)
    val linkCollectionId = repo.persistLinkCollection(linkCollection).id.get

    val retrievedHandbookLinks = repo.retrieveLinkCollectionsForHandbook(handbook.id.get)

    assert(retrievedHandbookLinks.length === 1)
    assert(retrievedHandbookLinks.head === linkCollection.copy(id = Some(linkCollectionId)))
  }

  transactedTest("That we can insert and retrieve a handbook link collection with links") {
    val handbook = componentRegistry.handbookRepository.persistHandbook(Handbook(None, "H title", None, "9900"))
    val links = List(Link(None, "title", "url", 0), Link(None, "title", "url", 1))
    val linkCollection = LinkCollection(None, "Collection name", handbook.id.get, 0, links)

    val persistedLinkCollection = repo.persistLinkCollection(linkCollection)

    val retrievedHandbookLinks = repo.retrieveLinkCollectionsForHandbook(handbook.id.get)

    assert(retrievedHandbookLinks.length === 1)
    assert(retrievedHandbookLinks.head === persistedLinkCollection)
  }

  transactedTest("That we can update the title and sort order of a handbook link collection") {
    val handbook = componentRegistry.handbookRepository.persistHandbook(Handbook(None, "H title", None, "9900"))
    val oldTitle = "Collection title"
    val oldSortOrder = 0
    val newTitle = "New collection title"
    val newSortOrder = 3
    val linkCollection = LinkCollection(None, oldTitle, handbook.id.get, oldSortOrder, Nil)

    val persistedLinkCollection = repo.persistLinkCollection(linkCollection)

    val updatedLinkCollection = repo.persistLinkCollection(persistedLinkCollection.copy(title = newTitle, sortOrder = newSortOrder))

    assert(persistedLinkCollection.id === updatedLinkCollection.id)
    assert(persistedLinkCollection.title === oldTitle)
    assert(persistedLinkCollection.sortOrder === oldSortOrder)
    assert(updatedLinkCollection.title === newTitle)
    assert(updatedLinkCollection.sortOrder === newSortOrder)
  }

  transactedTest("That we can delete a link collection") {
    val handbook = componentRegistry.handbookRepository.persistHandbook(Handbook(None, "H title", None, "9900"))
    val links = List(Link(None, "title", "url", 0), Link(None, "title", "url", 1))
    val linkCollection = LinkCollection(None, "Collection name", handbook.id.get, 0, links)

    repo.persistLinkCollection(linkCollection)
    val persistedLinkCollection = repo.persistLinkCollection(linkCollection.copy(sortOrder = 1))
    assert(repo.retrieveLinkCollectionsForHandbook(handbook.id.get).length === 2)

    repo.deleteLinkCollection(persistedLinkCollection.id.get)
    assert(repo.retrieveLinkCollectionsForHandbook(handbook.id.get).length === 1)
  }

  transactedTest("That we can add links to a link collection") {
    val handbook = componentRegistry.handbookRepository.persistHandbook(Handbook(None, "H title", None, "9900"))
    val links = List(Link(None, "title", "url", 0), Link(None, "title", "url", 1))
    val linkCollection = LinkCollection(None, "Collection name", handbook.id.get, 0, Nil)

    val persistedLinkCollection = repo.persistLinkCollection(linkCollection)

    assert(persistedLinkCollection.links === Nil)

    val persistedLink1 = repo.persistLink(links.head, persistedLinkCollection.id.get)
    val persistedLink2 = repo.persistLink(links.tail.head, persistedLinkCollection.id.get)

    val updatedLinkCollection = repo.retrieveLinkCollectionsForHandbook(handbook.id.get)

    assert(updatedLinkCollection.head.links.contains(persistedLink1))
    assert(updatedLinkCollection.head.links.contains(persistedLink2))
  }

  transactedTest("That we can update a link in link collection") {
    val handbook = componentRegistry.handbookRepository.persistHandbook(Handbook(None, "H title", None, "9900"))
    val link = Link(None, "title", "url", 1)
    val linkCollection = LinkCollection(None, "Collection name", handbook.id.get, 0, link :: Nil)

    val persistedLinkCollection = repo.persistLinkCollection(linkCollection)
    val persistedLink = persistedLinkCollection.links.head

    val updatedLink = repo.persistLink(persistedLink.copy(title = "new title", url = "new url", sortOrder = 123), persistedLinkCollection.id.get)
    val retrievedUpdatedLink = repo.retrieveLinkCollectionsForHandbook(handbook.id.get).head.links.head

    assert(updatedLink !== persistedLink)
    assert(updatedLink.title !== persistedLink.title)
    assert(updatedLink.url !== persistedLink.url)
    assert(updatedLink.sortOrder !== persistedLink.sortOrder)

    assert(retrievedUpdatedLink === updatedLink)
  }

  transactedTest("That we can delete a link from a link collection") {
    val handbook = componentRegistry.handbookRepository.persistHandbook(Handbook(None, "H title", None, "9900"))
    val link = Link(None, "title", "url", 1)
    val linkCollection = LinkCollection(None, "Collection name", handbook.id.get, 0, link :: Nil)

    val persistedLinks = repo.persistLinkCollection(linkCollection).links

    assert(persistedLinks.length === 1)

    repo.deleteLink(persistedLinks.head.id.get)

    val retrievedLinks = repo.retrieveLinkCollectionsForHandbook(handbook.id.get).head.links

    assert(retrievedLinks.length === 0)
  }
}
