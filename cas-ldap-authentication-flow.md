# CAS Authentication & LDAP Authorization Flow
**Comprehensive Visual Guide for KF Handbook System**

## Overview

This document provides a complete visual explanation of the CAS (Central Authentication Service) authentication and LDAP authorization flow in the KF Handbook system, covering user login, session management, and permission handling.

## System Architecture Overview

```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  CAS AUTHENTICATION & LDAP AUTHORIZATION ARCHITECTURE                                       │
│                                                                                             │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐               │
│  │ User        │     │ KF Handbook │     │ CAS Server  │     │ LDAP Server │               │
│  │ Browser     │────►│ Application │────►│ (SSO)       │────►│ (AD/LDAP)   │               │
│  │             │     │             │     │             │     │             │               │
│  └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘               │
│         │                     │                     │                     │                 │
│         │                     │                     │                     │                 │
│         ▼                     ▼                     ▼                     ▼                 │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐               │
│  │ Session     │     │ Filter      │     │ Ticket      │     │ User        │               │
│  │ Storage     │◄────│ Chain       │◄────│ Validation  │◄────│ Attributes  │               │
│  │             │     │             │     │             │     │             │               │
│  └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘               │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Complete Authentication Flow

### Phase 1: Initial Access Request
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 1: USER INITIATES ACCESS                                                            │
│                                                                                             │
│  User Action: Types URL in browser                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 🌐 https://handbook.kf.no/9900/safety-manual                                          │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Browser Request:                                                                          │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ GET /9900/safety-manual HTTP/1.1                                                      │   │
│  │ Host: handbook.kf.no                                                                  │   │
│  │ User-Agent: Mozilla/5.0...                                                            │   │
│  │ Cookie: (empty - no session yet)                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Application Processing:                                                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 1. Request hits web.xml filter chain                                                  │   │
│  │ 2. CharacterEncodingFilter: Sets UTF-8 encoding                                       │   │
│  │ 3. ReadingLinkValidFilter: Checks if valid reading link                               │   │
│  │ 4. CAS Single Sign Out Filter: Checks for logout requests                             │   │
│  │ 5. CAS Authentication Filter: 🚨 NO VALID SESSION FOUND                               │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 2: CAS Authentication Filter Processing
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 2: CAS AUTHENTICATION FILTER ACTIVATION                                             │
│                                                                                             │
│  Filter: WrappedAuthenticationFilter.scala                                                │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ class WrappedAuthenticationFilter extends GenericCasFilter {                          │   │
│  │   lazy val wrapperFilter = authenticationFilter                                       │   │
│  │   lazy val casConfigConfigKey = CasConfig                                             │   │
│  │   lazy val siteUrlConfigKey = SiteUrl                                                 │   │
│  │   lazy val settings: Settings = ProductionRegistry.componentRegistry.settings        │   │
│  │                                                                                       │   │
│  │   override val uncheckedPathInfos = FilterConstants.UNCHECKED_PATHS                  │   │
│  │ }                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Configuration Loading:                                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ From handboker.properties:                                                            │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ # CAS Configuration                                                               │ │   │
│  │ │ CasConfig=internal-demo                                                           │ │   │
│  │ │ SiteUrl=https://handbook.kf.no                                                    │ │   │
│  │ │                                                                                   │ │   │
│  │ │ # LDAP Configuration                                                              │ │   │
│  │ │ UseLDAP=true                                                                      │ │   │
│  │ │ LDAPSearchFilter=(mail=$EPOST)                                                    │ │   │
│  │ │ LDAPSearchBase=dc=kinn,dc=id,dc=local                                             │ │   │
│  │ │ LDAPHost=************                                                             │ │   │
│  │ │ LDAPPort=389                                                                      │ │   │
│  │ │ LDAPUser=CN=DelegeringAppUser,OU=KFApplicationUsers,DC=kinn,DC=id,DC=local       │ │   │
│  │ │ LDAPPwd=testtest                                                                  │ │   │
│  │ │ LDAPMailAttribute=mail                                                            │ │   │
│  │ │ LDAPMaxResults=5000                                                               │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Path Checking:                                                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Unchecked Paths (no authentication required):                                         │   │
│  │ • /publikum/*     (public pages)                                                      │   │
│  │ • /readinglink/*  (shared reading links)                                              │   │
│  │ • /api/public/*   (public API endpoints)                                              │   │
│  │ • /health         (health check)                                                      │   │
│  │                                                                                       │   │
│  │ Current path: /9900/safety-manual                                                     │   │
│  │ Result: 🚨 AUTHENTICATION REQUIRED                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 3: CAS Server Redirect
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 3: REDIRECT TO CAS SERVER                                                           │
│                                                                                             │
│  Application Response:                                                                      │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ HTTP/1.1 302 Found                                                                    │   │
│  │ Location: https://cas.kf.no/cas/login?service=https%3A%2F%2Fhandbook.kf.no%2F9900%2F │   │
│  │           safety-manual                                                                │   │
│  │ Set-Cookie: JSESSIONID=ABC123; Path=/; HttpOnly; Secure                               │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Browser Automatic Redirect:                                                              │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 🌐 Browser navigates to CAS login page                                                │   │
│  │                                                                                       │   │
│  │ URL: https://cas.kf.no/cas/login?service=https%3A%2F%2Fhandbook.kf.no%2F9900%2F      │   │
│  │      safety-manual                                                                    │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  CAS Login Page Display:                                                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │                        🏛️ KF CAS Login                                           │ │   │
│  │ │                                                                                 │ │   │
│  │ │ Username: [________________]                                                    │ │   │
│  │ │ Password: [________________]                                                    │ │   │
│  │ │                                                                                 │ │   │
│  │ │ [🔐 Login]                                                                      │ │   │
│  │ │                                                                                 │ │   │
│  │ │ Service: KF Handbook System                                                     │ │   │
│  │ │ You will be redirected after successful login                                  │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 4: User Authentication
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 4: USER PROVIDES CREDENTIALS                                                        │
│                                                                                             │
│  User Input:                                                                               │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Username: <EMAIL>                                                               │   │
│  │ Password: ••••••••••••••                                                               │   │
│  │ [🔐 Login] ← User clicks                                                               │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  CAS Server Processing:                                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 1. Validate credentials against Active Directory/LDAP                                 │   │
│  │ 2. Create Ticket Granting Ticket (TGT)                                                │   │
│  │ 3. Generate Service Ticket (ST) for handbook application                              │   │
│  │ 4. Store user session in CAS server                                                   │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  CAS Server Response:                                                                      │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ HTTP/1.1 302 Found                                                                    │   │
│  │ Location: https://handbook.kf.no/9900/safety-manual?ticket=ST-123456789-abcdef       │   │
│  │ Set-Cookie: CASTGC=TGT-987654321; Domain=.kf.no; Path=/; HttpOnly; Secure            │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Browser Redirect with Ticket:                                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 🌐 Browser returns to handbook application with service ticket                        │   │
│  │                                                                                       │   │
│  │ GET /9900/safety-manual?ticket=ST-123456789-abcdef HTTP/1.1                          │   │
│  │ Host: handbook.kf.no                                                                  │   │
│  │ Cookie: JSESSIONID=ABC123                                                             │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 5: Ticket Validation
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 5: CAS TICKET VALIDATION                                                            │
│                                                                                             │
│  Filter Chain Processing:                                                                  │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 1. CharacterEncodingFilter: ✅ UTF-8 encoding set                                      │   │
│  │ 2. ReadingLinkValidFilter: ✅ Not a reading link                                       │   │
│  │ 3. CAS Single Sign Out Filter: ✅ Not a logout request                                 │   │
│  │ 4. CAS Authentication Filter: ✅ Ticket parameter found                                │   │
│  │ 5. CAS Validation Filter: 🔍 VALIDATE TICKET                                          │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Validation Filter: WrappedCas20ProxyReceivingTicketValidationFilter                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Ticket Validation Request to CAS Server:                                              │   │
│  │                                                                                       │   │
│  │ GET https://cas.kf.no/cas/serviceValidate?                                            │   │
│  │     service=https%3A%2F%2Fhandbook.kf.no%2F9900%2Fsafety-manual&                     │   │
│  │     ticket=ST-123456789-abcdef                                                        │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  CAS Server Validation Response:                                                          │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ <?xml version="1.0" encoding="UTF-8"?>                                               │   │
│  │ <cas:serviceResponse xmlns:cas="http://www.yale.edu/tp/cas">                          │   │
│  │   <cas:authenticationSuccess>                                                         │   │
│  │     <cas:user><EMAIL></cas:user>                                               │   │
│  │     <cas:attributes>                                                                  │   │
│  │       <cas:email><EMAIL></cas:email>                                           │   │
│  │       <cas:fullName>John Doe</cas:fullName>                                           │   │
│  │       <cas:memberOf>CN=KF-Users,OU=Groups,DC=kinn,DC=id,DC=local</cas:memberOf>      │   │
│  │       <cas:memberOf>CN=KF-Admins,OU=Groups,DC=kinn,DC=id,DC=local</cas:memberOf>     │   │
│  │     </cas:attributes>                                                                 │   │
│  │   </cas:authenticationSuccess>                                                        │   │
│  │ </cas:serviceResponse>                                                                │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Validation Result:                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ ✅ Ticket is valid                                                                     │   │
│  │ ✅ User authenticated: <EMAIL>                                                  │   │
│  │ ✅ Basic attributes received from CAS                                                  │   │
│  │ 🔄 Proceed to LDAP lookup for detailed authorization                                  │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 6: LDAP User Lookup
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 6: LDAP AUTHORIZATION LOOKUP                                                        │
│                                                                                             │
│  LDAP Configuration Check:                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ From handboker.properties:                                                            │   │
│  │ UseLDAP=true ✅                                                                        │   │
│  │ MockBrukerAdm=false ✅                                                                 │   │
│  │                                                                                       │   │
│  │ LDAP Connection Parameters:                                                           │   │
│  │ • Host: ************:389                                                              │   │
│  │ • Search Base: dc=kinn,dc=id,dc=local                                                 │   │
│  │ • Search Filter: (mail=<EMAIL>)                                                │   │
│  │ • Bind User: CN=DelegeringAppUser,OU=KFApplicationUsers,DC=kinn,DC=id,DC=local       │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  LDAP Search Query:                                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 🔍 LDAP Search Request:                                                                │   │
│  │                                                                                       │   │
│  │ Base DN: dc=kinn,dc=id,dc=local                                                       │   │
│  │ Filter: (mail=<EMAIL>)                                                         │   │
│  │ Scope: SUBTREE                                                                        │   │
│  │ Attributes: [mail, displayName, memberOf, department, title]                         │   │
│  │ Max Results: 5000                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  LDAP Search Result:                                                                       │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ DN: CN=John Doe,OU=Users,OU=KF,DC=kinn,DC=id,DC=local                                │   │
│  │                                                                                       │   │
│  │ Attributes:                                                                           │   │
│  │ ├── mail: <EMAIL>                                                              │   │
│  │ ├── displayName: John Doe                                                             │   │
│  │ ├── department: Safety Department                                                     │   │
│  │ ├── title: Safety Manager                                                             │   │
│  │ ├── memberOf: CN=KF-Users,OU=Groups,DC=kinn,DC=id,DC=local                           │   │
│  │ ├── memberOf: CN=KF-Safety-Admins,OU=Groups,DC=kinn,DC=id,DC=local                   │   │
│  │ ├── memberOf: CN=Org-9900-Users,OU=Groups,DC=kinn,DC=id,DC=local                     │   │
│  │ └── memberOf: CN=Org-9900-Admins,OU=Groups,DC=kinn,DC=id,DC=local                    │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 7: User Object Creation
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 7: LDAP USER OBJECT CREATION                                                        │
│                                                                                             │
│  LDAPUser Model Processing:                                                                │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ case class LDAPUser(                                                                  │   │
│  │   email: String,                                                                      │   │
│  │   fullName: Option[String],                                                           │   │
│  │   organizations: Seq[String],                                                         │   │
│  │   language: Option[String],                                                           │   │
│  │   localUser: Boolean,                                                                 │   │
│  │   localAdmin: Boolean,                                                                │   │
│  │   globalAdmin: Boolean                                                                │   │
│  │ ) {                                                                                   │   │
│  │   def isAdmin: Boolean = localAdmin || globalAdmin                                    │   │
│  │   def hasAccess: Boolean = localUser || isAdmin                                       │   │
│  │ }                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Group Membership Analysis:                                                                │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Group Processing Logic:                                                               │   │
│  │                                                                                       │   │
│  │ CN=KF-Users,OU=Groups,DC=kinn,DC=id,DC=local                                         │   │
│  │ └── Result: Basic KF system access ✅                                                 │   │
│  │                                                                                       │   │
│  │ CN=KF-Safety-Admins,OU=Groups,DC=kinn,DC=id,DC=local                                 │   │
│  │ └── Result: Global admin privileges ✅                                                │   │
│  │                                                                                       │   │
│  │ CN=Org-9900-Users,OU=Groups,DC=kinn,DC=id,DC=local                                   │   │
│  │ └── Result: Access to organization 9900 ✅                                            │   │
│  │                                                                                       │   │
│  │ CN=Org-9900-Admins,OU=Groups,DC=kinn,DC=id,DC=local                                  │   │
│  │ └── Result: Admin privileges for organization 9900 ✅                                 │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Final LDAPUser Object:                                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ LDAPUser(                                                                             │   │
│  │   email = "<EMAIL>",                                                           │   │
│  │   fullName = Some("John Doe"),                                                        │   │
│  │   organizations = Seq("9900", "9901", "9902"),                                       │   │
│  │   language = Some("no"),                                                              │   │
│  │   localUser = true,                                                                   │   │
│  │   localAdmin = true,                                                                  │   │
│  │   globalAdmin = true                                                                  │   │
│  │ )                                                                                     │   │
│  │                                                                                       │   │
│  │ Computed Properties:                                                                  │   │
│  │ • isAdmin = true (localAdmin || globalAdmin)                                         │   │
│  │ • hasAccess = true (localUser || isAdmin)                                            │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Phase 8: Session Creation and Authorization
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 8: SESSION CREATION & AUTHORIZATION CHECK                                           │
│                                                                                             │
│  Session Storage:                                                                          │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ HttpSession Attributes:                                                               │   │
│  │                                                                                       │   │
│  │ session.setAttribute("CAS_USER", "<EMAIL>")                                   │   │
│  │ session.setAttribute("LDAP_USER", ldapUserObject)                                    │   │
│  │ session.setAttribute("USER_ORGANIZATIONS", Seq("9900", "9901", "9902"))              │   │
│  │ session.setAttribute("USER_PERMISSIONS", permissionMap)                              │   │
│  │ session.setAttribute("LAST_ACCESS", System.currentTimeMillis())                      │   │
│  │                                                                                       │   │
│  │ Session Timeout: 30 minutes (configurable)                                           │   │
│  │ Session ID: JSESSIONID=ABC123DEF456                                                   │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Authorization Check for Requested Resource:                                              │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Requested URL: /9900/safety-manual                                                    │   │
│  │                                                                                       │   │
│  │ Authorization Logic:                                                                  │   │
│  │ 1. Extract organization ID: "9900"                                                    │   │
│  │ 2. Check user organizations: ["9900", "9901", "9902"]                                │   │
│  │ 3. User has access to org 9900: ✅                                                    │   │
│  │ 4. Check handbook permissions for safety-manual                                      │   │
│  │ 5. User has read access: ✅                                                           │   │
│  │ 6. User has admin access: ✅ (can edit/sort/delete)                                  │   │
│  │                                                                                       │   │
│  │ Result: AUTHORIZED ✅                                                                  │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Permission Matrix Creation:                                                               │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Organization 9900 Permissions:                                                        │   │
│  │ ┌
</augment_code_snippet>