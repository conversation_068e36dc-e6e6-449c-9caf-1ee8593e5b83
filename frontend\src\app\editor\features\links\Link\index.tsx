import { useState } from "react";
import { <PERSON>ton, Group, Icon } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { Link } from "@/types";
import { LinkForm } from "../LinkForm";

interface LinkComponentProps {
  link: Link;
  onSave: (link: Link) => void;
  onDelete: () => void;
  showActions?: boolean;
}

export const LinkComponent = ({
  link,
  onSave,
  onDelete,
  showActions = true,
}: LinkComponentProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const t = usePrefixedTranslation(
    "editor.containers.LinkPage.components.Link"
  );

  const handleDelete = () => {
    if (
      window.confirm(t("deleteQuestion", { title: link.title }))
    ) {
      onDelete();
    }
  };

  const handleSave = (updatedLink: Link) => {
    onSave({ ...updatedLink, id: link.id, sortOrder: link.sortOrder });
    setIsEditing(false);
  };

  if (isEditing) {
    return (
      <div style={{ width: "100%" }}>
        <LinkForm
          link={link}
          sortOrder={link.sortOrder}
          onSave={handleSave}
          onCancel={() => setIsEditing(false)}
        />
      </div>
    );
  }

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        width: "100%",
        gap: "0.5rem",
      }}
    >
      <Icon icon="link" size="small" />
      <div style={{ flex: 1 }}>
        <a
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          style={{
            textDecoration: "none",
            color: "#0066cc",
            fontWeight: "bold",
            display: "block",
          }}
        >
          {link.title}
        </a>
        <div style={{ fontSize: "0.875rem", color: "#666" }}>{link.url}</div>
      </div>
      {showActions && (
        <Group>
          <Button
            size="small"
            onClick={() => setIsEditing(true)}
            title={t("edit")}
            icon="pencil"
          >
            {t("edit")}
          </Button>
          <Button
            size="small"
            color="danger"
            onClick={handleDelete}
            title={t("delete")}
            icon="TrashCan"
          >
            {t("delete")}
          </Button>
        </Group>
      )}
    </div>
  );
};
