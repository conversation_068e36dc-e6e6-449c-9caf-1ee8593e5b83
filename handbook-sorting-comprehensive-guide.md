Give # Comprehensive Guide: Handbook Sorting System

## Overview

This document provides an in-depth explanation of the handbook sorting system, covering the complete flow from user interaction to database updates, including visual diagrams of tree structures, database operations, and frontend-backend communication.

## System Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    SORTING SYSTEM ARCHITECTURE             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Frontend (React)          Backend (Scala)        Database  │
│  ┌─────────────────┐      ┌─────────────────┐    ┌─────────┐│
│  │ SortChildren    │      │ LocalHandbook   │    │ Chapter ││
│  │ Screen          │─────►│ Servlet         │───►│ Table   ││
│  │                 │      │                 │    │         ││
│  │ - Drag & Drop   │      │ - Sort API      │    │ Section ││
│  │ - State Mgmt    │      │ - Validation    │    │ Table   ││
│  │ - Save Action   │      │ - Persistence   │    │         ││
│  └─────────────────┘      └─────────────────┘    └─────────┘│
│           │                         │                  │    │
│           │                         │                  │    │
│           ▼                         ▼                  ▼    │
│  ┌─────────────────┐      ┌─────────────────┐    ┌─────────┐│
│  │ Redux Saga      │      │ Handbook        │    │ SQL     ││
│  │ - API Calls     │      │ Repository      │    │ Updates ││
│  │ - Error Handle  │      │ - Sort Logic    │    │         ││
│  │ - Success Toast │      │ - Transaction   │    │         ││
│  └─────────────────┘      └─────────────────┘    └─────────┘│
└─────────────────────────────────────────────────────────────┘
```

## Tree Structure Hierarchy

```
Handbook (TreeStructureHandbook)
├── Chapter 1 (TreeStructureChapter)
│   ├── Section 1.1 (Section)
│   ├── Section 1.2 (Section)
│   └── Sub-Chapter 1.3 (TreeStructureChapter)
│       ├── Section 1.3.1 (Section)
│       └── Section 1.3.2 (Section)
├── Chapter 2 (TreeStructureChapter)
│   ├── Section 2.1 (Section)
│   └── Section 2.2 (Section)
└── Chapter 3 (TreeStructureChapter)
    └── Section 3.1 (Section)
```

## Database Schema with Sort Order Fields

```
┌─────────────────────────────────────────────────────────────┐
│                    HANDBOOK TABLE                          │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ VARCHAR(36)                         │
│ title                │ VARCHAR(255)                        │
│ external_org_id      │ VARCHAR(36)                         │
│ imported_handbook_id │ VARCHAR(36)                         │
│ created_date         │ TIMESTAMP                           │
│ updated_date         │ TIMESTAMP                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ 1:N
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    CHAPTER TABLE                           │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ VARCHAR(36)                         │
│ title                │ VARCHAR(255)                        │
│ handbook_id (FK)     │ VARCHAR(36)                         │
│ parent_id (FK)       │ VARCHAR(36) [NULL for root]        │
│ sort_order           │ SMALLINT    [SORTING KEY]          │
│ created_date         │ TIMESTAMP                           │
│ updated_date         │ TIMESTAMP                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ 1:N
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    SECTION TABLE                           │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ VARCHAR(36)                         │
│ title                │ VARCHAR(255)                        │
│ parent_id (FK)       │ VARCHAR(36) [Chapter ID]           │
│ handbook_id (FK)     │ VARCHAR(36)                         │
│ sort_order           │ SMALLINT    [SORTING KEY]          │
│ html                 │ TEXT                                │
│ created_date         │ TIMESTAMP                           │
│ updated_date         │ TIMESTAMP                           │
└─────────────────────────────────────────────────────────────┘
```

## Complete Sorting Process Flow

### Phase 1: User Interaction and Frontend Processing

```
┌─────────────────────────────────────────────────────────────┐
│                    USER INTERACTION PHASE                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Step 1: User Initiates Sorting                           │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ User clicks "Sort" button in:                       │   │
│  │ - HandbookScreen.jsx (for chapters)                │   │
│  │ - ChapterScreen.jsx (for sections/sub-chapters)    │   │
│  │                                                     │   │
│  │ toggleSort() {                                      │   │
│  │   this.setState({ isSorting: true });              │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 2: Render Sort Interface                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ <SortChildrenScreen                                 │   │
│  │   items={chaptersAndSections}                       │   │
│  │   sortFunction={sortItemsFun}                       │   │
│  │   onCancel={this.toggleSort}                        │   │
│  │ />                                                  │   │
│  │                                                     │   │
│  │ Initial state:                                      │   │
│  │ items: [                                            │   │
│  │   { id: 'ch1', title: 'Intro', sortOrder: 1 },    │   │
│  │   { id: 'ch2', title: 'Safety', sortOrder: 2 },   │   │
│  │   { id: 'ch3', title: 'Appendix', sortOrder: 3 }  │   │
│  │ ]                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Phase 2: Drag and Drop Operations

```
┌─────────────────────────────────────────────────────────────┐
│                  DRAG & DROP INTERFACE                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Before Drag (Initial Order):                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ [Index 0] 📑 Introduction (id: ch1, sortOrder: 1)  │   │
│  │ [Index 1] 📑 Safety Rules (id: ch2, sortOrder: 2)  │   │
│  │ [Index 2] 📑 Appendices   (id: ch3, sortOrder: 3)  │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              │ User drags "Appendices"     │
│                              │ from index 2 to index 0     │
│                              ▼                              │
│  During Drag:                                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ onSortEnd({ oldIndex: 2, newIndex: 0 }) {          │   │
│  │   this.setState(state => ({                        │   │
│  │     items: arrayMove(state.items, 2, 0)           │   │
│  │   }));                                             │   │
│  │ }                                                  │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  After Drag (New Order):                                  │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ [Index 0] 📑 Appendices   (id: ch3, sortOrder: 3)  │   │
│  │ [Index 1] 📑 Introduction (id: ch1, sortOrder: 1)  │   │
│  │ [Index 2] 📑 Safety Rules (id: ch2, sortOrder: 2)  │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Note: sortOrder values haven't changed yet - only array   │
│  positions have changed in the frontend state              │
└─────────────────────────────────────────────────────────────┘
```

### Phase 3: Save Operation and API Call

```
┌─────────────────────────────────────────────────────────────┐
│                    SAVE OPERATION                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Step 1: User Clicks Save                                 │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ handleSave = () => {                                │   │
│  │   const { items } = this.state;                    │   │
│  │   this.setState({ isSaving: true });               │   │
│  │                                                     │   │
│  │   // Extract IDs in new order                      │   │
│  │   const sortedIds = items.map(item => item.id);    │   │
│  │   // Result: ["ch3", "ch1", "ch2"]                │   │
│  │                                                     │   │
│  │   this.props.sortFunction(sortedIds);              │   │
│  │   this.props.onCancel(); // Close sort interface   │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 2: Redux Action Dispatch                            │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ // In HandbookScreen.jsx                            │   │
│  │ sortItemsFun = (itemIds) => {                       │   │
│  │   this.props.sortItems(itemIds);                   │   │
│  │ }                                                   │   │
│  │                                                     │   │
│  │ // Redux action                                     │   │
│  │ dispatch(sortItems(["ch3", "ch1", "ch2"]));        │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 3: Saga Intercepts Action                           │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ // In saga.js                                       │   │
│  │ export function* sortItems({ payload: ids }) {      │   │
│  │   try {                                             │   │
│  │     yield call(api.saveSortOrder, ids);            │   │
│  │     yield call(fetchHandbooks);                    │   │
│  │     yield put(toast.success('Lagret sortering.')); │   │
│  │   } catch (error) {                                │   │
│  │     yield put(toast.error('Klarte ikke lagre'));   │   │
│  │   }                                                 │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Phase 4: Backend Processing

```
┌─────────────────────────────────────────────────────────────┐
│                  BACKEND API PROCESSING                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Step 1: HTTP Request                                      │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ POST /api/local-handbooks/sort/                     │   │
│  │ Content-Type: application/json                      │   │
│  │                                                     │   │
│  │ Request Body:                                       │   │
│  │ ["ch3", "ch1", "ch2"]                              │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 2: Servlet Processing                               │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ // In LocalHandbookServlet.scala                    │   │
│  │ post("/sort/?") {                                   │   │
│  │   val sortOrder = parsedBody.extract[List[String]]  │   │
│  │   // sortOrder = List("ch3", "ch1", "ch2")         │   │
│  │                                                     │   │
│  │   handbookService.persistSortOrder(sortOrder)       │   │
│  │   NoContent() // HTTP 204                          │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 3: Service Layer Processing                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ // In HandbookService.scala                         │   │
│  │ def persistSortOrder(sortOrder: List[String]) = {   │   │
│  │   inTransaction {                                   │   │
│  │     handbookRepository.persistSortOrder(sortOrder)  │   │
│  │   }                                                 │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Phase 5: Database Updates

```
┌─────────────────────────────────────────────────────────────┐
│                DATABASE UPDATE PROCESS                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Step 1: Repository Processing                            │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ // In HandbookRepository.scala                       │   │
│  │ def persistSortOrder(sortOrder: List[String]) = {    │   │
│  │   sortOrder.zipWithIndex.foreach { case (id, index) =>│   │
│  │     updateSortOrder(id, index)                      │   │
│  │   }                                                 │   │
│  │ }                                                   │   │
│  │                                                     │   │
│  │ Processing:                                         │   │
│  │ - ("ch3", 0) → Update ch3 to sortOrder = 0         │   │
│  │ - ("ch1", 1) → Update ch1 to sortOrder = 1         │   │
│  │ - ("ch2", 2) → Update ch2 to sortOrder = 2         │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 2: SQL Execution                                    │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ def updateSortOrder(id: String, sortOrder: Int) = { │   │
│  │   // Determine if it's a chapter or section         │   │
│  │   if (isChapter(id)) {                              │   │
│  │     sql"""                                          │   │
│  │       UPDATE chapter                                │   │
│  │       SET sort_order = $sortOrder                   │   │
│  │       WHERE id = $id                                │   │
│  │     """.update.run                                  │   │
│  │   } else {                                          │   │
│  │     sql"""                                          │   │
│  │       UPDATE section                                │   │
│  │       SET sort_order = $sortOrder                   │   │
│  │       WHERE id = $id                                │   │
│  │     """.update.run                                  │   │
│  │   }                                                 │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Database State Transformation

### Before Sorting Operation
```
CHAPTER TABLE:
┌──────┬─────────────┬─────────────┬───────────┬────────────┬─────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │ UPDATED_DATE│
├──────┼─────────────┼─────────────┼───────────┼────────────┼─────────────┤
│ ch1  │ Introduction│    hb1      │   NULL    │     1      │ 1623456789  │
│ ch2  │ Safety Rules│    hb1      │   NULL    │     2      │ 1623456790  │
│ ch3  │ Appendices  │    hb1      │   NULL    │     3      │ 1623456791  │
│ ch4  │ Equipment   │    hb1      │    ch2    │     1      │ 1623456792  │
└──────┴─────────────┴─────────────┴───────────┴────────────┴─────────────┘

SECTION TABLE:
┌──────┬─────────────┬─────────────┬───────────┬────────────┬─────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │ UPDATED_DATE│
├──────┼─────────────┼─────────────┼───────────┼────────────┼─────────────┤
│  s1  │ Overview    │    hb1      │    ch1    │     1      │ 1623456793  │
│  s2  │ Purpose     │    hb1      │    ch1    │     2      │ 1623456794  │
│  s3  │ General     │    hb1      │    ch2    │     1      │ 1623456795  │
│  s4  │ Emergency   │    hb1      │    ch2    │     2      │ 1623456796  │
└──────┴─────────────┴─────────────┴───────────┴────────────┴─────────────┘
```

### User Action: Drag "Appendices" to First Position
```
Frontend Operation:
- User drags item from index 2 to index 0
- arrayMove([ch1, ch2, ch3], 2, 0) → [ch3, ch1, ch2]
- Frontend sends: ["ch3", "ch1", "ch2"]
```

### After Sorting Operation
```
CHAPTER TABLE:
┌──────┬─────────────┬─────────────┬───────────┬────────────┬─────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │ UPDATED_DATE│
├──────┼─────────────┼─────────────┼───────────┼────────────┼─────────────┤
│ ch3  │ Appendices  │    hb1      │   NULL    │     0      │ 1623456800  │ ← Updated
│ ch1  │ Introduction│    hb1      │   NULL    │     1      │ 1623456801  │ ← Updated
│ ch2  │ Safety Rules│    hb1      │   NULL    │     2      │ 1623456802  │ ← Updated
│ ch4  │ Equipment   │    hb1      │    ch2    │     1      │ 1623456792  │ ← Unchanged
└──────┴─────────────┴─────────────┴───────────┴────────────┴─────────────┘

SECTION TABLE: (Unchanged - only root chapters were sorted)
┌──────┬─────────────┬─────────────┬───────────┬────────────┬─────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │ UPDATED_DATE│
├──────┼─────────────┼─────────────┼───────────┼────────────┼─────────────┤
│  s1  │ Overview    │    hb1      │    ch1    │     1      │ 1623456793  │
│  s2  │ Purpose     │    hb1      │    ch1    │     2      │ 1623456794  │
│  s3  │ General     │    hb1      │    ch2    │     1      │ 1623456795  │
│  s4  │ Emergency   │    hb1      │    ch2    │     2      │ 1623456796  │
└──────┴─────────────┴─────────────┴───────────┴────────────┴─────────────┘
```

## Frontend Re-rendering Process

```
┌─────────────────────────────────────────────────────────────┐
│                    FRONTEND RE-RENDER FLOW                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Step 1: Successful Save Response                          │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ HTTP 204 No Content ← Backend                       │   │
│  │ ↓                                                   │   │
│  │ Saga continues execution:                           │   │
│  │ yield call(fetchHandbooks); // Refetch data        │   │
│  │ yield put(toast.success('Lagret sortering.'));     │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 2: Data Refetch                                     │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ GET /api/handbooks/{id}/tree-structure              │   │
│  │ ↓                                                   │   │
│  │ HandbookApiService.retrieveHandbookTreeStructure()  │   │
│  │ ↓                                                   │   │
│  │ Database query with ORDER BY sort_order ASC        │   │
│  │ ↓                                                   │   │
│  │ Result: [ch3, ch1, ch2, ch4] (in new order)       │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 3: Tree Structure Rebuild                           │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ createTreeStructure() processes ordered data:       │   │
│  │                                                     │   │
│  │ TreeStructureHandbook {                             │   │
│  │   chapters: [                                       │   │
│  │     TreeStructureChapter(ch3, "Appendices"),       │   │
│  │     TreeStructureChapter(ch1, "Introduction", [s1,s2]),│
│  │     TreeStructureChapter(ch2, "Safety", [s3,s4,ch4])│   │
│  │   ]                                                 │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Step 4: Component Re-render                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ HandbookNode renders updated tree:                  │   │
│  │                                                     │   │
│  │ 📖 Safety Manual                                   │   │
│  │ ├── 📑 Appendices (sortOrder: 0) ← Now first      │   │
│  │ ├── 📑 Introduction (sortOrder: 1)                │   │
│  │ │   ├── 📄 Overview                               │   │
│  │ │   └── 📄 Purpose                                │   │
│  │ └── 📑 Safety Rules (sortOrder: 2)                │   │
│  │     ├── 📄 General                                │   │
│  │     ├── 📄 Emergency                              │   │
│  │     └── 📑 Equipment                              │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Advanced Sorting Scenarios

### Scenario 1: Mixed Content Sorting (Chapters + Sections)
```
┌─────────────────────────────────────────────────────────────┐
│            SORTING CHAPTERS AND SECTIONS TOGETHER          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Parent: "Safety Rules" Chapter                           │
│                                                             │
│  Before Sort:                                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ [0] 📄 General Rules (s1, sortOrder: 1)            │   │
│  │ [1] 📑 Equipment Safety (ch4, sortOrder: 2)        │   │
│  │ [2] 📄 Emergency Procedures (s2, sortOrder: 3)     │   │
│  │ [3] 📄 Training Requirements (s3, sortOrder: 4)    │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              │ User drags Equipment to end  │
│                              ▼                              │
│  After Drag:                                               │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ [0] 📄 General Rules (s1)                          │   │
│  │ [1] 📄 Emergency Procedures (s2)                   │   │
│  │ [2] 📄 Training Requirements (s3)                  │   │
│  │ [3] 📑 Equipment Safety (ch4) ← moved              │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              │ Frontend sends:              │
│                              │ ["s1", "s2", "s3", "ch4"]   │
│                              ▼                              │
│  Database Updates:                                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ UPDATE section SET sort_order = 0 WHERE id = 's1'   │   │
│  │ UPDATE section SET sort_order = 1 WHERE id = 's2'   │   │
│  │ UPDATE section SET sort_order = 2 WHERE id = 's3'   │   │
│  │ UPDATE chapter SET sort_order = 3 WHERE id = 'ch4'  │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              ▼                              │
│  Result Tree:                                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Safety Rules                                        │   │
│  │ ├── 📄 General Rules (sortOrder: 0)                │   │
│  │ ├── 📄 Emergency Procedures (sortOrder: 1)         │   │
│  │ ├── 📄 Training Requirements (sortOrder: 2)        │   │
│  │ └── 📑 Equipment Safety (sortOrder: 3)             │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Scenario 2: New Item Sort Order Computation
```
┌─────────────────────────────────────────────────────────────┐
│              COMPUTING SORT ORDER FOR NEW ITEMS            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Scenario: Adding new chapter to handbook                  │
│                                                             │
│  Current chapters:                                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ch3: "Appendices"   (sortOrder: 0)                  │   │
│  │ ch1: "Introduction" (sortOrder: 1)                  │   │
│  │ ch2: "Safety Rules" (sortOrder: 2)                  │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Algorithm in HandbookService:                            │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ def getNextSortOrder(parentId: Option[String]): Int = {│
│  │   val maxSortOrder = if (parentId.isDefined) {       │   │
│  │     // For sub-chapters/sections                     │   │
│  │     sql"""                                           │   │
│  │       SELECT COALESCE(MAX(sort_order), -1)          │   │
│  │       FROM (                                         │   │
│  │         SELECT sort_order FROM chapter              │   │
│  │         WHERE parent_id = ${parentId.get}           │   │
│  │         UNION ALL                                   │   │
│  │         SELECT sort_order FROM section              │   │
│  │         WHERE parent_id = ${parentId.get}           │   │
│  │       ) combined                                    │   │
│  │     """.query[Int].unique                           │   │
│  │   } else {                                          │   │
│  │     // For root chapters                            │   │
│  │     sql"""                                          │   │
│  │       SELECT COALESCE(MAX(sort_order), -1)         │   │
│  │       FROM chapter                                  │   │
│  │       WHERE parent_id IS NULL                      │   │
│  │     """.query[Int].unique                          │   │
│  │   }                                                 │   │
│  │   maxSortOrder + 1                                  │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Result: New chapter gets sortOrder = 3                   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ch3: "Appendices"     (sortOrder: 0)                │   │
│  │ ch1: "Introduction"   (sortOrder: 1)                │   │
│  │ ch2: "Safety Rules"   (sortOrder: 2)                │   │
│  │ ch5: "New Chapter"    (sortOrder: 3) ← Added        │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Error Handling and Edge Cases

### Case 1: Concurrent Modification
```
┌─────────────────────────────────────────────────────────────┐
│                  CONCURRENT MODIFICATION HANDLING          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Problem: Two users sorting same handbook simultaneously   │
│                                                             │
│  User A Timeline:                    User B Timeline:      │
│  ┌─────────────────────────────┐    ┌─────────────────────┐ │
│  │ 1. Loads handbook tree      │    │ 1. Loads same tree  │ │
│  │ 2. Drags ch1 to position 0  │    │ 2. Drags ch2 to pos 0│ │
│  │ 3. Saves: ["ch1","ch2","ch3"]│    │ 3. Saves: ["ch2","ch1","ch3"]│
│  │ 4. Success ✓                │    │ 4. Success ✓        │ │
│  └─────────────────────────────┘    └─────────────────────┘ │
│                                                             │
│  Database State After Both Operations:                     │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Final state depends on which operation completed    │   │
│  │ last - no conflict resolution implemented          │   │
│  │                                                     │   │
│  │ Potential Solution:                                 │   │
│  │ - Add version/timestamp checking                    │   │
│  │ - Implement optimistic locking                      │   │
│  │ - Show conflict resolution UI                       │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Case 2: Invalid Sort Order Data
```
┌─────────────────────────────────────────────────────────────┐
│                    ERROR HANDLING SCENARIOS                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Scenario 1: Non-existent ID in sort order                │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Frontend sends: ["ch1", "invalid-id", "ch3"]        │   │
│  │ ↓                                                   │   │
│  │ Backend validation:                                 │   │
│  │ def validateSortOrder(ids: List[String]) = {        │   │
│  │   val existingIds = getExistingIds()               │   │
│  │   val invalidIds = ids.filterNot(existingIds.contains)│
│  │   if (invalidIds.nonEmpty) {                       │   │
│  │     throw UserFriendlyException(                   │   │
│  │       s"Invalid IDs: ${invalidIds.mkString(", ")}")│   │
│  │   }                                                 │   │
│  │ }                                                   │   │
│  │ ↓                                                   │   │
│  │ Result: HTTP 400 Bad Request                       │   │
│  │ Frontend shows error toast                         │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Scenario 2: Database Transaction Failure                 │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ def persistSortOrder(sortOrder: List[String]) = {   │   │
│  │   inTransaction {                                   │   │
│  │     try {                                           │   │
│  │       sortOrder.zipWithIndex.foreach { case (id, i) =>│
│  │         updateSortOrder(id, i)                      │   │
│  │       }                                             │   │
│  │     } catch {                                       │   │
│  │       case e: SQLException =>                       │   │
│  │         log.error("Failed to update sort order", e)│   │
│  │         throw UserFriendlyException(                │   │
│  │           "Kunne ikke lagre sortering")            │   │
│  │     }                                               │   │
│  │   }                                                 │   │
│  │ }                                                   │   │
│  │ ↓                                                   │   │
│  │ Result: Transaction rolled back                     │   │
│  │ Frontend receives error and shows toast            │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Performance Considerations

### Database Query Optimization
```
┌─────────────────────────────────────────────────────────────┐
│                    PERFORMANCE OPTIMIZATIONS               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Index Strategy:                                           │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ CREATE INDEX idx_chapter_sort ON chapter(           │   │
│  │   handbook_id, parent_id, sort_order               │   │
│  │ );                                                  │   │
│  │                                                     │   │
│  │ CREATE INDEX idx_section_sort ON section(           │   │
│  │   handbook_id, parent_id, sort_order               │   │
│  │ );                                                  │   │
│  │                                                     │   │
│  │ Benefits:                                           │   │
│  │ - Fast ORDER BY sort_order queries                 │   │
│  │ - Efficient parent_id filtering                    │   │
│  │ - Quick MAX(sort_order) calculations               │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Batch Update Strategy:                                    │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Instead of individual updates:                      │   │
│  │ UPDATE chapter SET sort_order = ? WHERE id = ?     │   │
│  │ UPDATE chapter SET sort_order = ? WHERE id = ?     │   │
│  │ UPDATE chapter SET sort_order = ? WHERE id = ?     │   │
│  │                                                     │   │
│  │ Use batch update with CASE statement:              │   │
│  │ UPDATE chapter SET sort_order = CASE               │   │
│  │   WHEN id = 'ch1' THEN 0                          │   │
│  │   WHEN id = 'ch2' THEN 1                          │   │
│  │   WHEN id = 'ch3' THEN 2                          │   │
│  │   ELSE sort_order                                  │   │
│  │ END                                                │   │
│  │ WHERE id IN ('ch1', 'ch2', 'ch3')                 │   │
│  │                                                     │   │
│  │ Benefits:                                           │   │
│  │ - Single database round trip                       │   │
│  │ - Reduced lock contention                          │   │
│  │ - Better transaction performance                   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Frontend Performance
```
┌─────────────────────────────────────────────────────────────┐
│                  FRONTEND PERFORMANCE TIPS                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  React Optimization:                                       │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ // Use React.memo for drag items                    │   │
│  │ const DragItem = React.memo(({ item, index }) => {  │   │
│  │   return (                                          │   │
│  │     <SortableElement index={index}>                │   │
│  │       <div className="drag-item">                  │   │
│  │         {item.title}                               │   │
│  │       </div>                                       │   │
│  │     </SortableElement>                             │   │
│  │   );                                               │   │
│  │ });                                                │   │
│  │                                                     │   │
│  │ // Debounce save operations                        │   │
│  │ const debouncedSave = useMemo(                     │   │
│  │   () => debounce(handleSave, 300),                │   │
│  │   [handleSave]                                     │   │
│  │ );                                                 │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  State Management:                                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ // Normalize state structure                        │   │
│  │ const initialState = {                              │   │
│  │   items: {                                          │   │
│  │     'ch1': { id: 'ch1', title: 'Intro', ... },    │   │
│  │     'ch2': { id: 'ch2', title: 'Safety', ... }    │   │
│  │   },                                               │   │
│  │   sortOrder: ['ch1', 'ch2', 'ch3'],               │   │
│  │   isSorting: false,                               │   │
│  │   isSaving: false                                 │   │
│  │ };                                                 │   │
│  │                                                     │   │
│  │ // Benefits:                                        │   │
│  │ - O(1) item lookups                               │   │
│  │ - Separate sort order tracking                     │   │
│  │ - Minimal re-renders                              │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Testing Strategy

### Unit Tests
```
┌─────────────────────────────────────────────────────────────┐
│                      TESTING APPROACH                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Backend Tests (Scala):                                   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ class HandbookSortingSpec extends FlatSpec {         │   │
│  │                                                     │   │
│  │   "persistSortOrder" should "update sort orders" in {│   │
│  │     val sortOrder = List("ch3", "ch1", "ch2")      │   │
│  │     handbookService.persistSortOrder(sortOrder)     │   │
│  │                                                     │   │
│  │     val chapters = getChaptersOrderedBySort()       │   │
│  │     chapters.map(_.id) shouldBe sortOrder          │   │
│  │   }                                                 │   │
│  │                                                     │   │
│  │   it should "handle mixed content sorting" in {    │   │
│  │     val mixedOrder = List("s1", "ch4", "s2")       │   │
│  │     handbookService.persistSortOrder(mixedOrder)    │   │
│  │                                                     │   │
│  │     val items = getChildrenOrderedBySort("ch2")    │   │
│  │     items.map(_.id) shouldBe mixedOrder            │   │
│  │   }                                                 │   │
│  │                                                     │   │
│  │   it should "reject invalid IDs" in {              │   │
│  │     val invalidOrder = List("ch1", "invalid", "ch2")│   │
│  │     assertThrows[UserFriendlyException] {           │   │
│  │       handbookService.persistSortOrder(invalidOrder)│   │
│  │     }                                               │   │
│  │   }                                                 │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Frontend Tests (Jest/React Testing Library):             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ describe('SortChildrenScreen', () => {              │   │
│  │                                                     │   │
│  │   test('should reorder items on drag', () => {     │   │
│  │     const items = [                                │   │
│  │       { id: 'ch1', title: 'Intro' },              │   │
│  │       { id: 'ch2', title: 'Safety' }              │   │
│  │     ];                                             │   │
│  │                                                     │   │
│  │     render(<SortChildrenScreen items={items} />);  │   │
│  │                                                     │   │
│  │     // Simulate drag from index 1 to 0            │   │
│  │     fireEvent.dragStart(screen.getByText('Safety'));│   │
│  │     fireEvent.drop(screen.getByText('Intro'));     │   │
│  │                                                     │   │
│  │     expect(screen.getByText('Safety')).toBeInTheDocument();│
│  │     // Verify new order in DOM                     │   │
│  │   });                                              │   │
│  │                                                     │   │
│  │   test('should call save with correct order', () => {│   │
│  │     const mockSave = jest.fn();                    │   │
│  │     render(<SortChildrenScreen                     │   │
│  │       items={items}                               │   │
│  │       sortFunction={mockSave}                     │   │
│  │     />);                                          │   │
│  │                                                     │   │
│  │     // Perform drag and save                       │   │
│  │     // ...                                         │   │
│  │     fireEvent.click(screen.getByText('Lagre'));    │   │
│  │                                                     │   │
│  │     expect(mockSave).toHaveBeenCalledWith(          │   │
│  │       ['ch2', 'ch1']                              │   │
│  │     );                                             │   │
│  │   });                                              │   │
│  │ });                                                │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Integration Tests
```
┌─────────────────────────────────────────────────────────────┐
│                    INTEGRATION TESTING                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  End-to-End Test Scenario:                                │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ class HandbookSortingE2ESpec extends IntegrationSpec {│   │
│  │                                                     │   │
│  │   "complete sorting workflow" should "work" in {    │   │
│  │     // 1. Setup test data                          │   │
│  │     val handbook = createTestHandbook()            │   │
│  │     val chapters = createTestChapters(handbook.id) │   │
│  │                                                     │   │
│  │     // 2. Perform API call                         │   │
│  │     val sortOrder = List(chapters(2).id,           │   │
│  │                         chapters(0).id,           │   │
│  │                         chapters(1).id)           │   │
│  │                                                     │   │
│  │     val response = post(                           │   │
│  │       s"/handbooks/local/sort/",                  │   │
│  │       body = write(sortOrder),                    │   │
│  │       headers = authHeaders                       │   │
│  │     )                                              │   │
│  │                                                     │   │
│  │     // 3. Verify response                          │   │
│  │     response.status shouldBe 204                   │   │
│  │                                                     │   │
│  │     // 4. Verify database state                    │   │
│  │     val updatedChapters = getChaptersOrderedBySort()│   │
│  │     updatedChapters.map(_.id) shouldBe sortOrder   │   │
│  │                                                     │   │
│  │     // 5. Verify API returns correct tree          │   │
│  │     val treeResponse = get(                        │   │
│  │       s"/api/handbooks/${handbook.id}/tree-structure"│
│  │     )                                              │   │
│  │     val tree = parse(treeResponse.body)            │   │
│  │       .extract[TreeStructureHandbook]             │   │
│  │                                                     │   │
│  │     tree.chapters.map(_.id) shouldBe sortOrder     │   │
│  │   }                                                │   │
│  │ }                                                  │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Summary

This comprehensive guide covers the complete handbook sorting system, from user interaction through database persistence. Key points:

1. **Frontend**: React drag-and-drop with Redux state management
2. **Backend**: Scala REST API with transaction-safe persistence
3. **Database**: Sort order fields with proper indexing
4. **Tree Building**: Recursive algorithm respecting sort order
5. **Error Handling**: Validation and rollback mechanisms
6. **Performance**: Optimized queries and batch updates
7. **Testing**: Unit, integration, and E2E test strategies

The system ensures data consistency while providing a smooth user experience for organizing handbook content hierarchically.
