import React, { type ReactNode } from "react";
import { BaseErrorBoundary } from "./BaseErrorBoundary";
import { RouteErrorFallback } from "./ErrorFallback";
import type {
  ErrorBoundaryProps,
  ErrorFallbackProps,
} from "@/shared/types/errorBoundary";

export class RouteErrorBoundary extends BaseErrorBoundary {
  protected getErrorCategory():
    | "ui"
    | "api"
    | "auth"
    | "data"
    | "network"
    | "unknown" {
    const { error } = this.state;

    if (!error) return "unknown";

    const message = error.message.toLowerCase();

    if (
      message.includes("fetch") ||
      message.includes("load") ||
      message.includes("api")
    ) {
      return "api";
    }

    if (message.includes("auth") || message.includes("unauthorized")) {
      return "auth";
    }

    return "ui";
  }

  protected getErrorLevel(): "low" | "medium" | "high" | "critical" {
    const { error } = this.state;

    if (!error) return "medium";

    const message = error.message.toLowerCase();

    if (message.includes("auth") || message.includes("unauthorized")) {
      return "high";
    }

    if (message.includes("fetch") || message.includes("api")) {
      return "medium";
    }

    return "medium";
  }

  protected renderFallback(props: ErrorFallbackProps): ReactNode {
    return <RouteErrorFallback {...props} />;
  }
}

interface RouteErrorBoundaryWrapperProps
  extends Omit<ErrorBoundaryProps, "level"> {
  routeName?: string;
}

export const RouteErrorBoundaryWrapper: React.FC<
  RouteErrorBoundaryWrapperProps
> = ({ routeName, ...props }) => (
  <RouteErrorBoundary
    {...props}
    level="route"
    name={routeName ? `Route-${routeName}` : "RouteErrorBoundary"}
    showToast={true}
  />
);
