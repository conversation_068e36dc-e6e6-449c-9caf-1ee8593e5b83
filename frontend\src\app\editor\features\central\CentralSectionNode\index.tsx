import React from "react";
import { Icon, Tree } from "kf-bui";
import { useLocation } from "react-router-dom";
import type { CentralChapter, CentralSection } from "@/types";
import { shouldDisableNode } from "../../../utils/moveValidation";
import { useAppSelector } from "@/store";
import { selectSelectedCentralItem } from "@/store/slices/centralTreeSlice";

interface CentralSectionNodeProps {
  section: CentralSection;
  moving?: boolean;
  onSetSelectedItem?: (section: CentralSection) => void;
  disabled?: boolean;
  movedItem?: CentralChapter | CentralSection;
}

export const CentralSectionNode: React.FC<CentralSectionNodeProps> = ({
  section,
  moving = false,
  onSetSelectedItem,
  disabled = false,
  movedItem,
}) => {
  const location = useLocation();
  const selectedCentralItem = useAppSelector(selectSelectedCentralItem);

  const isActive = React.useMemo(() => {
    if (moving) return false;

    const currentPath = location.pathname;
    const sectionPath = `/section/${section.id}`;

    return currentPath.includes(sectionPath);
  }, [location.pathname, section.id, moving]);

  const getSectionUrl = () => {
    return `/central-editor/${section.centralHandbookId}/section/${section.id}/`;
  };

  if (moving && movedItem) {
    const isBeingMoved = section.id === movedItem.id;
    const isSelected = selectedCentralItem?.id === section.id;
    const isDisabled =
      !isBeingMoved && shouldDisableNode(section, movedItem, disabled);
    const isDifferentHandbook =
      movedItem && movedItem.centralHandbookId !== section.centralHandbookId;

    return (
      <Tree.Item
        id={section.id!}
        key={section.id}
        disabled={isDisabled}
        onClick={() => onSetSelectedItem?.(section)}
        style={{
          opacity:
            isDifferentHandbook || (isDisabled && !isBeingMoved && !isSelected)
              ? 0.5
              : 1,
          cursor:
            isDisabled && !isBeingMoved && !isSelected
              ? "not-allowed"
              : isBeingMoved
                ? "default"
                : "pointer",
          fontWeight: isBeingMoved || isSelected ? 600 : undefined,
          color: isBeingMoved ? "#1976d2" : isSelected ? "#2e7d32" : undefined,
          backgroundColor: isBeingMoved
            ? "#e3f2fd"
            : isSelected
              ? "#e8f5e8"
              : undefined,
          padding: isBeingMoved || isSelected ? "2px 4px" : undefined,
          borderRadius: isBeingMoved || isSelected ? "4px" : undefined,
          border: isSelected
            ? "2px solid #2e7d32"
            : isBeingMoved
              ? "2px solid #1976d2"
              : undefined,
        }}
      >
        <Icon icon="RegFileLines" size="small" style={{ marginRight: "4px" }} />
        {section.title}
        {isBeingMoved && (
          <span
            style={{ marginLeft: "8px", fontSize: "12px", color: "#1976d2" }}
          >
            ← flytter
          </span>
        )}
        {isSelected && (
          <span
            style={{ marginLeft: "8px", fontSize: "12px", color: "#2e7d32" }}
          >
            ← valgt som mål
          </span>
        )}
      </Tree.Item>
    );
  }

  return (
    <Tree.ItemLink
      to={getSectionUrl()}
      key={section.id}
      id={section.id!}
      style={isActive ? { fontWeight: "600", color: "#050037" } : undefined}
    >
      <Icon icon="RegFileLines" size="small" style={{ marginRight: "4px" }} />
      {section.title}
    </Tree.ItemLink>
  );
};
