import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { useNavigate } from "react-router-dom";
import {
  Modal,
  Button,
  Input,
  Checkbox,
  Select,
  Help,
  Label,
  Field,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { useSession } from "@/store/services/session/hooks";
import { usePrefixedTranslation } from "@/libs/i18n";

import { useSaveLocalHandbookMutation } from "@/store/services/handbook/localHandbookApi";
import { useOptimisticMutation } from "@/store/services/handbook/optimisticUpdateHooks";
import { handleOptimisticError } from "@/store/services/handbook/errorHandling";
import { useGetPublishedCentralHandbooksQuery } from "@/store/services/handbook/centralHandbookApi";
import type { Handbook } from "@/types";
import type { PublishedCentralHandbook } from "@/store/services/handbook/centralHandbookApi";

interface CreateOrUpdateLocalHandbookModalProps {
  handbook?: Handbook;
  isOpen: boolean;
  onHide: () => void;
}

const validationSchema = Yup.object().shape({
  title: Yup.string().required("Håndboka må ha en tittel"),
  isPublic: Yup.boolean().default(true),
  selectedCentralHandbookId: Yup.string().optional().default(""),
});

interface FormData {
  title: string;
  isPublic: boolean;
  selectedCentralHandbookId: string;
}

export const CreateOrUpdateLocalHandbookModal = ({
  handbook,
  isOpen,
  onHide,
}: CreateOrUpdateLocalHandbookModalProps) => {
  const { session } = useSession();
  const navigate = useNavigate();
  const t = usePrefixedTranslation("editor.containers.CreateOrUpdateHandbook");
  const isEditing = !!handbook;

  const [selectedCentralHandbook, setSelectedCentralHandbook] =
    useState<PublishedCentralHandbook | null>(null);
  const [title, setTitle] = useState<string>("");

  const [saveLocalHandbook, { isLoading: isSaving }] =
    useSaveLocalHandbookMutation();
  const { handleOptimisticUpdate } = useOptimisticMutation();

  const {
    data: centralHandbooks = [],
    isLoading: isLoadingCentral,
    error: centralError,
  } = useGetPublishedCentralHandbooksQuery();

  const {
    handleSubmit,
    register,
    formState: { errors, isValid },
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      title: "",
      isPublic: true,
      selectedCentralHandbookId: "",
    },
    mode: "onChange",
  });

  useEffect(() => {
    if (handbook) {
      const handbookTitle = handbook.title || "";
      setTitle(handbookTitle);
      reset({
        title: handbookTitle,
        isPublic: handbook.isPublic || false,
        selectedCentralHandbookId: "",
      });
      setSelectedCentralHandbook(null);
    } else {
      setTitle("");
      reset({
        title: "",
        isPublic: true,
        selectedCentralHandbookId: "",
      });
      setSelectedCentralHandbook(null);
    }
  }, [handbook, reset]);

  const handleCentralHandbookChange = (
    centralHandbook: PublishedCentralHandbook | null
  ) => {
    setSelectedCentralHandbook(centralHandbook);
    setValue("selectedCentralHandbookId", centralHandbook?.id || "");

    if (centralHandbook) {
      setTitle(centralHandbook.title);
      setValue("title", centralHandbook.title, {
        shouldDirty: true,
        shouldValidate: true,
      });
    }

    if (
      !centralHandbook &&
      selectedCentralHandbook &&
      title === selectedCentralHandbook.title
    ) {
      setTitle("");
      setValue("title", "", { shouldDirty: true, shouldValidate: true });
    }
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTitle = e.target.value;
    setTitle(newTitle);
    setValue("title", newTitle, { shouldDirty: true, shouldValidate: true });
  };

  const onSubmit = async (data: FormData) => {
    if (!session?.organization?.id) {
      toast.error("Ingen organisasjon valgt");
      return;
    }

    const handbookData =
      isEditing && handbook
        ? {
            ...handbook,
            title: data.title.trim(),
            isPublic: data.isPublic,
          }
        : ({
            title: data.title.trim(),
            isPublic: data.isPublic,
            importedHandbookId: selectedCentralHandbook?.id || undefined,
            localChange: Boolean(selectedCentralHandbook?.id),
            pendingChange: false,
            pendingDeletion: false,
            isPublished: false,
            externalOrgId: session?.organization?.id || "",
          } as Handbook);

    await handleOptimisticUpdate(
      () => saveLocalHandbook(handbookData),
      handbookData,
      {
        successMessage: isEditing ? "Håndbok oppdatert" : "Håndbok opprettet",
        errorMessage: "Kunne ikke lagre håndbok",
        onSuccess: (result: Handbook) => {
          onHide();
          if (!isEditing && result.id) {
            navigate(`/editor/${result.id}`);
          }
        },
        onError: (error) => {
          handleOptimisticError(error, "save handbook", {
            customMessage: "Kunne ikke lagre håndbok. Prøv igjen.",
          });
        },
      }
    );
  };

  return (
    <Modal isOpen={isOpen} onClose={onHide} autoFocus={false}>
      <Modal.Header onClose={onHide}>
        <Modal.Title>
          {isEditing ? t("editTitle") : t("createTitle")}
        </Modal.Title>
      </Modal.Header>

      <Modal.Body style={{ overflow: "unset" }}>
        <form onSubmit={handleSubmit(onSubmit)}>
          {!isEditing &&
            (!centralError ||
              centralHandbooks.length > 0 ||
              isLoadingCentral) && (
              <Field style={{ marginBottom: "1.5rem" }}>
                <Label htmlFor="central-handbook">Sentral håndbok</Label>
                <Select
                  id="central-handbook"
                  aria-label="Sentral håndbok"
                  clearable
                  placeholder="Velg sentral håndbok (valgfritt)"
                  options={centralHandbooks}
                  value={selectedCentralHandbook}
                  onChange={(handbook: PublishedCentralHandbook | null) =>
                    handleCentralHandbookChange(handbook)
                  }
                  getOptionLabel={(handbook: PublishedCentralHandbook) =>
                    handbook.title
                  }
                  getOptionValue={(handbook: PublishedCentralHandbook) =>
                    handbook.id!
                  }
                  disabled={isSaving || isLoadingCentral}
                  loading={isLoadingCentral}
                />
                <Help>
                  {isLoadingCentral
                    ? "Laster sentrale håndbøker..."
                    : centralError
                      ? "Kunne ikke laste sentrale håndbøker. Du kan fortsatt opprette en tom håndbok."
                      : centralHandbooks.length === 0
                        ? "Ingen sentrale håndbøker tilgjengelig. Du kan fortsatt opprette en tom håndbok."
                        : t("centralBased")}
                </Help>
              </Field>
            )}

          <Field style={{ marginBottom: "1.5rem" }}>
            <Label htmlFor="handbook-title">Tittel *</Label>
            <Input
              id="handbook-title"
              type="text"
              value={title}
              onChange={handleTitleChange}
              placeholder="Skriv inn tittel på håndboka"
              fullWidth
              autoFocus
            />
            {errors.title && <Help color="danger">{errors.title.message}</Help>}
            {selectedCentralHandbook && title && (
              <Help>
                {selectedCentralHandbook.title === title ? (
                  <span>{t("autoSync")}</span>
                ) : (
                  <span style={{ color: "#ff6600" }}>⚠️ {t("manualSync")}</span>
                )}
              </Help>
            )}
            {centralError && (
              <Help color="danger">
                Kunne ikke laste sentrale håndbøker. Du kan fortsatt opprette en
                tom håndbok.
              </Help>
            )}
          </Field>

          <Field>
            <Checkbox id="handbook-public" {...register("isPublic")}>
              {t("publicLabel")}
            </Checkbox>
          </Field>
        </form>
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={onHide} disabled={isSaving}>
          Avbryt
        </Button>
        <Button
          color="success"
          type="submit"
          form="handbook-form"
          onClick={handleSubmit(onSubmit)}
          disabled={!isValid || isSaving}
          loading={isSaving}
        >
          {t("saveButton")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
