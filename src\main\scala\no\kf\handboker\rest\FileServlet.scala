package no.kf.handboker.rest

import no.kf.exception.UserFriendlyException
import no.kf.handboker.model.KFFile
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.support.{ExternalOrgIdExtractionSupport, JsonSupport}
import org.scalatra.ScalatraServlet
import org.scalatra.servlet.FileUploadSupport
import no.kf.rest.ScalatraExceptions
import java.nio.charset.StandardCharsets.UTF_8

class FileServlet extends ScalatraServlet with SessionSupport with <PERSON>sonSupport with ExternalOrgIdExtractionSupport with FileUploadSupport {

  lazy val fileService = componentRegistry.fileService

  post("/?") {
    getFile match {
      case None => None
      case Some(file) => "location" -> fileService.persistFile(file)
    }
  }

  //TODO: Handle requests to non-existant images, empty route, possibly limit access only to images in reading link
  get("/:fileId") {
    try {
      val fileId = extractRequiredParam("fileId")
      val file = fileService.retrieveFile(fileId)

      contentType = file.contentType.getOrElse("image/png")
      response.addHeader("Content-Disposition", generateContentDispositionValue(file.name))
      file.byteArray
    } catch {
      case e: Exception =>
        ScalatraExceptions.notFound(Some("File not found"))
    }
  }

  delete("/:fileId") {
    val fileId = extractRequiredParam("fileId")

    fileService.deleteFile(fileId)
    204
  }

  def generateContentDispositionValue(fileName: String): String = {
    val urlEncodedFileName = new org.scalatra.util.RicherString.RicherStringImplicitClass(fileName).urlEncode(UTF_8)
    s"inline; filename=${"\""}$fileName${"\""}; filename*=UTF-8''$urlEncodedFileName"
  }

  def getFile: Option[KFFile] = {
    if (fileParams.get("file").isEmpty) return None

    val file = fileParams("file")

    val contentType = file.contentType.getOrElse({
      throw new UserFriendlyException("Ukjent filtype.")
    })

    Some(KFFile(contentType.split("/").last, file.get()))
  }

}
