import React from "react";
import { Icon, Tree } from "kf-bui";
import { useLocation } from "react-router-dom";
import type { CentralHandbookWithChildren } from "@/store/services/handbook/utils";
import type {
  CentralTreeNodeWithChildren,
  CentralChapter,
  CentralSection,
} from "@/types";
import { shouldDisableNode } from "../../../utils/moveValidation";
import { useIsHandbookPending } from "@/store/services/handbook/usePendingPublications";
import { useAppSelector } from "@/store";
import { selectSelectedCentralItem } from "@/store/slices/centralTreeSlice";
import { CentralChapterNode } from "../CentralChapterNode";

interface CentralHandbookNodeProps {
  handbook: CentralHandbookWithChildren;
  moving?: boolean;
  onSetSelectedItem?: (item: CentralTreeNodeWithChildren) => void;
  disabled?: boolean;
  movedItem?: CentralChapter | CentralSection;
}

export const CentralHandbookNode: React.FC<CentralHandbookNodeProps> = ({
  handbook,
  moving = false,
  onSetSelectedItem,
  disabled = false,
  movedItem,
}) => {
  const location = useLocation();
  const chapters = handbook.chapters || [];
  const isPending = useIsHandbookPending(handbook.id);
  const selectedCentralItem = useAppSelector(selectSelectedCentralItem);

  const isActive = React.useMemo(() => {
    if (moving) return false;

    const currentPath = location.pathname;
    const handbookPath = `/central-editor/${handbook.id}`;

    return currentPath.startsWith(handbookPath);
  }, [location.pathname, handbook.id, moving]);

  const items = chapters.map((chapter) => (
    <CentralChapterNode
      key={chapter.id}
      chapter={chapter}
      onSetSelectedItem={onSetSelectedItem}
      moving={moving}
      disabled={disabled}
      movedItem={movedItem}
    />
  ));

  const renderHandbookContent = () => (
    <>
      <Icon icon="book" size="small" style={{ marginRight: "4px" }} />
      {handbook.title}
      {isPending && (
        <Icon
          icon="spinner"
          size="small"
          style={{
            marginLeft: "8px",
            animation: "spin 3s linear infinite",
            color: "rgba(0, 0, 0, 0.3)",
          }}
          title="Publisering pågår..."
        />
      )}
    </>
  );

  if (moving && movedItem) {
    const isDisabled = shouldDisableNode(handbook, movedItem, disabled);
    const isBeingMoved = false;
    const isSelected = selectedCentralItem?.id === handbook.id;
    const containsMovedItem =
      movedItem && movedItem.centralHandbookId === handbook.id;
    const isDifferentHandbook =
      movedItem && movedItem.centralHandbookId !== handbook.id;

    return (
      <Tree.Item
        id={handbook.id!}
        key={handbook.id}
        items={items}
        onClick={() => onSetSelectedItem?.(handbook)}
        disabled={isDisabled}
        style={{
          opacity: isDifferentHandbook ? 0.5 : 1,
          cursor: isDisabled ? "not-allowed" : "pointer",
          fontWeight: isBeingMoved || isSelected ? 600 : undefined,
          color: isBeingMoved ? "#1976d2" : isSelected ? "#2e7d32" : undefined,
          backgroundColor: isBeingMoved
            ? "#e3f2fd"
            : isSelected
              ? "#e8f5e8"
              : containsMovedItem
                ? "#f5f5f5"
                : undefined,
          padding:
            isBeingMoved || isSelected || containsMovedItem
              ? "2px 4px"
              : undefined,
          borderRadius:
            isBeingMoved || isSelected || containsMovedItem ? "4px" : undefined,
          border: isSelected
            ? "2px solid #2e7d32"
            : isBeingMoved
              ? "2px solid #1976d2"
              : undefined,
        }}
      >
        {renderHandbookContent()}
        {isSelected && (
          <span
            style={{ marginLeft: "8px", fontSize: "12px", color: "#2e7d32" }}
          >
            ← valgt som mål
          </span>
        )}
      </Tree.Item>
    );
  }

  return (
    <Tree.ItemLink
      key={handbook.id}
      exact
      items={items}
      to={`/central-editor/${handbook.id}`}
      id={handbook.id!}
      style={isActive ? { fontWeight: "600", color: "#050037" } : undefined}
    >
      {renderHandbookContent()}
    </Tree.ItemLink>
  );
};
