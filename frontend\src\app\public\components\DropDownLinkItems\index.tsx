import React, {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { Icon } from "kf-bui";
import type { Link, LinkCollection } from "@/types";
interface DropdownProps {
  linkCollections: LinkCollection[] | [];
}

export const DropDownLinkItems: React.FC<DropdownProps> = ({
  linkCollections,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const clearHoverTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const handleMouseEnter = useCallback(() => {
    clearHoverTimeout();
    setIsOpen(true);
  }, [clearHoverTimeout]);

  const handleMouseLeave = useCallback((e: React.MouseEvent) => {
    const relatedTarget = e.relatedTarget as Element;
    const currentTarget = e.currentTarget as Element;

    if (relatedTarget && currentTarget.contains(relatedTarget)) {
      return;
    }

    timeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 200);
  }, []);

  const handleDropdownMouseEnter = useCallback(() => {
    clearHoverTimeout();
  }, [clearHoverTimeout]);

  const handleToggleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (isTouchDevice) {
      setIsOpen(!isOpen);
    }
  }, [isTouchDevice, isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isTouchDevice && containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isTouchDevice && isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isTouchDevice, isOpen]);

  // Detect touch device
  useEffect(() => {
    const detectTouchDevice = () => {
      const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isTabletOrMobile = window.matchMedia('(max-width: 1024px)').matches;
      setIsTouchDevice(hasTouchScreen || isTabletOrMobile);
    };

    detectTouchDevice();
    window.addEventListener('resize', detectTouchDevice);
    
    return () => {
      window.removeEventListener('resize', detectTouchDevice);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const displayTitle =
    linkCollections && linkCollections.length > 0 && linkCollections[0]
      ? linkCollections[0].title
      : "Lenker";

  return (
    <div
      className="links-dropdown"
      ref={containerRef}
      onMouseLeave={isTouchDevice ? undefined : handleMouseLeave}
    >
      <span 
        className="dropdown-toggle" 
        onMouseEnter={isTouchDevice ? undefined : handleMouseEnter}
        onClick={handleToggleClick}
        style={{ cursor: isTouchDevice ? 'pointer' : 'default' }}
      >
        {displayTitle}
        {isOpen ? <Icon icon="AngleUp" /> : <Icon icon="AngleDown" />}
      </span>

      {isOpen && (
        <div
          className={`dropdown-menu ${isOpen ? 'visible' : ''}`}
          onMouseEnter={isTouchDevice ? undefined : handleDropdownMouseEnter}
          onClick={(e) => e.stopPropagation()}
        >
          {linkCollections && linkCollections.length > 0 ? (
            linkCollections.map((collection) => (
              <Fragment key={collection.id}>
                {collection.links && collection.links.length > 0 ? (
                  [...collection.links]
                    .sort((a: Link, b: Link) => a.sortOrder - b.sortOrder)
                    .map((link) => (
                      <a
                        key={link.id}
                        className="dropdown-link"
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (link.url) {
                            window.open(
                              link.url,
                              "_blank",
                              "noopener,noreferrer"
                            );
                            setIsOpen(false);
                          }
                        }}
                      >
                        <span>{link.title}</span>
                        <Icon icon="ArrowUpRightFromSquare" />
                      </a>
                    ))
                ) : (
                  <div className="dropdown-link">
                    Ingen lenker i denne samlingen
                  </div>
                )}
              </Fragment>
            ))
          ) : (
            <div
              className="dropdown-section-title"
              style={{ textAlign: "center", padding: "1rem" }}
            >
              Ingen lenker tilgjengelig
            </div>
          )}
        </div>
      )}
    </div>
  );
};
