package no.kf.handboker.model

import no.kf.db.IDGenerator

case class Image (
                   id: String,
                   fileExtension: String,
                   createdName: String,
                   file: Array[Byte]
                 )

object Image {

  def apply(fileExtension: String, file: Array[Byte]): Image =  {
    val id = IDGenerator.generateUniqueId
    new Image(id, fileExtension, s"$id.$fileExtension", file)
  }
}
