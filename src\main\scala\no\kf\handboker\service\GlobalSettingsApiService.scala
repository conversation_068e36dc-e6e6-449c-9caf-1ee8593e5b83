package no.kf.handboker.service

import no.kf.config.{AccessKey, BrukerAdmBaseUrl, MockBrukerAdm}
import no.kf.handboker.config.AppSettingComponent
import no.kf.usermanagement.service.client.api.SettingsApiService

trait GlobalSettingsApiServiceComponent {
  this: AppSettingComponent =>

  class GlobalSettingsApiServiceImpl extends GlobalSettingsApiService with SettingsApiService {
    override val brukerAdmUrl: String = settings.settingFor(BrukerAdmBaseUrl)
    override val mockBrukerAdm: Boolean = settings.settingFor(MockBrukerAdm).toBoolean
    override val accessKey: String = settings.settingFor(AccessKey)
  }
}

trait GlobalSettingsApiService {
  def retrieveSsoLogOutUrl: Option[String]
}
