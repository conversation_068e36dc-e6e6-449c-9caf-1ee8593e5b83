package no.kf.handboker.service

import java.io.BufferedWriter

import no.kf.db.{ExportAndDeleteRepositoryComponent, TransactionManager}
import no.kf.handboker.model.local.LocalEditor
import no.kf.handboker.repository._
import no.kf.util.ResourceUtils.using
import org.jsoup.Jsoup
import org.jsoup.safety.Whitelist

import scala.collection.JavaConverters._
import scala.collection.mutable.{ArrayBuffer, ListBuffer}
import scala.xml.{Node, XML}

trait OrganizationDataServiceComponent extends TransactionManager {
  this: ExportAndDeleteRepositoryComponent
    with HandbookRepositoryComponent
    with SubscriptionRepositoryComponent
    with CentralAccessRepositoryComponent
    with CentralNotificationRepositoryComponent
    with HandbookLinkRepositoryComponent
    with ReadingLinkRepositoryComponent
    with LocalHandbookVersionRepositoryComponent
    with CentralHandbookServiceComponent
    with LocalHandbookServiceComponent
    with HandbookRepositoryComponent
  =>

  val organizationDataService: OrganizationDataService


  class OrganizationDataServiceImpl extends OrganizationDataService {
    private val repo = exportAndDeleteRepository

    /**
      * Recursivly find the parents, then the parents parents and so on, until the generation has no parents,
      * only keeping the latest occurance we can find and thus ensuring we have handbookIds sorted by depth
      */
    private def retrieveHandbookChapterIdsByDepth(handbookChapterIds: List[String]): List[String] = {
      val parentIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldParentChapterId, HandbookChapterTableDef.fieldId, handbookChapterIds)

      if (parentIds.isEmpty) {
        handbookChapterIds.distinct.reverse
      } else {
         retrieveHandbookChapterIdsByDepth(parentIds) ::: handbookChapterIds
      }
    }

    // Stream can not be evaluated outside a transaction
    override def retrieveDataForExternalOrg(externalOrgId: String): Stream[String] = inTransaction {
      val handbookIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookTableDef.tableName, HandbookTableDef.fieldId, HandbookTableDef.fieldExternalOrgId, List(externalOrgId))
      val handbookLinkCollectionIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookLinkCollectionTableDef.handbookLinkCollectionTableName, HandbookLinkCollectionTableDef.fieldId, HandbookLinkCollectionTableDef.fieldHandbookId, handbookIds)
      val chapterIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldId ,HandbookChapterTableDef.fieldHandbookId, handbookIds)
      val sectionIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldId ,HandbookSectionTableDef.fieldHandbookId, handbookIds)

      val handbookTSV = repo.retrieveTSVOfTableWhereFieldIn(HandbookTableDef.tableName, HandbookTableDef.fieldId, handbookIds)
      val subscriptionTSV = repo.retrieveTSVOfTableWhereFieldIn(SubscriptionTableDef.subscriptionTableName, SubscriptionTableDef.fieldHandbookId, handbookIds)
      val changeNotificationTSV = repo.retrieveTSVOfTableWhereFieldIn(ChangeNotificationTableDef.changeNoteTableName, ChangeNotificationTableDef.fieldHandbookId, handbookIds)
      val centralChangeNotificationTSV = repo.retrieveTSVOfTableWhereFieldIn(CentralChangeNotificationTableDef.notificationTableName, CentralChangeNotificationTableDef.fieldHandbookId, handbookIds)
      val handbookChapterTSV = repo.retrieveTSVOfTableWhereFieldIn(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldHandbookId, handbookIds)
      val handbookSectionTSV = repo.retrieveTSVOfTableWhereFieldIn(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldHandbookId, handbookIds)
      val centralContentAccess = repo.retrieveTSVOfTableWhereFieldIn(CentralAccessTableDef.accessTableName, CentralAccessTableDef.fieldExternalOrgId, List(externalOrgId))
      val handbookMetaDataTSV = repo.retrieveTSVOfTableWhereFieldIn(connectionManager.metaDataTable, "id", List("1"))
      val linkCollectionTSV = repo.retrieveTSVOfTableWhereFieldIn(HandbookLinkCollectionTableDef.handbookLinkCollectionTableName, HandbookLinkCollectionTableDef.fieldId, handbookLinkCollectionIds)
      val linkTSV = repo.retrieveTSVOfTableWhereFieldIn(HandbookLinkTableDef.handbookLinkTableName, HandbookLinkTableDef.fieldHandbookLinkCollectionId, handbookLinkCollectionIds)
      val localHandbookChapterVersionTSV = repo.retrieveTSVOfTableWhereFieldIn(LocalHandbookChapterVersionTableDef.tableName, LocalHandbookChapterVersionTableDef.fieldVersionOf, chapterIds)
      val localHandbookSectionVersionTSV = repo.retrieveTSVOfTableWhereFieldIn(LocalHandbookSectionVersionTableDef.tableName, LocalHandbookSectionVersionTableDef.fieldVersionOf, sectionIds)
      val localHandbookTitleVersionTSV = repo.retrieveTSVOfTableWhereFieldIn(LocalHandbookTitleVersionTableDef.tableName, LocalHandbookTitleVersionTableDef.fieldVersionOf, handbookIds)
      val commentTSV = repo.retrieveTSVOfTableWhereFieldIn(HandbookCommentTableDef.tableName, HandbookCommentTableDef.fieldHandbookId, handbookIds)
      handbookTSV ++ subscriptionTSV ++ changeNotificationTSV ++ centralChangeNotificationTSV ++ handbookChapterTSV ++ handbookSectionTSV ++ centralContentAccess ++ handbookMetaDataTSV ++
        linkCollectionTSV ++ linkTSV ++ localHandbookChapterVersionTSV ++ localHandbookSectionVersionTSV ++ commentTSV ++ localHandbookTitleVersionTSV
    }

    override def writeDataForExternalOrg(externalOrgId: String, bufferedWriter: BufferedWriter): Unit = inTransaction {
      using(bufferedWriter) { bufferedWriter => {
        val data = retrieveDataForExternalOrg(externalOrgId)
        data.foreach(tsv => {
          bufferedWriter.write(tsv.stripSuffix("\n"))
          bufferedWriter.write("\n")
        })
      }}
    }

    override def deleteDataForExternalOrg(externalOrgId: String): Unit = inTransaction {
      val handbookIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookTableDef.tableName, HandbookTableDef.fieldId, HandbookTableDef.fieldExternalOrgId, List(externalOrgId))
      val chapterIds = retrieveHandbookChapterIdsByDepth(repo.retrieveColumnDataFromTableWhereFieldIn(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldId, HandbookChapterTableDef.fieldHandbookId, handbookIds))
      val handbookLinkCollectionIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookLinkCollectionTableDef.handbookLinkCollectionTableName, HandbookLinkCollectionTableDef.fieldId, HandbookLinkCollectionTableDef.fieldHandbookId, handbookIds)
      val sectionIds = repo.retrieveColumnDataFromTableWhereFieldIn(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldId ,HandbookSectionTableDef.fieldHandbookId, handbookIds)

      repo.deleteFromTableWhereFieldIn(HandbookLinkTableDef.handbookLinkTableName, HandbookLinkTableDef.fieldHandbookLinkCollectionId, handbookLinkCollectionIds)
      repo.deleteFromTableWhereFieldIn(HandbookLinkCollectionTableDef.handbookLinkCollectionTableName, HandbookLinkCollectionTableDef.fieldId, handbookLinkCollectionIds)
      repo.deleteFromTableWhereFieldIn(CentralAccessTableDef.accessTableName, CentralAccessTableDef.fieldExternalOrgId, List(externalOrgId))
      repo.deleteFromTableWhereFieldIn(ChangeNotificationTableDef.changeNoteTableName, ChangeNotificationTableDef.fieldHandbookId, handbookIds)
      repo.deleteFromTableWhereFieldIn(CentralChangeNotificationTableDef.notificationTableName, ChangeNotificationTableDef.fieldHandbookId, handbookIds)
      repo.deleteFromTableWhereFieldIn(SubscriptionTableDef.subscriptionTableName, SubscriptionTableDef.fieldHandbookId, handbookIds)
      repo.deleteFromTableWhereFieldIn(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldHandbookId, handbookIds)
      repo.deleteFromTableWhereFieldIn(LocalHandbookSectionVersionTableDef.tableName, LocalHandbookSectionVersionTableDef.fieldVersionOf, sectionIds)
      repo.deleteFromTableWhereFieldIn(LocalHandbookChapterVersionTableDef.tableName, LocalHandbookChapterVersionTableDef.fieldVersionOf, chapterIds)
      repo.deleteFromTableWhereFieldIn(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldId, chapterIds)
      repo.deleteFromTableWhereFieldIn(HandbookTableDef.tableName, HandbookTableDef.fieldId, handbookIds)
      repo.deleteFromTableWhereFieldIn(HandbookCommentTableDef.tableName, HandbookCommentTableDef.fieldHandbookId, handbookIds)
      repo.deleteFromTableWhereFieldIn(LocalHandbookTitleVersionTableDef.tableName, LocalHandbookTitleVersionTableDef.fieldVersionOf, handbookIds)
    }

    private[service] def findLinks(html: String): List[String] = {
      val listOfAtags = Jsoup.parse(html).select("a").iterator().asScala.toList.map(_.toString)
      listOfAtags
    }

    private[service] def replaceLink(linkTag: String, test: String): String = {
      val targetRegex = """target=\".*?\"""".r
      val relRegex = """rel=\".*?\"""".r
      val target = targetRegex.findAllIn(linkTag).toList.headOption
      val rel = relRegex.findAllIn(linkTag).toList.headOption

      val updatedTargetLink = target match {
        case Some(target) => linkTag.replace(target, "target=\"_blank\"")
        case None => linkTag.patch(3, "target=\"_blank\" ", 0)
      }

      val updatedRelLink = rel match {
        case Some(target) => updatedTargetLink.replace(target, "rel=\"noopener\"")
        case None => updatedTargetLink.patch(3, "rel=\"noopener\" ", 0)
      }
      updatedRelLink
    }

    //    TODO: package private heller
    override def replaceLinksInSection(htmlOption: Option[String]): Option[String] = {
      htmlOption match {
        case Some(html) => Some(findLinks(html).fold(html)((accu, link) => accu.replace(link, replaceLink(link, html))))
        case None => None
      }
    }

    override def replaceTargetForLinksInHandbooks(currentUserId: String): String = inTransaction {
      val externalOrgs = localHandbookService.retrieveAllExternalOrgIds()
      val centralHandboksUpdated = centralHandbookService.retrieveAllCentralSections()
        .flatMap(section =>{
          val newSectionHtml = replaceLinksInSection(section.html)
          if(newSectionHtml != section.html){
            Some(centralHandbookService.persistCentralSection(section.copy(html = newSectionHtml), currentUserId))
          }else{
            None
          }
        })

      val localSectionsUpdated = externalOrgs.flatMap(orgId => {
        val localHandbooksForOrg = localHandbookService.retrieveHandbooksForExternalOrganization(orgId)
        val localSections = localHandbooksForOrg.flatMap( handbook => localHandbookService.retrieveSectionsForHandbook(handbook.id.get))
        localSections.flatMap(section => {
          val newSectionText = replaceLinksInSection(section.text)
          if(newSectionText != section.text){
            Some(handbookRepository.persistSection(section.copy(text = replaceLinksInSection(section.text))))
          } else{
            None
          }
        })
      })
    s"Lokale håndbok seksjoner oppdatert = ${localSectionsUpdated.length}, sentrale håndbok seksjoner oppdatert = ${centralHandboksUpdated.length}"
    }

    
    
    override def addSizeToImages(currentUserId: String): String = inTransaction {
      try{
        log.info(s"Starter bilde størrelse jobb")
        var totalImagesUpdated = 0
        var totalSectionsUpdated = 0
        var totalSections = 0
        var totalTimeTaken: Double = 0
        val externalOrgs = localHandbookService.retrieveAllExternalOrgIds()
        log.info(s"Organisasjoner funnet: " + externalOrgs)
        externalOrgs.foreach(extOrg => {
          log.info(s"Starter håndtering av organisasjon: " + extOrg)
          var imagesUpdated = 0
          var sectionsUpdated = 0
          val start = System.nanoTime()
          val handbooks = localHandbookService.retrieveHandbooksForExternalOrganization(extOrg)
          var index = 0
          log.info(s"Håndbøker hentet: " + handbooks.length)
          handbooks.foreach(handbook => {
            log.info("Starter jobb med håndbok index: " + index)
            val (totalImagesUpdated, totalSectionsUpdated) = localHandbookService.updateSectionsForHandbookWithUpdateImageDimensions(handbook.id.get, currentUserId)
            imagesUpdated += totalImagesUpdated
            sectionsUpdated += totalSectionsUpdated
            index += 1
          })
          val end = System.nanoTime()
          val elapsedTime = end - start
          val timeTaken = elapsedTime.asInstanceOf[Double] / 1000000000
          totalTimeTaken += timeTaken
          totalImagesUpdated += imagesUpdated
          totalSectionsUpdated += sectionsUpdated
          totalSections += handbooks.length
          log.info(s"Organisasjon fullført: ${extOrg}")
          log.info(s"Seksjoner lest: ${handbooks.length}, oppdatert: ${sectionsUpdated}, bilder oppdatert: ${imagesUpdated}. Dette tok: ${timeTaken} sekunder")
        })
        s"Lokale håndbok seksjoner lest: ${totalSections}, oppdatert: ${totalSectionsUpdated}, bilder oppdatert: ${totalImagesUpdated}, organisasjoner fullført: ${externalOrgs.length}. Dette tok: ${totalTimeTaken} sekunder"
      } catch {
        case e: Exception => {
          log.warn(s"Feil under bilde-størrelse jobb: ${e.getMessage}")
          s""
        }
      }
    }
  }
}

trait OrganizationDataService {
  def retrieveDataForExternalOrg(externalOrgId: String): Stream[String]
  def writeDataForExternalOrg(externalOrgId: String, bufferedWriter: BufferedWriter): Unit
  def deleteDataForExternalOrg(externalOrgId: String): Unit
  def replaceTargetForLinksInHandbooks(currentUserId: String): String
  def replaceLinksInSection(htmlOption: Option[String]): Option[String]
  def addSizeToImages(currentUserId: String): String
}
