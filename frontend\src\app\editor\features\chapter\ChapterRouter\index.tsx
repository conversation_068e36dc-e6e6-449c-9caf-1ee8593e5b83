import React from "react";
import { Routes, Route } from "react-router-dom";
import { ChapterScreen } from "../ChapterScreen";
import { EditChapterScreen } from "../EditChapterScreen";
import { MoveLocalChapterOrSection } from "../../shared/MoveLocalChapterOrSection";

export const ChapterRouter: React.FC = () => {
  return (
    <Routes>
      <Route path=":chapterId" element={<ChapterScreen />} />
      <Route path="add-new" element={<EditChapterScreen />} />
      <Route path=":chapterId/edit" element={<EditChapterScreen />} />
      <Route path=":chapterId/move" element={<MoveLocalChapterOrSection />} />
    </Routes>
  );
};