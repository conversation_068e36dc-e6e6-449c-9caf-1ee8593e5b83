create table handbookchapter_version(id VARCHAR(37), handbook_id VARCHAR(37), version_of VARCHAR(37), title NVARCHAR(2000), parent_chapter_id VARCHAR(37), orderindex smallint, created_date bigint, created_by varchar(100), updated_date bigint, updated_by varchar(100), version_date bigint, PRIMARY KEY(id))
ALTER TABLE handbookchapter_version ADD CONSTRAINT fk_handbookchapter_version_handbook FOREIGN KEY (handbook_id) REFERENCES handbook(id)
ALTER TABLE handbookchapter_version ADD CONSTRAINT fk_handbookchapter_version_chapter FOREIGN KEY (version_of) REFERENCES handbookchapter(id)
ALTER TABLE handbookchapter_version ADD CONSTRAINT fk_handbookchapter_version_parent_chapter FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)

create table handbooksection_version(id VARCHAR(37), handbook_id VARCHAR(37), version_of VARCHAR(37), title NVARCHAR(2000), html NVARCHAR(max), parent_chapter_id VARCHAR(37), orderindex smallint, created_date bigint, created_by varchar(100), updated_date bigint, updated_by varchar(100), text_updated_date bigint, text_updated_by varchar(100), version_date bigint, PRIMARY KEY(id))
ALTER TABLE handbooksection_version ADD CONSTRAINT fk_handbooksection_version_handbook FOREIGN KEY (handbook_id) REFERENCES handbook(id)
ALTER TABLE handbooksection_version ADD CONSTRAINT fk_handbooksection_version_section FOREIGN KEY (version_of) REFERENCES handbooksection(id)
ALTER TABLE handbooksection_version ADD CONSTRAINT fk_handbooksection_version_parent_chapter FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)

CREATE TABLE handbook_comment(id VARCHAR(37), text VARCHAR(4000), edited_by VARCHAR(100) NOT NULL, edited_date BIGINT NOT NULL, handbook_id VARCHAR(37) NOT NULL, PRIMARY KEY(id))

ALTER TABLE handbook_comment ADD CONSTRAINT fk_comments_handbook FOREIGN KEY (handbook_id) REFERENCES handbook(id)
