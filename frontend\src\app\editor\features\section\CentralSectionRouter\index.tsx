import React from "react";
import { Routes, Route, Navigate, useParams } from "react-router-dom";
import { CentralSectionScreen } from "../CentralSectionScreen";
import { EditCentralSection } from "../CentralSectionScreen/EditCentralSection";
import { MoveCentralChapterOrSection } from "../../shared/MoveCentralChapterOrSection";

// Legacy compatibility redirect component
const LegacyMoveRedirect: React.FC = () => {
  const { sectionId } = useParams<{ sectionId: string }>();
  return <Navigate to={`../${sectionId}/move`} replace />;
};

export const CentralSectionRouter: React.FC = () => {
  return (
    <Routes>
      <Route path=":sectionId" element={<CentralSectionScreen />} />
      <Route path="add-new" element={<EditCentralSection />} />
      <Route path=":sectionId/edit" element={<EditCentralSection />} />
      <Route path=":sectionId/move" element={<MoveCentralChapterOrSection />} />
      <Route path="move/:sectionId" element={<LegacyMoveRedirect />} />
    </Routes>
  );
};