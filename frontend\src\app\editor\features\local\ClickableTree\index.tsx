import { Card, Tree } from "kf-bui";
import { useCallback, useRef } from "react";
import { useParams } from "react-router-dom";
import { ClickableHandbookNode } from "../ClickableHandbookNode";
import { useAppDispatch } from "@/store";
import { selectLocalItem } from "@/store/slices/localTreeSlice";
import type { LocalTreeNodeWithChildren } from "@/types";
import {
  useLocalHandbooks,
  useLocalChapters,
  useLocalSections,
} from "@/store/services/handbook/hooks";
import {
  useGetLocalChapterQuery,
  useGetLocalSectionQuery,
} from "@/store/services/handbook/localHandbookApi";
import { transformLocalToTreeStructure } from "@/store/services/handbook/utils";
import { Spinner } from "@/shared/components/Spinner";

export const ClickableTree = () => {
  const dispatch = useAppDispatch();
  const treeRef = useRef<HTMLDivElement>(null);
  const { chapterId, sectionId } = useParams<{
    handbookId?: string;
    chapterId?: string;
    sectionId?: string;
  }>();

  const { data: movedChapter } = useGetLocalChapterQuery(chapterId!, {
    skip: !chapterId,
  });
  const { data: movedSection } = useGetLocalSectionQuery(sectionId!, {
    skip: !sectionId,
  });

  const movedItem = movedChapter || movedSection;

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case "Enter":
      case " ":
        event.preventDefault();
        break;
      case "Escape":
        event.preventDefault();
        break;
      default:
        return;
    }
  }, []);

  const {
    data: localHandbooks,
    error: localHandbooksError,
    isLoading: localHandbooksLoading,
  } = useLocalHandbooks();

  const {
    data: localChapters,
    error: localChaptersError,
    isLoading: localChaptersLoading,
  } = useLocalChapters();

  const {
    data: localSections,
    error: localSectionsError,
    isLoading: localSectionsLoading,
  } = useLocalSections();

  const handbooks =
    localHandbooks && localChapters && localSections
      ? transformLocalToTreeStructure(
          localHandbooks,
          localChapters,
          localSections
        )
      : [];

  const isLoading =
    localHandbooksLoading || localChaptersLoading || localSectionsLoading;

  const error = localHandbooksError || localChaptersError || localSectionsError;

  const handleSetSelectedItem = (item: LocalTreeNodeWithChildren) => {
    dispatch(selectLocalItem(item));
  };

  if (error) {
    return (
      <Card>
        <Card.Header>
          <Card.Title>Velg ny plassering</Card.Title>
        </Card.Header>
        <Card.Content>
          <div style={{ padding: "1rem", textAlign: "center", color: "red" }}>
            Feil ved lasting av lokale håndbøker
          </div>
        </Card.Content>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <Card.Header>
          <Card.Title>Velg ny plassering</Card.Title>
        </Card.Header>
        <Card.Content>
          <div style={{ padding: "1rem", textAlign: "center" }}>
            <Spinner text="Laster håndbøker..." />
          </div>
        </Card.Content>
      </Card>
    );
  }

  const items = handbooks.map((book) => (
    <ClickableHandbookNode
      key={book.id}
      handbook={book}
      onSetSelectedItem={handleSetSelectedItem}
      movedItem={movedItem}
    />
  ));

  return (
    <Card>
      <Card.Header>
        <Card.Title>Velg ny plassering</Card.Title>
      </Card.Header>
      <Card.Content style={{ paddingLeft: 0, paddingRight: 0 }}>
        <div
          ref={treeRef}
          onKeyDown={handleKeyDown}
          tabIndex={0}
          role="tree"
          aria-label="Velg ny plassering for flytting"
          style={{ outline: "none" }}
        >
          <Tree>{items}</Tree>
        </div>
      </Card.Content>
    </Card>
  );
};
