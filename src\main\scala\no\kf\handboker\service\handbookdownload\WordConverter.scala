package no.kf.handboker.service.handbookdownload

import org.docx4j.convert.in.xhtml.XHTMLImporterImpl
import org.docx4j.openpackaging.packages.WordprocessingMLPackage
import java.io.ByteArrayOutputStream

import no.kf.handboker.ProductionRegistry.componentRegistry.settings
import no.kf.handboker.config.SiteUrl
import org.docx4j.openpackaging.parts.WordprocessingML.{FooterPart, HeaderPart}
import org.docx4j.relationships.Relationship
import org.docx4j.wml._
import org.joda.time.DateTime
import no.kf.util.Logging
import org.docx4j.org.xhtmlrenderer.util.XRLog


object WordConverter extends Logging {

  lazy val factory = new ObjectFactory
  lazy val siteUrl = new String(settings.settingFor(SiteUrl))
  private val dateFormat = "dd.MM.yyyy"

  def html2Word(html: String, handbookName: String): Array[Byte] = {
    val output: ByteArrayOutputStream = new ByteArrayOutputStream
    XRLog.setLoggingEnabled(false)
    val wordMLPackage = WordprocessingMLPackage.createPackage()
    val XHTMLImporter = new XHTMLImporterImpl(wordMLPackage)
    wordMLPackage.getMainDocumentPart.getContent.addAll(
      XHTMLImporter.convert( html, siteUrl) )

    val headerRelationship = createHeaderPart(wordMLPackage, handbookName)
    val footerRelationship = createFooterPart(wordMLPackage)
    createHeaderAndFooterReferences(wordMLPackage, headerRelationship, footerRelationship)

    wordMLPackage.save(output)
    output.flush()
    output.close()

    output.toByteArray
  }

  private def createHeaderPart(wordMLPackage: WordprocessingMLPackage, handbookName: String): Relationship = {
    val headerPart = new HeaderPart()
    headerPart.setPackage(wordMLPackage)

    headerPart.setJaxbElement(createHeader(handbookName))
    wordMLPackage.getMainDocumentPart.addTargetPart(headerPart)
  }

  private def createHeader(handbookName: String): Hdr = {
    val hdr = factory.createHdr()
    val paragraph = factory.createP()

    val ppr = factory.createPPr()
    val pstyle = factory.createPPrBasePStyle()
    pstyle.setVal("Header")
    ppr.setPStyle(pstyle)
    paragraph.getContent.add(ppr)
    paragraph.getContent.add(createText(s"Utskrift for ${handbookName}"))
    // Add tabs to right justify the date thanks the the Header style
    val run = factory.createR()
    val tab = factory.createRTab()
    run.getContent.add(tab)
    paragraph.getContent.add(run)
    paragraph.getContent.add(tab)
    paragraph.getContent.add(createText(DateTime.now().toString(dateFormat)))

    hdr.getContent.add(paragraph)
    hdr
  }

  private def createFooterPart(wordMLPackage: WordprocessingMLPackage): Relationship = {
    val footerPart = new FooterPart()
    footerPart.setPackage(wordMLPackage)

    footerPart.setJaxbElement(createFooter())
    wordMLPackage.getMainDocumentPart.addTargetPart(footerPart)
  }

  private def createFooter(): Ftr = {
    val ftr = factory.createFtr()

    val paragraph = factory.createP()

    //Right justify the paragraph
    val ppr = factory.createPPr()
    val jc = factory.createJc()
    jc.setVal(JcEnumeration.RIGHT)
    ppr.setJc(jc)
    paragraph.getContent.add(ppr)

    paragraph.getContent.add(createText("Side "))
    paragraph.getContent.add(createFldChar(STFldCharType.BEGIN))
    paragraph.getContent.add(createInstrText("PAGE"))
    paragraph.getContent.add(createFldChar(STFldCharType.END))
    paragraph.getContent.add(createText(" av "))
    paragraph.getContent.add(createFldChar(STFldCharType.BEGIN))
    paragraph.getContent.add(createInstrText("NUMPAGES"))
    paragraph.getContent.add(createFldChar(STFldCharType.END))

    ftr.getContent.add(paragraph)

    ftr
  }

  private def createFldChar(charType: STFldCharType): R = {
    val run = createRWithFont()
    val fldChar = factory.createFldChar()
    fldChar.setFldCharType(charType)
    run.getContent.add(fldChar)
    run
  }

  private def createText(text: String): R = {
    val run = createRWithFont()
    val txt = new Text()
    txt.setSpace("preserve")
    txt.setValue(text)
    run.getContent.add(txt)
    run
  }

  private def createInstrText(text: String): R = {
    val run = createRWithFont()
    val txt = new Text()
    txt.setValue(text)
    run.getContent.add(factory.createRInstrText(txt))
    run
  }

  private def createRWithFont(): R = {
    val run = factory.createR()
    val rpr = factory.createRPr()
    val runFont = factory.createRFonts()
    runFont.setAscii("Arial")
    runFont.setHAnsi("Arial")
    rpr.setRFonts(runFont)
    run.getContent.add(rpr)
    run
  }


  private def createHeaderAndFooterReferences(wordMLPackage: WordprocessingMLPackage, headerRelationship: Relationship, footerRelationship: Relationship): Unit = {
    val sections = wordMLPackage.getDocumentModel.getSections()

    var sectPr = sections.get(sections.size() - 1).getSectPr()
    // There is always a section wrapper, but it might not contain a sectPr
    if (sectPr == null) {
      sectPr = factory.createSectPr()
      wordMLPackage.getMainDocumentPart.addObject(sectPr)
      sections.get(sections.size() - 1).setSectPr(sectPr)
    }

    val headerReference = factory.createHeaderReference()
    headerReference.setId(headerRelationship.getId)
    headerReference.setType(HdrFtrRef.DEFAULT)
    sectPr.getEGHdrFtrReferences.add(headerReference)

    val footerReference = factory.createFooterReference()
    footerReference.setId(footerRelationship.getId)
    footerReference.setType(HdrFtrRef.DEFAULT)
    sectPr.getEGHdrFtrReferences.add(footerReference)

  }

}
