package no.kf.handboker.repository

import no.kf.db.testing.TransactionRollBack
import no.kf.db.{DbConnectionManager, TransactionManager}
import no.kf.handboker.DefaultTestDI
import no.kf.util.Logging
import org.scalatest._

trait DbTestHandler extends Suite with BeforeAndAfterAll with BeforeAndAfterEach with Logging with DefaultTestDI with TransactionManager with TransactionRollBack {

  this: FunSuite =>

  log.info("Loading db test handler...")

  override val componentRegistry = new DefaultTestRegistry {
    log.info("Curver " + connectionManager.currentVersionNumber)

    override lazy val connectionManager: DbConnectionManager = new DbConnectionManagerImpl
    override lazy val handbookRepository: HandbookRepository = new HandbookRepositoryImpl
    override lazy val centralHandbookRepository: CentralHandbookRepositoryImpl = new CentralHandbookRepositoryImpl
    override lazy val centralAccessRepository: CentralAccessRepository = new CentralAccessRepositoryImpl
    override lazy val subscriptionRepository: SubscriptionRepository = new SubscriptionRepositoryImpl
    override lazy val handbookLinkRepository: HandbookLinkRepository = new HandbookLinkRepositoryImpl
    override lazy val publicationRepository: PublicationRepository = new PublicationRepositoryImpl
    override lazy val centralNotificationRepository: CentralNotificationRepository = new CentralNotificationRepositoryImpl
    override lazy val readingLinkRepository: ReadingLinkRepository = new ReadingLinkRepositoryImpl
    override lazy val localHandbookVersionRepository: LocalHandbookVersionRepository = new LocalHandbookVersionRepositoryImpl
  }

  val connectionManager = componentRegistry.connectionManager

  log.info("Done loading db test handler...")

  override def beforeAll() {
    connectionManager.setupDbPool()
  }

  override def afterAll() {
    connectionManager.releaseAllConnections()
  }

  def transactedTest(testName: String, testTags: Tag*)(action: => Unit): Unit = {
    test(testName, testTags: _*) {
      testInTransaction(action)
    }
  }

}
