import React from "react";
import { Input } from "kf-bui";

export interface SearchFieldProps {
  query: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: (event: React.FormEvent<HTMLFormElement>) => void;
  placeholder?: string;
  autoFocus?: boolean;
  size?: "small" | "medium" | "large";
}

export const SearchField: React.FC<SearchFieldProps> = ({
  query,
  onChange,
  onSubmit,
  placeholder = "Søk",
  autoFocus = false,
  size = "medium",
}) => {
  return (
    <form onSubmit={onSubmit}>
      <Input
        aria-label="Søk"
        iconLeft="search"
        autoFocus={autoFocus}
        size={size}
        placeholder={placeholder}
        onChange={onChange}
        value={query}
        type="search"
      />
    </form>
  );
};
