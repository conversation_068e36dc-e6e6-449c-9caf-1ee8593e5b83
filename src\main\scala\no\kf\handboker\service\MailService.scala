package no.kf.handboker.service

import java.io.IOException

import no.kf.exception.KfException
import no.kf.handboker.config._
import no.kf.handboker.mail.MailBuilder
import no.kf.mail.{AbstractMailService, AbstractSMTPAuthenticator}
import no.kf.util.{FileSystemUtil, Logging, PropertyIOUtils}

trait MailServiceComponent {
  this: AppSettingComponent
    with SubscriptionServiceComponent
    with Logging =>

  val mailService: MailService

  class MailService extends AbstractMailService  {
    override lazy val doSendMail = settings.settingFor(SMTPSendMailToRecipients).toBoolean
    override lazy val smtphost = settings.optionalSettingFor(SMTPHost).getOrElse("")
    override lazy val smtpport = settings.optionalSettingFor(SMTPPort).getOrElse("")
    override lazy val avsender = settings.settingFor(SMTPSentEmailFrom)
    override val overridingMailProperties = settings.optionalSettingFor(SMTPOverrideFile).map({
      overrideFile => MailPropertyLoader.readPropertyFile(overrideFile)
    })
    override val daysToKeepDisplayingEmailErrors: Int = 1

    override def getSMTPAuthenticator() = new SMTPAuthenticator

    override def checkIfRecipientIsLocketOut(email: String) = false

    class SMTPAuthenticator extends AbstractSMTPAuthenticator {
      lazy val username = settings.settingFor(SMTPUser)
      lazy val password = settings.settingFor(SMTPPwd)
    }

    private object MailPropertyLoader extends Logging {
      def readPropertyFile(fileName: String) =
        try {
          PropertyIOUtils.loadRessource(FileSystemUtil.getConfigFilePath(System.getProperty("handboker.config.dir"), fileName))
        } catch {
          case ex: IOException =>
            log.error("Kunne ikke finne filen " + fileName)
            throw new KfException("Avslutter.")
        }
    }

    def sendChangeNotificationMessages(): List[Boolean] = {
      val allChangeNotes = subscriptionService.retrieveChangesForEmails
      val messages = allChangeNotes.map{ case(emailAddress, notes) =>
        MailBuilder.createChangeNotificationMessagePerUser(emailAddress, notes)
      }

      messages.map(send).toList
    }
  }

}
