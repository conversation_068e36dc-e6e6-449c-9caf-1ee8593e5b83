package no.kf.handboker.repository

import no.kf.db.IDGenerator
import no.kf.db.RichSQL._
import no.kf.handboker.config.{AppSettingComponent, PublicWebAppPath, SiteUrl}
import no.kf.handboker.model.central.ReadingLink
import no.kf.util.Logging
import org.joda.time.DateTime

trait ReadingLinkRepositoryComponent {
  this: DbConnectionManagerComponent
    with AppSettingComponent =>

  val readingLinkRepository: ReadingLinkRepository

  object ReadingLinkTableDef {
    val tableName = "reading_link"

    val fieldId = "id"
    val fieldLink = "link"
    val fieldCentralSectionId = "central_section_id"
    val fieldCreatedDate = "created_date"
    val fieldValidTo = "valid_to"

    val tableColumns = List(fieldId, fieldLink, fieldCentralSectionId, fieldCreatedDate, fieldValidTo)
  }

  class ReadingLinkRepositoryImpl extends ReadingLinkRepository with Logging {

    override def persistReadingLink(readingLink: ReadingLink): ReadingLink = {
      if(readingLink.id.isDefined){
        updateReadingLink(readingLink)
      } else {
        insertReadingLink(readingLink)
      }
    }

    private def insertReadingLink(readingLink: ReadingLink): ReadingLink = {
      import ReadingLinkTableDef._

      val sql = s"INSERT INTO $tableName (${tableColumns.mkString(",")}) VALUES (${#?(tableColumns)})"
      val id = IDGenerator.generateUniqueId
      val link = new String(settings.settingFor(SiteUrl).concat(s"/public/readinglink/$id"))
      connectionManager.doWithConnection(
        _.ps(sql) << id << link << readingLink.centralSectionId << DateTime.now() << readingLink.validTo <<!
      )
      retrieveReadingLink(id).get
    }

    private def updateReadingLink(readingLink: ReadingLink): ReadingLink = {
      import ReadingLinkTableDef._

      val sql = s"UPDATE $tableName SET $fieldValidTo = ? WHERE $fieldId = ?"
      connectionManager.doWithConnection(
        _.ps(sql) << readingLink.validTo << readingLink.id <<!
      )
      retrieveReadingLink(readingLink.id.get).get
    }

    override def retrieveReadingLink(linkId: String): Option[ReadingLink] = {
      import ReadingLinkTableDef._

      val sql = s"SELECT ${tableColumns.mkString(",")} FROM $tableName WHERE $fieldId = ?"
      connectionManager.doWithConnection(
        _.ps(sql) << linkId <<# populateReadingLink
      )
    }

    override def retrieveAllReadingLinks(): List[ReadingLink] = {
      import ReadingLinkTableDef._

      val sql = s"SELECT ${tableColumns.mkString(",")} FROM $tableName"
      connectionManager.doWithConnection(
        _.ps(sql) <<! populateReadingLink
      )
    }

    override def retrieveAllValidReadingLinks(): List[ReadingLink] = {
      import ReadingLinkTableDef._

      val sql = s"SELECT ${tableColumns.mkString(",")} FROM $tableName WHERE $fieldValidTo >= ?"
      connectionManager.doWithConnection(
        _.ps(sql) << DateTime.now().getMillis <<! populateReadingLink
      )
    }

    override def retrieveReadingLinkForSection(sectionId: String): Option[ReadingLink] = {
      import ReadingLinkTableDef._

      val sql = s"SELECT ${tableColumns.mkString(",")} FROM $tableName WHERE $fieldCentralSectionId = ? AND $fieldValidTo >= ?"
      connectionManager.doWithConnection(
        _.ps(sql) << sectionId << DateTime.now.getMillis <<# populateReadingLink
      )
    }

    override def deleteReadingLink(linkId: String): Unit = {
      import ReadingLinkTableDef._
      
      val sql = s"DELETE FROM $tableName WHERE $fieldId = ?"
      connectionManager.doWithConnection(
        _.ps(sql) << linkId <<!
      )
    }

    override def deleteInvalidLinks(): Unit = {
      import ReadingLinkTableDef._

      val sql = s"DELETE FROM $tableName WHERE $fieldValidTo <= ?"
      connectionManager.doWithConnection(
        _.ps(sql) << DateTime.now().getMillis <<!
      )
    }

    private def populateReadingLink(rs: RichResultSet): ReadingLink = ReadingLink(rs, rs, rs, rs, rs)
  }
}

trait ReadingLinkRepository {
  def persistReadingLink(link: ReadingLink): ReadingLink
  def retrieveAllReadingLinks(): List[ReadingLink]
  def retrieveReadingLink(linkId: String): Option[ReadingLink]
  def retrieveAllValidReadingLinks(): List[ReadingLink]
  def retrieveReadingLinkForSection(sectionId: String): Option[ReadingLink]
  def deleteReadingLink(linkId: String): Unit
  def deleteInvalidLinks(): Unit
}