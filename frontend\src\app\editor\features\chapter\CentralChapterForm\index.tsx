import React, { useState, useCallback, useEffect } from "react";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import {
  Button,
  Title,
  Field,
  Input,
  Label,
  Form,
  Column,
  Columns,
  Help,
} from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import {
  useCreateCentralChapterMutation,
  useUpdateCentralChapterMutation,
  useGetCentralChaptersQuery,
  type UpdateCentralChapterRequest,
} from "@/store/services/handbook/centralHandbookApi";
import { toast } from "@/shared/components/Toast";
import { PageLeaveConfirmationModal } from "../../shared/PageLeaveConfirmationModal";

interface CentralChapterFormData {
  title: string;
}

const validationSchema: Yup.ObjectSchema<CentralChapterFormData> =
  Yup.object().shape({
    title: Yup.string().required("Kapittelet må ha en tittel"),
  });

export const CentralChapterForm: React.FC = () => {
  const t = usePrefixedTranslation("editor.containers.EditChapter");
  const tCommon = usePrefixedTranslation("editor.common");
  const navigate = useNavigate();

  const [showLeaveConfirmation, setShowLeaveConfirmation] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [searchParams] = useSearchParams();
  const { handbookId, chapterId } = useParams<{
    handbookId: string;
    chapterId?: string;
  }>();

  const parentId = searchParams.get("parent") || undefined;
  const isEditMode = Boolean(chapterId);

  const { data: chapters = [] } = useGetCentralChaptersQuery();
  const [createChapter, { isLoading: isCreating }] =
    useCreateCentralChapterMutation();
  const [updateChapter, { isLoading: isUpdating }] =
    useUpdateCentralChapterMutation();

  const currentChapter = isEditMode
    ? chapters.find((chapter) => chapter.id === chapterId)
    : undefined;

  const isSaving = isCreating || isUpdating;

  const {
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isValid, isDirty },
  } = useForm<CentralChapterFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      title: currentChapter?.title || "",
    },
    mode: "onChange",
  });

  useEffect(() => {
    setHasUnsavedChanges(isDirty);
  }, [isDirty]);

  useEffect(() => {
    if (currentChapter) {
      reset({
        title: currentChapter.title || "",
      });
    }
  }, [currentChapter, reset]);

  const titleValue = watch("title");

  const onSubmit = async (data: CentralChapterFormData) => {
    try {
      if (isEditMode && currentChapter && currentChapter.id) {
        const updatedChapter: UpdateCentralChapterRequest = {
          ...currentChapter,
          id: currentChapter.id,
          title: data.title,
        };
        await updateChapter(updatedChapter).unwrap();
        setHasUnsavedChanges(false);
        navigate(-1);
      } else {
        const newChapterData = {
          title: data.title,
          centralHandbookId: handbookId!,
          ...(parentId && { parentId }),
        };
        const result = await createChapter(newChapterData).unwrap();
        setHasUnsavedChanges(false);
        if (result?.id && handbookId) {
          navigate(`/central-editor/${handbookId}/chapter/${result.id}/`);
        } else {
          navigate(-1);
        }
      }
    } catch (error) {
      console.error("Failed to save chapter:", error);
      const errorMessage = isEditMode
        ? t("updateError") || "Feil ved oppdatering av kapittel"
        : t("createError") || "Feil ved opprettelse av kapittel";
      toast.error(errorMessage);
    }
  };

  const handleCancel = useCallback(() => {
    if (hasUnsavedChanges) {
      setShowLeaveConfirmation(true);
    } else {
      navigate(-1);
    }
  }, [hasUnsavedChanges, navigate]);

  const handleConfirmLeave = useCallback(() => {
    setShowLeaveConfirmation(false);
    setHasUnsavedChanges(false);
    navigate(-1);
  }, [navigate]);

  const handleCancelLeave = useCallback(() => {
    setShowLeaveConfirmation(false);
  }, []);

  return (
    <>
      <Form onSubmit={handleSubmit(onSubmit)}>
        <Title>{chapterId ? t("editTitle") : t("createTitle")}</Title>

        <Field>
          <Label htmlFor="title">{t("titleLabel")}</Label>
          <Input
            id="title"
            autoFocus
            readOnly={isSaving}
            placeholder={t("titleLabel")}
            value={titleValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setValue("title", e.target.value, { shouldValidate: true })
            }
            required
          />
          {errors.title && <Help color="danger">{errors.title.message}</Help>}
        </Field>

        <Columns responsive="mobile">
          <Column>
            <Button type="button" onClick={handleCancel}>
              {t("cancelButton")}
            </Button>
          </Column>
          <Column narrow>
            <Button
              loading={isSaving}
              disabled={!isValid}
              type="submit"
              color="primary"
            >
              {t("saveButton")}
            </Button>
          </Column>
        </Columns>
      </Form>

      <PageLeaveConfirmationModal
        isOpen={showLeaveConfirmation}
        onCancel={handleCancelLeave}
        onConfirm={handleConfirmLeave}
        title={tCommon("leaveEditorConfirmationTitle")}
        message={tCommon("leaveEditorConfirmationMessage")}
        t={tCommon}
      />
    </>
  );
};
