package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.handboker.model.local.{ChangeNotification, Handbook}
import no.kf.handboker.model.central.{CentralHandbook}
import no.kf.handboker.repository.SubscriptionRepositoryComponent
import no.kf.util.Logging
import no.kf.handboker.model.LDAPUser

trait SubscriptionServiceComponent extends TransactionManager with Logging {
  this: SubscriptionRepositoryComponent
    with LocalHandbookServiceComponent =>

  val subscriptionService: SubscriptionService

  class SubscriptionServiceImpl extends SubscriptionService {

    override def subscribe(email: String, handbook: Handbook) = inTransaction {
      if (!subscriptionRepository.retrieveSubscriptionsForUser(email).contains(handbook.id.get)) {
        subscriptionRepository.persistSubscription(email, handbook)
      }
    }

    override def unsubscribe(email: String, handbook: Handbook) = inTransaction(subscriptionRepository.deleteSubscription(email, handbook))

    override def deleteSubscription(email: String) = inTransaction(subscriptionRepository.deleteSubscription(email))

    override def unsubscribeAllForHandbook(handbookId: String) = inTransaction(subscriptionRepository.unsubscribeAllForHandbook(handbookId))

    override def persistChangeNotification(changeDescription: String, handbook: Handbook, centralHandbook: CentralHandbook) = inTransaction(subscriptionRepository.persistChangeNotification(changeDescription, handbook, centralHandbook))

    override def persistChangeNotifications(changeDescription: String, centralHandbook: CentralHandbook) = inTransaction {
      val handbooks = localHandbookService.retrieveHandbooksBasedOnCentralHandbookId(centralHandbook.id.get)
      log.info(changeDescription)
      handbooks.foreach(hb => persistChangeNotification(changeDescription, hb, centralHandbook))
    }
    override def retrieveChangesForEmails: Map[EmailAddress, List[ChangeNotification]] = inTransaction {
      val changeNotes = subscriptionRepository.retrieveAndDeleteAllChangeNotifications
      changeNotes.foreach(change => log.debug("Retrieving change notification from " + change.changedDate + " with description: " + change.changeDescription))

      val subscribedUsersPerHandbook = changeNotes.groupBy(_.handbook)

      val subscribersByHandbook = subscribedUsersPerHandbook.keys.map(hb => hb -> subscriptionRepository.retrieveSubscriptionsForHandbook(hb.id.get))

      val subscribersWithNotifications = subscribersByHandbook.flatMap { case (hb, subs) =>
        val notes = subscribedUsersPerHandbook(hb)
        subs.map(sub => sub -> notes)
      }.groupBy(_._1)

      subscribersWithNotifications.map { case (email, data) =>
        email -> data.flatMap(_._2).toList
      }
    }

    override def retrieveHandbookIdsForUserInExternalOrg(email: EmailAddress, externalOrgId: String): List[String] = inTransaction {
      val handbookIds = subscriptionRepository.retrieveSubscriptionsForUser(email)
      val handbookIdsInExternalOrg = localHandbookService.retrieveHandbooksForExternalOrganization(externalOrgId).map(_.id.get)
      handbookIds.filter(id => handbookIdsInExternalOrg.contains(id))
    }

    override def deleteSubscriptions(ldapUsers: List[LDAPUser]) = inTransaction {
      val subscribedUsers = subscriptionRepository.retrieveSubscriptions()
      subscribedUsers.foreach(user => {
        if (!ldapUsers.exists(ldapUser => ldapUser.email.trim.equalsIgnoreCase(user.trim))) {
          subscriptionService.deleteSubscription(user)
        }
      })
    }
  }
}

trait SubscriptionService {
  type EmailAddress = String

  def subscribe(email: String, handbook: Handbook)
  def unsubscribe(email: String, handbook: Handbook)
  def deleteSubscription(email: String)
  def unsubscribeAllForHandbook(handbookId: String)
  def persistChangeNotification(changeDescription: String, handbook: Handbook, centralHandbook: CentralHandbook): ChangeNotification
  def retrieveChangesForEmails: Map[EmailAddress, List[ChangeNotification]]
  def retrieveHandbookIdsForUserInExternalOrg(email: EmailAddress, externalOrgId: String): List[String]
  def persistChangeNotifications(changeDescription: String, centralHandbook: CentralHandbook)
  def deleteSubscriptions(ldapUsers: List[LDAPUser])
}
