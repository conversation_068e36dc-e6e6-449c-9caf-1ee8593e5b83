package no.kf.handboker.config

import no.kf.config._
import no.kf.config.Config._
import no.kf.util.FileSystemUtil
import no.kf.util.Logging

trait AppSettingComponent {
  val settings: Settings

  class AppSettings(propertyFilePath: String) extends Settings with DefaultConfig with Logging {
    override lazy val fileName: String = FileSystemUtil.getConfigFilePath(propertyFilePath, "handboker.properties")
    override lazy val useLdapConfigKey: Option[ConfigKey] = Some(UseLDAP)
  }

}

trait DefaultConfig {

  implicit def stringToSome(v: String) = Some(v)

  // NB: Settings that are not mentioned in defaultConfig will be ignored by no.kf.config.Settings (l. 63)
  lazy val defaultConfig: scala.collection.Map[no.kf.config.ConfigKey, Option[String]] = collection.mutable.LinkedHashMap[ConfigKey, Option[String]](
    PersistenceStore -> None,
    LogStore -> None,
    Database -> "DERBY",
    DatabaseHost -> "",
    DatabaseUsername -> "",
    DatabasePassword -> "",
    DatabaseName -> "derby",
    MockDataScript -> None,
    WebAppPort -> "5100",
    WebAppPath -> "/",
    PublicWebAppPath -> "/public",
    SiteUrl -> "http://localhost:5400/kvalitetsstyring/",
    UseLDAP -> "true",
    LDAPSearchFilter -> None,
    LDAPSearchBase -> None,
    LDAPHost -> None,
    LDAPPort -> None,
    LDAPUser -> None,
    LDAPPwd -> None,
    LDAPMailAttribute -> None,
    LDAPMaxResults -> "5000",
    MockBrukerAdm -> "false",
    BrukerAdmBaseUrl -> None,
    ApplicationId -> "kvalitetsstyring",
    AccessKey -> None,
    CasConfig -> None,
    RunMigrationScripts -> None,
    RunBatchJobs -> None,
    ImageFolder -> None,
    UploadFolder -> None,

    /* Start date for chapter and section versioning */
    VersioningStart -> None,

    /* Start date for correct versioning of deleted content */
    VersioningDeletedStart -> None,

    /* Central handbook settings */
    HandbooksSyncCron -> None,
    CacheTimeLimit -> "5",
    HandbookApiKey -> None,

    /* ** local editor pruning ** */
    PruneEditorsCron -> None,

    RemoveSubscriptionsCron -> None,

    ElasticSearchReIndexCron -> None,

    /* ** email setting ** */
    EmailNotificationsCron -> None,
    SMTPSendMailToRecipients -> "true",
    SMTPHost -> None,
    SMTPPort -> None,
    SMTPUser -> None,
    SMTPPwd -> None,
    SMTPSentEmailFrom -> None,
    SMTPOverrideFile -> None,

    /* Elastic Search settings */
    ElasticSearchClusterName -> None,
    ElasticSearchHost -> None,
    ElasticSearchPort -> None,
    MockElasticSearch -> None,
    ElasticSearchPageSize -> "10",

    /* Swagger */
    SwaggerUiHost -> None,

    /* Matomo */
    MatomoServer -> None,
    MatomoContainer -> None,
  )
}

case object MockDataScript extends ConfigKey {
  val description = "### Må kun brukes i test ###, aldri settes i prod!!!"
}

case object VersioningStart extends ConfigKey {
  val description = "Angir når fra hvilken data versjonering av kapitler og avsnitt startet"
  override val editable = true
  override val mandatoryProperty = true
}

case object VersioningDeletedStart extends ConfigKey {
  val description = "Angir fra hvilken dato korrekt versjonering av slettet innhold startet"
  override val editable = true
  override val mandatoryProperty = true
}

case object Database extends ConfigKey {
  val description = "Angir hvilken database du vil bruke. Støttede verdier er: MSSQL, MYSQL, DERBY og DERBYMEM (in memory derby)."
  override val editable = false
  override val exportToFile = true
}

case object DatabaseHost extends ConfigKey {
  val description = "Angir ip og port til databasen du vil bruke."
  override val editable = false
}

case object DatabaseUsername extends ConfigKey {
  val description = "Angir brukernavn for valgt database."
  override val editable = false
}

case object DatabasePassword extends ConfigKey {
  val description = "Angir passord for valgt database."
  override val editable = false
}

case object DatabaseName extends ConfigKey {
  val description = "Kun i bruk for test, aldri settes i prod!!!"
  override val visible = false
}

case object PersistenceStore extends ConfigKey {
  val description = "Angir sti til område hvor applikasjonen kan lagre data. Bør ligge på et sted der det er mulig å ta jevnlig backup."
  override val editable = false
  override val mandatoryProperty = true
}

case object LogStore extends ConfigKey {
  val description = "Angir sti til område hvor applikasjonen kan lagre logger. Bør ligge på et sted der det er mulig å ta jevnlig backup."
  override val editable = true
  override val mandatoryProperty = false
}

case object WebAppPort extends ConfigKey(intParse) {
  val description = "Angir portnummer systemet skal lytte på. F.eks. i http://minkommune.no:8080/kvalitet er 8080 portnummeret. Dersom man skal bruke en proxy server vil vi anbefale å ikke bruke port 80, men applikasjonen skal være synlig direkte kan man bruke port 80 her. Da vil url være http://minkommune.no/kvalitet"
  override val editable = false
  override val exportToFile = true
}

case object WebAppPath extends ConfigKey {
  val description = "Angir path som systemet skal nås på. For eksempel i http://minkommune.no/kvalitet er '/kvalitet' path. Denne verdien kan være bare være '/' som er standardverdi, som gjør at applikasjonen i dette tilfellet vil nås på http://minkommune.no. Om man skal bruke proxy server vil vi anbefale å gi en verdi her, som f.eks. '/kvalitet'"
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object PublicWebAppPath extends ConfigKey {
  val description = "Angir path som den IP-filtrerte delen av systemet skal nås på. For eksempel i http://minkommune.no/kvalitet er '/kvalitet' path. Denne verdien kan være bare være '/' som er standardverdi, som gjør at applikasjonen i dette tilfellet vil nås på http://minkommune.no. Om man skal bruke proxy server vil vi anbefale å gi en verdi her, som f.eks. '/kvalitet'"
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object SiteUrl extends ConfigKey {
  val description = "Angir ekstern URL adresse til systemet. Dersom man bruker en proxy server setter man her url som applikasjonen skal nås på. Et vanlig oppsett er f.eks. at selve applikasjonen settes opp på på http://minkommune.no:8080/portal, mens den tilgjengeliggjøres via proxy på http://minkommune.no/kvalitet."
  override val editable = true
  override val mandatoryProperty = true
}

case object UseLDAP extends ConfigKey(booleanParse) {
  val description = "Angir om LDAP skal benyttes som kilde for brukerdata for systemet."
}

case object LDAPSearchFilter extends ConfigKey {
  val description = "Angir filter som skal benyttes i søk etter bruker. $EPOST i strengen byttes ut med brukers epostadresse."
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object LDAPSearchBase extends ConfigKey {
  val description = "Angir startpunkt for søk i LDAP."
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object LDAPHost extends ConfigKey {
  val description = "Angir adresse til LDAP server."
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object LDAPPort extends ConfigKey(intParse) {
  val description = "Angir port til LDAP server."
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object LDAPUser extends ConfigKey {
  val description = "Angir brukernavn for tilgang til LDAP server."
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object LDAPPwd extends ConfigKey {
  val description = "Angir passord for tilgang til LDAP server."
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object LDAPMailAttribute extends ConfigKey {
  val description = "Angir LDAP attributt som identifiserer e-post adresse for en bruker."
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object LDAPMaxResults extends ConfigKey(intParse) {
  val description = "Angir maks antall treff i ldap-søk"
  override val isLDAPParameter = true
  override val editable = true
  override val exportToFile = true
}

case object CasConfig extends ConfigKey {
  val description = "Angir hvilken property fil som skal lastes for å hente url til CAS server. Eksempelvis vil verdi 'prod' lete etter filen './conf/cas-prop.properties'. Verdien 'test' vil lete etter filen './conf/cas-test.properties'. Se ellers installasjonsdokumentasjon."
  override val editable = false
  override val exportToFile = true
}

case object RunMigrationScripts extends ConfigKey {
  val description = "Angir om applikasjonen skal gjøre db script eller ikke"
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object RunBatchJobs extends ConfigKey {
  val description = "Angir om import/export jobber styrt av cron skal kjøres eller ikke"
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object ImageFolder extends ConfigKey {
  val description = "Angir filkatalog der systemet skal lagre og hente Bilders. Denne katalogen må både være lesbar og skrivbar."
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object UploadFolder extends ConfigKey {
  val description = "Angir filkatalog der systemet skal lagre og hente Bilders. Denne katalogen må både være lesbar og skrivbar."
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object LibreOfficeExecutableFolderWindows extends ConfigKey {
  val description = "Mappen LibreOffice executable'en (soffice.exe) ligger i"
  override val editable = true
  override val exportToFile = true
  override val mandatoryProperty = false
}

case object HandbooksSyncCron extends ConfigKey {
  val description = "Angir uttrykk for kjøring av cron-jobb som synkroniserer data for sentrale bøker."
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object ElasticSearchReIndexCron extends ConfigKey {
  val description = "Angir uttrykk for kjøring av cron-jobb som synkroniserer data for sentrale bøker."
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object CacheTimeLimit extends ConfigKey(intParse) {
  val description = "Angir hvor lang det tar før cache invaliderer verdiene sine og henter nye"
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object PruneEditorsCron extends ConfigKey {
  val description = "Angir uttrykk for kjøring av cron-jobb som fjerner lokale redaktører."
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object RemoveSubscriptionsCron extends ConfigKey {
  val description = "Angir uttrykk for kjøring av cron-jobb som fjerner lokale redaktører."
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

/* ***** settings for sending emails ***** */
case object EmailNotificationsCron extends ConfigKey {
  val description = "Angir uttrykk for kjøring av cron-jobb som sender epostvarslinger om endringer i sentrale bøker."
  override val editable = false
  override val exportToFile = true
  override val mandatoryProperty = true
}

case object SMTPSendMailToRecipients extends ConfigKey(booleanParse) {
  val description = "### Skal kun brukes i test ### Angir om mail skal sendes fra systemet."
}

case object SMTPHost extends ConfigKey {
  val description = "Angir adresse til SMTP server for utsending av epost."
  override val exportToFile = true
}

case object SMTPPort extends ConfigKey(intParse) {
  val description = "Angir portnummer til SMTP server."
  override val exportToFile = true
}

case object SMTPUser extends ConfigKey {
  val description = "Angir brukernavn til SMTP server. Denne verdien må settes dersom dette kreves av SMTP serveren."
  override val exportToFile = true
}

case object SMTPPwd extends ConfigKey {
  val description = "Angir passord til SMTP server, for å sende mail. Denne verdien må settes dersom dette kreves av SMTP serveren."
  override val exportToFile = true
}

case object SMTPSentEmailFrom extends ConfigKey {
  val description = "Angir 'from' feltet i eposter som systemet sender ut. Dette bør typisk påpeke at eposten ikke er i bruk, f.eks. <EMAIL>. OBS! Må være en gyldig epost."
  override val exportToFile = true
}

case object SMTPOverrideFile extends ConfigKey {
  val description = "Angir navn på fil med instillinger for mailoppsett. Overstyrer SMTPPort og SMTPHost dersom den er oppgitt, disse instillingene blir da ikke brukt. (SMTPSentEmailFrom, SMTPUser og SMTPPwd blir uansett brukt.) Instillingene tilsvarer javax.mail-properties."
  override val exportToFile = true
}


/* Settings for search through Elastic Search */
case object ElasticSearchClusterName extends ConfigKey {
  val description = "Angir navnet til Elastic Search clusteret som skal brukes."
}

case object ElasticSearchHost extends ConfigKey {
  val description = "Angir ip eller dns til Elastic Search databasen som skal brukes."
}

case object ElasticSearchPort extends ConfigKey(intParse) {
  val description = "Angir port til Elastic Seach databasen som skal brukes"
}

case object MockElasticSearch extends ConfigKey {
  val description = "Angir om Elastic Search databasen skal brukes eller bare mockes ut"
}

case object ElasticSearchPageSize extends ConfigKey(intParse) {
  val description = "Angir om hvor mange søketreff applikasjonen skal vise"
}

case object HandbookApiKey extends ConfigKey(intParse) {
  val description = "Angir en API-nøkkel for en gitt kommune. F.eks. HandbookApiKey.9900=Heia1234!"
  override val editable = false
  override val exportToFile = true
  override val repeatable = true
}


case object SwaggerUiHost extends ConfigKey {
  val description = "Angir URL for Swagger UI, For eksempel http://localhost"
  override val exportToFile = true
}

case object MatomoServer extends ConfigKey {
  val description = "Angir hvilken Matomo-server som skal brukes til tracking."
}

case object MatomoContainer extends ConfigKey {
  val description = "Angir hvilken Matomo-container som skal brukes til tracking."
}
