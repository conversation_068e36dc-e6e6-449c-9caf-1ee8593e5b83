ALTER TABLE central_handbooks.section DROP COLUMN status
ALTER TABLE central_handbooks.section DROP COLUMN issuer
ALTER TABLE central_handbooks.section DROP COLUMN document_type

ALTER TABLE central_handbooks.section ADD updated_by VARCHAR(200)
ALTER TABLE central_handbooks.handbook ADD version varchar(100)
ALTER TABLE central_handbooks.handbook ADD version_of varchar(37)
ALTER TABLE central_handbooks.chapter ADD version_of varchar(37)
ALTER TABLE central_handbooks.section ADD version_of varchar(37)

create table central_handbooks.publication(id varchar(37), publication_date bigint not null, created_date bigint not null, created_by varchar(100) not null, notify_by_email bigint not null, handbook_id varchar(100) not null, published bigint not null, primary key (id))
alter table central_handbooks.publication add constraint publication_handbook_central_id_fk FOREIGN KEY (handbook_id) references central_handbooks.handbook(central_id)

ALTER TABLE changenotification ADD chapter_id varchar(37)
ALTER TABLE changenotification ADD section_id varchar(37)
ALTER TABLE changenotification ADD change_html clob
ALTER TABLE changenotification ADD notify_in_gui smallint
ALTER TABLE changenotification ADD acknowledged_in_gui smallint
ALTER TABLE changenotification ADD concerns_title smallint
ALTER TABLE changenotification ADD deletion smallint

CREATE TABLE reading_link(id VARCHAR(37), link VARCHAR(200), central_section_id VARCHAR(100) NOT NULL, created_date BIGINT NOT NULL, valid_to BIGINT NOT NULL, PRIMARY KEY(id))
ALTER TABLE reading_link ADD CONSTRAINT fk_central_section_id FOREIGN KEY (central_section_id) REFERENCES central_handbooks.section(id)

ALTER TABLE handbook ADD pending_deletion smallint
ALTER TABLE handbookchapter ADD pending_deletion smallint
ALTER TABLE handbooksection ADD pending_deletion smallint
