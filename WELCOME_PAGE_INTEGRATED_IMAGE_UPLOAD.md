# Welcome Page Integrated Image Upload Implementation

## Overview

This implementation integrates image upload functionality directly into the existing draft creation and update endpoints, ensuring that images are only persisted when users actually save their drafts or publish them. This provides a better user experience by avoiding orphaned images from incomplete editing sessions.

## Key Design Decisions

### 1. Integrated Endpoints
Instead of separate image upload endpoints, image handling is integrated into:
- `POST /api/welcome-page/draft/:handbookId` - Create draft with optional image
- `PUT /api/welcome-page/draft/:handbookId` - Update draft with optional image and data

### 2. Flexible Content Types
Both endpoints support:
- **JSON requests** - Traditional application/json for data-only updates
- **Multipart requests** - multipart/form-data for image uploads with optional JSON data

### 3. Atomic Operations
Images and customization data are updated together in a single transaction, ensuring data consistency.

## Implementation Details

### REST Layer (`WelcomePageServlet.scala`)

**Enhanced Endpoints:**
```scala
// Create draft with optional image
post("/draft/:handbookId/?") {
  val uploadedImage = getUploadedImageIfPresent
  val draft = welcomePageService.createDraft(handbookId, createdBy)
  
  uploadedImage match {
    case Some(image) => welcomePageService.updateWelcomeImage(handbookId, image, createdBy)
    case None => draft
  }
}

// Update draft with optional image and data
put("/draft/:handbookId/?") {
  val uploadedImage = getUploadedImageIfPresent
  
  if (isMultipartRequest) {
    handleMultipartUpdate(handbookId, lastChangedBy, uploadedImage)
  } else {
    // Regular JSON update
    val customizationDto = parsedBody.extract[WelcomePageDto]
    welcomePageService.updateCustomization(handbookId, customizationDto, lastChangedBy)
  }
}
```

**Helper Methods:**
- `getUploadedImageIfPresent()` - Safely extracts images without throwing exceptions
- `isMultipartRequest()` - Detects multipart content type
- `handleMultipartUpdate()` - Processes combined image and JSON data

### Service Layer (`WelcomePageService.scala`)

**New Combined Method:**
```scala
def updateCustomizationWithImage(
  handbookId: String, 
  customization: WelcomePageDto, 
  image: Option[Image], 
  lastChangedBy: String
): Option[WelcomePageDto]
```

This method:
1. Handles image upload/replacement if provided
2. Updates customization data
3. Preserves existing image data if no new image is provided
4. Updates all collections (links, shortcuts)
5. Maintains audit trails

## Frontend Integration Patterns

### 1. Save Draft with Image
```javascript
const formData = new FormData();
formData.append('file', imageFile);
formData.append('data', JSON.stringify(welcomePageData));

fetch(`/api/welcome-page/draft/${handbookId}`, {
  method: 'PUT',
  body: formData
});
```

### 2. Save Draft without Image
```javascript
fetch(`/api/welcome-page/draft/${handbookId}`, {
  method: 'PUT',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(welcomePageData)
});
```

### 3. Create Draft with Initial Image
```javascript
const formData = new FormData();
formData.append('file', imageFile);

fetch(`/api/welcome-page/draft/${handbookId}`, {
  method: 'POST',
  body: formData
});
```

## Benefits

### 1. **Better UX**
- Images are only saved when user explicitly saves draft
- No orphaned images from abandoned editing sessions
- Single action saves both image and data

### 2. **Data Consistency**
- Atomic operations ensure image and data are always in sync
- Transaction management prevents partial updates
- Proper cleanup on draft discard

### 3. **Flexible API**
- Supports both image-only and data-only updates
- Backward compatible with existing JSON-based updates
- Progressive enhancement for image functionality

### 4. **Resource Management**
- Automatic cleanup of replaced images
- No temporary image storage needed
- Efficient file system usage

## Error Handling

### Image Validation Errors
- File size exceeding 5MB limit
- Unsupported file formats
- Invalid content types

### Data Validation Errors
- Malformed JSON in multipart requests
- Missing required fields
- Invalid customization data

### System Errors
- Draft not found (404)
- Image service failures
- Database transaction failures

## Migration from Separate Endpoints

The implementation maintains the separate image endpoints for backward compatibility:
- `POST /api/welcome-page/draft/:handbookId/image`
- `DELETE /api/welcome-page/draft/:handbookId/image`

These can be deprecated once frontend is updated to use the integrated approach.

## Testing Strategy

### Unit Tests
- Image upload validation
- Multipart request handling
- JSON parsing in multipart context
- Error scenarios

### Integration Tests
- End-to-end draft creation with image
- Combined image and data updates
- Transaction rollback scenarios
- File cleanup verification

## Performance Considerations

### 1. **Memory Usage**
- Images are processed in memory during upload
- 5MB limit prevents excessive memory consumption
- Streaming could be added for larger files if needed

### 2. **Transaction Size**
- Combined operations increase transaction scope
- Proper error handling prevents long-running transactions
- Image processing happens before database updates

### 3. **File System Operations**
- Old images are deleted immediately when replaced
- No temporary files are created
- Leverages existing ImageService optimizations

## Security Considerations

### 1. **File Validation**
- Strict content type checking
- File size limits enforced
- Supported format whitelist

### 2. **Authentication**
- All operations require authenticated user
- User context preserved in audit fields
- Draft isolation by handbook access

### 3. **Input Sanitization**
- JSON data validated through existing DTO extraction
- File content validated by ImageService
- Path traversal prevention in filename handling

This integrated approach provides a robust, user-friendly solution for image upload in the Welcome Page feature while maintaining the existing architecture patterns and ensuring data consistency.