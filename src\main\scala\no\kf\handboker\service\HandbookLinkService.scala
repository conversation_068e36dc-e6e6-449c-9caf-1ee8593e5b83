package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.handboker.model.local.{Link, LinkCollection}
import no.kf.handboker.repository.HandbookLinkRepositoryComponent

trait HandbookLinkServiceComponent extends TransactionManager {
  this: HandbookLinkRepositoryComponent =>

  val handbookLinkService: HandbookLinkService

  class HandbookLinkServiceImpl extends HandbookLinkService {
    override def retrieveLinkCollectionsForHandbook(handbookId: String): List[LinkCollection] = inTransaction {
      handbookLinkRepository.retrieveLinkCollectionsForHandbook(handbookId)
    }

    override def persistLinkCollection(linkCollection: LinkCollection): LinkCollection = inTransaction {
      handbookLinkRepository.persistLinkCollection(linkCollection)
    }

    override def deleteLinkCollection(linkCollectionId: String): Unit = inTransaction {
      handbookLinkRepository.deleteLinkCollection(linkCollectionId)
    }

    override def deleteLink(linkId: String): Unit = inTransaction {
      handbookLinkRepository.deleteLink(linkId)
    }

    override def persistLink(link: Link, linkCollectionId: String): Link = inTransaction {
      handbookLinkRepository.persistLink(link, linkCollectionId)
    }
  }
}

trait HandbookLinkService {
  def retrieveLinkCollectionsForHandbook(handbookId: String): List[LinkCollection]
  def persistLinkCollection(linkCollection: LinkCollection): LinkCollection
  def deleteLinkCollection(linkCollectionId: String): Unit

  def persistLink(link: Link, linkCollectionId: String): Link
  def deleteLink(linkId: String): Unit
}
