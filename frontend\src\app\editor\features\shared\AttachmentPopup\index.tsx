import React, { useEffect } from "react";
import { Icon } from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";

import { useGetAttachmentsQuery } from "@/store/services/handbook/localHandbookApi";
import { getFileTypeIcon } from "../AttachmentUtils";

import "./styles.css";

interface AttachmentPopupProps {
  sectionId: string;
  sectionType: "chapter" | "section";
  onClose: () => void;
}

export const AttachmentPopup: React.FC<AttachmentPopupProps> = ({
  sectionId,
  sectionType,
  onClose,
}) => {
  const t = usePrefixedTranslation("public.components.Attachments");

  const {
    data: files = [],
    isLoading,
    error,
  } = useGetAttachmentsQuery({
    type: sectionType,
    id: sectionId,
  });

  useEffect(() => {
    if (error) {
      console.error("Error loading attachments:", error);
      toast.error(t("errorLoading"));
    }
  }, [error, t]);

  const handleGetFile = async (fileId: string, fileName: string) => {
    try {
      // For now, we'll use a simple download approach
      // In a real implementation, you'd use the downloadFile query
      const link = document.createElement("a");
      link.href = `/files/${fileId.split("/").pop()}`;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      onClose();
    } catch (error) {
      console.error("Error downloading file:", error);
      toast.error("Feil ved nedlasting av fil");
    }
  };

  return (
    <div className="attachment-popup">
      <div className="arrow" />
      {isLoading ? (
        <div className="attachment-loading-message">
          <span className="loader" />
          <p>{t("loading")}</p>
        </div>
      ) : (
        <div className="attachment-public-content">
          {files
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map((file) => {
              const FileTypeIcon = getFileTypeIcon(file.title);
              return (
                <button
                  type="button"
                  key={file.id}
                  className="attachment-link"
                  onClick={() => handleGetFile(file.url, file.title)}
                >
                  <FileTypeIcon />
                  <span>{file.title}</span>
                </button>
              );
            })}
        </div>
      )}

      {!isLoading && files.length === 0 && (
        <div className="attachment-loading-message">
          <Icon icon="info" />
          <p>{t("noAttachments")}</p>
        </div>
      )}
    </div>
  );
};