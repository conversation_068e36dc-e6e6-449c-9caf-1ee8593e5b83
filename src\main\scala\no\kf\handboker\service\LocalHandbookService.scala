package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.exception.UserFriendlyException
import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.{VersioningDeletedStart, WebAppPath}
import no.kf.handboker.model.LDAPUser
import no.kf.handboker.model.central.CentralHandbook
import no.kf.handboker.model.local.{Chapter, Comment, Handbook, LocalEditor, Section}
import no.kf.handboker.repository.{CentralHandbookRepositoryComponent, CentralNotificationRepositoryComponent, HandbookRepositoryComponent, LocalHandbookVersionRepositoryComponent}
import no.kf.handboker.service.search.{SearchIndexServiceComponent, SearchServiceComponent}
import no.kf.handboker.util.section.ImageUtil.{replaceIframes, replaceImageData, updateImageDimensions}
import no.kf.util.Logging
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

import scala.annotation.tailrec
import scala.collection.mutable.ListBuffer

trait LocalHandbookServiceComponent extends TransactionManager {
  this: HandbookRepositoryComponent
    with CentralHandbookRepositoryComponent
    with CentralAccessServiceComponent
    with SubscriptionServiceComponent
    with CentralHandbookServiceComponent
    with LocalHandbookVersionRepositoryComponent
    with CentralNotificationRepositoryComponent
    with SearchIndexServiceComponent
    with SearchServiceComponent
    with ImageServiceComponent =>

  val localHandbookService: LocalHandbookService

  class LocalHandbookServiceImpl extends LocalHandbookService with Logging {

    private val dateFormat = "dd.MM.yyyy"

    lazy val versioningDeletedStart = DateTime.parse(ProductionRegistry.componentRegistry.settings.settingFor(VersioningDeletedStart), DateTimeFormat.forPattern(dateFormat))

    lazy val webAppPath = ProductionRegistry.componentRegistry.settings.settingFor(WebAppPath)

    override def persistHandbook(handbook: Handbook): Handbook = inTransaction {
      // When creating new handbooks, based on a central handbook, validate that we actually have access to the central handbook
      // Only do this when creating new handbooks, as we are still not sure how to handle removal of central handbook access
      // TODO: Write test for this
      throwIfNoCentralAccess(handbook.id, handbook.importedHandbookId, handbook.externalOrgId)

      val localChange = detectCentralHandbookTitleChange(handbook)

      if (handbook.id.nonEmpty){
        // If this is a not a new handbook, retrieve old handbook and check for title change. If changed, persist version of handbook title.
        val oldHandbook = retrieveHandbook(handbook.id.get).getOrElse(throw new Exception(s"Could not retrieve Handbook with id ${handbook.id.get}"))
        if (handbook.title != oldHandbook.title ) {
          localHandbookVersionRepository.insertLocalHandbookTitleVersion(oldHandbook, DateTime.now)
        }
      }

      val result = handbookRepository.persistHandbook(handbook.copy(localChange = localChange))

      searchIndexService.indexHandbook(result)

      // Automatically subscribe to changes for the user if this is a new handbook
      if (handbook.id.isEmpty) {
        result.createdBy.foreach(subscriptionService.subscribe(_, result))
      }
      result
    }

    override def persistChapter(chapter: Chapter, extOrgIdOption: Option[String] = None, centralUpdate: Boolean = false): Chapter = inTransaction {
      registerChapterChanges(chapter)
      val now = Some(DateTime.now)
      val updatedChapter = if (centralUpdate) {
        // For central updates, keep the existing localChapterUpdatedDate value
        handbookRepository.persistChapter(chapter.copy(
          updatedBy = Some("KF"),
          createdBy = if (chapter.id.isEmpty) Some("KF") else chapter.createdBy,
          updatedDate = now
        ))
      } else {
        // For local updates, set localChapterUpdatedDate to now
        handbookRepository.persistChapter(chapter.copy(
          localChapterUpdatedDate = now,
          updatedDate = now
        ))
      }
      // Update elasticsearch index
      extOrgIdOption.foreach(extOrgId => searchIndexService.indexChapter(updatedChapter, extOrgId))

      updatedChapter
    }

    override def persistSection(section: Section, currentUserid: Option[String], extOrgIdOption: Option[String] = None, centralUpdate : Boolean = false, centralTextUpdate: Boolean = false): Section = inTransaction {
      val updatedSection = handbookRepository.persistSection(registerSectionChanges(section, currentUserid, centralUpdate, centralTextUpdate))

      // Update elasticsearch index
      extOrgIdOption.foreach(extOrgId => searchIndexService.indexSection(updatedSection, extOrgId))

      updatedSection
    }

    override def retrieveAllLocalEditors() = inTransaction(handbookRepository.retrieveAllLocalEditors())
    override def retrieveLocalEditors(handbookId: String) = inTransaction(handbookRepository.retrieveLocalEditors(handbookId))
    override def insertLocalEditor(localEditor: LocalEditor): LocalEditor = inTransaction(handbookRepository.insertLocalEditor(localEditor))
    override def deleteLocalEditor(id: String)= inTransaction(handbookRepository.deleteLocalEditor(id))

    override def pruneLocalEditors(ldapEditors: List[LDAPUser]): List[LocalEditor] = inTransaction {
      val localEditors = handbookRepository.retrieveAllLocalEditors()
      val deletedEditors: ListBuffer[LocalEditor] = ListBuffer[LocalEditor]()
      localEditors.foreach(editor => {
        if (!ldapEditors.exists(ldapEditor => ldapEditor.email.trim.equalsIgnoreCase(editor.rightsHolder.trim))) {
          editor.id.foreach(handbookRepository.deleteLocalEditor)
          //subscriptionService.deleteSubscription(ldapEditor.email)
          deletedEditors += editor
        }
      })
      return deletedEditors.toList
    }

    override def persistSortOrder(sortOrder: List[String]) = inTransaction(handbookRepository.persistSortOrder(sortOrder))

    override def persistComment(comment: Comment): Comment = inTransaction(handbookRepository.persistComment(comment))

    override def retrieveComments(handbookId: String): List[Comment] = inTransaction(handbookRepository.retrieveComments(handbookId))

    override def deleteComment(id: String) = inTransaction(handbookRepository.deleteComment(id))

    override def retrieveHandbook(id: String): Option[Handbook] = inTransaction(handbookRepository.retrieveHandbook(id))

    override def retrieveChapter(id: String): Option[Chapter] = inTransaction(handbookRepository.retrieveChapter(id))

    override def retrieveSection(id: String): Option[Section] = inTransaction(handbookRepository.retrieveSection(id))

    override def retrieveSectionsWithImportedHandbookId(importedHandbookId: String, includeDeleted: Boolean): List[Section] = inTransaction(handbookRepository.retrieveSectionsWithImportedHandbookId(importedHandbookId, includeDeleted))

    //// Return sections, totalImagesUpdated and totalSectionsUpdated
    override def updateSectionsForHandbookWithUpdateImageDimensions(handbookId: String, currentUserId: String): (Int, Int) = inTransaction {
      var totalImagesUpdated = 0
      var totalSectionsUpdated = 0
      handbookRepository.retrieveSectionsForHandbook(handbookId)
        .map(section => updateImageDimensions(section))
        .map{ case (section, images, sectionUpdated) =>
          totalImagesUpdated += images
          totalSectionsUpdated += sectionUpdated
          localHandbookService.persistSection(section.copy(text = section.text), Some(currentUserId))
        }
      (totalImagesUpdated, totalSectionsUpdated)
    }

    override def retrieveSectionsForHandbook(handbookId: String, runImageReplacement: Boolean = true, onlyUpdateImageDimensions: Boolean = false): List[Section] = inTransaction {
      if(runImageReplacement){
        if(onlyUpdateImageDimensions)
          handbookRepository.retrieveSectionsForHandbook(handbookId).map(section => {
            val (sectionUpdated, imagesUpdated, sectionsUpdated) = updateImageDimensions(section)
            sectionUpdated //Return only the section to .map
          })
        else
          handbookRepository.retrieveSectionsForHandbook(handbookId).map(section => replaceImageData(section))
      }else{
        handbookRepository.retrieveSectionsForHandbook(handbookId)
      }
    }

    override def retrieveChaptersForHandbook(handbookId: String): List[Chapter] = inTransaction(handbookRepository.retrieveChaptersForHandbook(handbookId))

    override def deleteHandbook(id: String, externalOrgId: Option[String] = None) = inTransaction {
      handbookRepository.deleteHandbook(id)
      externalOrgId.foreach(extOrgId => searchService.doReindex(extOrgId))
    }

    override def deleteChapter(id: String, externalOrgId: Option[String] = None) = inTransaction {
      val chapter = handbookRepository.retrieveChapter(id).get
      versionChapterAndChildrenForDeletion(chapter)
      handbookRepository.deleteChapter(id)
      externalOrgId.foreach(extOrgId => searchService.doReindex(extOrgId))
    }

    override def deleteSection(id: String, externalOrgId: Option[String] = None) = inTransaction {
      val section = handbookRepository.retrieveSection(id).get
      localHandbookVersionRepository.insertLocalHandbookSectionVersion(section, DateTime.now)
      handbookRepository.persistSection(section.copy(pendingTitleChange = false, pendingDeletion = false))
      handbookRepository.deleteSection(id)
      externalOrgId.foreach(extOrgId => searchService.doReindex(extOrgId))
    }

    private def versionChapterAndChildrenForDeletion(chapter: Chapter): Unit = {
      chapter.id match {
        case Some(chapterId) =>
          try {
            inTransaction {
              val (subSections, subChapters) = handbookRepository.retrieveChildren(chapterId, withHtml = true)
              // Version all subsections
              subSections.foreach(section => {
                localHandbookVersionRepository.insertLocalHandbookSectionVersion(section, DateTime.now)
                handbookRepository.persistSection(section.copy(pendingTitleChange = false, pendingDeletion = false))
              })
              // Version all subchapters
              subChapters.foreach(versionChapterAndChildrenForDeletion)
              // Version chapter
              localHandbookVersionRepository.insertLocalHandbookChapterVersion(chapter, DateTime.now)
              handbookRepository.persistChapter(chapter.copy(pendingChange = false, pendingDeletion = false))
            }
          } catch {
            case e: Exception =>
              log.error(s"Failed to version chapter and children for deletion for chapterId=$chapterId. Error: ${e.getMessage}", e)
          }
        case None =>
          log.warn(s"Attempted to version chapter for deletion, but it has no ID. Chapter title: '${chapter.title}'")
      }
    }

    override def retrieveExternalOrganizationIdForHandbook(handbookId: String): String = inTransaction(handbookRepository.retrieveExternalOrganizationIdForHandbook(handbookId))

    override def retrieveHandbooksForExternalOrganization(externalOrgId: String): List[Handbook] = inTransaction(handbookRepository.retrieveHandbooksForExternalOrganization(externalOrgId))

    override def retrieveHandbooksForExternalOrganizationRestricted(externalOrgId: String, userId: String): List[Handbook] = inTransaction(handbookRepository.retrieveHandbooksForExternalOrganizationRestricted(externalOrgId, userId))

    override def retrieveHandbooksWithChaptersAndSectionsWithoutTextForExternalOrganization(externalOrgId: String, user: LDAPUser): (List[Handbook], List[Chapter], List[Section]) = inTransaction {
      val handbooks = if (user.isAdmin) retrieveHandbooksForExternalOrganization(externalOrgId) else retrieveHandbooksForExternalOrganizationRestricted(externalOrgId, user.email)
      val handbookIds = handbooks.map(_.id.get)
      val chapters = handbookIds.flatMap(retrieveChaptersForHandbook)
      val sections = handbookIds.flatMap(retrieveSectionsWithoutTextForHandbook)
      (handbooks, chapters, sections)
    }

    private def retrieveSectionsWithoutTextForHandbook(handbookId: String): List[Section] = inTransaction(handbookRepository.retrieveSectionsWithoutTextForHandbook(handbookId))

    override def retrieveAllExternalOrgIds(): List[String] = inTransaction {
      handbookRepository.retrieveAllExternalOrgIds()
    }

    override def retrieveChaptersBasedOnCentralChapter(centralChapterId: String, includeDeleted: Boolean = false): List[Chapter] = inTransaction(handbookRepository.retrieveChaptersBasedOnCentralChapter(centralChapterId, includeDeleted))

    override def retrieveSectionsBasedOnCentralSectionIdParentIdAndHandbookId(centralSectionId: String, parentId: String, centralHandbookId: String, includeDeleted: Boolean = false): List[Section] = inTransaction {
      val centralSectionOpt = centralHandbookService.retrieveCentralSectionByCentralIdAndParentId(centralSectionId, parentId, centralHandbookId, withHtml = false)
      centralSectionOpt match {
        case Some(centralSection) => handbookRepository.retrieveSectionsBasedOnCentralSection(centralSection.id.get, includeDeleted)
        case None => Nil
      }
    }

    private def detectCentralHandbookTitleChange(local: Handbook): Boolean = {
      var changed = false
      if (local.importedHandbookId.isDefined) {
        val centralHandbook = centralHandbookService.retrieveCentralHandbook(local.importedHandbookId.get, withHtml = false).get
        if (local.id.isEmpty && local.title != centralHandbook.title) {
          changed = true
        } else if (local.id.isDefined) {
          val oldHandbook = handbookRepository.retrieveHandbook(local.id.get)
          if (oldHandbook.isDefined && oldHandbook.get.pendingChange && oldHandbook.get.title == local.title) {
            changed = true
          } else if (oldHandbook.isDefined && oldHandbook.get.title != local.title) {
            changed = true
          }
        }
      }
      changed
    }

    override def throwIfNoCentralAccess(id: Option[String], importedHandbookId: Option[String], externalOrgId: String) {
      importedHandbookId.foreach(centralHandbookId => {
        if (id.isEmpty && !centralAccessService.hasAccess(centralHandbookId, externalOrgId)) {
          throw new UserFriendlyException("Din organisasjon har ikke tilgang til den sentrale håndboken.")
        }
      })
    }

    private def registerChapterChanges(chapter: Chapter) = {
      val handbook = handbookRepository.retrieveHandbook(chapter.handbookId).getOrElse(throw new NoSuchElementException(s"No Handbook with id ${chapter.handbookId} found for chapter with title ${chapter.title}"))
      if (chapter.id.isEmpty) {
        //subscriptionService.persistChangeNotification(s"Nytt kapittel: ${chapter.title}", handbook)
      } else {
        val oldChapter = retrieveChapter(chapter.id.get).getOrElse(throw new Exception(s"Could not retrieve Chapter with id ${chapter.id.get}"))
        val parentChapter = chapter.parentId.flatMap(handbookRepository.retrieveChapter)

        if (chapter.title != oldChapter.title) {
         // subscriptionService.persistChangeNotification(s"Kapittel ${oldChapter.title} endret tittel til ${chapter.title}", handbook)
        }

        if (chapter.sortOrder != oldChapter.sortOrder) {
          if (parentChapter.isEmpty) {
           // subscriptionService.persistChangeNotification(s"Endret rekkefølgen på kapitler under håndboken: ${handbook.title}", handbook)
          } else {
           // subscriptionService.persistChangeNotification(s"Endret rekkefølge på underkapitler og avsnitt i kapittel: ${parentChapter.get.title}", handbook)
          }
        }

        if (chapter.parentId != oldChapter.parentId) {
          //subscriptionService.persistChangeNotification(s"Endret nivå for kapittel: ${chapter.title}", handbook)
        }

        if (chapter.handbookId != oldChapter.handbookId) {
          //subscriptionService.persistChangeNotification(s"Endret håndbok for kapittel: ${chapter.title}", handbook)
        }

        if (chapter.title != oldChapter.title || chapter.sortOrder != oldChapter.sortOrder || chapter.parentId != oldChapter.parentId || chapter.handbookId != oldChapter.handbookId) {
          localHandbookVersionRepository.insertLocalHandbookChapterVersion(oldChapter, DateTime.now)
        }
      }
    }

    private def registerSectionChanges(section: Section, currentUserId: Option[String], centralUpdate: Boolean = false, centralTextUpdate: Boolean = false): Section = {
      val handbook = handbookRepository.retrieveHandbook(section.handbookId).getOrElse(throw new NoSuchElementException(s"No Handbook with id ${section.handbookId} found for section with title ${section.title}"))

      if (section.id.isEmpty) {
        //subscriptionService.persistChangeNotification(s"Nytt avsnitt: ${section.title}", handbook)
        section.copy(localTitleChange = false, localTextChange = false, updatedBy =currentUserId, textUpdatedBy = currentUserId)
      } else {
        var localTitleChange = false
        var localTextChange = false
        val oldSection = retrieveSection(section.id.get).getOrElse(throw new Exception(s"Could not retrieve section with id ${section.id.get}"))
        val parentChapter = handbookRepository.retrieveChapter(section.parentId).getOrElse(throw new Exception(s"Could not retrieve Parent Chapter with id ${section.parentId} for Chapter with id ${section.id.get}"))

        if (oldSection.localTitleChange && section.title == oldSection.title) {
          localTitleChange = true
        } else if (section.title != oldSection.title && section.localTitleChange) {
          localTitleChange = true
          //subscriptionService.persistChangeNotification(s"Avsnitt ${oldSection.title} endret tittel til ${section.title}", handbook)
        } else if (section.title != oldSection.title && !oldSection.pendingTitleChange) {
          localTitleChange = true
        }

        val textHasBeenChanged = section.text.getOrElse("").replaceAll("""src=".*?"""", "") != oldSection.text.getOrElse("").replaceAll("""src=".*?"""", "")
        if (oldSection.localTextChange && !textHasBeenChanged) {
          localTextChange = true
        } else if (textHasBeenChanged && section.localTextChange) {
          localTextChange = true
          //subscriptionService.persistChangeNotification(s"Endret tekst i avsnitt ${section.title}", handbook)
        } else if (textHasBeenChanged && !oldSection.pendingTextChange) {
          localTextChange = true
        }

        if (section.sortOrder != oldSection.sortOrder) {
          //subscriptionService.persistChangeNotification(s"Endret rekkefølge på underkapitler og avsnitt i kapittel: ${parentChapter.title}", handbook)
        }

        if (section.parentId != oldSection.parentId) {
          //subscriptionService.persistChangeNotification(s"Endret nivå for avsnitt i kapittel: ${parentChapter.title}", handbook)
        }

        if (section.title != oldSection.title || textHasBeenChanged || section.sortOrder != oldSection.sortOrder || section.parentId != oldSection.parentId || section.handbookId != oldSection.handbookId) {
          localHandbookVersionRepository.insertLocalHandbookSectionVersion(oldSection, DateTime.now)
        }
        val (updatedBy, updatedDate) = if (section.title != oldSection.title) {
          if(centralUpdate) {
            (Some("KF"), Some(DateTime.now))
          } else {
            (currentUserId, Some(DateTime.now))
          }
        } else (section.updatedBy, section.updatedDate)
        val (textUpdatedBy, textUpdatedDate) = if (textHasBeenChanged) {
          if(centralTextUpdate) {
            (Some("KF"), Some(DateTime.now))
          } else {
            (currentUserId, Some(DateTime.now))
          }
        } else (section.textUpdatedBy, section.textUpdatedDate)
        section.copy(localTitleChange = localTitleChange, localTextChange = localTextChange, updatedBy = updatedBy, updatedDate = updatedDate, textUpdatedBy = textUpdatedBy, textUpdatedDate = textUpdatedDate)
      }
    }


    override def persistChapterAndSectionsFromCentralHandbook(handbook: Handbook, latestCentralVersion: CentralHandbook, externalOrgId: Option[String], currentUserId: Option[String]) = inTransaction {
        val bookContent = centralHandbookService.retrieveChaptersAndSectionsWithText(latestCentralVersion.id.get)
        val centralChapters = bookContent._1.toSet
        val centralSections = bookContent._2

        val rootChapters = centralChapters.filter(_.parentId.isEmpty)
        val (persistedChildChapters, chapterIdMapping) = persistChildChapters(rootChapters, centralChapters -- rootChapters, handbook.id.get, externalOrgId, currentUserId)
        persistChildSections(centralSections, persistedChildChapters, externalOrgId, chapterIdMapping, centralChapters, currentUserId)
    }

    override def ifBasedOnCentralContentPersistAllChildrenToChapter(chapter: Chapter, externalOrgId: Option[String], currentUserId: Option[String]) = inTransaction {
      if (chapter.importedHandbookChapterId.isDefined && chapter.importedHandbookId.isDefined) {
        val centralHandbookVersion = centralHandbookService.retrieveLatestHandbookVersion(chapter.importedHandbookId.get)
        if (centralHandbookVersion.isEmpty) throw new RuntimeException(s"Sentral håndbok ${chapter.importedHandbookId.get} er ikke publisert, kan ikke opprette kapittel")

        val bookContent = centralHandbookService.retrieveChaptersAndSectionsWithText(centralHandbookVersion.get.id.get)
        val versionCentralChapters = bookContent._1.toSet
        val versionCentralSections = bookContent._2

        val versionCentralChapter = versionCentralChapters.find(c => c.importedHandbookChapterId == chapter.importedHandbookChapterId)
        val versionChildChapters = versionCentralChapters.filter(_.parentId == versionCentralChapter.get.id)
        val childrenWithLocalParent = versionChildChapters.map(child => child.copy(parentId = chapter.id))

        val (persistedChildChapters, chapterIdMapping) = persistChildChapters(childrenWithLocalParent, versionCentralChapters -- versionChildChapters, chapter.handbookId, externalOrgId, currentUserId)
        val persistedChildSections = persistChildSections(versionCentralSections, chapter :: persistedChildChapters, externalOrgId, chapterIdMapping + (chapter.id.get -> chapter.importedHandbookChapterId.get), versionCentralChapters, currentUserId)

        handbookRepository.retrieveHandbook(chapter.handbookId).foreach { handbook =>
          //persistedChildChapters.foreach(chap => subscriptionService.persistChangeNotification(s"Nytt kapittel: ${chap.title}", handbook))
          //persistedChildSections.foreach(sec => subscriptionService.persistChangeNotification(s"Nytt avsnitt: ${sec.title}", handbook))
        }
      }
    }

    private def persistChildChapters(parents: Set[Chapter], centralChapters: Set[Chapter], handbookId: String, externalOrgId: Option[String], currentUserId: Option[String]): (List[Chapter], Map[String, String]) = {
      @tailrec
      def persistChapters(parents: Set[Chapter], centralChapterMap: Map[Option[String], Set[Chapter]],
                          persistedChapterIdMap: Map[String, String] = Map[String, String](),
                          persistedChapters: List[Chapter] = Nil): (List[Chapter], Map[String, String]) = {

        var chapterIdMap = persistedChapterIdMap
        val persistedParents = parents.map(chapter => {
          val persistedChapter = handbookRepository.persistChapter(chapter.copy(id = None, handbookId = handbookId, createdBy = Some("KF"), updatedBy = Some("KF")), computeSortOrder = false)
          chapterIdMap += (persistedChapter.id.get -> chapter.id.get)
          externalOrgId.foreach(searchIndexService.indexChapter(persistedChapter, _))
          persistedChapter
        })

        val childrenWithUpdatedParent = persistedParents.flatMap { parent =>
          // find parent given old parent id, then set new parent id given old
          val oldparentId = chapterIdMap(parent.id.get)
          centralChapterMap.getOrElse(Some(oldparentId), Nil).map { child =>
            val newParentId = chapterIdMap.find(_._2 == oldparentId).map(_._1)
            child.copy(parentId = newParentId)
          }
        }

        if (childrenWithUpdatedParent.isEmpty) return Tuple2(persistedChapters ++ persistedParents, chapterIdMap)

        val rest = centralChapterMap -- persistedParents.map(_.importedHandbookChapterId)
        persistChapters(childrenWithUpdatedParent, rest, chapterIdMap, persistedChapters ++ persistedParents)
      }
      if(parents.nonEmpty){
        persistChapters(parents, centralChapters.groupBy(_.parentId))
      } else {
        (List.empty, Map.empty)
      }
    }

    private def persistChildSections(centralSections: List[Section], parents: List[Chapter], externalOrgId: Option[String], chapterIdMapping: Map[String, String], centralChapters: Set[Chapter], currentUserId: Option[String]): List[Section] = {
      if(centralSections.nonEmpty) {
        val sectionsGroupedByParent = centralSections.groupBy(_.parentId)
        parents.flatMap { parent => {
          val versionCentralParentId = centralChapters.find(c => c.importedHandbookChapterId == parent.importedHandbookChapterId).get.id.get
          sectionsGroupedByParent.getOrElse(versionCentralParentId, Nil).map { child => {
            val persistedSection = handbookRepository.persistSection(child.copy(id = None, parentId = parent.id.get, handbookId = parent.handbookId, createdBy = Some("KF"), updatedBy = Some("KF"), textUpdatedBy = Some("KF")), computeSortOrder = false)
            externalOrgId.foreach(searchIndexService.indexSection(persistedSection.copy(handbookId = parent.handbookId), _))
            persistedSection
          }}
        }}
      } else {
        List.empty
      }
    }

    override def retrieveDeletedAndNotDeletedChaptersBasedOnCentralHandbook(centralHandbookId: String): List[Chapter] = inTransaction(handbookRepository.retrieveDeletedAndNotDeletedChaptersBasedOnCentralHandbook(centralHandbookId))

    override def getNextSortOrderByParentAndHandbook(parentId: Option[String], handbookId: String): Int = {
      parentId match {
        case None => retrieveChaptersForHandbook(handbookId).filter(_.parentId.isEmpty).flatMap(_.sortOrder).sorted
        case Some(id) =>
          val children = inTransaction(handbookRepository.retrieveChildren(id))
          (children._1.collect { case section if section.handbookId == handbookId => section.sortOrder }
            ::: children._2.collect { case chapter if chapter.handbookId == handbookId => chapter.sortOrder }).flatten.sorted
      }
    }.lastOption.map(_ + 1).getOrElse(0)

    override def persistChapterWithChildren(chapter: Chapter, currentUserId: Option[String], externalOrgId: Option[String]): Chapter = inTransaction {
      val originalChapterId = if (chapter.importedHandbookChapterId.isDefined) {
        val impChapter = centralHandbookRepository.retrieveCentralChapter(chapter.importedHandbookChapterId.get).get
        if (impChapter.versionOf.isDefined) impChapter.versionOf else impChapter.id
      } else {
        None
      }

      val persistedChapter = persistChapter(chapter.copy(importedHandbookChapterId = originalChapterId, createdBy = currentUserId, updatedBy = currentUserId), externalOrgId, centralUpdate = originalChapterId.isDefined)
      ifBasedOnCentralContentPersistAllChildrenToChapter(persistedChapter, externalOrgId, currentUserId)
      persistedChapter
    }

    override def retrieveHandbookTitleVersion(handbookId: String, time: Option[DateTime]): List[Handbook] = inTransaction {
      localHandbookVersionRepository.retrieveHandbookTitleVersion(handbookId, time.get)
    }

    override def retrieveAllTitleVersions(handbookId: String): List[Handbook] = inTransaction {
      localHandbookVersionRepository.retrieveAllTitleVersions(handbookId)
    }

    override def retrieveHandbooksBasedOnCentralHandbookId(centralHandbookId: String): List[Handbook] = {
      handbookRepository.retrieveHandbooksBasedOnCentralHandbookId(centralHandbookId)
    }

    override def retrieveChapterAndSectionVersions(handbookId: String, time: Option[DateTime]): (List[Chapter], List[Section]) = inTransaction {

      def idCompare(s1: Product, s2: Product): Int = {
        val (id1: String, id2: String) = {
          val id1 = s1 match {
            case chapter: Chapter => if (chapter.versionOf.isDefined) chapter.versionOf.get else chapter.id.get
            case section: Section => if (section.versionOf.isDefined) section.versionOf.get else section.id.get
          }

          val id2 = s2 match {
            case chapter: Chapter => if (chapter.versionOf.isDefined) chapter.versionOf.get else chapter.id.get
            case section: Section => if (section.versionOf.isDefined) section.versionOf.get else section.id.get
          }
          (id1, id2)
        }
        id1.compareTo(id2)
      }

      val sectionVersions = localHandbookVersionRepository.retrieveSectionVersionsInHandbook(handbookId, time.get, withText = true)
      val chapterVersions = localHandbookVersionRepository.retrieveChapterVersionsInHandbook(handbookId, time.get)

      val rawUnversionedChapters = handbookRepository.retrieveDeletedAndNotDeletedChaptersForHandbook(handbookId)
      val rawUnversionedSections = handbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook(handbookId)

      val unversionedChapters = rawUnversionedChapters
        .filter(c =>
          if (time.isDefined) {
            if (chapterVersions.exists(cv => cv.versionOf == c.id))
              c.updatedDate.get.compareTo(time.get) <= 0
            else
              c.createdDate.get.compareTo(time.get) <= 0
          } else
            true
        )

      val unversionedSections = rawUnversionedSections
        .filter(s =>
          if (time.isDefined) {
            if (sectionVersions.exists(sv => sv.versionOf == s.id))
              s.updatedDate.get.compareTo(time.get) <= 0 && s.textUpdatedDate.get.compareTo(time.get) <= 0
            else
              s.createdDate.get.compareTo(time.get) <= 0
          } else
            true
        )

      // *** TODO: Remove/adjust the following code when solving Jira issue Knowit-108
      // Removing any versioned item pointing at a missing chapter or section, typically due to moving items between handbooks
      val filteredSectionVersions = sectionVersions.filter(s => rawUnversionedSections.exists(uvs => s.versionOf == uvs.id))
      val filteredChapterVersions = chapterVersions.filter(c => rawUnversionedChapters.exists(uvc => c.versionOf == uvc.id))
      // *** TODO: See above ^

      val sections: List[Section] = (filteredSectionVersions ++ unversionedSections)
        // Remove sections which are deleted, with deletion date after current time; keep everything else for folding
        .filterNot(section => section.isDeleted.getOrElse(false) && section.deletedDate.nonEmpty && (section.deletedDate.get.compareTo(time.get) > 0))
        .sortWith((s1, s2) => {
          if (idCompare(s1, s2) == 0)
            s1.updatedDate.get.compareTo(s2.updatedDate.get) < 0 || s1.textUpdatedDate.get.compareTo(s2.textUpdatedDate.get) < 0
          else
            idCompare(s1, s2) < 0
        })
        .reverse // latest first
        .foldLeft(List[Section]())((a, b) => { // remove duplicate sections
          if (a.isEmpty)
            a :+ b
          else if (idCompare(a.last, b) == 0)
            a
          else
            a :+ b
        })
        .filterNot(s => s.isDeleted.getOrElse(false))
        .map(s => replaceImageData(s)).map(s => replaceIframes(s))

      val chaptersSorted: List[Chapter] = (filteredChapterVersions ++ unversionedChapters)
        // Remove items which are deleted, with deletion date after current time; keep everything else for folding
        .filterNot(chapter => chapter.isDeleted.getOrElse(false) && chapter.deletedDate.isDefined && (chapter.deletedDate.get.compareTo(time.get) > 0))
        .sortWith((c1, c2) => {
          if (idCompare(c1, c2) == 0) c1.updatedDate.get.compareTo(c2.updatedDate.get) < 0 else idCompare(c1, c2) < 0
        })
        .reverse // latest first

      val chapters = chaptersSorted
        .foldLeft(List[Chapter]())((a, b) => { // remove duplicate chapters
          if (a.isEmpty)
            a :+ b
          else if (idCompare(a.last, b) == 0)
            a
          else
            a :+ b
        })
        .filterNot(s => s.isDeleted.getOrElse(false))

      (chapters, sections)
    }
  }
}

trait LocalHandbookService {
  def persistHandbook(handbook: Handbook): Handbook

  def persistChapter(chapter: Chapter, externalOrgId: Option[String] = None, centralUpdate: Boolean = false): Chapter

  def persistChapterWithChildren(chapter: Chapter, currentUserId: Option[String], externalOrgId: Option[String] = None): Chapter

  def persistSection(section: Section, currentUserId: Option[String], externalOrgId: Option[String] = None, centralUpdate: Boolean = false, centralTextUpdate: Boolean = false): Section

  def persistSortOrder(sortOrder: List[String])

  def persistComment(comment: Comment): Comment

  def retrieveComments(handbookId: String): List[Comment]

  def deleteComment(id: String)

  def retrieveLocalEditors(handbookId: String): List[LocalEditor]
  def retrieveAllLocalEditors(): List[LocalEditor]
  def insertLocalEditor(localEditor: LocalEditor): LocalEditor
  def deleteLocalEditor(id: String)
  def pruneLocalEditors(ldapEditors: List[LDAPUser]): List[LocalEditor]

  def throwIfNoCentralAccess(id: Option[String], importedHandbookId: Option[String], externalOrgId: String): Unit

  def retrieveHandbook(id: String): Option[Handbook]

  def retrieveChapter(id: String): Option[Chapter]

  def retrieveSection(id: String): Option[Section]

  def retrieveSectionsWithImportedHandbookId(importedHandbookId: String, includeDeleted: Boolean): List[Section]

  def retrieveAllExternalOrgIds(): List[String]

  def retrieveChaptersForHandbook(handbookId: String): List[Chapter]

  def retrieveSectionsForHandbook(handbookId: String, runImageReplacement: Boolean = true, onlyUpdateImageDimensions: Boolean = false): List[Section]

  def updateSectionsForHandbookWithUpdateImageDimensions(handbookId: String, currentUserId: String): (Int, Int)

  def deleteHandbook(id: String, externalOrgId: Option[String] = None)

  def deleteChapter(id: String, externalOrgId: Option[String] = None)

  def deleteSection(id: String, externalOrgId: Option[String] = None)

  def retrieveExternalOrganizationIdForHandbook(handbookId: String): String

  def retrieveHandbooksForExternalOrganization(externalOrgId: String): List[Handbook]

  def retrieveHandbooksForExternalOrganizationRestricted(externalOrgId: String, userId: String): List[Handbook]

  def retrieveHandbooksWithChaptersAndSectionsWithoutTextForExternalOrganization(externalOrgId: String, user: LDAPUser): (List[Handbook], List[Chapter], List[Section])

  def retrieveChaptersBasedOnCentralChapter(centralChapterId: String, includeDeleted: Boolean = false): List[Chapter]

  def retrieveSectionsBasedOnCentralSectionIdParentIdAndHandbookId(centralSectionId: String, parentId: String, centralHandbookId: String, includeDeleted: Boolean = false): List[Section]

  def persistChapterAndSectionsFromCentralHandbook(handbook: Handbook, latestCentralVersion: CentralHandbook, externalOrgId: Option[String], currentUserId: Option[String])

  protected def ifBasedOnCentralContentPersistAllChildrenToChapter(chapter: Chapter, externalOrgId: Option[String], currentUserId: Option[String])

  def retrieveDeletedAndNotDeletedChaptersBasedOnCentralHandbook(centralHandbookId: String): List[Chapter]

  def getNextSortOrderByParentAndHandbook(parentId: Option[String], handbookId: String): Int

  def retrieveHandbookTitleVersion(handbookId: String, time: Option[DateTime]): List[Handbook]

  def retrieveAllTitleVersions(handbookId: String): List[Handbook]

  def retrieveChapterAndSectionVersions(handbookId: String, time: Option[DateTime]): (List[Chapter], List[Section])

  def retrieveHandbooksBasedOnCentralHandbookId(centralHandbookId: String): List[Handbook]
}
