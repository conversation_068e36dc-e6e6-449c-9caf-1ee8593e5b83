package no.kf.handboker.service

import no.kf.db.IDGenerator
import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.model.local.{Link, LinkCollection}
import no.kf.handboker.{ComponentRegistry, DefaultTestDI}
import org.junit.runner.RunWith
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfterEach, FunSuite}
import org.scalatest.junit.JUnitRunner


@RunWith(classOf[JUnitRunner])
class HandbookLinkServiceTest extends FunSuite with DefaultTestDI with BeforeAndAfterEach {

  override val componentRegistry: ComponentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val handbookLinkService = new HandbookLinkServiceImpl
  }

  val service = componentRegistry.handbookLinkService
  val repo = componentRegistry.handbookLinkRepository

  override def beforeEach() {
    reset(repo)
  }

  test("That we call repo and return the result when retrieving link collections for a handbook") {
    val handbookId = IDGenerator.generateUniqueId
    val links = List(Link(Some(IDGenerator.generateUniqueId), "title", "url", 0), Link(Some(IDGenerator.generateUniqueId), "title", "url", 1))
    val linkCollections = List(LinkCollection(Some(IDGenerator.generateUniqueId), "Link collection title", handbookId, 0, links))
    when(repo.retrieveLinkCollectionsForHandbook(handbookId)).thenReturn(linkCollections)

    val res = service.retrieveLinkCollectionsForHandbook(handbookId)
    assert(res === linkCollections)

    verify(repo, times(1)).retrieveLinkCollectionsForHandbook(handbookId)
  }

  test("That we call repo and return the result when persisting a link collections") {
    val handbookId = IDGenerator.generateUniqueId
    val links = List(Link(Some(IDGenerator.generateUniqueId), "title", "url", 0), Link(Some(IDGenerator.generateUniqueId), "title", "url", 1))
    val linkCollection = LinkCollection(Some(IDGenerator.generateUniqueId), "Link collection title", handbookId, 0, links)
    when(repo.persistLinkCollection(linkCollection)).thenReturn(linkCollection)

    val res = service.persistLinkCollection(linkCollection)
    assert(res === linkCollection)

    verify(repo, times(1)).persistLinkCollection(linkCollection)
  }

  test("That we call repo when deleting link collection") {
    val linkCollectionId = IDGenerator.generateUniqueId

    service.deleteLinkCollection(linkCollectionId)

    verify(repo, times(1)).deleteLinkCollection(linkCollectionId)
  }

  test("That we call repo when deleting link") {
    val linkId = IDGenerator.generateUniqueId

    service.deleteLink(linkId)

    verify(repo, times(1)).deleteLink(linkId)
  }

  test("That we call repo and return the result when persisting link for handbook") {
    val linkCollectionId = IDGenerator.generateUniqueId
    val link = Link(Some(IDGenerator.generateUniqueId), "title", "url", 0)

    when(repo.persistLink(link, linkCollectionId)).thenReturn(link)

    val res = service.persistLink(link, linkCollectionId)
    assert(res === link)

    verify(repo, times(1)).persistLink(link, linkCollectionId)
  }
}
