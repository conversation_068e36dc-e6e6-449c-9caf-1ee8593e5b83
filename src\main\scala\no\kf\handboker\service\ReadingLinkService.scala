package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.handboker.model.central.ReadingLink
import no.kf.handboker.model.local.Section
import no.kf.handboker.repository.ReadingLinkRepositoryComponent
import org.joda.time.DateTime

trait ReadingLinkServiceComponent extends TransactionManager {
  this: ReadingLinkRepositoryComponent with CentralHandbookServiceComponent =>

  val readingLinkService: ReadingLinkService

  class ReadingLinkServiceImpl extends ReadingLinkService {
    override def persistReadingLink(link: ReadingLink): ReadingLink = inTransaction {
      if (!isLinkValid(link)){ None }
      readingLinkRepository.persistReadingLink(link)
    }

    override def retrieveReadingLink(linkId: String): Option[ReadingLink] = inTransaction(
      readingLinkRepository.retrieveReadingLink(linkId)
    )

    override def retrieveAllReadingLinks(): List[ReadingLink] = inTransaction(
      readingLinkRepository.retrieveAllReadingLinks()
    )

    override def retrieveDataForLink(linkId: String): Option[Section] = inTransaction {
      val readingLink = retrieveReadingLink(linkId)
      if (readingLink.isDefined) {
        centralHandbookService.retrieveCentralSection(readingLink.get.centralSectionId)
      } else { None }
    }

    override def retrieveReadingLinkForSection(sectionId: String): Option[ReadingLink] = inTransaction {
      readingLinkRepository.retrieveReadingLinkForSection(sectionId)
    }

    override def deleteReadingLink(linkId: String): Unit = inTransaction(
      readingLinkRepository.deleteReadingLink(linkId)
    )

    override def deleteInvalidLinks(): Unit = inTransaction(
      readingLinkRepository.deleteInvalidLinks()
    )

    override def isLinkValid(link: ReadingLink): Boolean = inTransaction{
      DateTime.now.getMillis < link.validTo.getMillis
    }
  }
}

trait ReadingLinkService {
  def persistReadingLink(link: ReadingLink): ReadingLink
  def retrieveAllReadingLinks(): List[ReadingLink]
  def retrieveReadingLink(linkId: String): Option[ReadingLink]
  def retrieveDataForLink(linkId: String): Option[Section]
  def retrieveReadingLinkForSection(sectionId: String): Option[ReadingLink]
  def deleteReadingLink(linkId: String): Unit
  def deleteInvalidLinks(): Unit
  def isLinkValid(link: ReadingLink): Boolean
}
