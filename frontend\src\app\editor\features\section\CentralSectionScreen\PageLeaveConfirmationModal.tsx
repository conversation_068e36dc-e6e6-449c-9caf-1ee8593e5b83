import React from 'react';
import { Button, Modal } from 'kf-bui';

interface PageLeaveConfirmationModalProps {
  onCancel: () => void;
  onConfirm: () => void;
  message: string;
  title: string;
  t: (key: string) => string;
}

export const PageLeaveConfirmationModal: React.FC<PageLeaveConfirmationModalProps> = ({ 
  onCancel, 
  onConfirm, 
  message, 
  title, 
  t 
}) => (
  <Modal isOpen onClose={onCancel}>
    <Modal.Header onClose={onCancel}>
      <Modal.Title>{title}</Modal.Title>
    </Modal.Header>
    <Modal.Body>
      <p>{message}</p>
    </Modal.Body>
    <Modal.Footer>
      <Button onClick={onCancel}>{t('leaveEditorConfirmationCancel')}</Button>
      <Button color="danger" outlined className="modal-leave-button" onClick={onConfirm}>
        {t('leaveEditorConfirmationLeave')}
      </Button>
    </Modal.Footer>
  </Modal>
);
