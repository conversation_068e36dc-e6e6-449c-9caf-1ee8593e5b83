import { useState, useEffect, useCallback } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Group,
  Icon,
  Menu,
  Title,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Spinner } from "@/shared/components/Spinner";

import {
  useGetReadingLinksQuery,
  useDeleteCentralHandbookMutation,
  useSortCentralItemsMutation,
  useGetCentralHandbooksQuery,
  useGetCentralChaptersQuery,
} from "@/store/services/handbook/centralHandbookApi";
import { useIsHandbookPending } from "@/store/services/handbook/useOptimizedPendingPublications";
import { NoSelectionScreen } from "../NoSelectionScreen";
import { CentralSortChildrenScreen as SortChildrenScreen } from "../../central/CentralSortChildrenScreen";
import { EditCentralHandbookButton } from "../EditCentralHandbookButton";
import { PubliseringsModal } from "../PubliseringsModal";
import { CentralDeleteButton as DeleteButton } from "../../central/CentralDeleteButton";
import { EksporteringsModal } from "../../EksporteringsModal";

export const CentralHandbookScreen = () => {
  const { handbookId } = useParams() as { handbookId: string };
  const t = usePrefixedTranslation("editor.containers.HandbookSelection");
  const navigate = useNavigate();

  const [isSorting, setIsSorting] = useState(false);
  const [showPubliseringsModal, setShowPubliseringsModal] = useState(false);
  const [showEksporteringsModal, setShowEksporteringsModal] = useState(false);

  const {
    data: handbooks = [],
    error: handbooksError,
    isLoading: isLoadingHandbooks,
  } = useGetCentralHandbooksQuery();
  const {
    data: allChapters = [],
    error: chaptersError,
    isLoading: isLoadingChapters,
  } = useGetCentralChaptersQuery();

  const [sortCentralItems, { isLoading: isSortingLoading }] =
    useSortCentralItemsMutation();

  const { data: readingLinks = [], error: readingLinksError } =
    useGetReadingLinksQuery();

  const isPendingPublication = useIsHandbookPending(handbookId);

  const [deleteCentralHandbook] = useDeleteCentralHandbookMutation();

  const centralHandbook = handbooks.find(
    (handbook) => handbook.id === handbookId
  );

  const chapters = allChapters.filter(
    (chapter) => chapter.centralHandbookId === handbookId && !chapter.parentId
  );

  useEffect(() => {
    setIsSorting(false);
  }, [handbookId]);

  const toggleSort = useCallback(() => {
    setIsSorting((prev) => !prev);
  }, []);

  const handlePublishHandbook = useCallback(() => {
    setShowPubliseringsModal(false);
  }, []);

  const handleDeleteHandbook = useCallback(async () => {
    if (!centralHandbook?.id) return;

    try {
      await deleteCentralHandbook(centralHandbook.id).unwrap();
      toast.success("Håndbok slettet");
      navigate("/central-editor");
    } catch (error) {
      console.error("Error deleting handbook:", error);
      toast.error("Feil ved sletting av håndbok");
    }
  }, [deleteCentralHandbook, centralHandbook?.id, navigate]);

  const renderChildren = useCallback(() => {
    if (!chapters.length) {
      return <div className="no-chapters">Denne håndboka har ingen kapitler</div>;
    }

    const sortedChapters = [...chapters].sort(
      (c1, c2) => (c1.sortOrder || 0) - (c2.sortOrder || 0)
    );

    return (
      <Menu>
        <Menu.List>
          {sortedChapters.map((chapter) => (
            <Menu.Item
              key={chapter.id}
              as={Link}
              to={`/central-editor/${chapter.centralHandbookId}/chapter/${chapter.id}/`}
            >
              <Icon
                icon="RegBookmark"
                size="small"
                style={{ marginRight: "4px" }}
              />
              {chapter.title}
            </Menu.Item>
          ))}
        </Menu.List>
      </Menu>
    );
  }, [chapters]);

  if (!handbookId) {
    return <NoSelectionScreen />;
  }

  if (isLoadingHandbooks || isLoadingChapters) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (handbooksError) {
    console.error("Error loading handbooks:", handbooksError);
    toast.error("Feil ved lasting av håndbøker");
  }

  if (chaptersError) {
    console.error("Error loading chapters:", chaptersError);
    toast.error("Feil ved lasting av kapitler");
  }

  if (readingLinksError) {
    console.error("Error loading reading links:", readingLinksError);
  }

  if (!centralHandbook) {
    if (isLoadingHandbooks || isLoadingChapters) {
      return (
        <div className="loading-container">
          <Spinner />
        </div>
      );
    }

    if (!isLoadingHandbooks && !isLoadingChapters && handbooks.length > 0) {
      console.error(`Central handbook with ID ${handbookId} not found`);
      toast.error(`Håndbok med ID ${handbookId} ble ikke funnet`);
    }
    return <NoSelectionScreen />;
  }

  if (isSorting) {
    return (
      <SortChildrenScreen
        items={chapters}
        onCancel={() => setIsSorting(false)}
        sortFunction={async (itemIds) => {
          try {
            await sortCentralItems(itemIds).unwrap();
            toast.success("Lagret sortering.");
            setIsSorting(false);
          } catch (error) {
            console.error("Error sorting chapters:", error);
            toast.error("Feil ved sortering av kapitler");
          }
        }}
        isSaving={isSortingLoading}
      />
    );
  }

  return (
    <>
      <Columns>
        <Column>
          <Title>
            <Icon icon="book" size="medium" style={{ marginRight: "1rem" }} />
            <span>{centralHandbook.title}</span>
          </Title>
        </Column>
      </Columns>

      {centralHandbook.id && (
        <Columns>
          <Column>
            <Group>
              <EditCentralHandbookButton centralHandbook={centralHandbook} />

              <Button
                control
                as={Link}
                to={`/central-editor/${centralHandbook.id}/chapter/add-new`}
                size="small"
                icon="plus"
              >
                {t("newChapter")}
              </Button>

              <Button
                control
                size="small"
                disabled={isPendingPublication}
                onClick={() => setShowPubliseringsModal(true)}
                icon={isPendingPublication ? "spinner" : "check"}
                className={isPendingPublication ? "pending-publication-button" : ""}
              >
                {isPendingPublication
                  ? "Venter publisering"
                  : "Publiser sentral håndbok"}
              </Button>

              {showPubliseringsModal && (
                <PubliseringsModal
                  isOpen={showPubliseringsModal}
                  handbook={centralHandbook}
                  toggleHide={() => setShowPubliseringsModal(false)}
                  handlePublish={handlePublishHandbook}
                />
              )}

              <Button
                control
                size="small"
                onClick={() => setShowEksporteringsModal(true)}
              >
                <span>Eksporter</span>
              </Button>

              {showEksporteringsModal && (
                <EksporteringsModal
                  centralHandbook={centralHandbook}
                  onHide={() => setShowEksporteringsModal(false)}
                  isOpen={showEksporteringsModal}
                />
              )}
            </Group>
          </Column>

          <Column narrow>
            <Group>

              <Button
                control
                active={isSorting}
                onClick={toggleSort}
                disabled={chapters.length <= 1}
                title="Sorter kapitlene til Håndboken"
                size="small"
              >
                {t("sortButton")}
              </Button>

              {centralHandbook && (
                <DeleteButton
                  toDelete={{
                    id: centralHandbook.id!,
                    title: centralHandbook.title,
                    type: "HANDBOOK",
                  }}
                  onDelete={handleDeleteHandbook}
                  readlinkExists={readingLinks.length > 0}
                />
              )}
            </Group>
          </Column>
        </Columns>
      )}

      <hr />
      {chapters !== undefined && renderChildren()}
    </>
  );
};
