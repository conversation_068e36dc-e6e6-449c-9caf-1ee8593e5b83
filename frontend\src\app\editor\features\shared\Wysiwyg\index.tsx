import {
  useRef,
  useImperativeHandle,
  forwardRef,
  useState,
  useEffect,
} from "react";
import { Editor } from "@tinymce/tinymce-react";
import type { Editor as TinyMCEEditor } from "tinymce";
import { usePrefixedTranslation } from "@/libs/i18n";

type ToolbarMode = "floating" | "sliding" | "scrolling" | "wrap";

interface WysiwygProps {
  id?: string;
  disabled?: boolean;
  value?: string;
  plugins?: string[];
  toolbar?: string;
  toolbar_mode?: ToolbarMode;
  height?: number;
  menubar?: string | boolean;
  statusbar?: boolean;
  imagesUploadUrl?: string;
  onChange?: (content: string) => void;
  onEditorContentChange?: (hasChanged: boolean) => void;
  onDirtyStateChange?: (isDirty: boolean) => void;
  [key: string]: unknown;
}

export interface WysiwygRef {
  setValue: (value: string) => void;
  isDirty: () => boolean;
  uploadImagesAndGetContent: () => Promise<string>;
  getContent: () => string;
  getEditor: () => TinyMCEEditor | null;
}

const defaultProps = {
  id: "wysiwyg-editor",
  menubar: "file edit view insert format tools table help",
  statusbar: true,
  plugins: [
    "advlist",
    "autolink",
    "lists",
    "link",
    "image",
    "charmap",
    "preview",
    "anchor",
    "searchreplace",
    "visualblocks",
    "code",
    "fullscreen",
    "insertdatetime",
    "media",
    "table",
    "help",
    "wordcount",
    "emoticons",
    "codesample",
    "pagebreak",
  ],
  toolbar:
    "undo redo | blocks | " +
    "bold italic forecolor backcolor | alignleft aligncenter " +
    "alignright alignjustify | bullist numlist outdent indent | " +
    "removeformat | link image media | pastetext languagetoggle | " +
    "code fullscreen preview | charmap emoticons | table | help",
  toolbar_mode: "wrap" as ToolbarMode,
  height: 500,
  disabled: false,
  imagesUploadUrl: "/handboker/image-upload/",
  value: "",
  onChange: (_content: string) => {},
  onEditorContentChange: (_hasChanged: boolean) => {},
  onDirtyStateChange: (_isDirty: boolean) => {},
};

export const Wysiwyg = forwardRef<WysiwygRef, WysiwygProps>((props, ref) => {
  const finalProps = { ...defaultProps, ...props };
  const {
    id,
    toolbar,
    toolbar_mode,
    height,
    disabled,
    menubar,
    statusbar,
    imagesUploadUrl,
    onChange,
    onEditorContentChange,
    onDirtyStateChange,
    value,
    plugins,
    ...rest
  } = finalProps;

  const editorRef = useRef<TinyMCEEditor | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentContent, setCurrentContent] = useState(value);
  const t = usePrefixedTranslation("editor.labels");

  useEffect(() => {
    if (value !== currentContent) {
      setCurrentContent(value);
    }
  }, [value]);

  const handleInit = (_evt: unknown, editor: TinyMCEEditor): void => {
    editorRef.current = editor;
    setIsLoading(false);
    // Restore content after language change
    if (currentContent) {
      editor.setContent(currentContent);
    }
  };

  const handleEditorChange = (content: string): void => {
    setCurrentContent(content);
    onChange(content);
    onEditorContentChange(true);

    if (editorRef.current) {
      const isDirty = editorRef.current.isDirty();
      onDirtyStateChange(isDirty);
    }
  };

  const initConfig = {
    skin: "oxide",
    content_css:
      "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap",
    content_style: `
        body { font-family: 'Inter', sans-serif; font-size: 16px; }
        p { margin: 0 0 1em 0; }
        table td { word-break: break-word; }
    `,
    height,
    menubar,
    statusbar,
    plugins,
    toolbar,
    toolbar_mode,
    elementpath: false,
    promotion: false,
    branding: false,
    language: "nb-NO",
    language_url: `https://cdn.jsdelivr.net/npm/tinymce-i18n@25.8.4/langs8/nb-NO.js`,
    browser_spellcheck: true,
    default_link_target: "_blank",
    link_context_toolbar: true,
    automatic_uploads: false,
    images_upload_url: imagesUploadUrl,
    image_title: true,
    paste_data_images: true,
    relative_urls: false,
    setup: (editor: TinyMCEEditor) => {
      editor.ui.registry.addButton("pastetext", {
        text: "Paste as Text",
        tooltip: "Paste clipboard content as plain text",
        onAction: () => {
          navigator.clipboard
            .readText()
            .then((text) => {
              editor.insertContent(text);
            })
            .catch((err) => {
              console.error("Failed to read clipboard:", err);
              editor.notificationManager.open({
                text: "Unable to access clipboard. Please paste using Ctrl+V.",
                type: "error",
              });
            });
        },
      });
    },
  };

  useImperativeHandle(
    ref,
    (): WysiwygRef => ({
      setValue: (newValue: string): void => {
        const content = newValue || "";
        setCurrentContent(content);
        editorRef.current?.setContent(content);
      },
      isDirty: (): boolean => {
        return editorRef.current?.isDirty() ?? currentContent !== value;
      },
      uploadImagesAndGetContent: async (): Promise<string> => {
        if (editorRef.current?.uploadImages) {
          await editorRef.current.uploadImages();
        }
        const content = editorRef.current?.getContent() || currentContent;
        setCurrentContent(content);
        return content;
      },
      getContent: (): string => {
        const content = editorRef.current?.getContent() || currentContent;
        setCurrentContent(content);
        return content;
      },
      getEditor: () => editorRef.current,
    }),
    [currentContent, value]
  );

  const LoadingSpinner = () => (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: height,
        border: "1px solid #ccc",
        borderRadius: "4px",
        backgroundColor: "#f9f9f9",
      }}
    >
      <div style={{ textAlign: "center" }}>
        <div
          style={{
            width: "24px",
            height: "24px",
            border: "3px solid #e0e0e0",
            borderTop: "3px solid #007acc",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
            margin: "0 auto 12px",
          }}
        />
        <span style={{ color: "#666", fontSize: "14px" }}>
          {t("loadingEditor")}
        </span>
      </div>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );

  return (
    <div style={{ position: "relative" }}>
      {isLoading && <LoadingSpinner />}
      <div style={{ visibility: isLoading ? "hidden" : "visible" }}>
        <Editor
          id={id}
          tinymceScriptSrc="https://cdn.jsdelivr.net/npm/tinymce@8.0.2/tinymce.min.js"
          licenseKey="gpl"
          onInit={handleInit}
          value={currentContent}
          onEditorChange={handleEditorChange}
          disabled={disabled}
          init={initConfig}
          {...rest}
        />
      </div>
    </div>
  );
});

Wysiwyg.displayName = "Wysiwyg";
