{"name": "handboker", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "handboker", "version": "0.0.0", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@tinymce/tinymce-react": "^6.2.1", "classnames": "^2.5.1", "glob": "^11.0.3", "html-react-parser": "^5.2.6", "js-cookie": "^3.0.5", "kf-bui": "^4.5.15", "lodash": "^4.17.21", "moment": "^2.30.1", "object-path-immutable": "^4.1.2", "query-string": "^9.2.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "react-intl": "^7.1.11", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-stickynode": "^5.0.2", "react-toastify": "^11.0.5", "redux": "^5.0.0", "redux-persist": "^6.0.0", "styled-components": "^6.1.19", "tinymce": "^8.0.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-stickynode": "^4.0.3", "@types/redux-devtools-extension": "^2.13.0", "@types/redux-persist": "^4.0.0", "@typescript-eslint/parser": "^8.37.0", "@typescript-eslint/typescript-estree": "^8.37.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "ts-morph": "^26.0.0", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/compat-data/-/compat-data-7.28.0.tgz", "integrity": "sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/core/-/core-7.28.3.tgz", "integrity": "sha1-rO3d5pxdHe9puDnQnvo+P/Wcl8s=", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.28.3", "@babel/helpers": "^7.28.3", "@babel/parser": "^7.28.3", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.3", "@babel/types": "^7.28.2", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.28.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/generator/-/generator-7.28.3.tgz", "integrity": "sha1-libBdBxlDLrDkSFpSg8tdFG47z4=", "license": "MIT", "dependencies": {"@babel/parser": "^7.28.3", "@babel/types": "^7.28.2", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha1-RqD276uAjVHSnOloWN0Qzocycz0=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "integrity": "sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.28.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz", "integrity": "sha1-orN9PaOyNE/ghdqyNEJvK5ovpfY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.28.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.28.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/helpers/-/helpers-7.28.3.tgz", "integrity": "sha1-uDFWwKIjLBM9G1Nd1dNFIRnH5EE=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/parser/-/parser-7.28.3.tgz", "integrity": "sha1-0tJbgUYhvKX+nRcryTeSVH56KnE=", "license": "MIT", "dependencies": {"@babel/types": "^7.28.2"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "integrity": "sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "integrity": "sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.28.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/runtime/-/runtime-7.28.4.tgz", "integrity": "sha1-pwImAW+r4lxXg7LyLT4cm8XKMyY=", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/template/-/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/traverse/-/traverse-7.28.3.tgz", "integrity": "sha1-aRGhB5XSzOQ+xqKM/8RAzKJZNDQ=", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.3", "@babel/template": "^7.27.2", "@babel/types": "^7.28.2", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@babel/types/-/types-7.28.2.tgz", "integrity": "sha1-2p2whWqaiOChOwGYgddRNYjPcSs=", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@dnd-kit/accessibility": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@dnd-kit/accessibility/-/accessibility-3.1.1.tgz", "integrity": "sha1-O0ICvWuzcKBzD2c0hneFkZvqxq8=", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@dnd-kit/core": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@dnd-kit/core/-/core-6.3.1.tgz", "integrity": "sha1-TDZAamLHuqxJlyb4mZNfk/Dm0AM=", "license": "MIT", "dependencies": {"@dnd-kit/accessibility": "^3.1.1", "@dnd-kit/utilities": "^3.2.2", "tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@dnd-kit/modifiers": {"version": "9.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@dnd-kit/modifiers/-/modifiers-9.0.0.tgz", "integrity": "sha1-lqAoDHexDHFu952Xks560ENwdx0=", "license": "MIT", "dependencies": {"@dnd-kit/utilities": "^3.2.2", "tslib": "^2.0.0"}, "peerDependencies": {"@dnd-kit/core": "^6.3.0", "react": ">=16.8.0"}}, "node_modules/@dnd-kit/sortable": {"version": "10.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@dnd-kit/sortable/-/sortable-10.0.0.tgz", "integrity": "sha1-H5OCuQ2DXNXGXZKCT6na+3jEw+g=", "license": "MIT", "dependencies": {"@dnd-kit/utilities": "^3.2.2", "tslib": "^2.0.0"}, "peerDependencies": {"@dnd-kit/core": "^6.3.0", "react": ">=16.8.0"}}, "node_modules/@dnd-kit/utilities": {"version": "3.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@dnd-kit/utilities/-/utilities-3.2.2.tgz", "integrity": "sha1-WjK2rzVtxfdNYbN9b3EppAQM7Xs=", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@emotion/babel-plugin": {"version": "11.13.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/babel-plugin/-/babel-plugin-11.13.5.tgz", "integrity": "sha1-6rjWXb3tdODs/SjcIY51YHxOe8A=", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/runtime": "^7.18.3", "@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/serialize": "^1.3.3", "babel-plugin-macros": "^3.1.0", "convert-source-map": "^1.5.0", "escape-string-regexp": "^4.0.0", "find-root": "^1.1.0", "source-map": "^0.5.7", "stylis": "4.2.0"}}, "node_modules/@emotion/babel-plugin/node_modules/convert-source-map": {"version": "1.9.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=", "license": "MIT"}, "node_modules/@emotion/babel-plugin/node_modules/stylis": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/stylis/-/stylis-4.2.0.tgz", "integrity": "sha1-edruAgiWTI/mlaQvz/ysYzohGlE=", "license": "MIT"}, "node_modules/@emotion/cache": {"version": "11.14.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/cache/-/cache-11.14.0.tgz", "integrity": "sha1-7kSyaYbuuTyL6Cu5Lx96myGy7XY=", "license": "MIT", "dependencies": {"@emotion/memoize": "^0.9.0", "@emotion/sheet": "^1.4.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "stylis": "4.2.0"}}, "node_modules/@emotion/cache/node_modules/stylis": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/stylis/-/stylis-4.2.0.tgz", "integrity": "sha1-edruAgiWTI/mlaQvz/ysYzohGlE=", "license": "MIT"}, "node_modules/@emotion/hash": {"version": "0.9.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/hash/-/hash-0.9.2.tgz", "integrity": "sha1-/5IhufWLTf5h5hmneIc0vWP2iYs=", "license": "MIT"}, "node_modules/@emotion/is-prop-valid": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/is-prop-valid/-/is-prop-valid-1.2.2.tgz", "integrity": "sha1-1BdQdmecaib6qSsDu3hvnlJhIzc=", "license": "MIT", "dependencies": {"@emotion/memoize": "^0.8.1"}}, "node_modules/@emotion/is-prop-valid/node_modules/@emotion/memoize": {"version": "0.8.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/memoize/-/memoize-0.8.1.tgz", "integrity": "sha1-wd2wQEKcbSHTjMlF/nXIGM+2jhc=", "license": "MIT"}, "node_modules/@emotion/memoize": {"version": "0.9.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/memoize/-/memoize-0.9.0.tgz", "integrity": "sha1-dFlp1kmXd3a0P8dkjFVqqkYrQQI=", "license": "MIT"}, "node_modules/@emotion/react": {"version": "11.14.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/react/-/react-11.14.0.tgz", "integrity": "sha1-z6rjXrxn3Z706i6azGzSnhV90F0=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.18.3", "@emotion/babel-plugin": "^11.13.5", "@emotion/cache": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/use-insertion-effect-with-fallbacks": "^1.2.0", "@emotion/utils": "^1.4.2", "@emotion/weak-memoize": "^0.4.0", "hoist-non-react-statics": "^3.3.1"}, "peerDependencies": {"react": ">=16.8.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/@emotion/serialize": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/serialize/-/serialize-1.3.3.tgz", "integrity": "sha1-0pFTEAXxfXBNBGOgMv5nnzdlCeg=", "license": "MIT", "dependencies": {"@emotion/hash": "^0.9.2", "@emotion/memoize": "^0.9.0", "@emotion/unitless": "^0.10.0", "@emotion/utils": "^1.4.2", "csstype": "^3.0.2"}}, "node_modules/@emotion/sheet": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/sheet/-/sheet-1.4.0.tgz", "integrity": "sha1-ySmcNNJIvCboJWNzX3iVPS78qDw=", "license": "MIT"}, "node_modules/@emotion/unitless": {"version": "0.10.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/unitless/-/unitless-0.10.0.tgz", "integrity": "sha1-KvL3x+UVD0l72r2EjOeyGKJ890U=", "license": "MIT"}, "node_modules/@emotion/use-insertion-effect-with-fallbacks": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/use-insertion-effect-with-fallbacks/-/use-insertion-effect-with-fallbacks-1.2.0.tgz", "integrity": "sha1-ioy3e1kOCa/7lg9P8emonlMnOL8=", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/@emotion/utils": {"version": "1.4.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/utils/-/utils-1.4.2.tgz", "integrity": "sha1-bfbEWIH8scQS1miKMRqYt/WcG1I=", "license": "MIT"}, "node_modules/@emotion/weak-memoize": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/weak-memoize/-/weak-memoize-0.4.0.tgz", "integrity": "sha1-XhP6yIfwjET3awzK8zcOsA/sm7Y=", "license": "MIT"}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/aix-ppc64/-/aix-ppc64-0.25.9.tgz", "integrity": "sha1-vvljUfFlIAVclHq6KIAu7ePJ6ak=", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/android-arm/-/android-arm-0.25.9.tgz", "integrity": "sha1-0qdT/ipMc7eUN9C6FIDi12AJdBk=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/android-arm64/-/android-arm64-0.25.9.tgz", "integrity": "sha1-0ucL59UaUpQlQiCR4Ny5A3TBVGw=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/android-x64/-/android-x64-0.25.9.tgz", "integrity": "sha1-UniDbjx651dhYmli+QKg1VNS5oM=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.25.9.tgz", "integrity": "sha1-8VE+r57I+hXcr0w0Gw8AXT6LR64=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.25.9.tgz", "integrity": "sha1-4n28O1B7OhzqO5KAoEuLa3Jfgr4=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.9.tgz", "integrity": "sha1-Nk4+W3of1F2SvgjGzF2JDKdZCMo=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.25.9.tgz", "integrity": "sha1-fIabRfrrPfZo4ZrOBzNaBxHsVqs=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-arm/-/linux-arm-0.25.9.tgz", "integrity": "sha1-bOS5yr8UgnQQFwHREridxnzFLzc=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.25.9.tgz", "integrity": "sha1-SNQoYXWMlAthq+pDupopsYbWy4s=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.25.9.tgz", "integrity": "sha1-IH5UiZt5ysnCbDI/wcqjLjFD8cQ=", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.25.9.tgz", "integrity": "sha1-C6SKEnFZqPartYJ/IRmLmZ/9H8A=", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.25.9.tgz", "integrity": "sha1-pNTMaT0YX2amr96U93KzjOXWTrU=", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.25.9.tgz", "integrity": "sha1-D1gFwcbWQ1odr9wEPLB6GQUDV9s=", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.25.9.tgz", "integrity": "sha1-Z3bt7OD4/KefM4Y5i1GD/yqCdUc=", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.25.9.tgz", "integrity": "sha1-P28p7wNpOER8IhjTCdyHUiWGGDA=", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/linux-x64/-/linux-x64-0.25.9.tgz", "integrity": "sha1-gx/gsOGoCouDkSJOojd9VSDhUn8=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.9.tgz", "integrity": "sha1-BvmdfuvgNfu+Q94BydfpjSoKpUg=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.25.9.tgz", "integrity": "sha1-25mFjmvtbnORH5Kojk7dOoxCmlI=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.9.tgz", "integrity": "sha1-r7iGyGfjb52GuyHoeOEYX11aCTU=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.25.9.tgz", "integrity": "sha1-MIVcn4OB+sag71tfMaxucQimbs8=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openharmony-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/openharmony-arm64/-/openharmony-arm64-0.25.9.tgz", "integrity": "sha1-LyFErzHmetwqjjcFwgwr2XvYgxQ=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openharmony"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.25.9.tgz", "integrity": "sha1-abmam1vSJsnrnGpz+ZD93Ul9cy4=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.25.9.tgz", "integrity": "sha1-14kzCnEq+RbIgyX0/+Rl+IVxnGs=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.25.9.tgz", "integrity": "sha1-UvxzVAa9SWiCU+dOToN6wroHieM=", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@esbuild/win32-x64/-/win32-x64-0.25.9.tgz", "integrity": "sha1-WFYk3IKc+258CqbDyn1+baqH408=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.21.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint/config-array/-/config-array-0.21.0.tgz", "integrity": "sha1-q9vL0WsSTGOAgXZjkqTWtQn3JjY=", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-array/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/config-array/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/config-helpers": {"version": "0.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint/config-helpers/-/config-helpers-0.3.1.tgz", "integrity": "sha1-0xbkeQW9ChqTH6UOZpua9BBNFhc=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.15.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint/core/-/core-0.15.2.tgz", "integrity": "sha1-WThjJ9eGLMNgPrx8eBWdLcxKho8=", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "integrity": "sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "14.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/globals/-/globals-14.0.0.tgz", "integrity": "sha1-iY10E8Kbq89rr+Vvyt3thYrack4=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "9.34.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint/js/-/js-9.34.0.tgz", "integrity": "sha1-/EIxaLnRDgjeqQiNCDeI7GRCmWs=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint/object-schema/-/object-schema-2.1.6.tgz", "integrity": "sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.3.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@eslint/plugin-kit/-/plugin-kit-0.3.5.tgz", "integrity": "sha1-/Ydk8O55yN2rTaZUYMZBzv7gF8U=", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.15.2", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@floating-ui/core": {"version": "1.7.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@floating-ui/core/-/core-1.7.3.tgz", "integrity": "sha1-Ri1yLwAeI+Rthv0r0NIbdpPMuLc=", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/dom": {"version": "1.7.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@floating-ui/dom/-/dom-1.7.4.tgz", "integrity": "sha1-7mZ1SZmHRcnD4+hGg7kJwx1smnc=", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.3", "@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/react": {"version": "0.27.16", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@floating-ui/react/-/react-0.27.16.tgz", "integrity": "sha1-bkhbUnC3oylv3E0PryrJq/lVovc=", "license": "MIT", "dependencies": {"@floating-ui/react-dom": "^2.1.6", "@floating-ui/utils": "^0.2.10", "tabbable": "^6.0.0"}, "peerDependencies": {"react": ">=17.0.0", "react-dom": ">=17.0.0"}}, "node_modules/@floating-ui/react-dom": {"version": "2.1.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@floating-ui/react-dom/-/react-dom-2.1.6.tgz", "integrity": "sha1-GJ9oEEPBQAVh9ily9GG5PwG/IjE=", "license": "MIT", "dependencies": {"@floating-ui/dom": "^1.7.4"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.10", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@floating-ui/utils/-/utils-0.2.10.tgz", "integrity": "sha1-oqHjgS0UUl9yXQEac+zrQf71vBw=", "license": "MIT"}, "node_modules/@formatjs/ecma402-abstract": {"version": "2.3.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@formatjs/ecma402-abstract/-/ecma402-abstract-2.3.4.tgz", "integrity": "sha1-6QxahGuisz2SvEAP3XCdpYgoD7w=", "license": "MIT", "dependencies": {"@formatjs/fast-memoize": "2.2.7", "@formatjs/intl-localematcher": "0.6.1", "decimal.js": "^10.4.3", "tslib": "^2.8.0"}}, "node_modules/@formatjs/fast-memoize": {"version": "2.2.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@formatjs/fast-memoize/-/fast-memoize-2.2.7.tgz", "integrity": "sha1-cH+d2utSKjL2cVu3lQsIMfTMexU=", "license": "MIT", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@formatjs/icu-messageformat-parser": {"version": "2.11.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@formatjs/icu-messageformat-parser/-/icu-messageformat-parser-2.11.2.tgz", "integrity": "sha1-ha6iEb6kCqge4dRKx6zMPPVQCnM=", "license": "MIT", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "@formatjs/icu-skeleton-parser": "1.8.14", "tslib": "^2.8.0"}}, "node_modules/@formatjs/icu-skeleton-parser": {"version": "1.8.14", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@formatjs/icu-skeleton-parser/-/icu-skeleton-parser-1.8.14.tgz", "integrity": "sha1-uVgdADY5CO+ymBf9/8MrefQdq+U=", "license": "MIT", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "tslib": "^2.8.0"}}, "node_modules/@formatjs/intl": {"version": "3.1.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@formatjs/intl/-/intl-3.1.6.tgz", "integrity": "sha1-TH/sbwgmKM+oCHH75/m8JkQwDjs=", "license": "MIT", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "@formatjs/fast-memoize": "2.2.7", "@formatjs/icu-messageformat-parser": "2.11.2", "intl-messageformat": "10.7.16", "tslib": "^2.8.0"}, "peerDependencies": {"typescript": "^5.6.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@formatjs/intl-localematcher": {"version": "0.6.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@formatjs/intl-localematcher/-/intl-localematcher-0.6.1.tgz", "integrity": "sha1-JdwwZ1Mgv2Wp1/c4dvweQGTA4pk=", "license": "MIT", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@hookform/resolvers": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@hookform/resolvers/-/resolvers-5.2.1.tgz", "integrity": "sha1-MzK0Zi/jAalprBt5XWY89ygykWY=", "license": "MIT", "dependencies": {"@standard-schema/utils": "^0.3.0"}, "peerDependencies": {"react-hook-form": "^7.55.0"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@humanfs/core/-/core-0.19.1.tgz", "integrity": "sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@humanfs/node/-/node-0.16.6.tgz", "integrity": "sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@humanwhocodes/retry/-/retry-0.3.1.tgz", "integrity": "sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@humanwhocodes/retry/-/retry-0.4.3.tgz", "integrity": "sha1-wrnS43TuYsWG062+qHGZsdenpro=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@isaacs/balanced-match": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@isaacs/balanced-match/-/balanced-match-4.0.1.tgz", "integrity": "sha1-MIHa28NGBmG3UedZHX+upd853Sk=", "license": "MIT", "engines": {"node": "20 || >=22"}}, "node_modules/@isaacs/brace-expansion": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@isaacs/brace-expansion/-/brace-expansion-5.0.0.tgz", "integrity": "sha1-Sz2rq32OdaQpQUqWvWe/TB0T4PM=", "license": "MIT", "dependencies": {"@isaacs/balanced-match": "^4.0.1"}, "engines": {"node": "20 || >=22"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@isaacs/cliui/-/cliui-8.0.2.tgz", "integrity": "sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=", "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.13", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz", "integrity": "sha1-Y0Khn0Q0dRjJPkOxrGnes8Rlah8=", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.5.tgz", "integrity": "sha1-aRKwDSxjHA0Vzhp6tXzWV/Ko+Lo=", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.30", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.30.tgz", "integrity": "sha1-SnbE2u7l3wn105QOCHRC+zbOK5k=", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@pkgr/core": {"version": "0.2.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@pkgr/core/-/core-0.2.9.tgz", "integrity": "sha1-0imnt/nawWehVpku8jx/AjZT9Ts=", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/pkgr"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@popperjs/core/-/core-2.11.8.tgz", "integrity": "sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=", "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@reduxjs/toolkit": {"version": "2.9.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@reduxjs/toolkit/-/toolkit-2.9.0.tgz", "integrity": "sha1-1LErkMJ3FqalBxk/jDsogHKbl9U=", "license": "MIT", "dependencies": {"@standard-schema/spec": "^1.0.0", "@standard-schema/utils": "^0.3.0", "immer": "^10.0.3", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "reselect": "^5.1.0"}, "peerDependencies": {"react": "^16.9.0 || ^17.0.0 || ^18 || ^19", "react-redux": "^7.2.1 || ^8.1.3 || ^9.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "react-redux": {"optional": true}}}, "node_modules/@rolldown/pluginutils": {"version": "1.0.0-beta.27", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.27.tgz", "integrity": "sha1-R9K/TO9tRwsi9YMbQg+JZOC/dV8=", "dev": true, "license": "MIT"}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.50.0.tgz", "integrity": "sha1-k5wb6WJdQo2FE+SrYNQG/o2yNxg=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.50.0.tgz", "integrity": "sha1-t0AFd1kD96j042PShAwdzvN3b/M=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.50.0.tgz", "integrity": "sha1-jARgPNzx7AzWsnFSs4J+SSlfKWI=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.50.0.tgz", "integrity": "sha1-GeyXbxzGY97yaSzX/7MpgfKwtzM=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.50.0.tgz", "integrity": "sha1-qWtK2DRiKfb8vZ1X8cUwQLA3wto=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.50.0.tgz", "integrity": "sha1-+lZaKCvFeWfuZmhgexgWeL3XTko=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.50.0.tgz", "integrity": "sha1-38iPcpXh+Y138lKWvnh+il1s7XU=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.50.0.tgz", "integrity": "sha1-Ms1wyHRVygMfA2EJDPF9paLvZtU=", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.50.0.tgz", "integrity": "sha1-Dn4f5yQeM4T2xrTM28+orYx4uGk=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.50.0.tgz", "integrity": "sha1-XUIfLz5KhHhsTf2c6X5ZXJtZ5/Q=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.50.0.tgz", "integrity": "sha1-oPtcfQ6IMZ4YrP2UNvGe45NUsCc=", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-ppc64-gnu": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-ppc64-gnu/-/rollup-linux-ppc64-gnu-4.50.0.tgz", "integrity": "sha1-pltZivEvJSEMMpXaVRpuNhbqSI0=", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.50.0.tgz", "integrity": "sha1-ELp3YhSuKFfFv0OJaQ2rsvuvfZg=", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.50.0.tgz", "integrity": "sha1-wqRsuqMp1fIeWAj1pmu5x4z2iqw=", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.50.0.tgz", "integrity": "sha1-oHRHvgadZEYuMMZmEb4gxFE5Y+0=", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.50.0.tgz", "integrity": "sha1-iIfFi9USQnVK6cVpR9bogzMtzHQ=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.50.0.tgz", "integrity": "sha1-ZAP9pyorO5+77v+T0U8cRe+XdfM=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-openharmony-arm64": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-openharmony-arm64/-/rollup-openharmony-arm64-4.50.0.tgz", "integrity": "sha1-UoCa/Mr/R+cxuWWgwW5Whr6BnV8=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openharmony"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.50.0.tgz", "integrity": "sha1-I/4A3btAsno4ibwemeYxDZc1OtU=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.50.0.tgz", "integrity": "sha1-UgtYgHa1k0E9kZkS1p39Vyih8wU=", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.50.0.tgz", "integrity": "sha1-2B7+ahIGDH/t35gF4qlMOrBnn0g=", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@standard-schema/spec": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@standard-schema/spec/-/spec-1.0.0.tgz", "integrity": "sha1-8ZO3PcMWxBcPLoKogdoPVQ1VG5w=", "license": "MIT"}, "node_modules/@standard-schema/utils": {"version": "0.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@standard-schema/utils/-/utils-0.3.0.tgz", "integrity": "sha1-PV5gjxbCOQwQUo6Y5Zrva/c8rns=", "license": "MIT"}, "node_modules/@tinymce/tinymce-react": {"version": "6.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@tinymce/tinymce-react/-/tinymce-react-6.3.0.tgz", "integrity": "sha1-9k7neqhfaxnamPUpKYi7LOLW7XM=", "license": "MIT", "dependencies": {"prop-types": "^15.6.2"}, "peerDependencies": {"react": "^19.0.0 || ^18.0.0 || ^17.0.1 || ^16.7.0", "react-dom": "^19.0.0 || ^18.0.0 || ^17.0.1 || ^16.7.0", "tinymce": "^8.0.0 || ^7.0.0 || ^6.0.0 || ^5.5.1"}, "peerDependenciesMeta": {"tinymce": {"optional": true}}}, "node_modules/@ts-morph/common": {"version": "0.27.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@ts-morph/common/-/common-0.27.0.tgz", "integrity": "sha1-6Dob18usBUBFxiRqfEyZ6rdpLUY=", "dev": true, "license": "MIT", "dependencies": {"fast-glob": "^3.3.3", "minimatch": "^10.0.1", "path-browserify": "^1.0.1"}}, "node_modules/@ts-morph/common/node_modules/minimatch": {"version": "10.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minimatch/-/minimatch-10.0.3.tgz", "integrity": "sha1-z3oDFKFsTZq3OncwoOjjw1AtR6o=", "dev": true, "license": "ISC", "dependencies": {"@isaacs/brace-expansion": "^5.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.28.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/babel__traverse/-/babel__traverse-7.28.0.tgz", "integrity": "sha1-B9cT1szg0mXJhJ2wy+YtP2Hzb3Q=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.2"}}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/estree/-/estree-1.0.8.tgz", "integrity": "sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=", "dev": true, "license": "MIT"}, "node_modules/@types/hoist-non-react-statics": {"version": "3.3.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/hoist-non-react-statics/-/hoist-non-react-statics-3.3.7.tgz", "integrity": "sha1-MG46OnOChSLvoTQRWdpIRudXOmw=", "license": "MIT", "dependencies": {"hoist-non-react-statics": "^3.3.0"}, "peerDependencies": {"@types/react": "*"}}, "node_modules/@types/js-cookie": {"version": "3.0.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/js-cookie/-/js-cookie-3.0.6.tgz", "integrity": "sha1-oEyhnod2h71En1rTfTOxBLcf35U=", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.17.20", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/lodash/-/lodash-4.17.20.tgz", "integrity": "sha1-HKdzYdc2NDLSn15VlQ2eweHG6pM=", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "24.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/node/-/node-24.3.0.tgz", "integrity": "sha1-ibCfRcuajuaUZvGO5YZOTD64Tew=", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~7.10.0"}}, "node_modules/@types/parse-json": {"version": "4.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/parse-json/-/parse-json-4.0.2.tgz", "integrity": "sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=", "license": "MIT"}, "node_modules/@types/react": {"version": "19.1.12", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/react/-/react-19.1.12.tgz", "integrity": "sha1-e/qnaqu7C0/gSTwho6epPTPok3s=", "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "19.1.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/react-dom/-/react-dom-19.1.9.tgz", "integrity": "sha1-WraV/OHoBBhHZ5MjZa5lacEbS0s=", "dev": true, "license": "MIT", "peerDependencies": {"@types/react": "^19.0.0"}}, "node_modules/@types/react-stickynode": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/react-stickynode/-/react-stickynode-4.0.3.tgz", "integrity": "sha1-d+WSz4RZAxlkhngRdCT5IeXRtNo=", "dev": true, "license": "MIT", "dependencies": {"@types/react": "*"}}, "node_modules/@types/react-transition-group": {"version": "4.4.12", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/react-transition-group/-/react-transition-group-4.4.12.tgz", "integrity": "sha1-tddlaEhbAqMHI4Jwv+lstR7ioEQ=", "license": "MIT", "peerDependencies": {"@types/react": "*"}}, "node_modules/@types/redux-devtools-extension": {"version": "2.13.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/redux-devtools-extension/-/redux-devtools-extension-2.13.0.tgz", "integrity": "sha1-47pllm6F4NHXLLUHhfml25aU2so=", "dev": true, "license": "MIT", "dependencies": {"redux": "^3.6.0"}}, "node_modules/@types/redux-devtools-extension/node_modules/redux": {"version": "3.7.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/redux/-/redux-3.7.2.tgz", "integrity": "sha1-BrcxIyFZAdJdBlvjQusCa8HIU3s=", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^1.0.3"}}, "node_modules/@types/redux-persist": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/redux-persist/-/redux-persist-4.0.0.tgz", "integrity": "sha1-nJ8p7vB54sNu1bAhQaEbEXhUpRQ=", "dev": true, "license": "MIT", "dependencies": {"redux": "^3.6.0"}}, "node_modules/@types/redux-persist/node_modules/redux": {"version": "3.7.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/redux/-/redux-3.7.2.tgz", "integrity": "sha1-BrcxIyFZAdJdBlvjQusCa8HIU3s=", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.2.1", "lodash-es": "^4.2.1", "loose-envify": "^1.1.0", "symbol-observable": "^1.0.3"}}, "node_modules/@types/stylis": {"version": "4.2.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/stylis/-/stylis-4.2.5.tgz", "integrity": "sha1-HapkVvQJWdBhV2mKZTqasKcCgd8=", "license": "MIT"}, "node_modules/@types/use-sync-external-store": {"version": "0.0.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz", "integrity": "sha1-YL6NIbqrjDBRMuucuRLtSXhSqtw=", "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.42.0.tgz", "integrity": "sha1-IXLQSWxC7ujHKUtmYWgRAJU/qI8=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.42.0", "@typescript-eslint/type-utils": "8.42.0", "@typescript-eslint/utils": "8.42.0", "@typescript-eslint/visitor-keys": "8.42.0", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.42.0", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ignore/-/ignore-7.0.5.tgz", "integrity": "sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/parser/-/parser-8.42.0.tgz", "integrity": "sha1-IOpm9IZ5gftbtiy+FFQlD8SkQKs=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.42.0", "@typescript-eslint/types": "8.42.0", "@typescript-eslint/typescript-estree": "8.42.0", "@typescript-eslint/visitor-keys": "8.42.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/project-service": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/project-service/-/project-service-8.42.0.tgz", "integrity": "sha1-Y26zQYtsQsmFVNzohJQ3CL9BpYM=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.42.0", "@typescript-eslint/types": "^8.42.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-8.42.0.tgz", "integrity": "sha1-NgFnV7yFtG6kK65Hth+UIe3e3eM=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.42.0", "@typescript-eslint/visitor-keys": "8.42.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/tsconfig-utils": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.42.0.tgz", "integrity": "sha1-IaPnQ5b9dEP/kwvEGyd4m6fpI24=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/type-utils/-/type-utils-8.42.0.tgz", "integrity": "sha1-1nM+ep+99a9gwJxgON/94T9OQlM=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.42.0", "@typescript-eslint/typescript-estree": "8.42.0", "@typescript-eslint/utils": "8.42.0", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/types/-/types-8.42.0.tgz", "integrity": "sha1-rhXAnOvaIEc3cpAgMzKOhzctsAg=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-8.42.0.tgz", "integrity": "sha1-WTw6+H1EYiUsDXI50XILhKG1aGQ=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/project-service": "8.42.0", "@typescript-eslint/tsconfig-utils": "8.42.0", "@typescript-eslint/types": "8.42.0", "@typescript-eslint/visitor-keys": "8.42.0", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/utils": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/utils/-/utils-8.42.0.tgz", "integrity": "sha1-lfjgxpf/L32l9y4WE1AR+HjYFcA=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.42.0", "@typescript-eslint/types": "8.42.0", "@typescript-eslint/typescript-estree": "8.42.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-8.42.0.tgz", "integrity": "sha1-h8bKqhrDB7xzqHwfxGn4jwFi8n4=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.42.0", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@vitejs/plugin-react": {"version": "4.7.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@vitejs/plugin-react/-/plugin-react-4.7.0.tgz", "integrity": "sha1-ZHr057t1rTrdV452KtmEuQ9KJLk=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.27", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/acorn/-/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-regex": {"version": "6.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ansi-regex/-/ansi-regex-6.2.0.tgz", "integrity": "sha1-LzAudVBDGxt3YnBf/7Us8f+iBEc=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/argparse/-/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "dev": true, "license": "Python-2.0"}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz", "integrity": "sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/array-includes/-/array-includes-3.1.9.tgz", "integrity": "sha1-HwzKoI6Qzbw+tDMhD5A60PF8Pzo=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.24.0", "es-object-atoms": "^1.1.1", "get-intrinsic": "^1.3.0", "is-string": "^1.1.1", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlast": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/array.prototype.findlast/-/array.prototype.findlast-1.2.5.tgz", "integrity": "sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flat": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz", "integrity": "sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz", "integrity": "sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.tosorted": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/array.prototype.tosorted/-/array.prototype.tosorted-1.1.4.tgz", "integrity": "sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3", "es-errors": "^1.3.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz", "integrity": "sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/async-function": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/async-function/-/async-function-1.0.0.tgz", "integrity": "sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/attr-accept": {"version": "2.2.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/attr-accept/-/attr-accept-2.2.5.tgz", "integrity": "sha1-1wYdlY5tT5e/hmXGi3WFGgcTq14=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "integrity": "sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=", "dev": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/babel-plugin-macros": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz", "integrity": "sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5", "cosmiconfig": "^7.0.0", "resolve": "^1.19.0"}, "engines": {"node": ">=10", "npm": ">=6"}}, "node_modules/babel-plugin-macros/node_modules/resolve": {"version": "1.22.10", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/resolve/-/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/blacklist": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/blacklist/-/blacklist-1.1.4.tgz", "integrity": "sha1-st0J1hd2JbLKppg1o3somV+povI=", "license": "MIT"}, "node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/braces/-/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/browserslist/-/browserslist-4.25.4.tgz", "integrity": "sha1-690OHRzzkRg0urOmzXuRfZur9a8=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001737", "electron-to-chromium": "^1.5.211", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bulma": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/bulma/-/bulma-1.0.4.tgz", "integrity": "sha1-lC3AF6OiAfqfDgyNs91S88/4ZxI=", "license": "MIT"}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha1-S1QowiK+mF15w9gmV0edvgtZstY=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha1-I43pNdKippKSjFOMfM+pEGf9Bio=", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/callsites/-/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelize": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/camelize/-/camelize-1.0.1.tgz", "integrity": "sha1-ibfhaIQFYzGjXWta0GQzLJHapsM=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/caniuse-lite": {"version": "1.0.30001739", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001739.tgz", "integrity": "sha1-s0zi1Wv8IvQ1KyrwFEEC1iOhJPQ=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/classnames": {"version": "2.5.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/classnames/-/classnames-2.5.1.tgz", "integrity": "sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=", "license": "MIT"}, "node_modules/clsx": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/clsx/-/clsx-2.1.1.tgz", "integrity": "sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/code-block-writer": {"version": "13.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/code-block-writer/-/code-block-writer-13.0.3.tgz", "integrity": "sha1-kPioR2OlAS2nr2ExndY4ZVrpC1s=", "dev": true, "license": "MIT"}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/color-name/-/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/cookie/-/cookie-1.0.2.tgz", "integrity": "sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/cosmiconfig": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/cosmiconfig/-/cosmiconfig-7.1.0.tgz", "integrity": "sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/cosmiconfig/node_modules/yaml": {"version": "1.10.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/yaml/-/yaml-1.10.2.tgz", "integrity": "sha1-IwHF/78StGfejaIzOkWeKeeSDks=", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/css-color-keywords": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/css-color-keywords/-/css-color-keywords-1.0.0.tgz", "integrity": "sha1-/qJhbcZ2spYmhrOvjb2+GAskTgU=", "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/css-to-react-native": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/css-to-react-native/-/css-to-react-native-3.2.0.tgz", "integrity": "sha1-zdgJn3ECThSeT2/hen1G7NVfHjI=", "license": "MIT", "dependencies": {"camelize": "^1.0.0", "css-color-keywords": "^1.0.0", "postcss-value-parser": "^4.0.2"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/csstype/-/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "license": "MIT"}, "node_modules/data-view-buffer": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/data-view-buffer/-/data-view-buffer-1.0.2.tgz", "integrity": "sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz", "integrity": "sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz", "integrity": "sha1-BoMH+bcat2274QKROJ4CCFZgYZE=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/date-fns": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/date-fns/-/date-fns-4.1.0.tgz", "integrity": "sha1-ZLPYP/9aqAQ49bGmM8LoO4ocLRQ=", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/debug/-/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.6.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/decimal.js/-/decimal.js-10.6.0.tgz", "integrity": "sha1-5kmkPjq5U6chkv9Zg4ZeUJ837Zo=", "license": "MIT"}, "node_modules/decode-uri-component": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/decode-uri-component/-/decode-uri-component-0.4.1.tgz", "integrity": "sha1-KsSFlmPHBL4iv323YKFJSkmrLMU=", "license": "MIT", "engines": {"node": ">=14.16"}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/deepmerge": {"version": "2.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/deepmerge/-/deepmerge-2.2.1.tgz", "integrity": "sha1-XT/yKgHAD2RUBaL7wX0HeKGAEXA=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/doctrine": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/doctrine/-/doctrine-2.1.0.tgz", "integrity": "sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/dom-helpers": {"version": "5.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/dom-helpers/-/dom-helpers-5.2.1.tgz", "integrity": "sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.7", "csstype": "^3.0.2"}}, "node_modules/dom-serializer": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/dom-serializer/-/dom-serializer-2.0.0.tgz", "integrity": "sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=", "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/dom-serializer/node_modules/entities": {"version": "4.5.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/entities/-/entities-4.5.0.tgz", "integrity": "sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "5.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/domhandler/-/domhandler-5.0.3.tgz", "integrity": "sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.3.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "3.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/domutils/-/domutils-3.2.2.tgz", "integrity": "sha1-7b/itmiwwdl8JLrw8QYrEyIhvHg=", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha1-165mfh3INIL4tw/Q9u78UNow9Yo=", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "integrity": "sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.212", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.212.tgz", "integrity": "sha1-m1QfkNfYQVzOqU1L5LuG5z4/lUc=", "dev": true, "license": "ISC"}, "node_modules/emoji-regex": {"version": "9.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/emoji-regex/-/emoji-regex-9.2.2.tgz", "integrity": "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=", "license": "MIT"}, "node_modules/entities": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/entities/-/entities-6.0.1.tgz", "integrity": "sha1-wow0pDN5yn9h0HQTCy9fcCCjBpQ=", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-abstract": {"version": "1.24.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-abstract/-/es-abstract-1.24.0.tgz", "integrity": "sha1-xEcy0r6wrMHtYN+ECGnjEG568yg=", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-negative-zero": "^2.0.3", "is-regex": "^1.2.1", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "stop-iteration-iterator": "^1.1.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-iterator-helpers": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-iterator-helpers/-/es-iterator-helpers-1.2.1.tgz", "integrity": "sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-set-tostringtag": "^2.0.3", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.6", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "iterator.prototype": "^1.1.4", "safe-array-concat": "^1.1.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha1-HE8sSDcydZfOadLKGQp/3RcjOME=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha1-8x274MGDsAptJutjJcgQwP0YvU0=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz", "integrity": "sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/es-to-primitive/-/es-to-primitive-1.3.0.tgz", "integrity": "sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/esbuild": {"version": "0.25.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/esbuild/-/esbuild-0.25.9.tgz", "integrity": "sha1-FauOOa5s3GTCT/iiwK71s/2fqXY=", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.9", "@esbuild/android-arm": "0.25.9", "@esbuild/android-arm64": "0.25.9", "@esbuild/android-x64": "0.25.9", "@esbuild/darwin-arm64": "0.25.9", "@esbuild/darwin-x64": "0.25.9", "@esbuild/freebsd-arm64": "0.25.9", "@esbuild/freebsd-x64": "0.25.9", "@esbuild/linux-arm": "0.25.9", "@esbuild/linux-arm64": "0.25.9", "@esbuild/linux-ia32": "0.25.9", "@esbuild/linux-loong64": "0.25.9", "@esbuild/linux-mips64el": "0.25.9", "@esbuild/linux-ppc64": "0.25.9", "@esbuild/linux-riscv64": "0.25.9", "@esbuild/linux-s390x": "0.25.9", "@esbuild/linux-x64": "0.25.9", "@esbuild/netbsd-arm64": "0.25.9", "@esbuild/netbsd-x64": "0.25.9", "@esbuild/openbsd-arm64": "0.25.9", "@esbuild/openbsd-x64": "0.25.9", "@esbuild/openharmony-arm64": "0.25.9", "@esbuild/sunos-x64": "0.25.9", "@esbuild/win32-arm64": "0.25.9", "@esbuild/win32-ia32": "0.25.9", "@esbuild/win32-x64": "0.25.9"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/escalade/-/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.34.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint/-/eslint-9.34.0.tgz", "integrity": "sha1-DqHywbXRZx248BqmuM5yIwIBb3s=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.21.0", "@eslint/config-helpers": "^0.3.1", "@eslint/core": "^0.15.2", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.34.0", "@eslint/plugin-kit": "^0.3.5", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.4.0", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-config-prettier": {"version": "10.1.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-config-prettier/-/eslint-config-prettier-10.1.8.tgz", "integrity": "sha1-FXNM5K+MJ3jMMvCwGzewtc0ey5c=", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "funding": {"url": "https://opencollective.com/eslint-config-prettier"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-prettier": {"version": "5.5.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.5.4.tgz", "integrity": "sha1-nWHE6hHeWvcE1O3xCMgsz6fy5hw=", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-plugin-prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-plugin-react": {"version": "7.37.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-plugin-react/-/eslint-plugin-react-7.37.5.tgz", "integrity": "sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.8", "array.prototype.findlast": "^1.2.5", "array.prototype.flatmap": "^1.3.3", "array.prototype.tosorted": "^1.1.4", "doctrine": "^2.1.0", "es-iterator-helpers": "^1.2.1", "estraverse": "^5.3.0", "hasown": "^2.0.2", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.9", "object.fromentries": "^2.0.8", "object.values": "^1.2.1", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.5", "semver": "^6.3.1", "string.prototype.matchall": "^4.0.12", "string.prototype.repeat": "^1.0.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"}}, "node_modules/eslint-plugin-react-hooks": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz", "integrity": "sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0"}}, "node_modules/eslint-plugin-react-refresh": {"version": "0.4.20", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz", "integrity": "sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=", "dev": true, "license": "MIT", "peerDependencies": {"eslint": ">=8.40"}}, "node_modules/eslint-plugin-react/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint-plugin-react/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/eslint-plugin-react/node_modules/semver": {"version": "6.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-scope": {"version": "8.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-scope/-/eslint-scope-8.4.0.tgz", "integrity": "sha1-iOZGogf61hQ2/6OetQUUcgBlXII=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "4.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "integrity": "sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/espree": {"version": "10.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/espree/-/espree-10.4.0.tgz", "integrity": "sha1-1U9JSdRikAWh+haNk3w/8ffiqDc=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/esquery/-/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/esutils/-/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/eventemitter3": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/eventemitter3/-/eventemitter3-5.0.1.tgz", "integrity": "sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=", "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz", "integrity": "sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fastq/-/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "integrity": "sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/file-selector": {"version": "2.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/file-selector/-/file-selector-2.1.2.tgz", "integrity": "sha1-/nx+6eVQlS37yGPXOxTcdA196LQ=", "license": "MIT", "dependencies": {"tslib": "^2.7.0"}, "engines": {"node": ">= 12"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/filter-obj": {"version": "5.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/filter-obj/-/filter-obj-5.1.0.tgz", "integrity": "sha1-W9iWdgAKcT19suGX9mAnRCjlJO0=", "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-root": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/find-root/-/find-root-1.1.0.tgz", "integrity": "sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=", "license": "MIT"}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/find-up/-/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/flat-cache/-/flat-cache-4.0.1.tgz", "integrity": "sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatpickr": {"version": "4.5.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/flatpickr/-/flatpickr-4.5.7.tgz", "integrity": "sha1-bvwNk8ZVR6p3KUIFxngw66vjVlw=", "license": "MIT"}, "node_modules/flatted": {"version": "3.3.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/flatted/-/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true, "license": "ISC"}, "node_modules/for-each": {"version": "0.3.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/for-each/-/for-each-0.3.5.tgz", "integrity": "sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/foreground-child": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/foreground-child/-/foreground-child-3.3.1.tgz", "integrity": "sha1-Mujp7Rtoo0l777msK2rfkqY4V28=", "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/formik": {"version": "2.4.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/formik/-/formik-2.4.6.tgz", "integrity": "sha1-TadcqA8agnqzWwj9mNWnbpKMloY=", "funding": [{"type": "individual", "url": "https://opencollective.com/formik"}], "license": "Apache-2.0", "dependencies": {"@types/hoist-non-react-statics": "^3.3.1", "deepmerge": "^2.1.1", "hoist-non-react-statics": "^3.3.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "react-fast-compare": "^2.0.1", "tiny-warning": "^1.0.2", "tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0"}}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/function.prototype.name/-/function.prototype.name-1.1.8.tgz", "integrity": "sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/functions-have-names/-/functions-have-names-1.2.3.tgz", "integrity": "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/get-symbol-description/-/get-symbol-description-1.1.0.tgz", "integrity": "sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-tsconfig": {"version": "4.10.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/get-tsconfig/-/get-tsconfig-4.10.1.tgz", "integrity": "sha1-00wcAfR9ZaYGw3qnoXe8PlarSy4=", "dev": true, "license": "MIT", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "funding": {"url": "https://github.com/privatenumber/get-tsconfig?sponsor=1"}}, "node_modules/glob": {"version": "11.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/glob/-/glob-11.0.3.tgz", "integrity": "sha1-nYCH5tct2zxHB7HSd4+A6j6u/NY=", "license": "ISC", "dependencies": {"foreground-child": "^3.3.1", "jackspeak": "^4.1.1", "minimatch": "^10.0.3", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^2.0.0"}, "bin": {"glob": "dist/esm/bin.mjs"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob/node_modules/minimatch": {"version": "10.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minimatch/-/minimatch-10.0.3.tgz", "integrity": "sha1-z3oDFKFsTZq3OncwoOjjw1AtR6o=", "license": "ISC", "dependencies": {"@isaacs/brace-expansion": "^5.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globals": {"version": "16.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/globals/-/globals-16.3.0.tgz", "integrity": "sha1-ZhGOdl3a+eLYgPfhdlhUP5Px9mc=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/globalthis/-/globalthis-1.0.4.tgz", "integrity": "sha1-dDDtOpddl7+1m8zkH1yruvplEjY=", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/gopd/-/gopd-1.2.0.tgz", "integrity": "sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/graphemer/-/graphemer-1.4.0.tgz", "integrity": "sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=", "dev": true, "license": "MIT"}, "node_modules/has-bigints": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/has-bigints/-/has-bigints-1.1.0.tgz", "integrity": "sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/has-proto/-/has-proto-1.2.0.tgz", "integrity": "sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha1-/JxqeDoISVHQuXH+EBjegTcHozg=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/hasown/-/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hoist-non-react-statics": {"version": "3.3.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "integrity": "sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"react-is": "^16.7.0"}}, "node_modules/html-dom-parser": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/html-dom-parser/-/html-dom-parser-5.1.1.tgz", "integrity": "sha1-nvss+gVfanHeG7LwfFsBnbUAT54=", "license": "MIT", "dependencies": {"domhandler": "5.0.3", "htmlparser2": "10.0.0"}}, "node_modules/html-react-parser": {"version": "5.2.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/html-react-parser/-/html-react-parser-5.2.6.tgz", "integrity": "sha1-VHqIIL5cdrwqEPC7Mt2RGdRAQ4s=", "license": "MIT", "dependencies": {"domhandler": "5.0.3", "html-dom-parser": "5.1.1", "react-property": "2.0.2", "style-to-js": "1.1.17"}, "peerDependencies": {"@types/react": "0.14 || 15 || 16 || 17 || 18 || 19", "react": "0.14 || 15 || 16 || 17 || 18 || 19"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/htmlparser2": {"version": "10.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/htmlparser2/-/htmlparser2-10.0.0.tgz", "integrity": "sha1-d60kkDe2a/jMmcbihu9zuDrrYh0=", "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.2.1", "entities": "^6.0.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ignore/-/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immer": {"version": "10.1.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/immer/-/immer-10.1.3.tgz", "integrity": "sha1-44oLl9tZlJ0x2bOBsEwuRBscN0c=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/immutable": {"version": "5.1.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/immutable/-/immutable-5.1.3.tgz", "integrity": "sha1-5khmlMi3bDfAY8ypI5n6ZAmGNNQ=", "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inline-style-parser": {"version": "0.2.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/inline-style-parser/-/inline-style-parser-0.2.4.tgz", "integrity": "sha1-9K9f5y5hKDn81FPZiaWGVm1pXyI=", "license": "MIT"}, "node_modules/internal-slot": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/internal-slot/-/internal-slot-1.1.0.tgz", "integrity": "sha1-HqyRdilH0vcFa8g42T4TsulgSWE=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/intl-messageformat": {"version": "10.7.16", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/intl-messageformat/-/intl-messageformat-10.7.16.tgz", "integrity": "sha1-2Qn5+fSrhX++aB1Vm5WN1N2fZlo=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "@formatjs/fast-memoize": "2.2.7", "@formatjs/icu-messageformat-parser": "2.11.2", "tslib": "^2.8.0"}}, "node_modules/is-array-buffer": {"version": "3.0.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-array-buffer/-/is-array-buffer-3.0.5.tgz", "integrity": "sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "license": "MIT"}, "node_modules/is-async-function": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-async-function/-/is-async-function-2.1.1.tgz", "integrity": "sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=", "dev": true, "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-bigint/-/is-bigint-1.1.0.tgz", "integrity": "sha1-3aejRF31ekJYPbQihoLrp8QXBnI=", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-boolean-object": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-boolean-object/-/is-boolean-object-1.2.2.tgz", "integrity": "sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-view": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-data-view/-/is-data-view-1.0.2.tgz", "integrity": "sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-date-object/-/is-date-object-1.1.0.tgz", "integrity": "sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz", "integrity": "sha1-7v3NxslN3QZ02chYh7+T+USpfJA=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-generator-function/-/is-generator-function-1.1.0.tgz", "integrity": "sha1-vz7tqTEgE5T1e126KAD5GiODCco=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-map": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-map/-/is-map-2.0.3.tgz", "integrity": "sha1-7elrf+HicLPERl46RlZYdkkm1i4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negative-zero": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-negative-zero/-/is-negative-zero-2.0.3.tgz", "integrity": "sha1-ztkDoCespjgbd3pXQwadc3akl0c=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-number-object/-/is-number-object-1.1.1.tgz", "integrity": "sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-plain-object": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-plain-object/-/is-plain-object-5.0.0.tgz", "integrity": "sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-regex/-/is-regex-1.2.1.tgz", "integrity": "sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-set/-/is-set-2.0.3.tgz", "integrity": "sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz", "integrity": "sha1-m2eES9m38ka6BwjDqT40Jpx3T28=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-string": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-string/-/is-string-1.1.1.tgz", "integrity": "sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-symbol/-/is-symbol-1.1.1.tgz", "integrity": "sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-typed-array/-/is-typed-array-1.1.15.tgz", "integrity": "sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakmap": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-weakmap/-/is-weakmap-2.0.2.tgz", "integrity": "sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-weakref/-/is-weakref-1.1.1.tgz", "integrity": "sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/is-weakset/-/is-weakset-2.0.4.tgz", "integrity": "sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isarray": {"version": "2.0.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/isarray/-/isarray-2.0.5.tgz", "integrity": "sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "license": "ISC"}, "node_modules/iterator.prototype": {"version": "1.1.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/iterator.prototype/-/iterator.prototype-1.1.5.tgz", "integrity": "sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "get-proto": "^1.0.0", "has-symbols": "^1.1.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/jackspeak": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/jackspeak/-/jackspeak-4.1.1.tgz", "integrity": "sha1-lodgMPRQUCBH/H6Mf8+M6BJOQ64=", "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/js-cookie": {"version": "3.0.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/js-cookie/-/js-cookie-3.0.5.tgz", "integrity": "sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=", "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=", "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsx-ast-utils": {"version": "3.3.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz", "integrity": "sha1-R2a9BajioRryIr7NGeFVdeUqhTo=", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "object.assign": "^4.1.4", "object.values": "^1.1.6"}, "engines": {"node": ">=4.0"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/keyv/-/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kf-bui": {"version": "4.5.15", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/kf-bui/-/kf-bui-4.5.15.tgz", "integrity": "sha1-nVZpcFEr27IetgR5ujnYjEx7Jy4=", "license": "UNLICENSED", "dependencies": {"blacklist": "1.1.4", "bulma": "1.0.4", "classnames": "^2.2.6", "date-fns": "^4.1.0", "flatpickr": "4.5.7", "formik": "2.4.6", "immutable": "5.1.3", "prop-types": "^15.7.2", "react-datepicker": "^8.4.0", "react-icons": "^5.5.0", "react-popper": "2.3.0", "react-select": "5.10.1", "react-travel": "1.3.6", "stylis": "^4.3.6", "uuid": "^10.0.0", "warning": "4.0.3"}, "peerDependencies": {"react": "^19.1", "react-dom": "^19.1", "react-router-dom": "^7.6.2", "styled-components": "^6.1.19"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/levn/-/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "license": "MIT"}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true, "license": "MIT"}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/memoize-one": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/memoize-one/-/memoize-one-6.0.0.tgz", "integrity": "sha1-slkbhx7YKUiu5HJ9xqvO7qyMEEU=", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/merge2/-/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/minimatch": {"version": "9.0.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minipass": {"version": "7.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/minipass/-/minipass-7.1.2.tgz", "integrity": "sha1-k6libOXl5mvU24aEnnUV6SNApwc=", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/moment": {"version": "2.30.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/moment/-/moment-2.30.1.tgz", "integrity": "sha1-+MkcB7enhuMMWZJt9TC06slpdK4=", "license": "MIT", "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ms/-/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object-path": {"version": "0.11.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object-path/-/object-path-0.11.8.tgz", "integrity": "sha1-7QAsArvdAHC3iidFXorgH8FNR0I=", "license": "MIT", "engines": {"node": ">= 10.12.0"}}, "node_modules/object-path-immutable": {"version": "4.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object-path-immutable/-/object-path-immutable-4.1.2.tgz", "integrity": "sha1-1441h/A8mkH4PdZGXP71qes5C7Q=", "license": "MIT", "dependencies": {"is-plain-object": "^5.0.0", "object-path": "^0.11.8"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object.assign/-/object.assign-4.1.7.tgz", "integrity": "sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.entries": {"version": "1.1.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object.entries/-/object.entries-1.1.9.tgz", "integrity": "sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-object-atoms": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.fromentries": {"version": "2.0.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object.fromentries/-/object.fromentries-2.0.8.tgz", "integrity": "sha1-9xldipuXvZXLwZmeqTns0aKwDGU=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.values": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/object.values/-/object.values-1.2.1.tgz", "integrity": "sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/optionator/-/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/own-keys": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/own-keys/-/own-keys-1.0.1.tgz", "integrity": "sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "integrity": "sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=", "license": "BlueOak-1.0.0"}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/path-browserify": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/path-browserify/-/path-browserify-1.0.1.tgz", "integrity": "sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/path-key/-/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "license": "MIT"}, "node_modules/path-scurry": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/path-scurry/-/path-scurry-2.0.0.tgz", "integrity": "sha1-nwUiifI62L+Tl6KgQl57hhXFhYA=", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^11.0.0", "minipass": "^7.1.2"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "11.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/lru-cache/-/lru-cache-11.1.0.tgz", "integrity": "sha1-r6+wYGBxCBMtvBz4rmYa+2lIYRc=", "license": "ISC", "engines": {"node": "20 || >=22"}}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/path-type/-/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz", "integrity": "sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.4.49", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/postcss/-/postcss-8.4.49.tgz", "integrity": "sha1-TqR5BIqwWas65h0IIZD6v9mU/hk=", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=", "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.6.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/prettier/-/prettier-3.6.2.tgz", "integrity": "sha1-zNoCoQA+u7K/2m+DoHSXj2CLk5M=", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz", "integrity": "sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/prop-types": {"version": "15.8.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/prop-types/-/prop-types-15.8.1.tgz", "integrity": "sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=", "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "node_modules/property-expr": {"version": "2.0.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/property-expr/-/property-expr-2.0.6.tgz", "integrity": "sha1-93vADVkopsdIQUrRKILoPySuweg=", "license": "MIT"}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/punycode/-/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/query-string": {"version": "9.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/query-string/-/query-string-9.2.2.tgz", "integrity": "sha1-oBBIJO390sHbLxivcc73q/ajsg8=", "license": "MIT", "dependencies": {"decode-uri-component": "^0.4.1", "filter-obj": "^5.1.0", "split-on-first": "^3.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/raf": {"version": "3.4.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/raf/-/raf-3.4.1.tgz", "integrity": "sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=", "license": "MIT", "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/react": {"version": "19.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react/-/react-19.1.1.tgz", "integrity": "sha1-BtkUnsXgg6Z/mh45zpewagO2RK8=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-datepicker": {"version": "8.7.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-datepicker/-/react-datepicker-8.7.0.tgz", "integrity": "sha1-MKcMQ9zHtGyCU+UEwaUcsgyC54I=", "license": "MIT", "dependencies": {"@floating-ui/react": "^0.27.15", "clsx": "^2.1.1", "date-fns": "^4.1.0"}, "peerDependencies": {"react": "^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc", "react-dom": "^16.9.0 || ^17 || ^18 || ^19 || ^19.0.0-rc"}}, "node_modules/react-dom": {"version": "19.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-dom/-/react-dom-19.1.1.tgz", "integrity": "sha1-Laqf9/OuOErrMOdtXuOMBG3ImJM=", "license": "MIT", "dependencies": {"scheduler": "^0.26.0"}, "peerDependencies": {"react": "^19.1.1"}}, "node_modules/react-dropzone": {"version": "14.3.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-dropzone/-/react-dropzone-14.3.8.tgz", "integrity": "sha1-p+qxGPikUv4/ixYtZEVOgbqDBYI=", "license": "MIT", "dependencies": {"attr-accept": "^2.2.4", "file-selector": "^2.1.0", "prop-types": "^15.8.1"}, "engines": {"node": ">= 10.13"}, "peerDependencies": {"react": ">= 16.8 || 18.0.0"}}, "node_modules/react-fast-compare": {"version": "2.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-fast-compare/-/react-fast-compare-2.0.4.tgz", "integrity": "sha1-6EtNRVsP7BE+BALDKTUnFRlvgfk=", "license": "MIT"}, "node_modules/react-hook-form": {"version": "7.62.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-hook-form/-/react-hook-form-7.62.0.tgz", "integrity": "sha1-LYHhPCxrbWNlSORAgYNBynUyGNA=", "license": "MIT", "engines": {"node": ">=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/react-hook-form"}, "peerDependencies": {"react": "^16.8.0 || ^17 || ^18 || ^19"}}, "node_modules/react-icons": {"version": "5.5.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-icons/-/react-icons-5.5.0.tgz", "integrity": "sha1-iqJdNUP/hCMWhdMzEWTAApnN+vI=", "license": "MIT", "peerDependencies": {"react": "*"}}, "node_modules/react-intersection-observer": {"version": "9.16.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-intersection-observer/-/react-intersection-observer-9.16.0.tgz", "integrity": "sha1-c3bVTtxHKTMAlhAQhE1TsnPuD7k=", "license": "MIT", "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}}, "node_modules/react-intl": {"version": "7.1.11", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-intl/-/react-intl-7.1.11.tgz", "integrity": "sha1-YVVgLEZiGtm2fdMXUNAJCOHgtRY=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@formatjs/ecma402-abstract": "2.3.4", "@formatjs/icu-messageformat-parser": "2.11.2", "@formatjs/intl": "3.1.6", "@types/hoist-non-react-statics": "^3.3.1", "@types/react": "16 || 17 || 18 || 19", "hoist-non-react-statics": "^3.3.2", "intl-messageformat": "10.7.16", "tslib": "^2.8.0"}, "peerDependencies": {"react": "16 || 17 || 18 || 19", "typescript": "^5.6.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/react-is": {"version": "16.13.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-is/-/react-is-16.13.1.tgz", "integrity": "sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=", "license": "MIT"}, "node_modules/react-popper": {"version": "2.3.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-popper/-/react-popper-2.3.0.tgz", "integrity": "sha1-F4kcYg4TINzjGLrZ/t5GpfcccLo=", "license": "MIT", "dependencies": {"react-fast-compare": "^3.0.1", "warning": "^4.0.2"}, "peerDependencies": {"@popperjs/core": "^2.0.0", "react": "^16.8.0 || ^17 || ^18", "react-dom": "^16.8.0 || ^17 || ^18"}}, "node_modules/react-popper/node_modules/react-fast-compare": {"version": "3.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-fast-compare/-/react-fast-compare-3.2.2.tgz", "integrity": "sha1-kpqXpTIwTOn+5LyuRCNPHOLCHUk=", "license": "MIT"}, "node_modules/react-property": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-property/-/react-property-2.0.2.tgz", "integrity": "sha1-1ayeJEzvVkiAphC8jYaL1vYP3aY=", "license": "MIT"}, "node_modules/react-redux": {"version": "9.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-redux/-/react-redux-9.2.0.tgz", "integrity": "sha1-lsOrI/uaOvLLRlS+S1HJieMjZvU=", "license": "MIT", "dependencies": {"@types/use-sync-external-store": "^0.0.6", "use-sync-external-store": "^1.4.0"}, "peerDependencies": {"@types/react": "^18.2.25 || ^19", "react": "^18.0 || ^19", "redux": "^5.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "redux": {"optional": true}}}, "node_modules/react-refresh": {"version": "0.17.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-refresh/-/react-refresh-0.17.0.tgz", "integrity": "sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-router": {"version": "7.8.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-router/-/react-router-7.8.2.tgz", "integrity": "sha1-nS1BR8pygyxVCsxg7WiAYtGPcLg=", "license": "MIT", "dependencies": {"cookie": "^1.0.1", "set-cookie-parser": "^2.6.0"}, "engines": {"node": ">=20.0.0"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}}, "node_modules/react-router-dom": {"version": "7.8.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-router-dom/-/react-router-dom-7.8.2.tgz", "integrity": "sha1-Jaj8NliBibrzu7XjYMj//70r6rw=", "license": "MIT", "dependencies": {"react-router": "7.8.2"}, "engines": {"node": ">=20.0.0"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}}, "node_modules/react-select": {"version": "5.10.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-select/-/react-select-5.10.1.tgz", "integrity": "sha1-6FjdmDWMzYZLZdU6sPtoLNXpa4k=", "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.0", "@emotion/cache": "^11.4.0", "@emotion/react": "^11.8.1", "@floating-ui/dom": "^1.0.1", "@types/react-transition-group": "^4.4.0", "memoize-one": "^6.0.0", "prop-types": "^15.6.0", "react-transition-group": "^4.3.0", "use-isomorphic-layout-effect": "^1.2.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react-stickynode": {"version": "5.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-stickynode/-/react-stickynode-5.0.2.tgz", "integrity": "sha1-kL4Ica5FMYjyyRh/TFmoCSIg6hI=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"classnames": "^2.0.0", "prop-types": "^15.6.0", "shallowequal": "^1.0.0", "subscribe-ui-event": "^3.0.0"}, "engines": {"node": ">=16", "npm": ">=8.4"}, "peerDependencies": {"react": "^0.14.2 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^0.14.2 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/react-toastify": {"version": "11.0.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-toastify/-/react-toastify-11.0.5.tgz", "integrity": "sha1-zkxC0Q7rQzmIqyJk0+RFxOnRMxM=", "license": "MIT", "dependencies": {"clsx": "^2.1.1"}, "peerDependencies": {"react": "^18 || ^19", "react-dom": "^18 || ^19"}}, "node_modules/react-transition-group": {"version": "4.4.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-transition-group/-/react-transition-group-4.4.5.tgz", "integrity": "sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/runtime": "^7.5.5", "dom-helpers": "^5.0.1", "loose-envify": "^1.4.0", "prop-types": "^15.6.2"}, "peerDependencies": {"react": ">=16.6.0", "react-dom": ">=16.6.0"}}, "node_modules/react-travel": {"version": "1.3.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/react-travel/-/react-travel-1.3.6.tgz", "integrity": "sha1-NDIyoh7J7ggYXQRWjFKm7HsCdZI=", "license": "MIT", "dependencies": {"prop-types": "^15.6.0"}, "peerDependencies": {"react": "0.14.x || ^15.0.0 || ^16.0.0", "react-dom": "0.14.x || ^15.0.0 || ^16.0.0"}}, "node_modules/redux": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/redux/-/redux-5.0.1.tgz", "integrity": "sha1-l/omiBzldGUAElWF1WQsd7bpRHs=", "license": "MIT"}, "node_modules/redux-persist": {"version": "6.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/redux-persist/-/redux-persist-6.0.0.tgz", "integrity": "sha1-tNKXL5hZWXwTDUDUsUb+zatRs6g=", "license": "MIT", "peerDependencies": {"redux": ">4.0.0"}}, "node_modules/redux-thunk": {"version": "3.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/redux-thunk/-/redux-thunk-3.1.0.tgz", "integrity": "sha1-lKpuBJd8MOFOiS6uhJeMGvYFj/M=", "license": "MIT", "peerDependencies": {"redux": "^5.0.0"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz", "integrity": "sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz", "integrity": "sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/reselect": {"version": "5.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/reselect/-/reselect-5.1.1.tgz", "integrity": "sha1-x2ax611VgpHl5VApitsL7MJLty4=", "license": "MIT"}, "node_modules/resolve": {"version": "2.0.0-next.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/resolve/-/resolve-2.0.0-next.5.tgz", "integrity": "sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-pkg-maps": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz", "integrity": "sha1-YWs9wsVwVrVYjDHN9LPWTbEzcg8=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/reusify/-/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rollup": {"version": "4.50.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/rollup/-/rollup-4.50.0.tgz", "integrity": "sha1-byN/WYtxY+3jPOgnr4U0ySmqoYY=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.8"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.50.0", "@rollup/rollup-android-arm64": "4.50.0", "@rollup/rollup-darwin-arm64": "4.50.0", "@rollup/rollup-darwin-x64": "4.50.0", "@rollup/rollup-freebsd-arm64": "4.50.0", "@rollup/rollup-freebsd-x64": "4.50.0", "@rollup/rollup-linux-arm-gnueabihf": "4.50.0", "@rollup/rollup-linux-arm-musleabihf": "4.50.0", "@rollup/rollup-linux-arm64-gnu": "4.50.0", "@rollup/rollup-linux-arm64-musl": "4.50.0", "@rollup/rollup-linux-loongarch64-gnu": "4.50.0", "@rollup/rollup-linux-ppc64-gnu": "4.50.0", "@rollup/rollup-linux-riscv64-gnu": "4.50.0", "@rollup/rollup-linux-riscv64-musl": "4.50.0", "@rollup/rollup-linux-s390x-gnu": "4.50.0", "@rollup/rollup-linux-x64-gnu": "4.50.0", "@rollup/rollup-linux-x64-musl": "4.50.0", "@rollup/rollup-openharmony-arm64": "4.50.0", "@rollup/rollup-win32-arm64-msvc": "4.50.0", "@rollup/rollup-win32-ia32-msvc": "4.50.0", "@rollup/rollup-win32-x64-msvc": "4.50.0", "fsevents": "~2.3.2"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/safe-array-concat/-/safe-array-concat-1.1.3.tgz", "integrity": "sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-push-apply": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/safe-push-apply/-/safe-push-apply-1.0.0.tgz", "integrity": "sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "integrity": "sha1-f4fftnoxUHguqvGFg/9dFxGsEME=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/scheduler": {"version": "0.26.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/scheduler/-/scheduler-0.26.0.tgz", "integrity": "sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc=", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/semver/-/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/set-cookie-parser": {"version": "2.7.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz", "integrity": "sha1-MBbxUAciAt++kPre4FNXPMidKUM=", "license": "MIT"}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/set-function-name/-/set-function-name-2.0.2.tgz", "integrity": "sha1-FqcFxaDcL15jjKltiozU4cK5CYU=", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-proto": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/set-proto/-/set-proto-1.0.0.tgz", "integrity": "sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/shallowequal": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/shallowequal/-/shallowequal-1.1.0.tgz", "integrity": "sha1-GI1SHelbkIdAT9TctosT3wrk5/g=", "license": "MIT"}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "4.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/signal-exit/-/signal-exit-4.1.0.tgz", "integrity": "sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=", "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/source-map": {"version": "0.5.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/split-on-first": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/split-on-first/-/split-on-first-3.0.0.tgz", "integrity": "sha1-8ElZyeqBAbmwu/NaYbnr6nhKI+c=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/stop-iteration-iterator": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz", "integrity": "sha1-9IH/cKVI9hJNAxLDqhTL+nqlQq0=", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "internal-slot": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/string-width": {"version": "5.1.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string-width/-/string-width-5.1.2.tgz", "integrity": "sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=", "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/string-width-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string.prototype.matchall": {"version": "4.0.12", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz", "integrity": "sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "regexp.prototype.flags": "^1.5.3", "set-function-name": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.repeat": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string.prototype.repeat/-/string.prototype.repeat-1.0.0.tgz", "integrity": "sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "node_modules/string.prototype.trim": {"version": "1.2.10", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz", "integrity": "sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz", "integrity": "sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz", "integrity": "sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "7.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/style-to-js": {"version": "1.1.17", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/style-to-js/-/style-to-js-1.1.17.tgz", "integrity": "sha1-SIsVWKjB/QU1KUPwiMw843aBPYM=", "license": "MIT", "dependencies": {"style-to-object": "1.0.9"}}, "node_modules/style-to-object": {"version": "1.0.9", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/style-to-object/-/style-to-object-1.0.9.tgz", "integrity": "sha1-NcZbcT9Kbboi09DGFDX5ZUI2U/A=", "license": "MIT", "dependencies": {"inline-style-parser": "0.2.4"}}, "node_modules/styled-components": {"version": "6.1.19", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/styled-components/-/styled-components-6.1.19.tgz", "integrity": "sha1-mkG023mjt6JHfa7Kvo3ZFyNSY9Y=", "license": "MIT", "dependencies": {"@emotion/is-prop-valid": "1.2.2", "@emotion/unitless": "0.8.1", "@types/stylis": "4.2.5", "css-to-react-native": "3.2.0", "csstype": "3.1.3", "postcss": "8.4.49", "shallowequal": "1.1.0", "stylis": "4.3.2", "tslib": "2.6.2"}, "engines": {"node": ">= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/styled-components"}, "peerDependencies": {"react": ">= 16.8.0", "react-dom": ">= 16.8.0"}}, "node_modules/styled-components/node_modules/@emotion/unitless": {"version": "0.8.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/@emotion/unitless/-/unitless-0.8.1.tgz", "integrity": "sha1-GCtaRwTvitkb3pP3qGCoj9kseaM=", "license": "MIT"}, "node_modules/styled-components/node_modules/stylis": {"version": "4.3.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/stylis/-/stylis-4.3.2.tgz", "integrity": "sha1-j3a3B3fdU+tmnG9YyZe/Cply5EQ=", "license": "MIT"}, "node_modules/styled-components/node_modules/tslib": {"version": "2.6.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tslib/-/tslib-2.6.2.tgz", "integrity": "sha1-cDrClCXns3zW/UVukkBNRtHz5K4=", "license": "0BSD"}, "node_modules/stylis": {"version": "4.3.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/stylis/-/stylis-4.3.6.tgz", "integrity": "sha1-fHuXGRy08ZXwPsq31S95Au03gyA=", "license": "MIT"}, "node_modules/subscribe-ui-event": {"version": "3.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/subscribe-ui-event/-/subscribe-ui-event-3.0.0.tgz", "integrity": "sha1-GPfD6HpQB7x5VQsE2fD1lid8IOQ=", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"eventemitter3": "^5.0.0", "raf": "^3.0.0"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/symbol-observable": {"version": "1.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/symbol-observable/-/symbol-observable-1.2.0.tgz", "integrity": "sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/synckit": {"version": "0.11.11", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/synckit/-/synckit-0.11.11.tgz", "integrity": "sha1-wLYZzyWKl/qiCRVdnNFpm1yZjLA=", "dev": true, "license": "MIT", "dependencies": {"@pkgr/core": "^0.2.9"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/synckit"}}, "node_modules/tabbable": {"version": "6.2.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tabbable/-/tabbable-6.2.0.tgz", "integrity": "sha1-cy+2K8AXXPzsJXMwvhh9z7ofO5c=", "license": "MIT"}, "node_modules/tiny-case": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tiny-case/-/tiny-case-1.0.3.tgz", "integrity": "sha1-2YDWa8crXVqcqG+3yf/bnImN3QM=", "license": "MIT"}, "node_modules/tiny-warning": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tiny-warning/-/tiny-warning-1.0.3.tgz", "integrity": "sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=", "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tinyglobby/-/tinyglobby-0.2.14.tgz", "integrity": "sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.5.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fdir/-/fdir-6.5.0.tgz", "integrity": "sha1-7Sq5Z6MxreYvGNB32uGSaE1Q01A=", "dev": true, "license": "MIT", "engines": {"node": ">=12.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/picomatch/-/picomatch-4.0.3.tgz", "integrity": "sha1-eWx2E20e6tcV2x57rXhd7daVoEI=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/tinymce": {"version": "8.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tinymce/-/tinymce-8.0.2.tgz", "integrity": "sha1-urqqQMFU0IMrQTMvJfNIW1iHpvI=", "license": "GPL-2.0-or-later"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toposort": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/toposort/-/toposort-2.0.2.tgz", "integrity": "sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=", "license": "MIT"}, "node_modules/ts-api-utils": {"version": "2.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ts-api-utils/-/ts-api-utils-2.1.0.tgz", "integrity": "sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=", "dev": true, "license": "MIT", "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/ts-morph": {"version": "26.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ts-morph/-/ts-morph-26.0.0.tgz", "integrity": "sha1-1DXMrJQh1GFf3ovob+54LxjNn3M=", "dev": true, "license": "MIT", "dependencies": {"@ts-morph/common": "~0.27.0", "code-block-writer": "^13.0.3"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tslib/-/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "license": "0BSD"}, "node_modules/tsx": {"version": "4.20.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/tsx/-/tsx-4.20.5.tgz", "integrity": "sha1-hWyLLxFMUKn0rhCBJpZ6Fn8kDcc=", "dev": true, "license": "MIT", "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "bin": {"tsx": "dist/cli.mjs"}, "engines": {"node": ">=18.0.0"}, "optionalDependencies": {"fsevents": "~2.3.3"}}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/type-check/-/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "2.19.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/type-fest/-/type-fest-2.19.0.tgz", "integrity": "sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz", "integrity": "sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz", "integrity": "sha1-hAegT314aE89JSqhoUPSt3tBYM4=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz", "integrity": "sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.7", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/typed-array-length/-/typed-array-length-1.0.7.tgz", "integrity": "sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/typescript/-/typescript-5.8.3.tgz", "integrity": "sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typescript-eslint": {"version": "8.42.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/typescript-eslint/-/typescript-eslint-8.42.0.tgz", "integrity": "sha1-6S9siFaeICs2HVyhZVrY4zoFVOo=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "8.42.0", "@typescript-eslint/parser": "8.42.0", "@typescript-eslint/typescript-estree": "8.42.0", "@typescript-eslint/utils": "8.42.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/unbox-primitive": {"version": "1.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/unbox-primitive/-/unbox-primitive-1.1.0.tgz", "integrity": "sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/undici-types": {"version": "7.10.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/undici-types/-/undici-types-7.10.0.tgz", "integrity": "sha1-SsLgWM5WtGKwVuYpzGoCOT0/81A=", "dev": true, "license": "MIT"}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/use-isomorphic-layout-effect": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/use-isomorphic-layout-effect/-/use-isomorphic-layout-effect-1.2.1.tgz", "integrity": "sha1-LxGlJWKPVkJFIcdI/qvC/8yWL84=", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "node_modules/use-sync-external-store": {"version": "1.5.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "integrity": "sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/uuid": {"version": "10.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/uuid/-/uuid-10.0.0.tgz", "integrity": "sha1-WpWqRU5uACclx5BV/UKqujDKYpQ=", "funding": ["https://github.com/sponsors/broofa", "https://github.com/sponsors/ctavan"], "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/vite": {"version": "7.1.4", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/vite/-/vite-7.1.4.tgz", "integrity": "sha1-NUlEr/tV4a/wFXQGt04NCjIy35o=", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.5.0", "picomatch": "^4.0.3", "postcss": "^8.5.6", "rollup": "^4.43.0", "tinyglobby": "^0.2.14"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^20.19.0 || >=22.12.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^20.19.0 || >=22.12.0", "jiti": ">=1.21.0", "less": "^4.0.0", "lightningcss": "^1.21.0", "sass": "^1.70.0", "sass-embedded": "^1.70.0", "stylus": ">=0.54.8", "sugarss": "^5.0.0", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite/node_modules/fdir": {"version": "6.5.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/fdir/-/fdir-6.5.0.tgz", "integrity": "sha1-7Sq5Z6MxreYvGNB32uGSaE1Q01A=", "dev": true, "license": "MIT", "engines": {"node": ">=12.0.0"}, "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vite/node_modules/picomatch": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/picomatch/-/picomatch-4.0.3.tgz", "integrity": "sha1-eWx2E20e6tcV2x57rXhd7daVoEI=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/vite/node_modules/postcss": {"version": "8.5.6", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/postcss/-/postcss-8.5.6.tgz", "integrity": "sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/warning": {"version": "4.0.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/warning/-/warning-4.0.3.tgz", "integrity": "sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/which/-/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz", "integrity": "sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/which-builtin-type/-/which-builtin-type-1.2.1.tgz", "integrity": "sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/which-collection/-/which-collection-1.0.2.tgz", "integrity": "sha1-Yn73YkOSChB+fOjpYZHevksWwqA=", "dev": true, "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/which-typed-array/-/which-typed-array-1.1.19.tgz", "integrity": "sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "8.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "integrity": "sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=", "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "license": "MIT"}, "node_modules/wrap-ansi-cjs/node_modules/string-width": {"version": "4.2.3", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/ansi-styles/-/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/yallist/-/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.8.1", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/yaml/-/yaml-2.8.1.tgz", "integrity": "sha1-GHCqArYx9+gyi5P4vFdPrF1sTXk=", "dev": true, "license": "ISC", "optional": true, "peer": true, "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/yup": {"version": "1.7.0", "resolved": "https://pkgs.dev.azure.com/kommuneforlaget/9f980686-c421-4c7e-bd25-a0ba6c9d44d0/_packaging/kf-bui-feed/npm/registry/yup/-/yup-1.7.0.tgz", "integrity": "sha1-XS/uzMFyXDm/7W7Gd8wGIlJ9r68=", "license": "MIT", "dependencies": {"property-expr": "^2.0.5", "tiny-case": "^1.0.3", "toposort": "^2.0.2", "type-fest": "^2.19.0"}}}}