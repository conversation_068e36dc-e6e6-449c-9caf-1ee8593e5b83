package no.kf.handboker.model

case class SearchResult (totalHits: Long, ms: Long, page: Int, pageSize: Int, results: Iterable[SearchHit])

// TODO: Use enums? Enumeratum has better json4s serializing as-of March 2017
trait SearchHit {
  val id: String
  val title: String
  val highlight: Option[String]
  val isHandbook: Boolean = false
  val isChapter: Boolean = false
  val isSection: Boolean = false
}

case class HandbookHit(id: String, title: String, highlight: Option[String], override val isHandbook: Boolean = true) extends SearchHit
case class ChapterHit(id: String, title: String, highlight: Option[String], handbookId: Option[String], override val isChapter: Boolean = true) extends SearchHit
case class SectionHit(id: String, title: String, highlight: Option[String], textHighlight: Option[String], handbookId: Option[String], override val isSection: Boolean = true) extends SearchHit

case class ResultHit(id: String, handbookTitle: String, chapterTitle: String, sectionTitle: String, sectionText: String, 
                     highlight: Option[String], handbookId: String, textHighlight: Option[String], title: String) extends SearchHit
