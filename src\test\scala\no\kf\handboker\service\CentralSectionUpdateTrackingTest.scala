package no.kf.handboker.service

import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.model.central.{CentralHandbook, CentralSection}
import org.joda.time.DateTime
import org.mockito.Matchers.any
import org.mockito.Mockito._
import org.mockito.{ArgumentMatcher, Matchers}
import org.scalatest.{BeforeAndAfterEach, FunSuite}

class CentralSectionUpdateTrackingTest extends FunSuite with DefaultTestDI with BeforeAndAfterEach {

  override val componentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val centralHandbookService = new CentralHandbookServiceImpl
  }

  private val repo = componentRegistry.centralHandbookRepository
  private val service = componentRegistry.centralHandbookService
  private val currentUser = "<EMAIL>"

  override def beforeEach() {
    reset(repo)
  }

  test("That updating only title sets titleUpdatedDate and titleUpdatedBy") {
    val originalSection = CentralSection(
      id = Some("section-id"),
      title = "Original Title",
      parentId = "parent-id",
      centralHandbookId = "handbook-id",
      html = Some("Original HTML"),
      titleUpdatedDate = Some(DateTime.now.minusDays(5)),
      htmlUpdatedDate = Some(DateTime.now.minusDays(3)),
      titleUpdatedBy = Some("original-user"),
      htmlUpdatedBy = Some("original-user")
    )

    val updatedSection = originalSection.copy(title = "New Title")
    val handbook = CentralHandbook(Some("handbook-id"), "Test Handbook")

    when(repo.retrieveCentralHandbook("handbook-id")).thenReturn(Some(handbook))
    when(repo.retrieveCentralSection("section-id")).thenReturn(Some(originalSection))
    when(repo.persistCentralSection(any(), any())).thenReturn(updatedSection)

    val result = service.persistCentralSection(updatedSection, currentUser)

    // Verify that the repository was called with a section that has updated title fields
    verify(repo).persistCentralSection(Matchers.argThat(new ArgumentMatcher[CentralSection] {
      override def matches(section: Any): Boolean = {
        val s = section.asInstanceOf[CentralSection]
        s.titleUpdatedBy.contains(currentUser) &&
        s.htmlUpdatedBy == originalSection.htmlUpdatedBy &&
        s.htmlUpdatedDate == originalSection.htmlUpdatedDate
      }
    }), currentUser)
  }

  test("That updating only HTML sets htmlUpdatedDate and htmlUpdatedBy") {
    val originalSection = CentralSection(
      id = Some("section-id"),
      title = "Original Title",
      parentId = "parent-id",
      centralHandbookId = "handbook-id",
      html = Some("Original HTML"),
      titleUpdatedDate = Some(DateTime.now.minusDays(5)),
      htmlUpdatedDate = Some(DateTime.now.minusDays(3)),
      titleUpdatedBy = Some("original-user"),
      htmlUpdatedBy = Some("original-user")
    )

    val updatedSection = originalSection.copy(html = Some("New HTML"))
    val handbook = CentralHandbook(Some("handbook-id"), "Test Handbook")

    when(repo.retrieveCentralHandbook("handbook-id")).thenReturn(Some(handbook))
    when(repo.retrieveCentralSection("section-id")).thenReturn(Some(originalSection))
    when(repo.persistCentralSection(any(), any())).thenReturn(updatedSection)

    val result = service.persistCentralSection(updatedSection, currentUser)

    // Verify that the repository was called with a section that has updated HTML fields
    verify(repo).persistCentralSection(Matchers.argThat(new ArgumentMatcher[CentralSection] {
      override def matches(section: Any): Boolean = {
        val s = section.asInstanceOf[CentralSection]
        s.htmlUpdatedBy.contains(currentUser) &&
        s.titleUpdatedBy == originalSection.titleUpdatedBy &&
        s.titleUpdatedDate == originalSection.titleUpdatedDate
      }
    }), currentUser)
  }

  test("That updating both title and HTML sets both update fields") {
    val originalSection = CentralSection(
      id = Some("section-id"),
      title = "Original Title",
      parentId = "parent-id",
      centralHandbookId = "handbook-id",
      html = Some("Original HTML"),
      titleUpdatedDate = Some(DateTime.now.minusDays(5)),
      htmlUpdatedDate = Some(DateTime.now.minusDays(3)),
      titleUpdatedBy = Some("original-user"),
      htmlUpdatedBy = Some("original-user")
    )

    val updatedSection = originalSection.copy(title = "New Title", html = Some("New HTML"))
    val handbook = CentralHandbook(Some("handbook-id"), "Test Handbook")

    when(repo.retrieveCentralHandbook("handbook-id")).thenReturn(Some(handbook))
    when(repo.retrieveCentralSection("section-id")).thenReturn(Some(originalSection))
    when(repo.persistCentralSection(any(), any())).thenReturn(updatedSection)

    val result = service.persistCentralSection(updatedSection, currentUser)

    // Verify that the repository was called with a section that has both fields updated
    verify(repo).persistCentralSection(Matchers.argThat(new ArgumentMatcher[CentralSection] {
      override def matches(section: Any): Boolean = {
        val s = section.asInstanceOf[CentralSection]
        s.titleUpdatedBy.contains(currentUser) &&
        s.htmlUpdatedBy.contains(currentUser)
      }
    }), currentUser)
  }

  test("That updating neither title nor HTML preserves original update fields") {
    val originalSection = CentralSection(
      id = Some("section-id"),
      title = "Original Title",
      parentId = "parent-id",
      centralHandbookId = "handbook-id",
      html = Some("Original HTML"),
      titleUpdatedDate = Some(DateTime.now.minusDays(5)),
      htmlUpdatedDate = Some(DateTime.now.minusDays(3)),
      titleUpdatedBy = Some("original-user"),
      htmlUpdatedBy = Some("original-user")
    )

    val updatedSection = originalSection.copy(sortOrder = 999) // Change something else
    val handbook = CentralHandbook(Some("handbook-id"), "Test Handbook")

    when(repo.retrieveCentralHandbook("handbook-id")).thenReturn(Some(handbook))
    when(repo.retrieveCentralSection("section-id")).thenReturn(Some(originalSection))
    when(repo.persistCentralSection(any(), any())).thenReturn(updatedSection)

    val result = service.persistCentralSection(updatedSection, currentUser)

    // Verify that the repository was called with a section that preserves original update fields
    verify(repo).persistCentralSection(Matchers.argThat(new ArgumentMatcher[CentralSection] {
      override def matches(section: Any): Boolean = {
        val s = section.asInstanceOf[CentralSection]
        s.titleUpdatedBy == originalSection.titleUpdatedBy &&
        s.htmlUpdatedBy == originalSection.htmlUpdatedBy &&
        s.titleUpdatedDate == originalSection.titleUpdatedDate &&
        s.htmlUpdatedDate == originalSection.htmlUpdatedDate
      }
    }), currentUser)
  }
}