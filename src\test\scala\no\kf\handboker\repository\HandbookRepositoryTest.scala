package no.kf.handboker.repository

import _root_.no.kf.exception.UserFriendlyException
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import _root_.no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import org.apache.derby.shared.common.error.DerbySQLIntegrityConstraintViolationException
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.Matchers._
import org.scalatest.junit.JUnitRunner
import org.scalatest.{BeforeAndAfterAll, FunSuite}

@RunWith(classOf[JUnitRunner])
class HandbookRepositoryTest extends FunSuite with <PERSON>bTestHandler with BeforeAndAfterAll {

  val repository = componentRegistry.handbookRepository
  val currentUser = "<EMAIL>"
  override def beforeAll(): Unit = inTransaction {
    val handbook = componentRegistry.centralHandbookRepository.persistCentralHandbook(
      CentralHandbook(None, "How to train your dragons - 8", None, Some(DateTime.now), None, Some("CreatedBy"), None), currentUser)
    val chapter = componentRegistry.centralHandbookRepository.persistCentralChapter(
      CentralChapter(None,"Let them know who is the boss", parentId = None, handbook.id.get, None, Some(DateTime.now), None, None, Some("CreatedBy"), None), currentUser)
    componentRegistry.centralHandbookRepository.persistCentralSection(
      CentralSection(None, "Give them the stick", chapter.id.get, handbook.id.get, None, None, Some(DateTime.now), Some(DateTime.now), Some(DateTime.now), None, None, Some("CreatedBy"), Some("UpdatedBy"), None, None), currentUser)
  }

  override def afterAll(): Unit = inTransaction {
    connectionManager.doWithConnection(_.prepareCall("DELETE FROM central_handbooks.section").execute())
    connectionManager.doWithConnection(_.prepareCall("DELETE FROM central_handbooks.chapter").execute())
    connectionManager.doWithConnection(_.prepareCall("DELETE FROM central_handbooks.handbook").execute())
    connectionManager.doWithConnection(_.prepareCall("DELETE FROM central_handbooks.handbook_structure").execute())
  }


  transactedTest("That we can store and retrieve a handbook with nested chapters and a few sections") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    assert(null != handbook)
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None))
    assert(null != chapter1)
    val chapter2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, chapter1.id, None))
    assert(null != chapter2)
    val section1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    assert(null != section1)
    val section2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    assert(null != section2)
    val section3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, handbook.id.get, parentId = chapter2.id.get, None))
    assert(null != section3)
    //Sanity
    assert(section2 != section1)

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(chapter1.copy(sortOrder = None) === repository.retrieveChapter(chapter1.id.get).get.copy(sortOrder = None))
    assert(chapter2.copy(sortOrder = None) === repository.retrieveChapter(chapter2.id.get).get.copy(sortOrder = None))
    assert(section1.copy(sortOrder = None) === repository.retrieveSection(section1.id.get).get.copy(sortOrder = None))
    assert(section2.copy(sortOrder = None) === repository.retrieveSection(section2.id.get).get.copy(sortOrder = None))
    assert(section3.copy(sortOrder = None) === repository.retrieveSection(section3.id.get).get.copy(sortOrder = None))
    //Sanity
    assert(repository.retrieveSection(section3.id.get).get != repository.retrieveSection(section1.id.get).get)
  }

  transactedTest("That sort order is computed correctly for chapter and section insert") {
    val handbook = repository.persistHandbook(Handbook(None, "hb", None, "9999"))
    val x = repository.retrieveChaptersForHandbook(handbook.id.get)
    val y = repository.retrieveDeletedAndNotDeletedSectionsForHandbook(handbook.id.get)
    val ch1 = repository.persistChapter(Chapter(None, "ch1", None, None, handbook.id.get, None, None))
    assert(ch1.sortOrder.isDefined && ch1.sortOrder.get == 0)
    val ch2 = repository.persistChapter(Chapter(None, "ch2", None, None, handbook.id.get, ch1.id, None))
    assert(ch2.sortOrder.isDefined && ch2.sortOrder.get == 0)
    val s1 = repository.persistSection(Section(None, "s1", Some("<h1>s1</h1>"), None, None, handbook.id.get, parentId = ch1.id.get, None))
    assert(s1.sortOrder.isDefined && s1.sortOrder.get == 1)
    val s2 = repository.persistSection(Section(None, "s2", Some("<h1>s2</h1>"), None, None, handbook.id.get, parentId = ch1.id.get, None))
    assert(s2.sortOrder.isDefined && s2.sortOrder.get == 2)
    val s3 = repository.persistSection(Section(None, "s3", Some("<h1>s3</h1>"), None, None, handbook.id.get, parentId = ch2.id.get, None))
    assert(s3.sortOrder.isDefined && s3.sortOrder.get == 0)
  }

  transactedTest("That we can store and retrieve changes in a handbook") {
    val name1 = "name1"
    val name2 = "name2"
    val extOrgId1 = "9999"
    val extOrgId2 = "9990"
    val boolFlag1 = false
    val boolFlag2 = true

    val handbook = repository.persistHandbook(Handbook(None, name1, None, extOrgId1, boolFlag1, boolFlag1))

    val changedNotStored = handbook.copy(title = name2, externalOrgId = extOrgId2, localChange = boolFlag2, pendingChange = boolFlag2)
    assert(changedNotStored != handbook)

    val changedStored = repository.persistHandbook(changedNotStored)
    assert(changedNotStored.updatedDate.get.getMillis <= changedStored.updatedDate.get.getMillis)
    assert(changedNotStored === changedStored.copy(updatedDate = changedNotStored.updatedDate))

    val retrieved = repository.retrieveHandbook(handbook.id.get).get
    assert(changedNotStored.updatedDate.get.getMillis <= retrieved.updatedDate.get.getMillis)
    assert(changedNotStored === retrieved.copy(updatedDate = changedNotStored.updatedDate))
  }

  transactedTest("That we can store and retrieve changes in a chapter") {
    val name1 = "name1"
    val name2 = "name2"
    val boolFlag1 = false
    val boolFlag2 = true

    val handbook = repository.persistHandbook(Handbook(None, "Håndbok", None, "9999"))
    val chapter = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None, createdBy = Some("<EMAIL>"), updatedBy = Some("<EMAIL>")))
    val chapterToChange = repository.persistChapter(Chapter(None, name1, None, None, handbook.id.get, None, None, boolFlag1, boolFlag1, createdBy = Some("<EMAIL>"), updatedBy = Some("<EMAIL>")))

    val changedNotStored = chapterToChange.copy(title = name2, parentId = chapter.id, localChange = boolFlag2, pendingChange = boolFlag2, createdBy = Some("<EMAIL>"), updatedBy = Some("<EMAIL>"))
    assert(changedNotStored != chapterToChange)

    val changedStored = repository.persistChapter(changedNotStored)
    assert(changedNotStored.updatedDate.get.getMillis <= changedStored.updatedDate.get.getMillis)
    assert(changedNotStored === changedStored.copy(updatedDate = changedNotStored.updatedDate))

    val retrieved = repository.retrieveChapter(chapterToChange.id.get).get
    assert(changedNotStored.updatedDate.get.getMillis <= retrieved.updatedDate.get.getMillis)
    assert(changedNotStored === retrieved.copy(updatedDate = changedNotStored.updatedDate))
    assert(retrieved.createdBy.contains("<EMAIL>"))
    assert(retrieved.updatedBy.contains("<EMAIL>"))
  }

  transactedTest("That we can store and retrieve changes in a section") {
    val name1 = "name1"
    val name2 = "name2"
    val text1 = Option("Dette er en tekst")
    val text2 = Option("Dette er en helt annen tekst")
    val boolFlag1 = false
    val boolFlag2 = true

    val handbook1 = repository.persistHandbook(Handbook(None, "Håndbok", None, "9999"))
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook1.id.get, None, None))
    val handbook2 = repository.persistHandbook(Handbook(None, "Håndbok2", None, "9999"))
    val chapter2 = repository.persistChapter(Chapter(None, "Brannvern2", None, None, handbook2.id.get, None, None))


    val section = repository.persistSection(Section(None, name1, text1, None, None, handbook1.id.get, parentId = chapter1.id.get, None, boolFlag1, boolFlag1))

    val changedNotStored = section.copy(title = name2, text = text2, handbookId = handbook2.id.get, parentId = chapter2.id.get, sortOrder = None, localTitleChange = boolFlag2, pendingTitleChange = boolFlag2)
    assert(section != changedNotStored)

    val changedStored = repository.persistSection(changedNotStored)
    assert(changedNotStored.updatedDate.get.getMillis <= changedStored.updatedDate.get.getMillis)
    assert(changedNotStored === changedStored.copy(updatedDate = changedNotStored.updatedDate))

    val retrieved = repository.retrieveSection(section.id.get).get
    assert(changedNotStored.updatedDate.get.getMillis <= retrieved.updatedDate.get.getMillis)
    assert(changedNotStored === retrieved.copy(updatedDate = changedNotStored.updatedDate))
  }

  transactedTest("That we can delete a handbook with all its children") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None))
    val chapter2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, chapter1.id, None))
    val section1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, handbook.id.get, parentId = chapter2.id.get, None))

    val hb = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val c1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, hb.id.get, None, None))
    val c2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, hb.id.get, c1.id, None))
    val s1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, hb.id.get, parentId = c2.id.get, None))

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(chapter1 === repository.retrieveChapter(chapter1.id.get).get)
    assert(chapter2 === repository.retrieveChapter(chapter2.id.get).get)
    assert(section1 === repository.retrieveSection(section1.id.get).get)
    assert(section2 === repository.retrieveSection(section2.id.get).get)
    assert(section3 === repository.retrieveSection(section3.id.get).get)
    assert(hb === repository.retrieveHandbook(hb.id.get).get)
    assert(c1 === repository.retrieveChapter(c1.id.get).get)
    assert(c2 === repository.retrieveChapter(c2.id.get).get)
    assert(s1 === repository.retrieveSection(s1.id.get).get)
    assert(s2 === repository.retrieveSection(s2.id.get).get)
    assert(s3 === repository.retrieveSection(s3.id.get).get)

    repository.deleteHandbook(handbook.id.get)

    assert(None === repository.retrieveHandbook(handbook.id.get))
    assert(None === repository.retrieveChapter(chapter1.id.get))
    assert(None === repository.retrieveChapter(chapter2.id.get))
    assert(None === repository.retrieveSection(section1.id.get))
    assert(None === repository.retrieveSection(section2.id.get))
    assert(None === repository.retrieveSection(section3.id.get))
    assert(hb === repository.retrieveHandbook(hb.id.get).get)
    assert(c1 === repository.retrieveChapter(c1.id.get).get)
    assert(c2 === repository.retrieveChapter(c2.id.get).get)
    assert(s1 === repository.retrieveSection(s1.id.get).get)
    assert(s2 === repository.retrieveSection(s2.id.get).get)
    assert(s3 === repository.retrieveSection(s3.id.get).get)
  }

  transactedTest("That we can delete a chapter with all its children") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None))
    val chapter2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, chapter1.id, None))
    val chapter3 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, None, None))
    val section1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, handbook.id.get, parentId = chapter2.id.get, None))
    val section4 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, handbook.id.get, parentId = chapter3.id.get, None))

    val hb = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val c1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, hb.id.get, None, None))
    val c2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, hb.id.get, c1.id, None))
    val s1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, hb.id.get, parentId = c2.id.get, None))

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(chapter1 === repository.retrieveChapter(chapter1.id.get).get)
    assert(chapter2 === repository.retrieveChapter(chapter2.id.get).get)
    assert(chapter3 === repository.retrieveChapter(chapter3.id.get).get)
    assert(section1 === repository.retrieveSection(section1.id.get).get)
    assert(section2 === repository.retrieveSection(section2.id.get).get)
    assert(section3 === repository.retrieveSection(section3.id.get).get)
    assert(section4 === repository.retrieveSection(section4.id.get).get)
    assert(hb === repository.retrieveHandbook(hb.id.get).get)
    assert(c1 === repository.retrieveChapter(c1.id.get).get)
    assert(c2 === repository.retrieveChapter(c2.id.get).get)
    assert(s1 === repository.retrieveSection(s1.id.get).get)
    assert(s2 === repository.retrieveSection(s2.id.get).get)
    assert(s3 === repository.retrieveSection(s3.id.get).get)

    repository.deleteChapter(chapter1.id.get)

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(None === repository.retrieveChapter(chapter1.id.get))
    assert(None === repository.retrieveChapter(chapter2.id.get))
    assert(chapter3 === repository.retrieveChapter(chapter3.id.get).get)
    assert(None === repository.retrieveSection(section1.id.get))
    assert(None === repository.retrieveSection(section2.id.get))
    assert(None === repository.retrieveSection(section3.id.get))
    assert(section4 === repository.retrieveSection(section4.id.get).get)
    assert(hb === repository.retrieveHandbook(hb.id.get).get)
    assert(c1 === repository.retrieveChapter(c1.id.get).get)
    assert(c2 === repository.retrieveChapter(c2.id.get).get)
    assert(s1 === repository.retrieveSection(s1.id.get).get)
    assert(s2 === repository.retrieveSection(s2.id.get).get)
    assert(s3 === repository.retrieveSection(s3.id.get).get)
  }

  transactedTest("That we can delete a section") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None))
    val chapter2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, chapter1.id, None))
    val section1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, handbook.id.get, parentId = chapter2.id.get, None))

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(chapter1 === repository.retrieveChapter(chapter1.id.get).get)
    assert(chapter2 === repository.retrieveChapter(chapter2.id.get).get)
    assert(section1 === repository.retrieveSection(section1.id.get).get)
    assert(section2 === repository.retrieveSection(section2.id.get).get)
    assert(section3 === repository.retrieveSection(section3.id.get).get)

    repository.deleteSection(section3.id.get)

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(chapter1 === repository.retrieveChapter(chapter1.id.get).get)
    assert(chapter2 === repository.retrieveChapter(chapter2.id.get).get)
    assert(section1 === repository.retrieveSection(section1.id.get).get)
    assert(section2 === repository.retrieveSection(section2.id.get).get)
    assert(None === repository.retrieveSection(section3.id.get))
  }

  transactedTest("That we can retrieve all chapters for a handbook") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None))
    val chapter2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, chapter1.id, None))
    val section1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, handbook.id.get, parentId = chapter2.id.get, None))
    val hb = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val c1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, hb.id.get, None, None))
    val c2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, hb.id.get, c1.id, None))
    val s1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, hb.id.get, parentId = c2.id.get, None))

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(chapter1 === repository.retrieveChapter(chapter1.id.get).get)
    assert(chapter2 === repository.retrieveChapter(chapter2.id.get).get)
    assert(section1 === repository.retrieveSection(section1.id.get).get)
    assert(section2 === repository.retrieveSection(section2.id.get).get)
    assert(section3 === repository.retrieveSection(section3.id.get).get)
    assert(hb === repository.retrieveHandbook(hb.id.get).get)
    assert(c1 === repository.retrieveChapter(c1.id.get).get)
    assert(c2 === repository.retrieveChapter(c2.id.get).get)
    assert(s1 === repository.retrieveSection(s1.id.get).get)
    assert(s2 === repository.retrieveSection(s2.id.get).get)
    assert(s3 === repository.retrieveSection(s3.id.get).get)

    val chapters = repository.retrieveChaptersForHandbook(handbook.id.get)

    assert(2 === chapters.size)
    assert(chapters.contains(chapter1))
    assert(chapters.contains(chapter2))
  }

  transactedTest("That we can retrieve all sections for a handbook") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None))
    val chapter2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, chapter1.id, None))
    val section1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, handbook.id.get, parentId = chapter2.id.get, None))
    val hb = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val c1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, hb.id.get, None, None))
    val c2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, hb.id.get, c1.id, None))
    val s1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, hb.id.get, parentId = c1.id.get, None))
    val s3 = repository.persistSection(Section(None, "Skjema for Risikoanalyse", Some("<h1>Analyse av risiko</h1>"), None, None, hb.id.get, parentId = c2.id.get, None))

    assert(handbook === repository.retrieveHandbook(handbook.id.get).get)
    assert(chapter1 === repository.retrieveChapter(chapter1.id.get).get)
    assert(chapter2 === repository.retrieveChapter(chapter2.id.get).get)
    assert(section1 === repository.retrieveSection(section1.id.get).get)
    assert(section2 === repository.retrieveSection(section2.id.get).get)
    assert(section3 === repository.retrieveSection(section3.id.get).get)
    assert(hb === repository.retrieveHandbook(hb.id.get).get)
    assert(c1 === repository.retrieveChapter(c1.id.get).get)
    assert(c2 === repository.retrieveChapter(c2.id.get).get)
    assert(s1 === repository.retrieveSection(s1.id.get).get)
    assert(s2 === repository.retrieveSection(s2.id.get).get)
    assert(s3 === repository.retrieveSection(s3.id.get).get)

    val sections = repository.retrieveSectionsForHandbook(handbook.id.get)

    assert(3 === sections.size)
    assert(sections.contains(section1))
    assert(sections.contains(section2))
    assert(sections.contains(section3))
  }

  transactedTest("That we can retrieve all handbooks for an organization") {
    val handbook1 = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val handbook2 = repository.persistHandbook(Handbook(None, "Byggforskrifter", None, "9999"))
    val handbook3 = repository.persistHandbook(Handbook(None, "Tullehåndbok som ikke skal hentes opp", None, "1111"))

    assert(handbook1 === repository.retrieveHandbook(handbook1.id.get).get)
    assert(handbook2 === repository.retrieveHandbook(handbook2.id.get).get)
    assert(handbook3 === repository.retrieveHandbook(handbook3.id.get).get)

    val kfHandbooks = repository.retrieveHandbooksForExternalOrganization("9999")

    assert(2 === kfHandbooks.size)
    assert(kfHandbooks.contains(handbook1))
    assert(kfHandbooks.contains(handbook2))
  }


  transactedTest("That we can't store a chapter for an unstored handbook.") {
    val chapter = Chapter(None, "Skademelding", None, None, "notAValidHandbook", None, None)
    intercept[DerbySQLIntegrityConstraintViolationException] {
      repository.persistChapter(chapter)
    }
  }

  transactedTest("That we can set new title on handbooks") {
    val handbooks = List (
      Handbook(None, "KF HMS-Håndbok", None, "9999"),
      Handbook(None, "KF HMS-Håndbok", None, "9999"),
      Handbook(None, "KF HMS-Håndbok", None, "9999")
    ).map(repository.persistHandbook)

    val newTitle = "New Title"
    repository.setNewTitleOnHandbooks(newTitle, handbooks.map(_.id.get))
    repository.retrieveHandbooksForExternalOrganization("9999").forall(_.title == newTitle)
  }

  transactedTest("That we can set pending flags to true on handbooks") {
    val handbook = generateHandbook()
    val handbooks = List (handbook, handbook, handbook).map(repository.persistHandbook)
    repository.setPendingChangesFlagToTrueOnHandbooks(handbooks.map(_.id.get))
    repository.retrieveHandbooksForExternalOrganization("9999").forall(_.pendingChange)
  }

  transactedTest("That we can persist sort order in a Handbook") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter1 = repository.persistChapter(Chapter(None, "Brannvern", None, None, handbook.id.get, None, None))
    val chapter2 = repository.persistChapter(Chapter(None, "Rutiner", None, None, handbook.id.get, chapter1.id, None))
    val section1 = repository.persistSection(Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))
    val section2 = repository.persistSection(Section(None, "Fritidsskade", Some("<h1>Fritidsskade</h1>"), None, None, handbook.id.get, parentId = chapter1.id.get, None))

    val sortOrder  = List(chapter2.id, section2.id, section1.id)
    repository.persistSortOrder(sortOrder.flatten)

    val chapters1 = repository.retrieveChaptersForHandbook(handbook.id.get).filter(c => c.parentId.isDefined)
    val chapters = chapters1.map(c => c.id -> c.sortOrder)
    val sections1 = repository.retrieveSectionsForHandbook(handbook.id.get)
    val sections = sections1.map(s => s.id -> s.sortOrder)
    val newSortOrder = (chapters ++ sections).sortBy(_._2)
    assert(sortOrder === newSortOrder.map(_._1))
    assert(sortOrder === newSortOrder.map(_._1))
  }

  transactedTest("That we can't store a chapter with only one imported handbook field defined.") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter = repository.persistChapter(Chapter(None, "Brannvern", Option("importedChapterId"), Option("importedHandbookId"), handbook.id.get, None, None))

    intercept[RuntimeException] {
      repository.persistChapter(chapter.copy(importedHandbookChapterId = None))
    }

    intercept[RuntimeException] {
      repository.persistChapter(chapter.copy(importedHandbookId = None))
    }
  }

  transactedTest("That we can't store a section with only one imported handbook field defined.") {
    val handbook = repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9999"))
    val chapter = repository.persistChapter(Chapter(None, "Brannvern", Option("importedChapterId"), Option("importedHandbookId"), handbook.id.get, None, None))

    val section = Section(None, "Yrkesskade", Some("<h1>Yrkesskade</h1>"), Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, parentId = chapter.id.get, None)

    intercept[RuntimeException] {
      repository.persistSection(section.copy(importedHandbookSectionId = None))
    }

    intercept[RuntimeException] {
      repository.persistSection(section.copy(importedHandbookId = None))
    }
  }

  transactedTest("That we can retrieve every chapter ever created for a Handbook") {
    val handbook = generateHandbook()
    val chapter1 = generateChapter(handbook.id.get)
    val chapter2 = generateChapter(handbook.id.get)
    val chapter3 = generateChapter(handbook.id.get)

    repository.deleteChapter(chapter1.id.get)
    val res = repository.retrieveDeletedAndNotDeletedChaptersForHandbook(handbook.id.get)
    assert(res.size === 3)
  }

  transactedTest("That we can retrieve every chapter ever based on a central Handbook") {
    val handbook = generateHandbook()
    val chapter1 = generateChapter(handbook.id.get)
    val chapter2 = generateChapter(handbook.id.get)
    val chapter3 = generateChapter(handbook.id.get)

    repository.deleteChapter(chapter1.id.get)
    val res = repository.retrieveDeletedAndNotDeletedChaptersBasedOnCentralHandbook(handbook.importedHandbookId.get)
    assert(res.size === 3)
  }

  transactedTest("That we can retrieve every Handbook based on a central Handbook") {
    val handbook = generateHandbook()
    generateHandbook()
    generateHandbook()
    assert(repository.retrieveHandbooksBasedOnCentralHandbookId(handbook.importedHandbookId.get).size === 3)

    val locHb = repository.persistHandbook(Handbook(None, "Helt lokal", handbook.importedHandbookId, "9999"))
    repository.persistChapter(Chapter(None, "title", Some("importedChapterId"), handbook.importedHandbookId, handbook.id.get, None, None))
    assert(repository.retrieveHandbooksBasedOnCentralHandbookId(handbook.importedHandbookId.get).size === 4)
  }

  transactedTest("That we can remove central content from a chapter") {
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get)
    repository.persistChapter(chapter.copy(pendingChange = true, localChange = true))
    repository.removeCentralContentFromChapter(chapter.id.get)

    val res = repository.retrieveChapter(chapter.id.get).get
    assert(res.importedHandbookChapterId.isEmpty && res.importedHandbookId.isEmpty && !res.localChange && !res.pendingChange)
  }

  transactedTest("That we can remove central content from a section") {
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get)
    val section = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    repository.removeCentralContentFromSection(section.id.get)

    val res = repository.retrieveSection(section.id.get).get
    assert(res.importedHandbookSectionId.isEmpty && res.importedHandbookId.isEmpty && !res.localTitleChange && !res.pendingTitleChange)
  }

  transactedTest("That we can see who created and updated a handbook") {
    val updatedBy = Option("<EMAIL>")
    val createdBy = Option("<EMAIL>")
    val handbook = generateHandbook(updatedBy, createdBy)
    assert(handbook.updatedBy === updatedBy)
    assert(handbook.createdBy === createdBy)

    val newUpdatedBy = Option("<EMAIL>")
    val updatedHandbook = repository.persistHandbook(handbook.copy(updatedBy = newUpdatedBy))
    assert(updatedHandbook.updatedBy === newUpdatedBy)
    assert(updatedHandbook.createdBy !== newUpdatedBy)
    assert(updatedHandbook.createdBy === createdBy)
  }

  transactedTest("That we can see who created and updated a chapter") {
    val updatedBy = Option("<EMAIL>")
    val createdBy = Option("<EMAIL>")
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get, updatedBy, createdBy)
    assert(chapter.updatedBy === updatedBy)
    assert(chapter.createdBy === createdBy)

    val newUpdatedBy = Option("<EMAIL>")
    val updatedChapter = repository.persistChapter(chapter.copy(updatedBy = newUpdatedBy))
    assert(updatedChapter.updatedBy === newUpdatedBy)
    assert(updatedChapter.createdBy !== newUpdatedBy)
    assert(updatedChapter.createdBy === createdBy)
  }

  transactedTest("That we can see who created and updated a section") {
    val updatedBy = Option("<EMAIL>")
    val createdBy = Option("<EMAIL>")
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get)
    val section = repository.persistSection(Section(None, "sectionTitle", Option("text"), Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, Option(1), updatedBy = updatedBy, createdBy = createdBy))
    assert(section.updatedBy == updatedBy)
    assert(section.createdBy == createdBy)

    val newUpdatedBy = Option("<EMAIL>")
    val updatedSection = repository.persistSection(section.copy(updatedBy = newUpdatedBy))
    assert(updatedSection.updatedBy === newUpdatedBy)
    assert(updatedSection.createdBy !== newUpdatedBy)
    assert(updatedSection.createdBy === createdBy)
  }

  transactedTest("That we cannot set a chapter as an ancestor of it self") {
    val handbookId = generateHandbook().id.get
    val chapterParent = repository.persistChapter(Chapter(None, "chapter1", None, None, handbookId, None, None))
    val chapterChild = repository.persistChapter(Chapter(None, "chapter1", None, None, handbookId, chapterParent.id, None))

    intercept[UserFriendlyException] {
      repository.persistChapter(chapterParent.copy(parentId = chapterChild.id))
    }
  }

  transactedTest("That we cannot set the chapter as a direct child of it self") {
    val handbookId = generateHandbook().id.get
    val chapter = repository.persistChapter(Chapter(None, "chapter1", None, None, handbookId, None, None))

    intercept[UserFriendlyException] {
      repository.persistChapter(chapter.copy(parentId = chapter.id))
    }
  }

  transactedTest("That when we move a chapter to another handbook we also update it's descendants handbookIds") {
    val handbookId = generateHandbook().id.get
    val chapterRoot = repository.persistChapter(Chapter(None, "Root chapter", None, None, handbookId, None, None))

    val child1 = repository.persistChapter(Chapter(None, "child1", None, None, handbookId, chapterRoot.id, None))
    val child1_1 = repository.persistChapter(Chapter(None, "child1_1", None, None, handbookId, child1.id, None))
    val child1_1_1 = repository.persistSection(Section(None, "child1_1_1", None, None, None, handbookId, child1_1.id.get, None))

    val child2 = repository.persistChapter(Chapter(None, "child2", None, None, handbookId, chapterRoot.id, None))
    val child2_1 = repository.persistSection(Section(None, "child2_1", None, None, None, handbookId, child2.id.get, None))

    val otherHandbookId = generateHandbook().id.get
    repository.persistChapter(chapterRoot.copy(handbookId = otherHandbookId))

    val chapters = repository.retrieveChaptersForHandbook(otherHandbookId)
    assert(chapters.size === 4)
    val sections = repository.retrieveSectionsForHandbook(otherHandbookId)
    assert(sections.size === 2)

    assert(repository.retrieveChaptersForHandbook(handbookId).isEmpty)
    assert(repository.retrieveSectionsForHandbook(handbookId).isEmpty)
  }

  transactedTest("That we can set the handbook as open to the public") {
    val handbook = generateHandbook()
    assert(!handbook.isPublic)
    val publicHandbook = repository.persistHandbook(handbook.copy(isPublic = true))
    assert(publicHandbook.isPublic)
  }

  transactedTest("That we can retrieve all external organizations connected to handbooks") {
    val externalOrgId1 = "9900"
    val externalOrgId2 = "9999"
    val externalOrgId3 = "0621"

    repository.persistHandbook(Handbook(None, "Handbook1", Option("importedHandbookId"), externalOrgId1))
    repository.persistHandbook(Handbook(None, "Handbook2", Option("importedHandbookId"), externalOrgId2))
    repository.persistHandbook(Handbook(None, "Handbook3", Option("importedHandbookId"), externalOrgId3))
    repository.persistHandbook(Handbook(None, "Handbook3", Option("importedHandbookId-1337"), externalOrgId3))

    val externalOrgIds = repository.retrieveAllExternalOrgIds()
    externalOrgIds.size shouldBe 3
    externalOrgIds should contain(externalOrgId1)
    externalOrgIds should contain(externalOrgId2)
    externalOrgIds should contain(externalOrgId3)
  }

  transactedTest("That we can retrieve non deleted sections by importedHandbookId") {
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get)
    val section1 = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section2 = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section3 = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))

    repository.deleteSection(section2.id.get)

    val sections = repository.retrieveSectionsWithImportedHandbookId("importedHandbookId", includeDeleted = false)
    assert(sections.length === 2)
    assert(sections.contains(section1))
    assert(sections.contains(section3))
  }

  transactedTest("That we can retrieve deleted and non deleted sections by importedHandbookId") {
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get)
    val section1 = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section2 = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section3 = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section4 = repository.persistSection(Section(None, "Title", None, Option("importedSectionId"), Option("another importedHandbookId"), handbook.id.get, chapter.id.get, None))

    repository.deleteSection(section2.id.get)

    val sections = repository.retrieveSectionsWithImportedHandbookId("importedHandbookId", includeDeleted = true)
    assert(sections.length === 3)
    assert(sections.contains(section1))
    assert(sections.contains(section3))
  }

  transactedTest("That we can retrieve non deleted sections based on central section") {
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get)
    val centralSectionId = "importedSectionId"
    val section1 = repository.persistSection(Section(None, "Title", None, Option(centralSectionId), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section2 = repository.persistSection(Section(None, "Title", None, Option(centralSectionId), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section3 = repository.persistSection(Section(None, "Title", None, Option(centralSectionId), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))

    repository.deleteSection(section2.id.get)
    val res = repository.retrieveSectionsBasedOnCentralSection(centralSectionId, includeDeleted = false)
    assert(res.length === 2)
    assert(res.contains(section1))
    assert(res.contains(section3))
  }

  transactedTest("That we can retrieve deleted and non deleted sections based on central section") {
    val handbook = generateHandbook()
    val chapter = generateChapter(handbook.id.get)
    val centralSectionId = "importedSectionId"
    val section1 = repository.persistSection(Section(None, "Title", None, Option(centralSectionId), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section2 = repository.persistSection(Section(None, "Title", None, Option(centralSectionId), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))
    val section3 = repository.persistSection(Section(None, "Title", None, Option(centralSectionId), Option("importedHandbookId"), handbook.id.get, chapter.id.get, None))

    repository.deleteSection(section2.id.get)
    val res = repository.retrieveSectionsBasedOnCentralSection(centralSectionId, includeDeleted = true)
    assert(res.length === 3)
    assert(res.contains(section1))
    assert(res.contains(section2))
    assert(res.contains(section3))
  }

  def generateHandbook(updatedBy: Option[String] = None, createdBy: Option[String] = None): Handbook = {
    repository.persistHandbook(Handbook(None, "KF HMS-Håndbok", Option("importedHandbookId"), "9999", updatedBy = updatedBy, createdBy = createdBy))
  }

  def generateChapter(handbookId: String, updatedBy: Option[String] = None, createdBy: Option[String] = None): Chapter = {
    repository.persistChapter(Chapter(None, "Brannvern", Option("importedChapterId"), Option("importedHandbookId"), handbookId, None, None, updatedBy = updatedBy, createdBy = createdBy))
  }
}
