import React from "react";
import { But<PERSON> } from "kf-bui";
import { FormattedMessage } from "react-intl";
import { usePrefixedTranslation } from "@/libs/i18n";
import { CentralDeleteModal } from "../CentralDeleteModal";
import { BooleanToggler } from "@/components/BooleanToggler";

export type CentralDeleteItemType =
  | "HANDBOOK"
  | "CHAPTER"
  | "SECTION";

export interface CentralDeleteItem {
  id: string;
  title: string;
  type: CentralDeleteItemType;
}

interface CentralDeleteButtonProps {
  toDelete: CentralDeleteItem;
  onDelete: (keepLocal?: boolean) => void;
  readlinkExists?: boolean;
}

/**
 * CentralDeleteButton - Delete button component exclusively for Central Editor
 * This component handles deletion operations within the central editor.
 * Used exclusively in the Central Editor flow (/central-editor/)
 */
export const CentralDeleteButton: React.FC<CentralDeleteButtonProps> = ({
  onDelete,
  toDelete,
  readlinkExists = false,
}) => {
  const t = usePrefixedTranslation("editor.containers.ChapterSelection");

  return (
    <BooleanToggler>
      {(toggle, isVisible) => (
        <span>
          <Button
            key="button"
            onClick={toggle}
            icon="trash"
            control
            outlined
            color="danger"
            size="small"
          >
            {t("deleteButton")}
          </Button>

          {isVisible && (
            <CentralDeleteModal
              isOpen={isVisible}
              key="modal"
              onHide={toggle}
              onDelete={onDelete}
              title={t("deleteTitle")}
              text={
                <div>
                  <p>
                    {t("deleteQuestion", {
                      title: toDelete.title,
                    })}
                  </p>
                  {toDelete.type !== "SECTION" && <p>{t("deleteWarning")}</p>}
                  {toDelete.type === "HANDBOOK" && (
                    <p>
                      <b>MERK! </b>
                      <FormattedMessage id="editor.containers.DeleteButton.deleteLocalWarning" />
                    </p>
                  )}
                  {readlinkExists && (
                    <p>
                      <b>OBS!</b>
                      <FormattedMessage id="editor.containers.DeleteButton.readLinkDeleteWarning" />
                    </p>
                  )}
                </div>
              }
            />
          )}
        </span>
      )}
    </BooleanToggler>
  );
};