import React from "react";
import { Routes, Route } from "react-router-dom";
import { LocalSectionScreen } from "../LocalSectionScreen";
import { EditSectionScreen } from "../EditSectionScreen";
import { MoveLocalChapterOrSection } from "../../shared/MoveLocalChapterOrSection";

export const SectionRouter: React.FC = () => {
  return (
    <Routes>
      <Route path=":sectionId" element={<LocalSectionScreen />} />
      <Route path="add-new" element={<EditSectionScreen />} />
      <Route path=":sectionId/edit" element={<EditSectionScreen />} />
      <Route path=":sectionId/move" element={<MoveLocalChapterOrSection />} />
    </Routes>
  );
};
