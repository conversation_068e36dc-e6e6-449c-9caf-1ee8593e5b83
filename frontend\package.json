{"name": "handboker", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "analyze": "tsx src/analysis/cli.ts", "test-analysis": "tsx src/analysis/test-runner.ts", "generate-report": "tsx src/analysis/generate-report.ts", "typescript-analyze": "tsx src/analysis/typescript-cli.ts --analyze --report", "typescript-enhance": "tsx src/analysis/typescript-cli.ts --enhance --report", "test-typescript": "tsx src/analysis/test-typescript-analyzer.ts && tsx src/analysis/test-typescript-enhancer.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@tinymce/tinymce-react": "^6.2.1", "classnames": "^2.5.1", "glob": "^11.0.3", "html-react-parser": "^5.2.6", "js-cookie": "^3.0.5", "kf-bui": "^4.5.15", "lodash": "^4.17.21", "moment": "^2.30.1", "object-path-immutable": "^4.1.2", "query-string": "^9.2.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "react-intl": "^7.1.11", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-stickynode": "^5.0.2", "react-toastify": "^11.0.5", "redux": "^5.0.0", "redux-persist": "^6.0.0", "styled-components": "^6.1.19", "tinymce": "^8.0.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-stickynode": "^4.0.3", "@types/redux-devtools-extension": "^2.13.0", "@types/redux-persist": "^4.0.0", "@typescript-eslint/parser": "^8.37.0", "@typescript-eslint/typescript-estree": "^8.37.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "prettier": "^3.6.2", "ts-morph": "^26.0.0", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}