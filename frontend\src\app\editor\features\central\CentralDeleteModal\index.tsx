import React, { useState, useCallback, type ReactNode } from "react";
import { Modal, Button, Content, Checkbox } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

interface CentralDeleteModalProps {
  isOpen: boolean;
  onHide: () => void;
  onDelete: (keepLocal?: boolean) => void;
  title: string;
  text: ReactNode;
  keepButton?: boolean;
}

/**
 * CentralDeleteModal - Delete confirmation modal exclusively for Central Editor
 * This component handles delete confirmation dialogs within the central editor.
 * Used exclusively in the Central Editor flow (/central-editor/)
 */
export const CentralDeleteModal: React.FC<CentralDeleteModalProps> = ({
  isOpen,
  onHide,
  onDelete,
  title,
  text,
  keepButton = false,
}) => {
  const t = usePrefixedTranslation("editor.components.DeleteModal");
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [confirmed, setConfirmed] = useState<boolean>(false);

  const handleDeleteClick = useCallback(
    (keepLocal?: boolean) => {
      setSubmitting(true);
      onDelete(keepLocal);
    },
    [onDelete]
  );

  const handleConfirmChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmed(e.target.checked);
  };

  const handleHide = submitting ? () => {} : onHide;

  return (
    <Modal isOpen={isOpen} onClose={handleHide}>
      <Modal.Header onClose={handleHide}>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Content>{text}</Content>
        <Checkbox checked={confirmed} onChange={handleConfirmChange}>
          Bekreft sletting av sentralt innhold
        </Checkbox>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={handleHide} disabled={submitting}>
          {t("cancelButton")}
        </Button>
        {keepButton && (
          <Button
            onClick={() => handleDeleteClick(true)}
            outlined
            disabled={submitting}
            loading={submitting}
          >
            Behold lokalt innhold
          </Button>
        )}
        <Button
          onClick={() => handleDeleteClick(false)}
          disabled={!confirmed || submitting}
          color="danger"
          outlined
          loading={submitting}
        >
          {t("deleteButton")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
