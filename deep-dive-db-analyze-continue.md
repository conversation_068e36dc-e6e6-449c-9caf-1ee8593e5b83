# Deep Dive Database Analysis - Continued

## 2.2 Central Handbook Publishing Flow (Continued)

### 2.2.1 Publishing New Central Version (Continued)

```
│ │         (c.newHtml, now)                                            │   │
│ │       case _ => (oldSection.html, oldSection.htmlUpdatedDate)      │   │
│ │     }                                                               │   │
│ │                                                                     │   │
│ │     INSERT INTO central_handbooks.section (                        │   │
│ │       id, central_id, title, html,                                 │   │
│ │       central_handbook_id, central_parent_id,                      │   │
│ │       title_updated_date, html_updated_date,                       │   │
│ │       title_updated_by, html_updated_by,                           │   │
│ │       created_date, updated_date, version_of                       │   │
│ │     ) VALUES (                                                      │   │
│ │       newSectionId, newSectionId, newTitle, newHtml,               │   │
│ │       newCentralId, newChapterId,                                   │   │
│ │       titleUpdated, htmlUpdated,                                    │   │
│ │       currentUser, currentUser,                                     │   │
│ │       now, now, oldSection.id                                       │   │
│ │     )                                                               │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 4: PROPAGATE CHANGES TO LOCAL HANDBOOKS                               │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ After central publishing, mark all local handbooks as having        │   │
│ │ pending changes                                                     │   │
│ │                                                                     │   │
│ │ UPDATE handbook SET                                                 │   │
│ │   pendingchanges = 1,                                              │   │
│ │   pending_changes_updated_date = ?                                 │   │
│ │ WHERE importedhandbook_id = ?                                      │   │
│ │                                                                     │   │
│ │ This affects ALL organizations using this central handbook         │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.3 Synchronization Flow - Local with Central

#### 2.3.1 Detecting and Resolving Changes

```
SYNCHRONIZATION PROCESS
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ STEP 1: USER VIEWS PENDING CHANGES                                         │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ GET /handbooks/local/{id}/pending-changes                           │   │
│ │                                                                     │   │
│ │ LocalHandbookService.getPendingChanges(handbookId) {                │   │
│ │                                                                     │   │
│ │   // 1. Get current local handbook version                         │   │
│ │   val localHandbook = getHandbook(handbookId)                      │   │
│ │   val centralId = localHandbook.importedHandbookId                 │   │
│ │                                                                     │   │
│ │   // 2. Get latest central version                                 │   │
│ │   val latestCentral = centralHandbookService                       │   │
│ │     .getLatestVersion(centralId)                                   │   │
│ │                                                                     │   │
│ │   // 3. Compare structures and content                             │   │
│ │   val changes = compareHandbookVersions(                           │   │
│ │     localHandbook, latestCentral                                   │   │
│ │   )                                                                 │   │
│ │                                                                     │   │
│ │   changes                                                           │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 2: CHANGE COMPARISON ALGORITHM                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ compareHandbookVersions(local, central) {                           │   │
│ │                                                                     │   │
│ │   val changes = mutable.ListBuffer[Change]()                       │   │
│ │                                                                     │   │
│ │   // Compare each local section with its central counterpart       │   │
│ │   local.sections.foreach { localSection =>                         │   │
│ │     val centralSectionId = localSection.importedSectionId          │   │
│ │     val centralSection = central.findSection(centralSectionId)     │   │
│ │                                                                     │   │
│ │     if (centralSection.isDefined) {                                │   │
│ │       val cs = centralSection.get                                  │   │
│ │                                                                     │   │
│ │       // Check title changes                                       │   │
│ │       if (cs.title != localSection.title &&                       │   │
│ │           cs.titleUpdatedDate > localSection.updatedDate) {       │   │
│ │         changes += TitleChange(                                    │   │
│ │           sectionId = localSection.id,                             │   │
│ │           oldTitle = localSection.title,                           │   │
│ │           newTitle = cs.title,                                     │   │
│ │           centralUpdatedDate = cs.titleUpdatedDate,                │   │
│ │           hasLocalModifications = localSection.manualmerge == 1    │   │
│ │         )                                                           │   │
│ │       }                                                             │   │
│ │                                                                     │   │
│ │       // Check content changes                                     │   │
│ │       if (cs.html != localSection.html &&                         │   │
│ │           cs.htmlUpdatedDate > localSection.updatedDate) {        │   │
│ │         changes += ContentChange(                                  │   │
│ │           sectionId = localSection.id,                             │   │
│ │           oldHtml = localSection.html,                             │   │
│ │           newHtml = cs.html,                                       │   │
│ │           centralUpdatedDate = cs.htmlUpdatedDate,                 │   │
│ │           hasLocalModifications = localSection.manualmerge == 1    │   │
│ │         )                                                           │   │
│ │       }                                                             │   │
│ │     }                                                               │   │
│ │   }                                                                 │   │
│ │                                                                     │   │
│ │   changes.toList                                                    │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 3: USER RESOLVES CONFLICTS                                            │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ POST /handbooks/local/{id}/resolve-changes                          │   │
│ │ {                                                                   │   │
│ │   "resolutions": [                                                  │   │
│ │     {                                                               │   │
│ │       "sectionId": "sec-456",                                      │   │
│ │       "changeType": "title",                                       │   │
│ │       "resolution": "accept_central", // or "keep_local"           │   │
│ │       "customValue": null                                          │   │
│ │     },                                                              │   │
│ │     {                                                               │   │
│ │       "sectionId": "sec-456",                                      │   │
│ │       "changeType": "content",                                     │   │
│ │       "resolution": "merge_custom",                                │   │
│ │       "customValue": "<p>Merged content...</p>"                    │   │
│ │     }                                                               │   │
│ │   ]                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 4: APPLY RESOLUTIONS                                                  │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ LocalHandbookService.applyChangeResolutions() {                     │   │
│ │                                                                     │   │
│ │   inTransaction {                                                   │   │
│ │     resolutions.foreach { resolution =>                            │   │
│ │       val section = getSection(resolution.sectionId)               │   │
│ │       val now = System.currentTimeMillis()                         │   │
│ │                                                                     │   │
│ │       resolution.changeType match {                                │   │
│ │         case "title" =>                                            │   │
│ │           val newTitle = resolution.resolution match {             │   │
│ │             case "accept_central" => getCentralTitle(section)      │   │
│ │             case "keep_local" => section.title                     │   │
│ │             case "merge_custom" => resolution.customValue          │   │
│ │           }                                                         │   │
│ │                                                                     │   │
│ │           UPDATE handbooksection SET                               │   │
│ │             title = ?,                                             │   │
│ │             updated_date = ?,                                      │   │
│ │             updated_by = ?,                                        │   │
│ │             manualmerge = CASE                                     │   │
│ │               WHEN ? = 'keep_local' OR ? = 'merge_custom'          │   │
│ │               THEN 1 ELSE 0 END                                    │   │
│ │           WHERE id = ?                                             │   │
│ │                                                                     │   │
│ │         case "content" =>                                          │   │
│ │           // Similar logic for HTML content                       │   │
│ │       }                                                             │   │
│ │     }                                                               │   │
│ │                                                                     │   │
│ │     // Clear pending changes flag                                  │   │
│ │     UPDATE handbook SET                                            │   │
│ │       pendingchanges = 0,                                         │   │
│ │       pending_changes_updated_date = ?                            │   │
│ │     WHERE id = ?                                                   │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. Complex Query Patterns

### 3.1 Handbook Tree Structure Retrieval

```
TREE STRUCTURE QUERY PATTERN
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ CHALLENGE: Get complete handbook with nested chapters and sections         │
│                                                                             │
│ APPROACH 1: MULTIPLE QUERIES (Current Implementation)                      │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ def getHandbookWithStructure(handbookId: String) = {                │   │
│ │                                                                     │   │
│ │   // 1. Get handbook                                               │   │
│ │   val handbook = getHandbook(handbookId)                           │   │
│ │                                                                     │   │
│ │   // 2. Get all chapters                                           │   │
│ │   val chapters = getChaptersForHandbook(handbookId)                │   │
│ │                                                                     │   │
│ │   // 3. Get all sections                                           │   │
│ │   val sections = getSectionsForHandbook(handbookId)                │   │
│ │                                                                     │   │
│ │   // 4. Build tree structure in memory                             │   │
│ │   buildTreeStructure(handbook, chapters, sections)                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ SQL Queries Executed:                                               │   │
│ │ 1. SELECT * FROM handbook WHERE id = ?                             │   │
│ │ 2. SELECT * FROM handbookchapter WHERE handbook_id = ?             │   │
│ │    ORDER BY parent_chapter_id, orderindex                          │   │
│ │ 3. SELECT * FROM handbooksection WHERE handbook_id = ?             │   │
│ │    ORDER BY parent_chapter_id, orderindex                          │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ APPROACH 2: SINGLE COMPLEX QUERY (Alternative)                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ WITH RECURSIVE handbook_tree AS (                                   │   │
│ │   -- Base case: handbook                                           │   │
│ │   SELECT                                                            │   │
│ │     h.id as handbook_id,                                           │   │
│ │     h.title as handbook_title,                                     │   │
│ │     NULL as chapter_id,                                            │   │
│ │     NULL as chapter_title,                                         │   │
│ │     NULL as section_id,                                            │   │
│ │     NULL as section_title,                                         │   │
│ │     NULL as section_html,                                          │   │
│ │     0 as level,                                                     │   │
│ │     h.title as path                                                │   │
│ │   FROM handbook h                                                   │   │
│ │   WHERE h.id = ?                                                    │   │
│ │                                                                     │   │
│ │   UNION ALL                                                         │   │
│ │                                                                     │   │
│ │   -- Recursive case: chapters                                      │   │
│ │   SELECT                                                            │   │
│ │     ht.handbook_id,                                                │   │
│ │     ht.handbook_title,                                             │   │
│ │     c.id as chapter_id,                                            │   │
│ │     c.title as chapter_title,                                      │   │
│ │     NULL as section_id,                                            │   │
│ │     NULL as section_title,                                         │   │
│ │     NULL as section_html,                                          │   │
│ │     ht.level + 1,                                                   │   │
│ │     ht.path || ' > ' || c.title                                    │   │
│ │   FROM handbook_tree ht                                            │   │
│ │   JOIN handbookchapter c ON c.handbook_id = ht.handbook_id         │   │
│ │   WHERE ht.level = 0                                               │   │
│ │                                                                     │   │
│ │   UNION ALL                                                         │   │
│ │                                                                     │   │
│ │   -- Recursive case: sections                                      │   │
│ │   SELECT                                                            │   │
│ │     ht.handbook_id,                                                │   │
│ │     ht.handbook_title,                                             │   │
│ │     ht.chapter_id,                                                 │   │
│ │     ht.chapter_title,                                              │   │
│ │     s.id as section_id,                                            │   │
│ │     s.title as section_title,                                      │   │
│ │     s.html as section_html,                                        │   │
│ │     ht.level + 1,                                                   │   │
│ │     ht.path || ' > ' || s.title                                    │   │
│ │   FROM handbook_tree ht                                            │   │
│ │   JOIN handbooksection s ON s.parent_chapter_id = ht.chapter_id    │   │
│ │   WHERE ht.level = 1                                               │   │
│ │ )                                                                   │   │
│ │ SELECT * FROM handbook_tree ORDER BY level, path                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 Cross-Schema Synchronization Queries

```
SYNCHRONIZATION QUERY PATTERNS
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ QUERY 1: FIND ALL LOCAL HANDBOOKS NEEDING SYNC                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SELECT                                                              │   │
│ │   h.id as local_handbook_id,                                       │   │
│ │   h.title as local_title,                                          │   │
│ │   h.external_org_id,                                               │   │
│ │   h.importedhandbook_id as central_id,                             │   │
│ │   h.pending_changes_updated_date,                                  │   │
│ │   ch.title as central_title,                                       │   │
│ │   ch.updated_date as central_updated_date                          │   │
│ │ FROM handbook h                                                     │   │
│ │ JOIN central_handbooks.handbook ch                                  │   │
│ │   ON h.importedhandbook_id = ch.central_id                         │   │
│ │ WHERE h.pendingchanges = 1                                         │   │
│ │   AND h.deleted = 0                                                │   │
│ │   AND ch.updated_date > h.pending_changes_updated_date             │   │
│ │ ORDER BY h.external_org_id, h.title                                │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ QUERY 2: DETAILED SECTION-LEVEL CHANGES                                    │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SELECT                                                              │   │
│ │   ls.id as local_section_id,                                       │   │
│ │   ls.title as local_title,                                         │   │
│ │   ls.html as local_html,                                           │   │
│ │   ls.updated_date as local_updated_date,                           │   │
│ │   ls.manualmerge as has_local_changes,                             │   │
│ │   cs.title as central_title,                                       │   │
│ │   cs.html as central_html,                                         │   │
│ │   cs.title_updated_date,                                           │   │
│ │   cs.html_updated_date,                                            │   │
│ │   cs.title_updated_by,                                             │   │
│ │   cs.html_updated_by,                                              │   │
│ │   CASE                                                              │   │
│ │     WHEN cs.title != ls.title                                      │   │
│ │       AND cs.title_updated_date > ls.updated_date                  │   │
│ │     THEN 1 ELSE 0                                                  │   │
│ │   END as title_changed,                                            │   │
│ │   CASE                                                              │   │
│ │     WHEN cs.html != ls.html                                        │   │
│ │       AND cs.html_updated_date > ls.updated_date                   │   │
│ │     THEN 1 ELSE 0                                                  │   │
│ │   END as content_changed                                           │   │
│ │ FROM handbooksection ls                                            │   │
│ │ JOIN central_handbooks.section cs                                  │   │
│ │   ON ls.importedhandbooksection_id = cs.id                        │   │
│ │ WHERE ls.handbook_id = ?                                           │   │
│ │   AND ls.deleted = 0                                               │   │
│ │   AND (                                                             │   │
│ │     (cs.title != ls.title AND cs.title_updated_date > ls.updated_date) │   │
│ │     OR                                                              │   │
│ │     (cs.html != ls.html AND cs.html_updated_date > ls.updated_date)│   │
│ │   )                                                                 │   │
│ │ ORDER BY ls.parent_chapter_id, ls.orderindex                       │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ QUERY 3: BULK UPDATE AFTER SYNC RESOLUTION                                 │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ -- Update multiple sections based on user resolutions              │   │
│ │ UPDATE handbooksection SET                                          │   │
│ │   title = CASE                                                      │   │
│ │     WHEN id = 'sec-1' THEN 'New Title 1'                          │   │
│ │     WHEN id = 'sec-2' THEN 'New Title 2'                          │   │
│ │     ELSE title                                                      │   │
│ │   END,                                                              │   │
│ │   html = CASE                                                       │   │
│ │     WHEN id = 'sec-1' THEN '<p>New content 1</p>'                 │   │
│ │     WHEN id = 'sec-3' THEN '<p>New content 3</p>'                 │   │
│ │     ELSE html                                                       │   │
│ │   END,                                                              │   │
│ │   manualmerge = CASE                                               │   │
│ │     WHEN id IN ('sec-2', 'sec-4') THEN 1  -- Keep local changes   │   │
│ │     ELSE 0                                 -- Accept central       │   │
│ │   END,                                                              │   │
│ │   updated_date = ?,                                                │   │
│ │   updated_by = ?                                                   │   │
│ │ WHERE id IN ('sec-1', 'sec-2', 'sec-3', 'sec-4')                  │   │
│ │   AND handbook_id = ?                                              │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. Performance Optimization Strategies

### 4.1 Database Connection Management

```
CONNECTION POOL CONFIGURATION
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ HikariCP Configuration (application.conf):                                 │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ db.default.driver="com.mysql.cj.jdbc.Driver"                       │   │
│ │ db.default.url="*************************************"             │   │
│ │ db.default.username="handbook_user"                                │   │
│ │ db.default.password="secure_password"                              │   │
│ │                                                                     │   │
│ │ # Connection Pool Settings                                          │   │
│ │ db.default.hikaricp.minimumIdle=5                                  │   │
│ │ db.default.hikaricp.maximumPoolSize=20                             │   │
│ │ db.default.hikaricp.connectionTimeout=30000    # 30 seconds        │   │
│ │ db.default.hikaricp.idleTimeout=600000         # 10 minutes        │   │
│ │ db.default.hikaricp.maxLifetime=1800000        # 30 minutes        │   │
│ │ db.default.hikaricp.leakDetectionThreshold=60000 # 1 minute        │   │
│ │                                                                     │   │
│ │ # Performance Tuning                                                │   │
│ │ db.default.hikaricp.cachePrepStmts=true                            │   │
│ │ db.default.hikaricp.prepStmtCacheSize=250                          │   │
│ │ db.default.hikaricp.prepStmtCacheSqlLimit=2048                     │   │
│ │ db.default.hikaricp.useServerPrepStmts=true                        │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ Connection Usage Pattern:                                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ def inTransaction[T](block: => T): T = {                            │   │
│ │   val connection = connectionPool.getConnection()                   │   │
│ │   try {                                                             │   │
│ │     connection.setAutoCommit(false)                                │   │
│ │     val result = block                                             │   │
│ │     connection.commit()                                            │   │
│ │     result                                                          │   │
│ │   } catch {                                                         │   │
│ │     case ex: Exception =>                                          │   │
│ │       connection.rollback()                                        │   │
│ │       throw ex                                                      │   │
│ │   } finally {                                                       │   │
│ │     connection.setAutoCommit(true)                                 │   │
│ │     connection.close() // Returns to pool                          │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 Query Optimization and Indexing

```
DATABASE INDEXING STRATEGY
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ CRITICAL INDEXES FOR PERFORMANCE                                            │
│                                                                             │
│ Local Schema Indexes:                                                       │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ -- Primary access patterns                                          │   │
│ │ CREATE INDEX idx_handbook_external_org                              │   │
│ │   ON handbook(external_org_id, deleted);                           │   │
│ │                                                                     │   │
│ │ CREATE INDEX idx_handbook_pending_changes                           │   │
│ │   ON handbook(pendingchanges, importedhandbook_id)                  │   │
│ │   WHERE pendingchanges = 1;                                        │   │
│ │                                                                     │   │
│ │ CREATE INDEX idx_chapter_handbook                                   │   │
│ │   ON handbookchapter(handbook_id, parent_chapter_id, orderindex);  │   │
│ │                                                                     │   │
│ │ CREATE INDEX idx_section_chapter                                    │   │
│ │   ON handbooksection(parent_chapter_id, orderindex);               │   │
│ │                                                                     │   │
│ │ CREATE INDEX idx_section_handbook                                   │   │
│ │   ON handbooksection(handbook_id, deleted);                        │   │
│ │                                                                     │   │
│ │ -- Synchronization queries                                          │   │
│ │ CREATE INDEX idx_section_imported                                   │   │
│ │   ON handbooksection(importedhandbooksection_id, updated_date);    │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ Central Schema Indexes:                                                     │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ CREATE INDEX idx_central_handbook_version                           │   │
│ │   ON central_handbooks.handbook(central_id, version_of);            │   │
│ │                                                                     │   │
│ │ CREATE INDEX idx_central_section_handbook                           │   │
│ │   ON central_handbooks.section(central_handbook_id, sort_order);    │   │
│ │                                                                     │   │
│ │ CREATE INDEX idx_central_section_change_tracking                    │   │
│ │   ON central_handbooks.section(title_updated_date, html_updated_date);│   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ Query Execution Plans:                                                      │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ EXPLAIN SELECT * FROM handbook                                      │   │
│ │ WHERE external_org_id = '9900' AND deleted = 0;                    │   │
│ │                                                                     │   │
│ │ Result: Uses idx_handbook_external_org                              │   │
│ │ Cost: ~0.1ms for 1000 handbooks                                    │   │
│ │                                                                     │   │
│ │ EXPLAIN SELECT * FROM handbooksection                               │   │
│ │ WHERE handbook_id = 'hb-123' AND deleted = 0                       │   │
│ │ ORDER BY parent_chapter_id, orderindex
</augment_code_snippet>