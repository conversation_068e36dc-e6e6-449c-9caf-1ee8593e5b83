package no.kf.handboker.service.search

import com.sksamuel.elastic4s.ElasticDsl._
import com.sksamuel.elastic4s.analysis.{Analysis, CustomAnalyzer, EdgeNGramTokenizer, StopTokenFilter}
import com.sksamuel.elastic4s.fields.{KeywordField, TextField}
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import no.kf.util.Logging
import org.jsoup.Jsoup

/**
  * SearchIndexService exposes index handling in Elastic Search to other components
  */
trait SearchIndexServiceComponent {
  this: ElasticClientManagerServiceComponent =>

  val searchIndexService: SearchIndexService

  object SearchIndexDef {
    val id = "id"
    val handbook_id = "handbook_id"
    val externalOrgId = "externalOrgId"
    val doc_type = "document_type"

    val HANDBOOK_TYPE = "handbook_type"
    val handbook_title = "handbook_title"

    val CHAPTER_TYPE = "chapter_type"
    val chapter_title = "chapter_title"

    val SECTION_TYPE = "section_type"
    val section_title = "section_title"
    val section_text = "text"
    
  }

  class SearchIndexServiceImpl extends SearchIndexService with Logging {
    import SearchIndexDef._

    override def createIndexForOrg(extOrgId: String) = {
      log.info(s"Creating index for externalOrgId: $extOrgId")
      
      elasticClient.execute {
        createIndex(extOrgId).mapping(
          properties(
            KeywordField(id),
            keywordField(doc_type),
            KeywordField(handbook_id),
            KeywordField(externalOrgId),
            TextField(handbook_title, analyzer = Some("custom_analyzer"), searchAnalyzer = Some("search_analyzer")),
            TextField(chapter_title, analyzer = Some("custom_analyzer"), searchAnalyzer = Some("search_analyzer")),
            TextField(section_title, analyzer = Some("custom_analyzer"), searchAnalyzer = Some("search_analyzer")),
            TextField(section_text, analyzer = Some("custom_analyzer"), searchAnalyzer = Some("search_analyzer"))
          )
        ).analysis(
        Analysis(
          analyzers = List(
            CustomAnalyzer(
              "custom_analyzer",
              tokenizer = "custom_edgeN",
              tokenFilters = List("lowercase")
            ),
            CustomAnalyzer(
              "search_analyzer",
              tokenizer = "standard",
              tokenFilters = List("stop_filter")
            )
          ),
          tokenizers = List(
            EdgeNGramTokenizer("custom_edgeN", minGram = 2, maxGram = 30, tokenChars = List("letter", "digit", "punctuation", "symbol"))
          ),
          tokenFilters = List(
            StopTokenFilter("stop_filter", language = Some("norwegian"), ignoreCase = Some(true))
          )
        )
      )
    }.await
      log.info(s"Successfully created index for externalOrgId: $extOrgId")
    }
    
    override def resetDatabase() = {
      log.debug("Will remove all indexes, types and documents")
      elasticClient.execute {
        deleteIndex("_all")
      }.await
      log.debug("All indexes, types and documents removed.")
    }

    override def indexHandbook(handbook: Handbook) = {
      log.debug(s"Indexing Handbook with ID ${handbook.id}")
      checkEntityForNoId(handbook)

      if (!indexDoExists(handbook.externalOrgId)) {
        createIndexForOrg(handbook.externalOrgId)
      }

      elasticClient.execute {
        updateById(handbook.externalOrgId, handbook.id.get).docAsUpsert (
          id -> handbook.id.get,
          doc_type -> HANDBOOK_TYPE,
          handbook_title -> handbook.title,
          externalOrgId -> handbook.externalOrgId
        )
      }.await
    }

    override def indexChapter(chapter: Chapter, extOrgId: String) = {
      log.debug(s"Indexing Chapter with ID ${chapter.id}")
      checkEntityForNoId(chapter)

      if (!indexDoExists(extOrgId)) {
        createIndexForOrg(extOrgId)
      }

      elasticClient.execute {
        updateById(extOrgId, chapter.id.get).docAsUpsert (
          id -> chapter.id.get,
          doc_type -> CHAPTER_TYPE,
          chapter_title -> chapter.title,
          handbook_id -> chapter.handbookId,
          externalOrgId -> extOrgId
        )
      }.await
    }


    /**
      * When indexing a section we strip away all HTML from the section
      * text before indexing it. This is because we don't want the HTML to be indexed.
      * Elasticsearch has a StripHTML filter, but that only strips before indexing, when retrieving fragments
      * of search results we still get HTML back. To circumvent this, we simply index plain text instead of HTML
      */
    override def indexSection(section: Section, extOrgId: String) = {
      log.debug(s"Indexing Section with ID ${section.id}")
      checkEntityForNoId(section)

      if (!indexDoExists(extOrgId)) {
        createIndexForOrg(extOrgId)
      }

      elasticClient.execute {
        updateById(extOrgId, section.id.get).docAsUpsert (
          id -> section.id.get,
          doc_type -> SECTION_TYPE,
          section_title -> section.title,
          handbook_id -> section.handbookId,
          externalOrgId -> extOrgId,
          section_text -> Jsoup.parse(section.text.getOrElse("")).text() // Remove all HTML
        )
      }.await
    }

    override def deleteEntriesFromIndex(index: String) = {
      log.info(s"Deleting entries from index $index")
      elasticClient.execute {
        deleteIndex(index)
      }
    }

    private def indexDoExists(indexName: String): Boolean = {
      val existsDefinition = elasticClient.execute(
        indexExists(indexName)
      ).await
      existsDefinition.result.exists
    }

    private def checkEntityForNoId(entity: { val id: Option[String] }) = {
      if (entity.id.isEmpty) {
        throw new IllegalArgumentException("Cannot index entity without ID")
      }
    }
  }
}

trait SearchIndexService {
  def createIndexForOrg(externalOrgId: String)
  def indexHandbook(handbook: Handbook)
  def indexChapter(chapter: Chapter, externalOrgId: String)
  def indexSection(section: Section, externalOrgId: String)

  def deleteEntriesFromIndex(index: String)
  def resetDatabase()
}
