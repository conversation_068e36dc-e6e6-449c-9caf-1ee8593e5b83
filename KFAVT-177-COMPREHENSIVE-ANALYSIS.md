# KFAVT-177 - Comprehensive Analysis & Documentation

## 📋 Main Task Overview

**Ticket:** KFAVT-177  
**Title:** HB - Backend Blocker Identification & Analysis — {Prep for New UI Revamp}  
**Status:** In Progress  
**Priority:** Normal  
**Assignee:** <PERSON><PERSON>  
**Reporter:** poornima.m  
**Created:** July 1, 2025  
**Labels:** August_1st, Handbook  

### Project Context
- **Project:** KF Handbook and DR (Aventude - KF)
- **Project Key:** KFAVT
- **Category:** Utvikling (Development)

### Time Tracking
- **Total Time Spent:** 234,000 seconds (65 hours)
- **Progress:** 100% complete on subtasks
- **Work Ratio:** -1

---

## 🎯 Subtasks Analysis

### 1. KFAVT-178: Database Schema Deep-Dive
**Status:** Documented ✅  
**Time Spent:** 36,000 seconds (10 hours)  
**Assignee:** <PERSON><PERSON> Samaranayake  

#### Objective
Reverse-engineer the existing handbook application database schema and identify all relevant entities, table relationships, constraints, and indexes.

#### Key Deliverables
- **ER diagram** (annotated)
- **Glossary of important tables/fields** and their purposes
- **Schema pain points** or coupling issues that could affect new UI integration

#### Work Completed
- Comprehensive database schema analysis
- Documentation of table relationships and constraints
- Identification of areas requiring attention for UI revamp
- **External Reference:** [Handbook Application Database Schema- Comprehensive ER Design.docx](https://aventudex.sharepoint.com/:w:/r/sites/KFHandbook/_layouts/15/Doc.aspx?sourcedoc=%7B4ADF17FE-5840-4CA8-8588-FDF81876654A%7D&file=Handbook%20Application%20Database%20Schema-%20Comprehensive%20ER%20Design.docx&action=default&mobileredirect=true)

#### Key Focus Areas
- Local handbook content lifecycle
- Content versioning logic
- Permissions and organization-specific data
- Central vs. local content relationships

---

### 2. KFAVT-179: End-to-End Data Flow Mapping (Current UI)
**Status:** Documented ✅  
**Time Spent:** 28,800 seconds (8 hours)  
**Assignee:** Kushan Samaranayake  

#### Objective
Investigate how the current UI fetches and manipulates data from backend APIs, focusing on the local handbook content lifecycle and user interaction triggers.

#### Key Deliverables
- **Detailed data flow diagram**
- **Sequence diagram** showing flow from UI to DB
- **Catalog of relevant existing API endpoints** with summary descriptions

#### Comprehensive Documentation Completed

##### Data Flow Architecture
```
┌─────────────────────────────────────────────────────────────────────────┐
│                           CLIENT BROWSER                                │
│                                                                         │
│  ┌───────────────┐     ┌───────────────┐     ┌───────────────┐         │
│  │ Public UI     │     │ Editor UI     │     │ Central       │         │
│  │ (React)       │     │ (React)       │     │ Editor UI     │         │
│  └───────┬───────┘     └───────┬───────┘     └───────┬───────┘         │
└──────────┼─────────────────────┼─────────────────────┼─────────────────┘
           │                     │                     │
           │  HTTP/JSON          │  HTTP/JSON          │  HTTP/JSON
           │                     │                     │
┌──────────┼─────────────────────┼─────────────────────┼─────────────────┐
│          ▼                     ▼                     ▼                  │
│  ┌───────────────┐     ┌───────────────┐     ┌───────────────┐         │
│  │ Public        │     │ Local         │     │ Central       │         │
│  │ Handbook API  │     │ Handbook API  │     │ Handbook API  │         │
│  └───────┬───────┘     └───────┬───────┘     └───────┬───────┘         │
│          │                     │                     │                  │
│  ┌───────▼───────────────────────────────────────────▼───────┐         │
│  │                    Session Support                        │         │
│  └───────────────────────────┬───────────────────────────────┘         │
│                              │                                         │
│  ┌───────────────────────────▼───────────────────────────────┐         │
│  │                    Service Layer                          │         │
│  │  ┌───────────────┐     ┌───────────────┐     ┌─────────┐ │         │
│  │  │ Local         │     │ Central       │     │ Search  │ │         │
│  │  │ Handbook      │     │ Handbook      │     │ Service │ │         │
│  │  │ Service       │     │ Service       │     │         │ │         │
│  │  └───────┬───────┘     └───────┬───────┘     └────┬────┘ │         │
│  └──────────┼─────────────────────┼───────────────────┼──────┘         │
│             │                     │                   │                │
│  ┌──────────▼─────────────────────▼───────────────────▼──────┐         │
│  │                    Repository Layer                       │         │
│  │  ┌───────────────┐     ┌───────────────┐     ┌─────────┐ │         │
│  │  │ Local         │     │ Central       │     │ Search  │ │         │
│  │  │ Handbook      │     │ Handbook      │     │ Index   │ │         │
│  │  │ Repository    │     │ Repository    │     │         │ │         │
│  │  └───────┬───────┘     └───────┬───────┘     └────┬────┘ │         │
│  └──────────┼─────────────────────┼───────────────────┼──────┘         │
│             │                     │                   │                │
│             ▼                     ▼                   ▼                │
│  ┌─────────────────┐     ┌────────────────┐    ┌────────────┐         │
│  │  MSSQL          │     │  MSSQL         │    │ Elastic    │         │
│  │  (Local         │     │  (Central      │    │ Search     │         │
│  │   Handbook DB)  │     │   Handbook DB) │    │            │         │
│  └─────────────────┘     └────────────────┘    └────────────┘         │
└─────────────────────────────────────────────────────────────────────────┘
```

##### API Endpoint Catalog

**Public Handbook API:**
- `GET /:externalOrgId/:handbookId` - Retrieves handbook with chapters and sections
- `GET /section/:sectionId` - Retrieves specific section with content
- `GET /search/:externalOrgId/:query` - Searches handbook content
- `GET /readinglink/:linkId` - Retrieves content via reading link

**Local Handbook Editor API:**
- `GET /handbooks/local/` - Retrieves all local handbooks
- `POST /handbooks/local/:id` - Creates or updates handbook
- `GET/POST/DELETE /handbooks/local/chapter/:id` - Chapter operations
- `GET/POST/DELETE /handbooks/local/section/:id` - Section operations
- `GET /handbooks/local/section/versions/:sectionId` - Version history
- `POST /handbooks/local/sort` - Updates sort order

**Central Handbook API:**
- `GET /handbooks/central/` - Retrieves all central handbooks
- `GET /handbooks/central/:handbookId` - Retrieves specific central handbook
- `GET /handbooks/central/:handbookId/content` - Retrieves chapters and sections

**Authentication & Session API:**
- `GET /session` - Retrieves current session information
- `GET /logout` - Logs out current user

##### Authentication Flow
1. **CAS Authentication** - Initial authentication via Central Authentication Service
2. **Session Management** - Session creation and maintenance via SessionSupport
3. **Organization Context** - Users associated with organizations via external_org_id
4. **API Access Control** - Role-based permissions for different handbook types

---

### 3. KFAVT-180: Customization Page (New UI Feature)
**Status:** Documented ✅  
**Time Spent:** 28,800 seconds (8 hours)  
**Assignee:** Kushan Samaranayake  

#### Objective
Understand planned front-end changes to the customization section and identify required backend changes including new tables and API endpoints.

#### Key Deliverables
- **Proposed table structure designs** with column names and data types
- **High-level outline of API endpoints needed**
- **Summary of required DAO/service interfaces**
- **Security implications and multi-tenancy concerns**

#### Welcome Page Customization - Backend Investigation

##### 🎯 Core Objectives
- Support dual state (DRAFT, PUBLISHED) for welcome page content
- Ensure atomic publishing behavior
- Enable "Apply Defaults" functionality
- Provide clean, testable architecture
- Support editor workflows including drag-and-drop reordering

##### ✅ Final Design: Central Versioning with `welcome_page_version`

**Key Benefits:**
- **Simplicity:** Single source of truth for version state
- **Atomic Publishing:** Single record update to change version status
- **Data Integrity:** Enforced through database constraints
- **Audit Trail:** Clear history of published versions
- **Performance:** Efficient queries for current state

##### 🧱 Proposed Database Schema

**1. `welcome_page_version` (Central Version Control)**
```sql
CREATE TABLE welcome_page_version (
    id BIGSERIAL PRIMARY KEY,
    handbook_id BIGINT NOT NULL REFERENCES handbook(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by VARCHAR(255),
    UNIQUE (handbook_id, status)
);
```

**2. `welcome_page_customization` (Design & Content)**
```sql
CREATE TABLE welcome_page_customization (
    id BIGSERIAL PRIMARY KEY,
    version_id BIGINT NOT NULL REFERENCES welcome_page_version(id) ON DELETE CASCADE,
    primary_color VARCHAR(7) NOT NULL,
    secondary_color VARCHAR(7) NOT NULL,
    welcome_image VARCHAR(255),
    image_title VARCHAR(255),
    alt_title VARCHAR(255),
    image_credits VARCHAR(255),
    welcome_header TEXT,
    welcome_text TEXT,
    -- Audit fields
    color_updated_by VARCHAR(255),
    color_updated_at TIMESTAMPTZ,
    image_updated_by VARCHAR(255),
    image_updated_at TIMESTAMPTZ,
    welcome_header_updated_by VARCHAR(255),
    welcome_header_updated_at TIMESTAMPTZ,
    welcome_text_updated_by VARCHAR(255),
    welcome_text_updated_at TIMESTAMPTZ,
    UNIQUE (version_id)
);
```

**3. Navigation & Content Tables**
- `welcome_page_link_collection` - Link collections with versioning
- `welcome_page_link` - Individual links within collections
- `welcome_page_shortcut_collection` - Shortcut collections
- `welcome_page_shortcut` - Individual shortcuts

##### 🔄 Workflow Implementation

**Editing Flow:**
1. Editor requests draft via `GET /api/handbooks/{id}/welcome-page/draft`
2. If no draft exists, create one by copying from published version
3. Changes saved to draft version
4. Preview available before publishing

**Publishing Flow:**
1. Editor clicks "Publish"
2. System archives current published version
3. Updates draft status to PUBLISHED
4. Content immediately visible to end users

**Apply Defaults:**
1. Editor clicks "Reset to Defaults"
2. System loads default values from configuration
3. Updates draft version with defaults

---

### 4. KFAVT-181: Local "Text Chip" Feature for Edited Central Content
**Status:** Documented ✅  
**Time Spent:** 25,200 seconds (7 hours)  
**Assignee:** Kushan Samaranayake  

#### Objective
Analyze how to support visual indicators ("chips") for locally edited content in the new UI and determine impact on existing table structures.

#### Key Deliverables
- **Conceptual model documentation**
- **Diagrams showing local vs. central content divergence**
- **Risk analysis for introducing chips**
- **Flag/label system proposal**

#### Backend Investigation Summary

**Key Finding:** No backend changes needed! 

##### Existing Fields Support the Feature
1. **`importedHandbookSectionId: Option[String]`** - None indicates purely local section
2. **`localTextChange: Boolean`** - Indicates if section's text has been locally modified

##### Implementation Approach
```javascript
const needsLocalTextChip = 
  section.importedHandbookSectionId === undefined || // Purely local section
  section.localTextChange; // Or imported but locally modified
```

##### Field Behavior
- `localTextChange` remains true even after reverting changes (meets requirement)
- `importedHandbookSectionId` is None for purely local sections
- Frontend can determine chip visibility using existing data

**Conclusion:** This leverages existing fields and their current behavior, making it the most efficient solution.

---

### 5. KFAVT-182: Enhanced Search Functionality
**Status:** Documented ✅  
**Time Spent:** 86,400 seconds (24 hours)  
**Assignee:** Kushan Samaranayake  

#### Objective
Review new UI requirements for search results displaying chapter titles, sections, and last edited timestamps, with exact location redirects.

#### Key Deliverables
- **Existing limitations assessment**
- **Required DB columns identification**
- **DTO changes and new API suggestions**
- **Redirect logic and ID mapping analysis**

#### 🔍 Search Functionality Review & Enhancement Plan

##### ✅ Currently Working
- Basic search across handbooks, chapters, and sections
- Results show which handbook they belong to
- Basic keyword matching with relevance scoring
- Pagination of search results

##### 🔄 Needs Improvement

**1. Search Result Navigation**
- **Currently:** Only shows handbook context
- **Needed:** Direct links to specific chapters/sections
- **Impact:** Users waste time finding where results appear

**2. Finding Recent Content**
- **Currently:** No way to sort by date
- **Needed:** "Sort by Most Recent" option
- **Impact:** Users can't easily find recently updated content

**3. Highlighting & Context**
- **Currently:** Basic highlighting exists but is limited
- **Needed:** 
  - Multiple highlighted fragments per result
  - Highlight matches in titles and section text
  - Show surrounding context for each match

**4. "More Matches" Feature**
- **Currently:** Only shows first match in content
- **Needed:**
  - Show up to 3 matches per result
  - "Show more" option for additional matches
  - Visual indicator of total matches

**5. Search Accuracy**
- **Currently:** Basic text matching
- **Needed:**
  - Support for exact phrases
  - Better handling of special characters
  - Improved relevance ranking

##### 💡 Recommended Priority
1. Improve result navigation (highest impact)
2. Add date sorting
3. Enhance highlighting
4. Add "more matches" feature
5. Refine search accuracy

##### 🚀 Next Steps
- Confirm priorities with stakeholders
- Schedule implementation phases
- Plan for user testing
- Schedule deployment

---

### 6. KFAVT-183: Organizational Shortcuts Management
**Status:** Documented ✅  
**Time Spent:** No time logged  
**Assignee:** Kushan Samaranayake  

#### Objective
Explore how to model shortcuts in new tables for organizations to create/manage shortcuts for quick handbook access.

#### Key Deliverables
- **Proposed table schema and constraints**
- **High-level endpoint specifications**
- **Permission model considerations**
- **Required database migrations (draft)**

#### Status
This subtask is marked as "Documented" but appears to have minimal work completed based on the lack of time logging and comments. This may require follow-up to ensure completion.

---

### 7. KFAVT-184: Settings & Page Preferences Persistence
**Status:** Documented ✅  
**Time Spent:** 28,800 seconds (8 hours)  
**Assignee:** Kushan Samaranayake  

#### Objective
Understand how the new UI settings page intends to save preferences like language, layout style, etc., and investigate schema requirements.

#### Key Deliverables
- **Mapping between UI settings and DB fields**
- **Schema gaps or column-level blockers identification**
- **Summary of options (reuse vs extend) and risks**

#### Implementation Plan: Local Text Tag Toggle

##### Objective
Add a user-friendly toggle in handbook settings to control visibility of local text tags across the application.

##### Business Value
- Enhances user experience by providing control over UI elements
- Reduces visual clutter when text tags are not needed
- Offers flexibility for different handbook configurations

##### Technical Implementation

**1. Database Changes**
- Create new `handbook_settings` table for handbook-specific configurations
- Add indexes for optimal performance

**2. Backend Development**
- **API Endpoints:**
  - `GET /api/handbooks/{id}/settings/local-text-tag` - Check current setting
  - `PUT /api/handbooks/{id}/settings/local-text-tag` - Update setting
- **Key Features:**
  - Per-handbook setting storage
  - Default value handling
  - Audit logging of changes

**3. Frontend Integration**
- Add toggle switch in handbook settings panel
- Implement real-time updates when setting changes
- Add appropriate loading and success/error states

**4. Testing Strategy**
- Unit tests for backend logic
- Integration tests for API endpoints
- UI tests for toggle functionality
- Cross-browser testing

##### Technical Notes
- Setting disabled by default
- Changes take effect immediately after saving
- No impact on existing functionality when disabled

##### Dependencies
- Database migration must be deployed before code changes
- Frontend team needs to implement UI toggle

---

## 📊 Overall Project Status

### Completion Summary
- **Main Task:** In Progress
- **Subtasks Completed:** 7/7 (100%)
- **Total Time Investment:** 234,000 seconds (65 hours)
- **Documentation Status:** All subtasks documented

### Key Achievements
1. **Complete database schema analysis** with comprehensive ER documentation
2. **End-to-end data flow mapping** with detailed API catalog
3. **Welcome page customization architecture** with versioning strategy
4. **Local text chip implementation** using existing fields (no backend changes needed)
5. **Search functionality enhancement plan** with prioritized improvements
6. **Settings persistence strategy** with toggle implementation plan

### Technical Insights
- **No Backend Changes Needed:** Local text chip feature can be implemented using existing database fields
- **Versioning Strategy:** Central versioning approach chosen for welcome page customization
- **API Architecture:** Comprehensive mapping of existing endpoints completed
- **Search Enhancements:** Clear roadmap for improving search functionality
- **Database Schema:** Thorough analysis completed with external documentation

### Risk Mitigation
- **Performance:** Indexes and optimized queries planned for new features
- **Data Integrity:** Database constraints and validation strategies defined
- **User Experience:** Real-time updates and proper loading states planned
- **Backward Compatibility:** Existing functionality preserved during enhancements

### Next Steps
1. **Stakeholder Review:** Present findings and get approval for implementation priorities
2. **Implementation Planning:** Schedule development phases based on priority
3. **Resource Allocation:** Assign development resources to high-priority items
4. **Testing Strategy:** Implement comprehensive testing for all new features
5. **Deployment Planning:** Coordinate database migrations with code deployments

---

## 🔗 External References
- [Handbook Application Database Schema- Comprehensive ER Design.docx](https://aventudex.sharepoint.com/:w:/r/sites/KFHandbook/_layouts/15/Doc.aspx?sourcedoc=%7B4ADF17FE-5840-4CA8-8588-FDF81876654A%7D&file=Handbook%20Application%20Database%20Schema-%20Comprehensive%20ER%20Design.docx&action=default&mobileredirect=true)

---

**Document Generated:** August 4, 2025  
**Analysis Completed By:** AI Assistant  
**Source:** Jira KFAVT-177 and all related subtasks