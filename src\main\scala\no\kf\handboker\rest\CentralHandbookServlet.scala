package no.kf.handboker.rest

import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.ScalatraExceptions
import no.kf.rest.support.JsonSupport
import no.kf.util.NorwegianStringOrdering._
import org.joda.time.DateTime
import org.scalatra.ScalatraServlet

class CentralHandbookServlet extends ScalatraServlet with SessionSupport with JsonSupport {

  lazy val centralHandbookService = componentRegistry.centralHandbookService
  lazy val centralAccessService = componentRegistry.centralAccessService
  lazy val centralHandbookPublicationService = componentRegistry.centralHandbookPublicationService
  lazy val readingLinkService = componentRegistry.readingLinkService

  /**
   * Retrieve the central handbooks an external organization has access to
   */
  get("/?") {
    centralHandbookService
      .retrieveCentralHandbooks(currentExternalOrganizationId)
      .sortBy(_.title)
  }

  /**
   * Retrieve the central handbook contents an external organization has access to
   */
  get("/:handbookId/content/?") {
    val handbook = centralHandbookService.retrieveChaptersAndSectionsWithText(extractRequiredParam("handbookId"))
    Map(
      "chapters" -> handbook._1,
      "sections" -> handbook._2
    )
  }

  /**
   * Get a central handbook
   */
  get("/:handbookId/?") {
    centralHandbookService
      .retrieveCentralHandbook(extractRequiredParam("handbookId"), true)
      .getOrElse(ScalatraExceptions.notFound())
  }

  get("/:handbookId/pending-publications/?") {
    centralHandbookService.getPendingPublication(extractRequiredParam("handbookId")).size > 0
  }

  /**
   * Get a section (with text)
   */
  get("/:handbookId/section/:id/?") {
    centralHandbookService
      .retrieveCentralSection(extractRequiredParam("id"))
      .getOrElse(ScalatraExceptions.notFound())
  }

  /**
   * Get a section (with text)
   */
  get("/:handbookId/section/:id/latest/?") {
    centralHandbookService
      .retrieveLatestPublishedCentralSection(extractRequiredParam("id"), extractRequiredParam("handbookId"))
      .getOrElse(ScalatraExceptions.notFound())
  }

  /**
   * Get a chapter
   */
  get("/:handbookId/chapter/:id/?") {
    centralHandbookService
      .retrieveCentralChapter(extractRequiredParam("id"), extractRequiredParam("handbookId"))
      .getOrElse(ScalatraExceptions.notFound())
  }

  /**
   * Get a chapter
   */
  get("/:handbookId/chapter/:id/latest/?") {
    centralHandbookService
      .retrieveLatestPublishedCentralChapter(extractRequiredParam("id"), extractRequiredParam("handbookId"))
      .getOrElse(ScalatraExceptions.notFound())
  }

  /**
   * Get all central handbooks
   * KfAdmin only
   */
  get("/handbooks/?") {
    forKFAdminsOnly()
    centralHandbookService.retrieveAllCentralHandbooks.sortBy(_.title)
  }

  get("/versions/:centralHandbookId/?") {
    val centralHandbookId = extractRequiredParam("centralHandbookId")
    centralHandbookService.retrieveAllHandbookVersions(centralHandbookId)
  }

  /**
   * Get all chapters
   * KfAdmin only
   */
  get("/chapters/?") {
    forKFAdminsOnly()
    centralHandbookService.retrieveAllCentralChapters()
  }

  /**
   * Get all sections
   * KfAdmin only
   */
  get("/sections/?") {
    forKFAdminsOnly()
    centralHandbookService.retrieveAllCentralSections()
  }

  /**
   * Post a new handbook
   * KfAdmin only
   */
  post("/handbook/?") {
    forKFAdminsOnly()
    val handbook = parsedBody.extract[CentralHandbook]
    centralHandbookService.persistCentralHandbook(handbook, currentUser.email)
  }

  /**
   * Post a new chapter
   * KfAdmin only
   */
  post("/chapter/?") {
    forKFAdminsOnly()
    val beforeJson = parsedBody.toString
    val chapter = parsedBody.extract[CentralChapter]
    centralHandbookService.persistCentralChapter(chapter, currentUser.email)
  }

  /**
   * Post a new section
   * KfAdmin only
   */
  post("/section/?") {
    forKFAdminsOnly()
    val section = parsedBody.extract[CentralSection]
    centralHandbookService.persistCentralSection(section, currentUser.email)
  }

  /**
   * Delete a central handbook
   * Kfadmin only
   */
  delete("/handbook/:handbookId/?") {
    forKFAdminsOnly()
    val centralHandbookId = extractRequiredParam("handbookId")
    centralHandbookService.deleteCentralHandbook(centralHandbookId)
  }

  /**
   * Delete a central chapter
   * Kfadmin only
   */
  delete("/chapter/:chapterId/?") {
    forKFAdminsOnly()
    val centralChapterId = extractRequiredParam("chapterId")
    centralHandbookService.deleteCentralChapter(centralChapterId)
  }

  /**
   * Delete a central section
   * Kfadmin only
   */
  delete("/section/:sectionId/?") {
    forKFAdminsOnly()
    val centralSectionId = extractRequiredParam("sectionId")
    centralHandbookService.deleteCentralSection(centralSectionId)
  }

  /**
   * Sort chapters and sections for a handbook
   * */
  post("/sort/") {
    forKFAdminsOnly()
    val toSort = parsedBody.extract[List[String]]
    centralHandbookService.sortCentralElements(toSort)
  }

  /**
   * Get reading links
   */
  get("/readingLinks/?") {
    //forKFAdminsOnly()
    readingLinkService.retrieveAllReadingLinks()
  }

  /**
   * Change an external organization's access to central handbooks
   * KfAdmin only
   */
  post("/access/:externalOrg/?") {
    forKFAdminsOnly()

    val externalOrgId = extractRequiredParam("externalOrg")
    val handbookIds = parsedBody.extract[List[String]]
    centralAccessService.updateOrgAccesses(externalOrgId, handbookIds)
  }

  /**
   * Get an external organization's access to central handbooks
   * KfAdmin only
   */
  get("/access/:externalOrg/?") {
    forKFAdminsOnly()

    val externalOrgId = extractRequiredParam("externalOrg")
    centralAccessService.retrieveOrgAccesses(externalOrgId)
  }

  get("/published/?") {
    forKFAdminsOnly()
    val allHandbooks = centralHandbookService
      .retrieveAllCentralHandbooks
      .sortBy(_.title)
    allHandbooks.filter(_.isPublished)
  }

  post("/publish/:handbookId/?") {
    forKFAdminsOnly()
    val handbookId = extractRequiredParam("handbookId")
    val publicationDate = parsedBody.extract[Option[String]]
    val date = if (publicationDate.isDefined) DateTime.parse(publicationDate.get) else DateTime.now
    centralHandbookPublicationService.createPublication(handbookId, date, currentUser.email)
  }

  def forKFAdminsOnly() = {
    if (!userIsKFAdmin(currentUser)) {
      ScalatraExceptions.forbiddenException("You do not have sufficient privileges")
    }
  }
}
