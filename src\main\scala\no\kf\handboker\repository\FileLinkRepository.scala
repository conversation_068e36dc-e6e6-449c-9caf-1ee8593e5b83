package no.kf.handboker.repository

import no.kf.db.{IDGenerator, UpsertSupport}
import no.kf.db.RichSQL._
import no.kf.handboker.model.local.{FileLink}
import no.kf.util.Logging
import org.joda.time.DateTime

trait FileLinkRepositoryComponent {
  this: DbConnectionManagerComponent
    with HandbookRepositoryComponent =>

  val fileLinkRepository: FileLinkRepository

  object FileLinkTableDef {
    val fileLinkTableName = "file_link"

    val fieldId = "id"
    val fieldTitle = "title"
    val fieldUrl = "url"
    val fieldSortOrder = "sort_order"
    val fieldOwnerId = "owner_id"
    val fieldBelongsTo = "belongs_to"
    val fieldCreated = "created_date"
    val fieldUpdated = "updated_date"
    val fieldCreatedBy = "created_by"
    val fieldUpdatedBy = "updated_by"
    val fieldSize= "file_size"
    val fields = List(fieldId, fieldTitle, fieldBelongsTo, fieldOwnerId, fieldUrl, fieldSortOrder,fieldSize, fieldCreated, fieldUpdated,fieldCreatedBy,fieldUpdatedBy)
  }

  class FileLinkRepositoryImpl extends FileLinkRepository with UpsertSupport with Logging {

    override def retrieveFileLinks(ownerId: String, belongsTo: String): List[FileLink] = {
      import FileLinkTableDef._
      val sql = s"SELECT ${fields.mkString(", ")} FROM $fileLinkTableName WHERE $fieldOwnerId = ? AND $fieldBelongsTo = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << ownerId << belongsTo <<! populateFileLink
      }
    }

    override def retrieveFileLinkCount(ownerId: String, belongsTo: String): Int = {
      import FileLinkTableDef._
      val sql = s"SELECT COUNT(*) FROM $fileLinkTableName WHERE $fieldOwnerId = ? AND $fieldBelongsTo = ?"
      val (count) = connectionManager.doWithConnection {
        _.ps(sql) << ownerId << belongsTo <<# populateCount
      }.get

      count
    }

    override def persistLink(link: FileLink, user: String): FileLink = {
      import FileLinkTableDef._
      val sql = s"INSERT INTO $fileLinkTableName (${fields.mkString(",")}) VALUES (${#?(fields)})"
      val id = IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) << id << link.title << link.belongsTo << link.ownerId << link.url << link.sortOrder << link.size << DateTime.now << DateTime.now << user << user <<!
      }
      link.copy(id = Some(id))
    }

    override def deleteLink(linkId: String): Unit = {
      import FileLinkTableDef._
      val sql = s"DELETE FROM $fileLinkTableName WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << linkId <<!
      }
    }
    private def populateFileLink(rs: RichResultSet): FileLink = FileLink(rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs)

    private def populateCount(rs: RichResultSet): Int = rs
  }
}

trait FileLinkRepository {
  def retrieveFileLinks(ownerId: String, belongsTo: String): List[FileLink]
  def persistLink(link: FileLink, user: String): FileLink
  def deleteLink(linkId: String): Unit
  def retrieveFileLinkCount(ownerId: String, belongsTo: String): Int
}
