# CAS Authentication & LDAP Authorization Flow (Continued - Part 4)
**Monitoring, Logging & Production Deployment**

## Monitoring and Alerting (Continued)

### Alert Thresholds and Response Procedures
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  ALERT CONFIGURATION AND THRESHOLDS                                                         │
│                                                                                             │
│  Critical Alerts (Immediate Response Required):                                             │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Authentication System Down:                                                           │   │
│  │ • CAS server unreachable for >2 minutes                                               │   │
│  │ • LDAP server connection failure rate >50%                                            │   │
│  │ • Application authentication success rate <80%                                        │   │
│  │                                                                                       │   │
│  │ Security Incidents:                                                                   │   │
│  │ • >10 failed login attempts from single IP in 5 minutes                              │   │
│  │ • >5 unauthorized access attempts per user in 10 minutes                             │   │
│  │ • CSRF token validation failure rate >5%                                              │   │
│  │ • Session hijacking indicators detected                                               │   │
│  │                                                                                       │   │
│  │ Performance Degradation:                                                              │   │
│  │ • Average authentication time >10 seconds                                             │   │
│  │ • LDAP query response time >5 seconds                                                 │   │
│  │ • Cache hit ratio <70%                                                                │   │
│  │ • Connection pool exhaustion (>90% utilization)                                       │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Warning Alerts (Monitor and Investigate):                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ System Health:                                                                        │   │
│  │ • Authentication success rate 80-90%                                                  │   │
│  │ • LDAP connection failures 10-50%                                                     │   │
│  │ • Average authentication time 5-10 seconds                                            │   │
│  │ • Memory usage >80% for cache                                                         │   │
│  │                                                                                       │   │
│  │ User Behavior:                                                                        │   │
│  │ • Unusual login patterns (off-hours, new locations)                                   │   │
│  │ • Multiple concurrent sessions per user                                               │   │
│  │ • High number of session timeouts                                                     │   │
│  │ • Permission escalation attempts                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Alert Response Procedures:                                                                │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Critical Alert Response (Within 5 minutes):                                           │   │
│  │ 1. Acknowledge alert in monitoring system                                             │   │
│  │ 2. Check system status dashboard                                                      │   │
│  │ 3. Verify CAS and LDAP server connectivity                                            │   │
│  │ 4. Review recent application logs                                                     │   │
│  │ 5. Escalate to on-call engineer if needed                                             │   │
│  │ 6. Implement fallback procedures if available                                         │   │
│  │                                                                                       │   │
│  │ Security Incident Response:                                                           │   │
│  │ 1. Document incident details and timeline                                             │   │
│  │ 2. Block suspicious IP addresses if confirmed threat                                  │   │
│  │ 3. Invalidate affected user sessions                                                  │   │
│  │ 4. Notify security team and affected users                                            │   │
│  │ 5. Preserve logs for forensic analysis                                                │   │
│  │ 6. Update security policies if needed                                                 │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Comprehensive Logging Strategy
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  AUTHENTICATION LOGGING FRAMEWORK                                                           │
│                                                                                             │
│  Log4j Configuration (log4j2.xml):                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ <Configuration>                                                                       │   │
│  │   <Appenders>                                                                         │   │
│  │     <!-- Authentication Events -->                                                    │   │
│  │     <RollingFile name="AuthLog"                                                       │   │
│  │                  fileName="logs/authentication.log"                                   │   │
│  │                  filePattern="logs/authentication-%d{yyyy-MM-dd}-%i.log.gz">         │   │
│  │       <PatternLayout pattern="%d{ISO8601} [%t] %-5level %logger{36} - %msg%n"/>      │   │
│  │       <Policies>                                                                      │   │
│  │         <TimeBasedTriggeringPolicy />                                                 │   │
│  │         <SizeBasedTriggeringPolicy size="100MB"/>                                     │   │
│  │       </Policies>                                                                     │   │
│  │       <DefaultRolloverStrategy max="30"/>                                             │   │
│  │     </RollingFile>                                                                    │   │
│  │                                                                                       │   │
│  │     <!-- Security Events -->                                                          │   │
│  │     <RollingFile name="SecurityLog"                                                   │   │
│  │                  fileName="logs/security.log"                                         │   │
│  │                  filePattern="logs/security-%d{yyyy-MM-dd}-%i.log.gz">               │   │
│  │       <PatternLayout pattern="%d{ISO8601} [%t] %-5level %logger{36} - %msg%n"/>      │   │
│  │       <Policies>                                                                      │   │
│  │         <TimeBasedTriggeringPolicy />                                                 │   │
│  │         <SizeBasedTriggeringPolicy size="50MB"/>                                      │   │
│  │       </Policies>                                                                     │   │
│  │       <DefaultRolloverStrategy max="90"/>                                             │   │
│  │     </RollingFile>                                                                    │   │
│  │   </Appenders>                                                                        │   │
│  │                                                                                       │   │
│  │   <Loggers>                                                                           │   │
│  │     <Logger name="no.kf.handbook.auth" level="INFO" additivity="false">              │   │
│  │       <AppenderRef ref="AuthLog"/>                                                    │   │
│  │     </Logger>                                                                         │   │
│  │     <Logger name="no.kf.handbook.security" level="WARN" additivity="false">          │   │
│  │       <AppenderRef ref="SecurityLog"/>                                                │   │
│  │     </Logger>                                                                         │   │
│  │   </Loggers>                                                                          │   │
│  │ </Configuration>                                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Structured Logging Format:                                                               │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Authentication Success Log Entry:                                                     │   │
│  │ {                                                                                     │   │
│  │   "timestamp": "2025-01-27T10:15:30.123Z",                                           │   │
│  │   "event": "AUTHENTICATION_SUCCESS",                                                 │   │
│  │   "user": "<EMAIL>",                                                          │   │
│  │   "sessionId": "ABC123DEF456",                                                       │   │
│  │   "sourceIP": "*************",                                                       │   │
│  │   "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)...",                      │   │
│  │   "casTicket": "ST-123456789-abcdef",                                                │   │
│  │   "authenticationTime": 1250,                                                        │   │
│  │   "ldapLookupTime": 340,                                                             │   │
│  │   "organizations": ["9900", "9901"],                                                 │   │
│  │   "permissions": {                                                                   │   │
│  │     "globalAdmin": true,                                                             │   │
│  │     "localAdmin": true                                                               │   │
│  │   }                                                                                  │   │
│  │ }                                                                                     │   │
│  │                                                                                       │   │
│  │ Security Incident Log Entry:                                                          │   │
│  │ {                                                                                     │   │
│  │   "timestamp": "2025-01-27T10:20:15.456Z",                                           │   │
│  │   "event": "UNAUTHORIZED_ACCESS_ATTEMPT",                                            │   │
│  │   "severity": "HIGH",                                                                │   │
│  │   "user": "<EMAIL>",                                                          │   │
│  │   "requestedResource": "/9999/confidential-handbook",                                │   │
│  │   "userOrganizations": ["9900", "9901"],                                             │   │
│  │   "sourceIP": "*************",                                                       │   │
│  │   "sessionId": "ABC123DEF456",                                                       │   │
│  │   "action": "ACCESS_DENIED",                                                         │   │
│  │   "alertSent": true                                                                  │   │
│  │ }                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Production Deployment and Configuration

### Environment-Specific Configuration
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PRODUCTION ENVIRONMENT CONFIGURATION                                                       │
│                                                                                             │
│  Production Properties (src/main/resources/handboker.properties):                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ # Production CAS Configuration                                                        │   │
│  │ CasConfig=production                                                                  │   │
│  │ CasServerUrl=https://cas.kf.no/cas                                                    │   │
│  │ SiteUrl=https://handbook.kf.no                                                        │   │
│  │ MockBrukerAdm=false                                                                   │   │
│  │                                                                                       │   │
│  │ # LDAP Production Settings                                                            │   │
│  │ UseLDAP=true                                                                          │   │
│  │ LDAPHost=************                                                                 │   │
│  │ LDAPPort=389                                                                          │   │
│  │ LDAPBaseDN=dc=kinn,dc=id,dc=local                                                     │   │
│  │ LDAPBindDN=CN=DelegeringAppUser,OU=Service Accounts,DC=kinn,DC=id,DC=local           │   │
│  │ LDAPBindPassword=${LDAP_PASSWORD}  # Environment variable                             │   │
│  │                                                                                       │   │
│  │ # Connection Pool Production Settings                                                 │   │
│  │ LDAPPool.initialSize=10                                                               │   │
│  │ LDAPPool.maxActive=50                                                                 │   │
│  │ LDAPPool.maxIdle=20                                                                   │   │
│  │ LDAPPool.minIdle=5                                                                    │   │
│  │ LDAPPool.maxWait=10000                                                                │   │
│  │                                                                                       │   │
│  │ # Security Settings                                                                   │   │
│  │ SessionTimeout=30                                                                     │   │
│  │ CSRFProtection=true                                                                   │   │
│  │ SecureCookies=true                                                                    │   │
│  │ HTTPSOnly=true                                                                        │   │
│  │                                                                                       │   │
│  │ # Monitoring and Logging                                                              │   │
│  │ LogLevel=INFO                                                                         │   │
│  │ SecurityLogLevel=WARN                                                                 │   │
│  │ PerformanceMonitoring=true                                                            │   │
│  │ MetricsEnabled=true                                                                   │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Staging Environment (src/staging/resources/handboker.properties):                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ # Staging CAS Configuration                                                           │   │
│  │ CasConfig=staging                                                                     │   │
│  │ CasServerUrl=https://cas-staging.kf.no/cas                                            │   │
│  │ SiteUrl=https://handbook-staging.kf.no                                                │   │
│  │ MockBrukerAdm=false                                                                   │   │
│  │                                                                                       │   │
│  │ # Use BrukerAdm API instead of LDAP for staging                                       │   │
│  │ UseLDAP=false                                                                         │   │
│  │ BrukerAdmBaseUrl=https://brukeradm-staging.kf.no                                      │   │
│  │ ApplicationId=handboker-staging                                                       │   │
│  │ AccessKey=${BRUKERADM_API_KEY}                                                        │   │
│  │                                                                                       │   │
│  │ # Relaxed security for testing                                                        │   │
│  │ SessionTimeout=60                                                                     │   │
│  │ LogLevel=DEBUG                                                                        │   │
│  │ SecurityLogLevel=INFO                                                                 │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### SSL/TLS Configuration and Security Headers
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  SSL/TLS AND SECURITY HEADERS CONFIGURATION                                                 │
│                                                                                             │
│  Apache/Nginx SSL Configuration:                                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ <VirtualHost *:443>                                                                   │   │
│  │   ServerName handbook.kf.no                                                           │   │
│  │   DocumentRoot /var/www/handbook                                                      │   │
│  │                                                                                       │   │
│  │   # SSL Configuration                                                                 │   │
│  │   SSLEngine on                                                                        │   │
│  │   SSLCertificateFile /etc/ssl/certs/handbook.kf.no.crt                               │   │
│  │   SSLCertificateKeyFile /etc/ssl/private/handbook.kf.no.key                          │   │
│  │   SSLCertificateChainFile /etc/ssl/certs/intermediate.crt                            │   │
│  │                                                                                       │   │
│  │   # Security Headers                                                                  │   │
│  │   Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains" │   │
│  │   Header always set X-Frame-Options "SAMEORIGIN"                                     │   │
│  │   Header always set X-Content-Type-Options "nosniff"                                 │   │
│  │   Header always set X-XSS-Protection "1; mode=block"                                 │   │
│  │   Header always set Referrer-Policy "strict-origin-when-cross-origin"               │   │
│  │   Header always set Content-Security-Policy "default-src 'self'; script-src 'self' │   │
│  │          'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:;"  │   │
│  │                                                                                       │   │
│  │   # Proxy to Application Server                                                       │   │
│  │   ProxyPass / http://localhost:8080/                                                  │   │
│  │   ProxyPassReverse / http://localhost:8080/                                           │   │
│  │   ProxyPreserveHost On                                                                │   │
│  │   ProxyAddHeaders On                                                                  │   │
│  │ </VirtualHost>                                                                        │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Application Security Filter (web.xml):                                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ <filter>                                                                              │   │
│  │   <filter-name>SecurityHeadersFilter</filter-name>                                   │   │
│  │   <filter-class>no.kf.handbook.security.SecurityHeadersFilter</filter-class>         │   │
│  │   <init-param>                                                                        │   │
│  │     <param-name>hstsMaxAge</param-name>                                               │   │
│  │     <param-value>31536000</param-value>                                               │   │
│  │   </init-param>                                                                       │   │
│  │   <init-param>                                                                        │   │
│  │     <param-name>contentSecurityPolicy</param-name>                                   │   │
│  │     <param-value>default-src 'self'; script-src 'self' 'unsafe-inline';</param-value>│   │
│  │   </init-param>                                                                       │   │
│  │ </filter>                                                                             │   │
│  │                                                                                       │   │
│  │ <filter-mapping>                                                                      │   │
│  │   <filter-name>SecurityHeadersFilter</filter-name>                                   │   │
│  │   <url-pattern>/*</url-pattern>                                                       │   │
│  │ </filter-mapping>                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Database and Session Store Configuration
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  DATABASE AND SESSION PERSISTENCE                                                           │
│                                                                                             │
│  Database Configuration (application.conf):                                                │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ # Database Configuration                                                              │   │
│  │ db.default.driver=org.postgresql.Driver                                               │   │
│  │ db.default.url="*********************************************"                       │   │
│  │ db.default.username=${DB_USERNAME}                                                    │   │
│  │ db.default.password=${DB_PASSWORD}                                                    │   │
│  │                                                                                       │   │
│  │ # Connection Pool Settings                                                            │   │
│  │ db.default.hikaricp.minimumIdle=5                                                     │   │
│  │ db.default.hikaricp.maximumPoolSize=20                                                │   │
│  │ db.default.hikaricp.connectionTimeout=30000                                           │   │
│  │ db.default.hikaricp.idleTimeout=600000                                                │   │
│  │ db.default.hikaricp.maxLifetime=1800000                                               │   │
│  │                                                                                       │   │
│  │ # Session Store Configuration                                                         │   │
│  │ play.modules.enabled += "play.modules.reactivemongo.ReactiveMongoModule"             │   │
│  │ mongodb.uri = "mongodb://session-store.kf.no:27017/handbook_sessions"                │   │
│  │                                                                                       │   │
│  │ # Session Configuration                                                               │   │
│  │ play.http.session.cookieName="HANDBOOK_SESSION"                                       │   │
│  │ play.http.session.secure=true                                                         │   │
│  │ play.http.session.httpOnly=true                                                       │   │
│  │ play.http.session.maxAge=1800000  # 30 minutes                                        │   │
│  │ play.http.session.domain=".kf.no"                                                     │   │
│  │ play.http.session.sameSite="strict"                                                   │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Session Persistence Schema:                                                               │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ MongoDB Session Document:                                                             │   │
│  │ {                                                                                     │   │
│  │   "_id": "ABC123DEF456",                                                              │   │
│  │   "userId": "<EMAIL>",                                                         │   │
│  │   "createdAt": ISODate("2025-01-27T10:15:30.123Z"),                                  │   │
│  │   "lastAccessedAt": ISODate("2025-01-27T10:45:15.456Z"),                             │   │
│  │   "expiresAt": ISODate("2025-01-27T11:15:30.123Z"),                                  │   │
│  │   "sourceIP": "*************",                                                       │   │
│  │   "userAgent": "Mozilla/5.0...",                                                     │   │
│  │   "userData": {                                                                       │   │
│  │     "email": "<EMAIL>",                                                        │   │
│  │     "fullName": "John Doe",                                                           │   │
│  │     "organizations": ["9900", "9901"],                                                │   │
│  │     "permissions": {                                                                  │   │
│  │       "globalAdmin": true,                                                            │   │
│  │       "localAdmin": true                                                              │   │
│  │     },                                                                                │   │
│  │     "csrfToken": "xyz789abc123"                                                       │   │
│  │   },                                                                                  │   │
│  │   "securityFlags": {                                                                  │   │
│  │     "ipValidated": true,                                                              │   │
│  │     "userAgentValidated": true,                                                       │   │
│  │     "sessionRegenerated": true                                                        │   │
│  │   }                                                                                   │   │
│  │ }                                                                                     │   │
│  │                                                                                       │   │
│  │ Indexes:                                                                              │   │
│  │ • { "_id": 1 }                                                                        │   │
│  │ • { "userId": 1 }                                                                     │   │
│  │ • { "expiresAt": 1 } (TTL index for automatic cleanup)                               │   │
│  │ • { "sourceIP": 1, "createdAt": 1 } (security monitoring)                           │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Disaster Recovery and Business Continuity

### Failover Scenarios and Recovery Procedures
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  DISASTER RECOVERY PROCEDURES                                                               │
│                                                                                             │
│  Scenario 1: CAS Server Outage                                                             │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Impact: New users cannot authenticate, existing sessions continue                     │   │
│  │                                                                                       │   │
│  │ Immediate Actions (0-5 minutes):                                                      │   │
│  │ 1. Verify CAS server status and connectivity                                          │   │
│  │ 2. Check if issue is network-related or server failure                               │   │
│  │ 3. Activate emergency authentication bypass if available                              │   │
│  │ 4. Notify users via status page                                                       │   │
│  │                                                                                       │   │
│  │ Short-term Mitigation (5-30 minutes):                                                 │   │
│  │ 1. Switch to backup CAS server if available                                           │   │
│  │ 2. Extend session timeouts to reduce re-authentication needs                          │   │
│  │ 3. Enable read-only mode for critical operations                                      │   │
│  │ 4. Contact CAS server administrators                                                  │   │
│  │                                                                                       │   │
│  │ Long-term Recovery (30+ minutes):                                                     │   │
│  │ 1. Restore primary CAS server or implement permanent failover                        │   │
│  │ 2. Validate all authentication flows                                                  │   │
│  │ 3. Monitor for authentication issues                                                  │   │
│  │ 4. Post-incident review and documentation                                             │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Scenario 2: LDAP Server Failure                                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Impact: User authorization fails, degraded service                                    │   │
│  │                                                                                       │   │
│  │ Automatic Failover (0-2 minutes):                                                     │   │
│  │ 1. LDAP connection pool detects failures                                              │   │
│  │ 2. Switch to secondary LDAP server if configured                                      │   │
│  │ 3. Fall back to BrukerAdm API if available                                            │   │
│  │ 4. Use cached user data for existing sessions                                         │   │
│  │                                                                                       │   │
│  │ Manual Intervention (2-15 minutes):                                                   │   │
│  │ 1. Verify LDAP server status and logs                                                 │   │
│  │ 2. Check network connectivity and DNS resolution                                      │   │
│  │ 3. Enable emergency user provisioning if needed                                       │   │
│  │ 4. Extend cache timeouts to reduce LDAP dependency                                    │   │
│  │                                                                                       │   │
│  │ Recovery Actions:                                                                     │   │
│  │ 1. Restore LDAP service or implement permanent alternative                            │   │
│  │ 2. Refresh user caches with updated data                                              │   │
│  │ 3. Validate user permissions and access                                               │   │
│  │ 4. Monitor for authorization issues                                                   │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Scenario 3: Complete Application Outage                                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Impact: All handbook services unavailable                                             │   │
│  │                                                                                       │   │
│  │ Emergency Response (0-10 minutes):                                                    │   │
│  │ 1. Activate disaster recovery site if available                                       │   │
│  │ 2. Restore from latest database backup                                                │   │
│  │ 3. Deploy application to backup infrastructure                                        │   │
│  │ 4. Update DNS to point to backup site                                                 │   │
│  │                                                                                       │   │
│  │ Service Restoration (10-60