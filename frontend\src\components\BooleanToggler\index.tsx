import React, { type ReactNode, useState, useCallback } from "react";

export interface BooleanTogglerProps {
  children: (toggle: () => void, show: boolean) => ReactNode;
  initialValue?: boolean;
}

export const BooleanToggler: React.FC<BooleanTogglerProps> = ({
  children,
  initialValue = false,
}) => {
  const [show, setShow] = useState<boolean>(initialValue);

  const handleToggle = useCallback(() => {
    setShow((prevShow) => !prevShow);
  }, []);

  return <>{children(handleToggle, show)}</>;
};
