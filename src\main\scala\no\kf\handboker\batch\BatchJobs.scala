package no.kf.handboker.batch

import no.kf.handboker.config.{EmailNotificationsCron, HandbooksSyncCron, PruneEditorsCron, ElasticSearchReIndexCron, RemoveSubscriptionsCron}
import no.kf.handboker.{ComponentRegistry, ProductionRegistry}
import org.quartz.Scheduler
import org.quartz.JobBuilder.newJob
import org.quartz.impl.StdSchedulerFactory
import org.quartz.TriggerBuilder.newTrigger
import org.quartz.CronScheduleBuilder.cronSchedule

object BatchJobs {

  private lazy val scheduler: Scheduler = StdSchedulerFactory.getDefaultScheduler
  private lazy val componentRegistry: ComponentRegistry = ProductionRegistry.componentRegistry

  private def initializeSentralHandbookBatchJob = {
    scheduler.start()

    val job = newJob(classOf[SentralHandbookBatchJob])
        .withIdentity("SentralHandbook")
        .build()

    val trigger = newTrigger()
      .withIdentity("SentralHandbookTrigger")
      .startNow()
      .withSchedule(cronSchedule(componentRegistry.settings.settingFor(HandbooksSyncCron)))
      .forJob(job)
      .build()

    scheduler.scheduleJob(job, trigger)
  }

  private def initializeEmailNotificationsBatchJob = {
    scheduler.start()

    val job = newJob(classOf[EmailNotificationsBatchJob])
        .withIdentity("EmailNotifications")
        .build()

    val trigger = newTrigger()
      .withIdentity("EmailNotificationsTrigger")
      .startNow()
      .withSchedule(cronSchedule(componentRegistry.settings.settingFor(EmailNotificationsCron)))
      .forJob(job)
      .build()

    scheduler.scheduleJob(job, trigger)
  }

  private def initializePruneEditorsBatchJob = {
    scheduler.start()

    val job = newJob(classOf[PruneEditorsBatchJob])
      .withIdentity("PruneEditors")
      .build()

    val trigger = newTrigger()
      .withIdentity("PruneEditorsTrigger")
      .startNow()
      .withSchedule(cronSchedule(componentRegistry.settings.settingFor(PruneEditorsCron)))
      .forJob(job)
      .build()

    scheduler.scheduleJob(job, trigger)
  }

  private def initializeRemoveSubscriptionsBatchJob = {
    scheduler.start()

    val job = newJob(classOf[RemoveSubscriptionsBatchJob])
      .withIdentity("RemoveSubscriptions")
      .build()

    val trigger = newTrigger()
      .withIdentity("RemoveSubscriptionsTrigger")
      .startNow()
      .withSchedule(cronSchedule(componentRegistry.settings.settingFor(RemoveSubscriptionsCron)))
      .forJob(job)
      .build()

    scheduler.scheduleJob(job, trigger)
  }

  private def initializeElasticSearchReIndexJob = {
    scheduler.start()

    val job = newJob(classOf[ElasticSearchReIndexJob])
      .withIdentity("ElasticSearchReIndex")
      .build()

    val trigger = newTrigger()
      .withIdentity("ElasticSearchReIndexTrigger")
      .startNow()
      .withSchedule(cronSchedule(componentRegistry.settings.settingFor(ElasticSearchReIndexCron)))
      .forJob(job)
      .build()

    scheduler.scheduleJob(job, trigger)
  }

  def initializeBatchCronJobs = {
    initializeSentralHandbookBatchJob
    initializeEmailNotificationsBatchJob
    initializePruneEditorsBatchJob
    initializeElasticSearchReIndexJob
    initializeRemoveSubscriptionsBatchJob
  }

}
