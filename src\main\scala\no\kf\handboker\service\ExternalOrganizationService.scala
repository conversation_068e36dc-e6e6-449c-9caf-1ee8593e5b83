package no.kf.handboker.service

import no.kf.config.{AccessKey, ApplicationId, BrukerAdmBaseUrl, MockBrukerAdm}
import no.kf.handboker.config.{AppSettingComponent, CacheTimeLimit}
import no.kf.handboker.util.cache.Memoize
import no.kf.model.BrukerAdmExtOrg
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization.read

import scalaj.http._

trait ExternalOrganizationServiceComponent {
  this: AppSettingComponent =>

  class ExternalOrganizationServiceImpl extends ExternalOrganizationService {
    lazy val cacheTimeMinutes = settings.settingFor(CacheTimeLimit).toInt
    lazy val mockBrukerAdm = settings.settingFor(MockBrukerAdm).toBoolean
    private implicit lazy val jsonFormats: org.json4s.Formats = DefaultFormats

    // For fetching from brukeradm
    lazy val appId = settings.settingFor(ApplicationId)
    lazy val baseUrl = settings.settingFor(BrukerAdmBaseUrl)
    lazy val accessKey = settings.settingFor(AccessKey)
    lazy val brukerAdmUrl: String = s"$baseUrl/open/services/application-access/v2/$appId/accesskey/$accessKey"

    // Caches the result of the executed function
    private lazy val memoizedOrganizations = Memoize[Seq[BrukerAdmExtOrg]](doFetch, cacheTimeMinutes * 60000) // convert to milliseconds

    /**
      * Do the actuall call to BrukerAdm
      * @return
      */
    private def doFetch(): Seq[BrukerAdmExtOrg] = {
      if (mockBrukerAdm) {
        Seq(
          BrukerAdmExtOrg("0220", "Asker", Option("nb"), Nil),
          BrukerAdmExtOrg("9900", "Kommuneforlaget", Option("nb"), Nil),
          BrukerAdmExtOrg("0301", "Oslo", Option("nb"), Nil),
          BrukerAdmExtOrg("9999", "Storevik", Option("nb"), Nil),
          BrukerAdmExtOrg("3805", "Larvik", Option("nb"), Nil)
        )
      } else {
        val response = Http(brukerAdmUrl)
          .param("accesskey", accessKey)
          .asString

        read[Seq[BrukerAdmExtOrg]](response.body)
      }
    }

    override def fetchOrganizationsFromBrukerAdm(): Seq[BrukerAdmExtOrg] = {
      // Read from cache
      memoizedOrganizations()
    }
  }
}

trait ExternalOrganizationService {
  def fetchOrganizationsFromBrukerAdm(): Seq[BrukerAdmExtOrg]
}
