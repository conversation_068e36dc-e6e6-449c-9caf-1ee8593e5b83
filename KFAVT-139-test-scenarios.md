# Test Scenarios for KFAVT-139: Handbook Change Date Display

This document outlines comprehensive test scenarios for verifying the correct display of change dates in the merge/change handling view (KFAVT-139). The focus is on ensuring that the system accurately displays the last modification date for each specific section or chapter, rather than showing the last synchronization date.

---

## Test Environment Details

**Required Browser Versions:**
- Google Chrome 137.0.7151.120
- Microsoft Edge 138.0.3351.55

**Application Version:**
- Handbooks Production 2.2.33

**Issue Overview:**
The merge/change handling view currently displays incorrect "last central change" dates. Instead of showing when specific chapters/sections were actually modified, it shows the last synchronization date for all items, leading to misleading information for editors making merge decisions.

---

## Test Scenarios

### 1. Basic Date Display Functionality
**Description:** Verify that the system displays correct modification dates for chapters and sections in the merge view.

**Prerequisites:**
- Access to both local and central handbooks
- Admin permissions
- Test data with known modification dates

**Steps:**
1. Access the merge view for a specific handbook
2. Note the current displayed dates for multiple sections/chapters
3. Perform a central handbook synchronization
4. Review the dates again for the same sections/chapters
5. Compare the displayed dates with actual modification timestamps

**Expected Results:**
- Each section/title displays its actual last modification date
- Dates remain unchanged for unmodified content after sync
- Modification dates are accurate and match database records

### 2. Synchronization Impact Testing
**Description:** Validate that synchronization operations don't incorrectly update modification dates.

**Prerequisites:**
- Test environment with multiple handbooks
- Ability to perform manual synchronization
- Access to both central and local instances

**Steps:**
1. Document current dates for a set of test sections
2. Modify content in the central handbook
3. Perform synchronization
4. Verify dates for:
   - Modified sections (should update)
   - Unmodified sections (should remain unchanged)
5. Repeat process with different content types

**Expected Results:**
- Only modified content shows updated dates
- Unmodified content retains original dates
- Dates accurately reflect the actual modification time

### 3. Mixed Content Date Handling
**Description:** Test date handling for different content types (titles, HTML content) within the same section.

**Prerequisites:**
- Test sections with various content types
- Admin access to both central and local systems

**Steps:**
1. Select a test section with both title and content
2. Note current dates for both components
3. Modify only the title
4. Verify date updates for title only
5. Modify only the content
6. Verify date updates for content only
7. Modify both simultaneously
8. Confirm appropriate date updates

**Expected Results:**
- Title and content modifications tracked separately
- Correct timestamps shown for each component
- Combined modifications properly reflected

### 4. Edge Cases
**Description:** Test system behavior with various edge cases and unusual scenarios.

**Prerequisites:**
- Test environment with sample data
- Admin access rights

**Steps:**
1. Test with:
   - Extremely old modification dates
   - Future dates (if possible)
   - NULL dates in database
   - Multiple rapid modifications
   - Concurrent modifications
2. Document system behavior in each case

**Expected Results:**
- System handles all date scenarios gracefully
- No display errors or inconsistencies
- Proper error handling for invalid dates

### 5. Performance Impact
**Description:** Evaluate system performance with the new date tracking implementation.

**Prerequisites:**
- Access to performance monitoring tools
- Test environment with significant data volume

**Steps:**
1. Measure baseline performance metrics
2. Perform operations with:
   - Small handbooks (< 10 sections)
   - Medium handbooks (10-50 sections)
   - Large handbooks (> 50 sections)
3. Monitor:
   - Page load times
   - Synchronization duration
   - Database query performance

**Expected Results:**
- No significant performance degradation
- Response times within acceptable limits
- Efficient database queries

### 6. Long-term Monitoring
**Description:** Monitor date accuracy and system stability over an extended period.

**Prerequisites:**
- Monitoring tools configured
- Test environment with regular usage

**Steps:**
1. Set up monitoring for:
   - Date accuracy checks
   - Synchronization operations
   - Error occurrences
2. Monitor system for multiple days
3. Document any anomalies
4. Verify date consistency

**Expected Results:**
- Consistent date accuracy over time
- No date reset issues
- Stable system performance

---

## Success Criteria and Metrics

**Primary Requirements:**
- All modification dates accurately displayed
- Separate tracking for title and content changes
- No false date updates during synchronization

**Failure Handling:**
- Clear error messages for date-related issues
- Graceful handling of invalid dates
- Proper logging of date-related problems

**Performance Standards:**
- Page load time < 2 seconds with date information
- Synchronization overhead < 10% compared to previous version
- No impact on database query performance

---

## Regression Testing Guidelines

**When to Run Tests:**
- After any changes to date handling logic
- Before each production deployment
- After database schema updates
- During major version upgrades

**Special Considerations:**
- Test with various database states
- Verify backwards compatibility
- Check all supported browsers
- Test with different time zones

**Documentation Requirements:**
- Record all test execution results
- Document any deviations from expected behavior
- Maintain test data sets
- Update test cases as needed

---

> **Note for Test Executors:**
> These test scenarios are designed to ensure comprehensive validation of the date display functionality. Pay special attention to synchronization operations and their impact on displayed dates. Document any unexpected behavior or edge cases encountered during testing.

---

**Related Information:**
- [Return to Test Cases Directory](Handbook Feature Tests)
- [View Jira Ticket](https://kf-no.atlassian.net/browse/KFAVT-139)
- [Database Schema Documentation](Database Schema)
- Last Updated: July 18, 2025
