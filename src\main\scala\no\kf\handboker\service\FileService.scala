package no.kf.handboker.service

import better.files._
import no.kf.exception.KfException
import no.kf.handboker.config.{AppSettingComponent, UploadFolder}
import no.kf.handboker.model.KFFile
import no.kf.util.Logging

trait FileServiceComponent {
  this: AppSettingComponent =>

  val fileService: FileService

  class FileServiceImpl extends FileService with Logging {
    lazy val rootFolder = settings.settingFor(UploadFolder)

    override def persistFile(kfFile: KFFile): String = {
      write(buildOutputDirectory(kfFile.createdName), kfFile.file)

      s"/handboker/files/${kfFile.createdName}"
    }

    override def retrieveFile(fileName: String): File = {
      read(buildOutputDirectory(fileName))
    }

    override def deleteFile(fileName: String): Boolean = {
      delete(buildOutputDirectory(fileName))
    }

    private def buildOutputDirectory(fileName: String): File = {
      rootFolder / folderNameFromId(fileName) / fileName
    }

    private def folderNameFromId(id: String) = id.take(2)

    def write(file: File, content: Array[Byte]): Unit = {
      file.createIfNotExists(createParents = true)(File.Attributes.default)
        .writeByteArray(content)(File.OpenOptions.default)
    }

    def read(file: File): File = {
      throwErrorIf(!file.exists, s"Could not find the following file: ${file.path}")
      file
    }

    def delete(file: File): Boolean = {
      if (file.exists) {
        file.delete(false)
        true
      } else {
        log.warn(s"Unable to delete. Could not find file ${file.pathAsString}")
        false
      }
    }

    private def throwErrorIf(func: => Boolean, error: String) = if (func) {
      log.error(error)
      throw new KfException(error)
    }
  }

}

trait FileService {
  def persistFile(kfFile: KFFile): String
  def retrieveFile(fileId: String): File
  def deleteFile(fileId: String): Boolean
}
