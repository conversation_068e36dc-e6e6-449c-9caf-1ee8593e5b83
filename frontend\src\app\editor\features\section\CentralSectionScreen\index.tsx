import React, { useState, useCallback, useEffect } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import { Button, Icon, Title, Columns, Column, Group } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Spinner } from "@/shared/components/Spinner";
import { NoSelectionScreen } from "../../handbook/NoSelectionScreen";
import { CentralDeleteButton as DeleteButton } from "../../central/CentralDeleteButton";
import { CentralMetadata as Metadata } from "../../central/CentralMetadata";
import { Wysiwyg } from "../../shared/Wysiwyg";
import { ReadingLinkModal } from "../ReadingLinkModal";
import {
  useGetCentralSectionsQuery,
  useDeleteCentralSectionMutation,
  useGetReadingLinksQuery,
} from "@/store/services/handbook/centralHandbookApi";
import { toast } from "@/shared/components/Toast";

export const CentralSectionScreen: React.FC = () => {
  const t = usePrefixedTranslation("editor.containers.SectionSelection");
  const navigate = useNavigate();

  const { sectionId } = useParams<{ sectionId: string }>();
  const [showLinkModal, setShowLinkModal] = useState<boolean>(false);

  const { data: sections = [], isLoading: isLoadingSections } =
    useGetCentralSectionsQuery();
  const { data: readingLinks = [] } = useGetReadingLinksQuery();
  const [deleteCentralSection] = useDeleteCentralSectionMutation();

  const centralSection = sections.find((section) => section.id === sectionId);

  useEffect(() => {
    if (
      !isLoadingSections &&
      sectionId &&
      !centralSection &&
      sections.length > 0
    ) {
      const timer = setTimeout(() => {
        const currentSection = sections.find(
          (section) => section.id === sectionId
        );
        if (!currentSection && sections.length > 0) {
          toast.error(t("editor.error.sectionNotFound", { id: sectionId }));
        }
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isLoadingSections, sectionId, centralSection, sections, t]);

  const handleCloseLinkModal = useCallback(() => {
    setShowLinkModal(false);
  }, []);

  const handleOpenLinkModal = useCallback(() => {
    setShowLinkModal(true);
  }, []);

  const handleDeleteSection = useCallback(async () => {
    if (!centralSection?.id) return;

    try {
      await deleteCentralSection(centralSection.id).unwrap();
      toast.success(t("editor.success.sectionDeleted"));
      navigate(
        `/central-editor/${centralSection.centralHandbookId}/chapter/${centralSection.parentId}`
      );
    } catch (error) {
      console.error("Error deleting section:", error);
      toast.error(t("editor.error.sectionDeleteFailed"));
    }
  }, [
    deleteCentralSection,
    centralSection?.id,
    centralSection?.centralHandbookId,
    centralSection?.parentId,
    navigate,
    t,
  ]);

  if (!sectionId) {
    return <NoSelectionScreen />;
  }

  if (isLoadingSections) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (!centralSection) {
    return <NoSelectionScreen />;
  }

  return (
    <>
      <Columns>
        <Column>
          <Title>
            <Icon
              icon="RegFileLines"
              size="medium"
              style={{ marginRight: "1rem" }}
            />
            <span>{centralSection.title}</span>
          </Title>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <Group>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralSection.centralHandbookId}/section/${centralSection.id}/edit`}
              size="small"
              icon="pencil"
            >
              {t("editButton")}
            </Button>
            <Button control size="small" onClick={handleOpenLinkModal}>
              Hent lenke
            </Button>
          </Group>
        </Column>

        <Column narrow>
          <Group>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralSection.centralHandbookId}/section/${centralSection.id}/move`}
              size="small"
            >
              {t("moveButton")}
            </Button>
            <DeleteButton
              toDelete={{
                id: centralSection.id!,
                title: centralSection.title,
                type: "SECTION" as const,
              }}
              onDelete={handleDeleteSection}
              readlinkExists={readingLinks && readingLinks.length > 0}
            />
          </Group>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <hr />
          <Metadata
            element={{
              ...centralSection,
              handbookId: centralSection.centralHandbookId,
              localTitleChange: false,
              pendingTitleChange: false,
              localTextChange: false,
              pendingTextChange: false,
              pendingDeletion: false,
              isDeleted: false,
              textUpdatedBy: centralSection.htmlUpdatedBy,
              textUpdatedDate: centralSection.htmlUpdatedDate,
            }}
          />
          <hr />
        </Column>
      </Columns>

      <Wysiwyg value={centralSection.html} disabled />

      <ReadingLinkModal
        centralSection={centralSection}
        isOpen={showLinkModal}
        onClose={handleCloseLinkModal}
      />
    </>
  );
};
