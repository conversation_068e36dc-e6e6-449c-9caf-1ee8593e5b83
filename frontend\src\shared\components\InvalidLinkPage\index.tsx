import React from "react";
import { Section, Container, Title, Subtitle } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

export const InvalidLinkPage: React.FC = () => {
  const t = usePrefixedTranslation("common.components.InvalidLinkPage");

  return (
    <Section>
      <Container>
        <Title>{t("header")}</Title>
        <Subtitle>{t("title")}</Subtitle>
        <p>{t("message")}</p>
      </Container>
    </Section>
  );
};