import { baseApi, API_BASE_URLS } from "@/store/api";
import { generateTempId } from "./optimisticUpdateHooks";
import type {
  Handbook,
  Chapter,
  Section,
  Comment,
  LocalEditor,
  Link,
  LinkCollection,
  AttachmentFile,
  AttachmentUploadResponse,
  AttachmentCountResponse,
  SaveAttachmentsRequest,
} from "@/types";

export interface LocalHandbooksGetResponse {
  handbooks: Handbook[];
  chapters: Chapter[];
  sections: Section[];
}

export const localHandbookApi = baseApi.injectEndpoints({
  overrideExisting: process.env.NODE_ENV === "development",
  endpoints: (builder) => ({
    getLocalHandbooks: builder.query<LocalHandbooksGetResponse, void>({
      query: () => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local`,
        method: "GET",
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      }),
      transformResponse: (response: {
        handbooks: Omit<Handbook, "type">[];
        chapters: Omit<Chapter, "type">[];
        sections: Omit<Section, "type">[];
      }) => {
        return {
          handbooks: response.handbooks.map((handbook) => ({
            ...handbook,
            type: "HANDBOOK" as const,
          })),
          chapters: response.chapters.map((chapter) => ({
            ...chapter,
            type: "CHAPTER" as const,
          })),
          sections: response.sections.map((section) => ({
            ...section,
            type: "SECTION" as const,
          })),
        };
      },
      keepUnusedDataFor: 300,
      providesTags: (result) =>
        result
          ? [
              ...result.handbooks.map(({ id }) => ({
                type: "Handbook" as const,
                id,
              })),
              ...result.chapters.map(({ id }) => ({
                type: "Chapter" as const,
                id,
              })),
              ...result.sections.map(({ id }) => ({
                type: "Section" as const,
                id,
              })),
              "Handbook",
              "Chapter",
              "Section",
            ]
          : ["Handbook", "Chapter", "Section"],
    }),


    getLocalHandbook: builder.query<Handbook, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/handbook/${id}/`,
        method: "GET",
      }),
      transformResponse: (response: Omit<Handbook, "type">) => ({
        ...response,
        type: "HANDBOOK" as const,
      }),
      providesTags: (_result, _error, id) => [{ type: "Handbook", id }],
    }),

    saveLocalHandbook: builder.mutation<Handbook, Handbook>({
      query: (handbook) => {
        const { type, ...handbookData } = handbook;
        return {
          url: `${API_BASE_URLS.HANDBOOKS}/local/handbook/${handbook.id || ""}`,
          method: "POST",
          body: handbookData,
        };
      },
      onQueryStarted: async (handbook, { dispatch, queryFulfilled }) => {
        // Optimistic update for getLocalHandbooks query
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalHandbooks",
            undefined,
            (draft) => {
              if (handbook.id) {
                // Update existing handbook
                const existingIndex = draft.handbooks.findIndex(
                  (h) => h.id === handbook.id
                );
                if (existingIndex !== -1) {
                  draft.handbooks[existingIndex] = {
                    ...draft.handbooks[existingIndex],
                    ...handbook,
                  };
                }
              } else {
                // Add new handbook with temporary ID
                const tempId = generateTempId("handbook");
                const newHandbook = { ...handbook, id: tempId };
                draft.handbooks.unshift(newHandbook);
              }
            }
          )
        );

        // Also optimistically update the individual getLocalHandbook query if it exists
        let handbookPatchResult = null;
        if (handbook.id) {
          handbookPatchResult = dispatch(
            localHandbookApi.util.updateQueryData(
              "getLocalHandbook",
              handbook.id,
              (draft) => {
                Object.assign(draft, handbook);
              }
            )
          );
        }

        try {
          const { data: savedHandbook } = await queryFulfilled;

          // Update both queries with the actual server response
          dispatch(
            localHandbookApi.util.updateQueryData(
              "getLocalHandbooks",
              undefined,
              (draft) => {
                if (!handbook.id && savedHandbook.id) {
                  // Create operation - replace temp handbook with real one
                  const tempHandbook = draft.handbooks.find((h) =>
                    h.id?.startsWith("temp-")
                  );
                  if (tempHandbook) {
                    Object.assign(tempHandbook, savedHandbook);
                  }
                } else if (handbook.id) {
                  // Update operation - update existing handbook with server response
                  const existingIndex = draft.handbooks.findIndex(
                    (h) => h.id === handbook.id
                  );
                  if (existingIndex !== -1) {
                    draft.handbooks[existingIndex] = savedHandbook;
                  }
                }
              }
            )
          );

          // Update or create the individual handbook query cache
          dispatch(
            localHandbookApi.util.upsertQueryData(
              "getLocalHandbook",
              savedHandbook.id!,
              savedHandbook
            )
          );

        } catch (error) {
          // Revert optimistic updates on error
          patchResult.undo();
          if (handbookPatchResult) {
            handbookPatchResult.undo();
          }
        }
      },
      invalidatesTags: (_result, error, { id }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? [{ type: "Handbook", id }] : [];
      },
    }),

    deleteLocalHandbook: builder.mutation<void, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/${id}`,
        method: "DELETE",
      }),
      onQueryStarted: async (handbookId, { dispatch, queryFulfilled }) => {
        // Optimistic update - remove handbook from list
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalHandbooks",
            undefined,
            (draft) => {
              draft.handbooks = draft.handbooks.filter(
                (h) => h.id !== handbookId
              );
              // Also remove associated chapters and sections
              draft.chapters = draft.chapters.filter(
                (c) => c.handbookId !== handbookId
              );
              draft.sections = draft.sections.filter(
                (s) => s.handbookId !== handbookId
              );
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, id) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        // On error, only invalidate the specific handbook, not all chapters/sections
        return error ? [{ type: "Handbook", id }] : [];
      },
    }),


    getLocalChapter: builder.query<Chapter, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/chapter/${id}/`,
        method: "GET",
      }),
      transformResponse: (response: Omit<Chapter, "type">) => ({
        ...response,
        type: "CHAPTER" as const,
      }),
      providesTags: (_result, _error, id) => [{ type: "Chapter", id }],
    }),

    saveLocalChapter: builder.mutation<
      Chapter,
      { chapter: Partial<Chapter>; centralChange?: boolean }
    >({
      query: ({ chapter, centralChange = false }) => {
        const { type, ...chapterData } = chapter;
        return {
          url: `${API_BASE_URLS.HANDBOOKS}/local/chapter${chapter.id ? `/${chapter.id}/${centralChange ? "KF" : "local"}` : ""}`,
          method: "POST",
          body: chapterData,
        };
      },
      onQueryStarted: async ({ chapter }, { dispatch, queryFulfilled }) => {
        // Optimistic update for getLocalHandbooks query
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalHandbooks",
            undefined,
            (draft) => {
              if (chapter.id) {
                // Update existing chapter
                const existingIndex = draft.chapters.findIndex(
                  (c) => c.id === chapter.id
                );
                if (existingIndex !== -1) {
                  draft.chapters[existingIndex] = {
                    ...draft.chapters[existingIndex],
                    ...chapter,
                  };
                }
              } else {
                // Add new chapter with temporary ID
                const tempId = generateTempId("chapter");
                const newChapter = {
                  ...chapter,
                  id: tempId,
                  sortOrder: draft.chapters.filter(
                    (c) => c.handbookId === chapter.handbookId
                  ).length,
                } as Chapter;
                draft.chapters.push(newChapter);
              }
            }
          )
        );

        // Also optimistically update the individual getLocalChapter query if it exists
        let chapterPatchResult = null;
        if (chapter.id) {
          chapterPatchResult = dispatch(
            localHandbookApi.util.updateQueryData(
              "getLocalChapter",
              chapter.id,
              (draft) => {
                Object.assign(draft, chapter);
              }
            )
          );
        }

        try {
          const { data: savedChapter } = await queryFulfilled;

          // Update both queries with the actual server response
          dispatch(
            localHandbookApi.util.updateQueryData(
              "getLocalHandbooks",
              undefined,
              (draft) => {
                if (!chapter.id && savedChapter.id) {
                  // Create operation - replace temp chapter with real one
                  const tempChapter = draft.chapters.find((c) =>
                    c.id?.startsWith("temp-")
                  );
                  if (tempChapter) {
                    Object.assign(tempChapter, savedChapter);
                  }
                } else if (chapter.id) {
                  // Update operation - update existing chapter with server response
                  const existingIndex = draft.chapters.findIndex(
                    (c) => c.id === chapter.id
                  );
                  if (existingIndex !== -1) {
                    draft.chapters[existingIndex] = savedChapter;
                  }
                }
              }
            )
          );

          // Update or create the individual chapter query cache
          dispatch(
            localHandbookApi.util.upsertQueryData(
              "getLocalChapter",
              savedChapter.id!,
              savedChapter
            )
          );

        } catch (error) {
          // Revert optimistic updates on error
          patchResult.undo();
          if (chapterPatchResult) {
            chapterPatchResult.undo();
          }
        }
      },
      invalidatesTags: (_result, error, { chapter: { id } }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        // On error, only invalidate the specific chapter, not all chapters/handbooks
        return error ? [{ type: "Chapter", id }] : [];
      },
    }),

    deleteLocalChapter: builder.mutation<void, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/chapter/${id}`,
        method: "DELETE",
      }),
      onQueryStarted: async (chapterId, { dispatch, queryFulfilled }) => {
        // Optimistic update - remove chapter and its sections from list
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalHandbooks",
            undefined,
            (draft) => {
              draft.chapters = draft.chapters.filter((c) => c.id !== chapterId);
              // Also remove sections that belong to this chapter
              draft.sections = draft.sections.filter(
                (s) => s.parentId !== chapterId
              );
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, id) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        // On error, only invalidate the specific chapter, not all chapters/sections/handbooks
        return error ? [{ type: "Chapter", id }] : [];
      },
    }),

    getLocalChapterVersions: builder.query<Chapter[], string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/chapter/versions/${id}/`,
        method: "GET",
      }),
      transformResponse: (response: Omit<Chapter, "type">[]) => {
        return response.map((chapter) => ({
          ...chapter,
          type: "CHAPTER" as const,
        }));
      },
      providesTags: (_result, _error, id) => [
        { type: "Chapter", id: `versions-${id}` },
      ],
    }),

    getLocalChapterVersion: builder.query<Chapter, string>({
      query: (versionId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/chapter/version/${versionId}/`,
        method: "GET",
      }),
      transformResponse: (response: Omit<Chapter, "type">) => ({
        ...response,
        type: "CHAPTER" as const,
      }),
      providesTags: (_result, _error, versionId) => [
        { type: "Chapter", id: `version-${versionId}` },
      ],
    }),


    getLocalSection: builder.query<Section, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/section/${id}/`,
        method: "GET",
      }),
      transformResponse: (response: Omit<Section, "type">) => ({
        ...response,
        type: "SECTION" as const,
      }),
      providesTags: (_result, _error, id) => [{ type: "Section", id }],
    }),

    saveLocalSection: builder.mutation<
      Section,
      {
        section: Section;
        centralChange?: boolean;
        centralTextChange?: boolean;
      }
    >({
      query: ({
        section,
        centralChange = false,
        centralTextChange = false,
      }) => {
        const { type, ...sectionData } = section;
        return {
          url: `${API_BASE_URLS.HANDBOOKS}/local/section${
            section.id
              ? `/${section.id}/${centralChange ? "KF" : "local"}/${centralTextChange ? "KF" : "local"}`
              : ""
          }`,
          method: "POST",
          body: sectionData,
        };
      },
      onQueryStarted: async ({ section }, { dispatch, queryFulfilled }) => {
        // Optimistic update for getLocalHandbooks query
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalHandbooks",
            undefined,
            (draft) => {
              if (section.id) {
                // Update existing section
                const existingIndex = draft.sections.findIndex(
                  (s) => s.id === section.id
                );
                if (existingIndex !== -1) {
                  draft.sections[existingIndex] = {
                    ...draft.sections[existingIndex],
                    ...section,
                  };
                }
              } else {
                // Add new section with temporary ID
                const tempId = generateTempId("section");
                const newSection = {
                  ...section,
                  id: tempId,
                  sortOrder: draft.sections.filter(
                    (s) => s.parentId === section.parentId
                  ).length,
                };
                draft.sections.push(newSection);
              }
            }
          )
        );

        // Also optimistically update the individual getLocalSection query if it exists
        let sectionPatchResult = null;
        if (section.id) {
          sectionPatchResult = dispatch(
            localHandbookApi.util.updateQueryData(
              "getLocalSection",
              section.id,
              (draft) => {
                Object.assign(draft, section);
              }
            )
          );
        }

        try {
          const { data: savedSection } = await queryFulfilled;

          // Update both queries with the actual server response
          dispatch(
            localHandbookApi.util.updateQueryData(
              "getLocalHandbooks",
              undefined,
              (draft) => {
                if (!section.id && savedSection.id) {
                  // Create operation - replace temp section with real one
                  const tempSection = draft.sections.find((s) =>
                    s.id?.startsWith("temp-")
                  );
                  if (tempSection) {
                    Object.assign(tempSection, savedSection);
                  }
                } else if (section.id) {
                  // Update operation - update existing section with server response
                  const existingIndex = draft.sections.findIndex(
                    (s) => s.id === section.id
                  );
                  if (existingIndex !== -1) {
                    draft.sections[existingIndex] = savedSection;
                  }
                }
              }
            )
          );

          // Update or create the individual section query cache
          dispatch(
            localHandbookApi.util.upsertQueryData(
              "getLocalSection",
              savedSection.id!,
              savedSection
            )
          );

        } catch (error) {
          // Revert optimistic updates on error
          patchResult.undo();
          if (sectionPatchResult) {
            sectionPatchResult.undo();
          }
        }
      },
      invalidatesTags: (_result, error, { section: { id } }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        // On error, only invalidate the specific section, not all sections/chapters/handbooks
        return error ? [{ type: "Section", id }] : [];
      },
    }),

    deleteLocalSection: builder.mutation<void, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/section/${id}`,
        method: "DELETE",
      }),
      onQueryStarted: async (sectionId, { dispatch, queryFulfilled }) => {
        // Optimistic update - remove section from list
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalHandbooks",
            undefined,
            (draft) => {
              draft.sections = draft.sections.filter((s) => s.id !== sectionId);
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, id) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        // On error, only invalidate the specific section, not all sections/chapters/handbooks
        return error ? [{ type: "Section", id }] : [];
      },
    }),

    getLocalSectionVersions: builder.query<Section[], string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/section/versions/${id}/`,
        method: "GET",
      }),
      transformResponse: (response: Omit<Section, "type">[]) => {
        return response.map((section) => ({
          ...section,
          type: "SECTION" as const,
        }));
      },
      providesTags: (_result, _error, id) => [
        { type: "Section", id: `versions-${id}` },
      ],
    }),

    getLocalSectionVersion: builder.query<Section, string>({
      query: (versionId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/section/version/${versionId}/`,
        method: "GET",
      }),
      transformResponse: (response: Omit<Section, "type">) => ({
        ...response,
        type: "SECTION" as const,
      }),
      providesTags: (_result, _error, versionId) => [
        { type: "Section", id: `version-${versionId}` },
      ],
    }),


    sortLocalItems: builder.mutation<string[], string[]>({
      query: (ids) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/sort/`,
        method: "POST",
        body: ids,
      }),
      onQueryStarted: async (sortedIds, { dispatch, queryFulfilled }) => {
        // Optimistic update - reorder items in cache
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalHandbooks",
            undefined,
            (draft) => {
              // Apply new sort order to handbooks, chapters, and sections
              const updateSortOrder = (items: any[]) => {
                sortedIds.forEach((id, index) => {
                  const item = items.find((item) => item.id === id);
                  if (item) {
                    item.sortOrder = index;
                  }
                });
                // Sort the array by the new sortOrder
                items.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
              };

              updateSortOrder(draft.handbooks);
              updateSortOrder(draft.chapters);
              updateSortOrder(draft.sections);
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: ["Handbook", "Chapter", "Section"],
    }),


    getLocalComments: builder.query<Comment[], string>({
      query: (handbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/comments/${handbookId}`,
        method: "GET",
      }),
      providesTags: (result, _error, handbookId) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Comment" as const, id })),
              { type: "Comment", id: `handbook-${handbookId}` },
            ]
          : [{ type: "Comment", id: `handbook-${handbookId}` }],
    }),

    createLocalComment: builder.mutation<Comment, Omit<Comment, "id">>({
      query: (newComment) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/comments/`,
        method: "POST",
        body: newComment,
      }),
      onQueryStarted: async (newComment, { dispatch, queryFulfilled }) => {
        // Optimistic update - add comment to list
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalComments",
            newComment.handbookId,
            (draft) => {
              const tempComment = {
                ...newComment,
                id: generateTempId("comment"),
                createdDate: new Date().toISOString(),
              } as Comment;
              draft.push(tempComment);
            }
          )
        );

        try {
          const { data: savedComment } = await queryFulfilled;

          // Update the temporary comment with the real one
          if (savedComment) {
            dispatch(
              localHandbookApi.util.updateQueryData(
                "getLocalComments",
                newComment.handbookId,
                (draft) => {
                  const tempIndex = draft.findIndex((c) =>
                    c.id?.startsWith("temp-")
                  );
                  if (tempIndex !== -1) {
                    draft[tempIndex] = savedComment;
                  }
                }
              )
            );
          }
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, { handbookId }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? [{ type: "Comment", id: `handbook-${handbookId}` }] : [];
      },
    }),

    updateLocalComment: builder.mutation<Comment, Comment>({
      query: (comment) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/comments/`,
        method: "POST",
        body: comment,
      }),
      onQueryStarted: async (updatedComment, { dispatch, queryFulfilled }) => {
        // Optimistic update - update comment in list
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalComments",
            updatedComment.handbookId,
            (draft) => {
              const index = draft.findIndex((c) => c.id === updatedComment.id);
              if (index !== -1) {
                draft[index] = {
                  ...draft[index],
                  ...updatedComment,
                  updatedDate: new Date().toISOString(),
                };
              }
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, { id, handbookId: _handbookId }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? [{ type: "Comment", id }] : [];
      },
    }),

    // Delete a comment.
    deleteLocalComment: builder.mutation<
      void,
      { id: string; handbookId: string }
    >({
      query: ({ id }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/comments/${id}`,
        method: "DELETE",
      }),
      onQueryStarted: async (
        { id, handbookId },
        { dispatch, queryFulfilled }
      ) => {
        // Optimistic update - remove comment from list
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalComments",
            handbookId,
            (draft) => {
              return draft.filter((c) => c.id !== id);
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, { id }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? [{ type: "Comment", id }] : [];
      },
    }),


    // Get all local editors for a specific handbook.
    getLocalEditors: builder.query<LocalEditor[], string>({
      query: (handbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/editors/${handbookId}`,
        method: "GET",
      }),
      providesTags: (result, _error, handbookId) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: "Editor" as const, id })),
              { type: "Editor", id: `handbook-${handbookId}` },
            ]
          : [{ type: "Editor", id: `handbook-${handbookId}` }],
    }),

    // Add a new local editor.
    addLocalEditor: builder.mutation<
      LocalEditor,
      Omit<LocalEditor, "id" | "addedDate">
    >({
      query: (newEditor) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/editors/`,
        method: "POST",
        body: newEditor,
      }),
      invalidatesTags: (_result, error, { handbookId }) => {
        // Only invalidate if there was an error
        return error ? [{ type: "Editor", id: `handbook-${handbookId}` }] : [];
      },
    }),

    //
    // Delete a local editor assignment.
    deleteLocalEditor: builder.mutation<void, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/editors/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (_result, error, id) => {
        // Only invalidate if there was an error
        return error ? [{ type: "Editor", id }] : [];
      },
    }),

    // Get all handbook subscriptions for the current user.
    getLocalSubscriptions: builder.query<string[], void>({
      query: () => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/subscriptions/`,
        method: "GET",
      }),
      providesTags: [{ type: "User", id: "subscriptions" }],
    }),

    // Subscribe or unsubscribe from a handbook.
    toggleLocalSubscription: builder.mutation<
      void,
      { handbookId: string; subscribe: boolean }
    >({
      query: ({ handbookId, subscribe }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/local/${subscribe ? "subscribe" : "unsubscribe"}/${handbookId}`,
        method: "POST",
      }),
      onQueryStarted: async (
        { handbookId, subscribe },
        { dispatch, queryFulfilled }
      ) => {
        // Optimistic update for getLocalSubscriptions query
        const patchResult = dispatch(
          localHandbookApi.util.updateQueryData(
            "getLocalSubscriptions",
            undefined,
            (draft) => {
              if (subscribe) {
                // Add subscription if not already present
                if (!draft.includes(handbookId)) {
                  draft.push(handbookId);
                }
              } else {
                // Remove subscription
                const index = draft.indexOf(handbookId);
                if (index !== -1) {
                  draft.splice(index, 1);
                }
              }
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      // Only invalidate the specific subscription query, not all Handbook-tagged queries
      invalidatesTags: (_, error) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? [{ type: "User", id: "subscriptions" }] : [];
      },
    }),

    // Get all link collections for a specific handbook.
    getLinkCollections: builder.query<LinkCollection[], string>({
      query: (handbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/links/${handbookId}`,
        method: "GET",
      }),
      providesTags: (result, _error, handbookId) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: "LinkCollection" as const,
                id,
              })),
              { type: "LinkCollection", id: `handbook-${handbookId}` },
            ]
          : [{ type: "LinkCollection", id: `handbook-${handbookId}` }],
    }),

    // Create or update a link collection.
    saveLinkCollection: builder.mutation<LinkCollection, LinkCollection>({
      query: (linkCollection) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/links/link-collection`,
        method: "POST",
        body: linkCollection,
      }),
      invalidatesTags: (_result, error, { handbookId }) => {
        // Only invalidate if there was an error
        return error
          ? [{ type: "LinkCollection", id: `handbook-${handbookId}` }]
          : [];
      },
    }),

    // Delete a link collection.
    deleteLinkCollection: builder.mutation<void, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/links/link-collection/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (_result, error, id) => {
        // Only invalidate if there was an error
        return error ? [{ type: "LinkCollection", id }] : [];
      },
    }),

    // Create or update a link within a collection.
    saveLink: builder.mutation<Link, { link: Link; linkCollectionId: string }>({
      query: ({ link, linkCollectionId }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/links/link/${linkCollectionId}`,
        method: "POST",
        body: link,
      }),
      invalidatesTags: (_result, error, { linkCollectionId }) => {
        // Only invalidate if there was an error
        return error ? [{ type: "LinkCollection", id: linkCollectionId }] : [];
      },
    }),

    // Delete a link.
    deleteLink: builder.mutation<void, string>({
      query: (id) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/links/link/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["LinkCollection"],
    }),


    // Upload a file to temporary storage.
    uploadFile: builder.mutation<
      AttachmentUploadResponse,
      { file: File; onProgress?: (progress: number) => void }
    >({
      query: ({ file }) => {
        const formData = new FormData();
        formData.append("file", file);
        return {
          url: "/files",
          method: "POST",
          body: formData,
        };
      },
    }),

    // Delete a file from temporary or permanent storage.
    deleteFile: builder.mutation<void, string>({
      query: (fileId) => ({
        url: `/files/${fileId}`,
        method: "DELETE",
      }),
    }),

    // Download a file (returns blob).
    downloadFile: builder.query<Blob, string>({
      query: (fileId) => ({
        url: `/files/${fileId}`,
        method: "GET",
        responseHandler: (response) => response.blob(),
      }),
    }),

    // Get attachments for a chapter or section.
    getAttachments: builder.query<
      AttachmentFile[],
      { type: "chapter" | "section"; id: string }
    >({
      query: ({ type, id }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/file-links/${type}/${id}`,
        method: "GET",
      }),
      providesTags: (_result, _error, { type, id }) => [
        { type: "Attachment", id: `${type}-${id}` },
        "Attachment",
      ],
    }),

    // Get attachment count for a chapter or section.
    getAttachmentCount: builder.query<
      AttachmentCountResponse,
      { type: "chapter" | "section"; id: string }
    >({
      query: ({ type, id }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/file-links/${type}/${id}/count`,
        method: "GET",
      }),
      providesTags: (_result, _error, { type, id }) => [
        { type: "AttachmentCount", id: `${type}-${id}` },
      ],
    }),

    // Save attachment links (create/update/delete attachments for a chapter or section).
    saveAttachments: builder.mutation<AttachmentFile[], SaveAttachmentsRequest>(
      {
        query: (data) => ({
          url: `${API_BASE_URLS.HANDBOOKS}/file-links`,
          method: "POST",
          body: data,
        }),
        invalidatesTags: (_result, error, { belongsTo, ownerId }) => {
          // Only invalidate if there was an error
          return error
            ? [
                {
                  type: "Attachment",
                  id: `${belongsTo.toLowerCase()}-${ownerId}`,
                },
                {
                  type: "AttachmentCount",
                  id: `${belongsTo.toLowerCase()}-${ownerId}`,
                },
              ]
            : [];
        },
      }
    ),
  }),
});

// Export hooks for use in components
export const {
  // Main data hooks
  useGetLocalHandbooksQuery,
  useLazyGetLocalHandbooksQuery,

  // Handbook hooks
  useGetLocalHandbookQuery,
  useLazyGetLocalHandbookQuery,
  useSaveLocalHandbookMutation,
  useDeleteLocalHandbookMutation,

  // Chapter hooks
  useGetLocalChapterQuery,
  useLazyGetLocalChapterQuery,
  useSaveLocalChapterMutation,
  useDeleteLocalChapterMutation,
  useGetLocalChapterVersionsQuery,
  useGetLocalChapterVersionQuery,
  useLazyGetLocalChapterVersionsQuery,
  useLazyGetLocalChapterVersionQuery,

  // Section hooks
  useGetLocalSectionQuery,
  useLazyGetLocalSectionQuery,
  useSaveLocalSectionMutation,
  useDeleteLocalSectionMutation,
  useGetLocalSectionVersionsQuery,
  useGetLocalSectionVersionQuery,
  useLazyGetLocalSectionVersionsQuery,
  useLazyGetLocalSectionVersionQuery,

  // Sorting hooks
  useSortLocalItemsMutation,

  // Comment hooks
  useGetLocalCommentsQuery,
  useCreateLocalCommentMutation,
  useUpdateLocalCommentMutation,
  useDeleteLocalCommentMutation,
  useLazyGetLocalCommentsQuery,

  // Editor hooks
  useGetLocalEditorsQuery,
  useAddLocalEditorMutation,
  useDeleteLocalEditorMutation,
  useLazyGetLocalEditorsQuery,

  // Subscription hooks
  useGetLocalSubscriptionsQuery,
  useToggleLocalSubscriptionMutation,
  useLazyGetLocalSubscriptionsQuery,

  // Link Collection hooks
  useGetLinkCollectionsQuery,
  useSaveLinkCollectionMutation,
  useDeleteLinkCollectionMutation,
  useSaveLinkMutation,
  useDeleteLinkMutation,
  useLazyGetLinkCollectionsQuery,

  // Attachment hooks
  useUploadFileMutation,
  useDeleteFileMutation,
  useDownloadFileQuery,
  useLazyDownloadFileQuery,
  useGetAttachmentsQuery,
  useLazyGetAttachmentsQuery,
  useGetAttachmentCountQuery,
  useLazyGetAttachmentCountQuery,
  useSaveAttachmentsMutation,
} = localHandbookApi;
