package no.kf.handboker.repository

import no.kf.db.IDGenerator
import no.kf.db.RichSQL._
import no.kf.handboker.config.AppSettingComponent
import no.kf.handboker.model.Publication
import no.kf.util.Logging
import org.joda.time.DateTime


trait PublicationRepositoryComponent {

  this: AppSettingComponent with DbConnectionManagerComponent =>
  val publicationRepository: PublicationRepository

  object PublicationTableDef {
    val tableName = "central_handbooks.publication"
    val fieldId = "id"
    val fieldPublicationDate = "publication_date"
    val fieldNotifyByEmail = "notify_by_email"
    val fieldCreatedDate = "created_date"
    val fieldCreatedBy = "created_by"
    val fieldHandbookId = "handbook_id"
    val fieldPublished = "published"

    val tableColumns = List(
      fieldId,
      fieldPublicationDate,
      fieldCreatedDate,
      fieldCreatedBy,
      fieldNotifyByEmail,
      fieldHandbookId,
      fieldPublished
    )
    val updateTable: String = tableColumns.filterNot(_.matches(s"$fieldId")).mkString("", "=?,", "=?")
    val columnString: String = tableColumns.mkString(",")
    val selectString: String = tableColumns.map(column => tableName + "." + column).mkString(",")
  }

  class PublicationRepositoryImpl extends PublicationRepository with Logging {


    private val publicationQuery = s"SELECT ${PublicationTableDef.columnString} FROM ${PublicationTableDef.tableName} "

    private def executePublicationQuery(publicationQueryFunc: => RichPreparedStatement): List[Publication] = {
      publicationQueryFunc <<! populatePublication
    }

    override def getPublication(publicationId: String): Publication = {
      val res = connectionManager.doWithConnection { conn =>
        executePublicationQuery(conn.ps(publicationQuery + s"WHERE ${PublicationTableDef.fieldId} = ?") << publicationId)
      }
      res.head
    }

    private def insertPublication(publication: Publication): Publication = {
      val newId = IDGenerator.generateUniqueId
      val sql = s"INSERT INTO ${PublicationTableDef.tableName}(${PublicationTableDef.columnString}) " +
        s"values (${#?(PublicationTableDef.tableColumns)})"
      connectionManager.doWithConnection {
        conn =>
          conn.ps(sql) <<
            newId <<
            publication.publicationDate <<
            publication.createdDate <<
            publication.createdBy <<
            publication.notifyByEmail <<
            publication.handbookId <<
            publication.published <<!
      }
      getPublication(newId)
    }

    private def updatePublication(publication: Publication): Publication = {
      import PublicationTableDef._
      val sql = s"update $tableName set $fieldPublicationDate = ?, $fieldPublished = ?, $fieldNotifyByEmail = ? where $fieldId = ?"
      connectionManager.doWithConnection {
        conn =>
          conn.ps(sql) <<
            publication.publicationDate <<
            publication.published <<
            publication.notifyByEmail <<
            publication.id <<!
      }
      getPublication(publication.id.get)
    }

    override def persistPublication(publication: Publication): Publication = {
      val existingUnprocessedPublications = getPublicationCandidates.filter(c => c.handbookId == publication.handbookId)
      if (existingUnprocessedPublications.size > 1) throw new RuntimeException("Sentral håndbok har mer enn 1 ubehandlet publisering")
      if (publication.id.isEmpty && existingUnprocessedPublications.isEmpty) {
        insertPublication(publication)
      } else {
        val existingPublication = existingUnprocessedPublications.head
        updatePublication(publication.copy(id = existingPublication.id))
      }
    }

    override def deletePublication(publicationId: String): Unit = {
      val sql = s"DELETE FROM ${PublicationTableDef.tableName} WHERE ${PublicationTableDef.fieldId} = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << publicationId <<!
      }
    }

    private def populatePublication(rs: RichResultSet): Publication = {
      val id: Option[String] = rs
      val publicationDate: DateTime = rs
      val createdDate: DateTime = rs
      val createdBy: String = rs
      val notifyByEmail: Boolean = rs
      val handbookId: String = rs
      val published: Boolean = rs

      Publication(id, publicationDate, notifyByEmail, createdDate, createdBy, handbookId, published)
    }

    override def getPublicationCandidates: List[Publication] = {
      val today = DateTime.now
      val sql = s"${publicationQuery} WHERE ${PublicationTableDef.fieldPublished} = 0 AND ${PublicationTableDef.fieldPublicationDate} <= ?"
      connectionManager.doWithConnection {
        conn =>
          conn.ps(sql) <<
            today <<! populatePublication
      }
    }

    override def getPendingPublication(handbookId: String) : List[Publication] = {
      val sql = s"${publicationQuery} WHERE ${PublicationTableDef.fieldPublished} = 0 AND ${PublicationTableDef.fieldHandbookId} = ?"
      connectionManager.doWithConnection {
        conn =>
          conn.ps(sql) <<
            handbookId <<! populatePublication
      }
    }
  }

}

trait PublicationRepository {
  def persistPublication(publication: Publication): Publication

  def getPublication(publicationId: String): Publication

  def getPendingPublication(handbookId: String): List[Publication]

  def deletePublication(publicationId: String): Unit

  def getPublicationCandidates: List[Publication]
}
