<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
  <id>bin</id>
  <formats>
    <format>zip</format>
  </formats>
  <fileSets>
    <fileSet>
        <directory>${project.build.directory}</directory>
        <outputDirectory>.</outputDirectory>
        <includes>
            <include>handboker.war</include>
            <include>handboker.properties</include>
        </includes>
    </fileSet>
  </fileSets>
</assembly>
