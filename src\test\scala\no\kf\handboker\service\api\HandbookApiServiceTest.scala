package no.kf.handboker.service.api

import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.model.api.TreeStructureHandbook
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import no.kf.util.Logging
import no.kf.test.TimeTestHelp
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.mockito.Mockito._
import org.scalatest.FunSuite
import org.scalatest.junit.JUnitRunner
import org.scalatest.mockito.MockitoSugar

@RunWith(classOf[JUnitRunner])
class HandbookApiServiceTest extends FunSuite with DefaultTestDI with MockitoSugar with Logging with TimeTestHelp {

  override val componentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val handbookApiService = new HandbookApiServiceImpl
  }

  val service = componentRegistry.handbookApiService

  val externalOrgId = "9900"
  val handbookId = "handbookId"

  test("That we call the right method when retrieving metadata for handbooks") {
    when(componentRegistry.localHandbookService.retrieveHandbooksForExternalOrganization(externalOrgId)).thenReturn(Nil)

    service.retrieveHandbooksWithMetadata(externalOrgId)

    verify(componentRegistry.localHandbookService, times(1)).retrieveHandbooksForExternalOrganization(externalOrgId)
  }

  test("That we create correctly structured trees of a handbook, chapters and sections") {
    val handbook = createHandbook(Option(handbookId))
    val chapters = createChapters()
    val sectionsForChapter1 = createSections("chapterId1")
    val sectionsForChapter2 = createSections("chapterId2")
    val sectionsForChapter3 = createSections("chapterId3")
    val sectionsForChapter4 = createSections("chapterId4")
    val sectionsForChapter5 = createSections("chapterId5")
    val allSections: List[Section] = List(sectionsForChapter1, sectionsForChapter2, sectionsForChapter3, sectionsForChapter4, sectionsForChapter5).flatten

    when(componentRegistry.localHandbookService.retrieveHandbook(handbookId)).thenReturn(Option(handbook))
    when(componentRegistry.localHandbookService.retrieveChaptersForHandbook(handbookId)).thenReturn(chapters)
    when(componentRegistry.localHandbookService.retrieveSectionsForHandbook(handbookId)).thenReturn(allSections)

    val actualStructureOption: Option[TreeStructureHandbook] = service.retrieveHandbookTreeStructure(handbookId, externalOrgId)
    assert(actualStructureOption.isDefined)

    val expectedStructure: TreeStructureHandbook = createExpectedStructure()
    val actualStructure = actualStructureOption.get

    assert(expectedStructure.id === actualStructure.id)
    assert(3 === actualStructure.chapters.size)
    assert(actualStructure.chapters.exists(chapter => chapter.id.get === "chapterId1"))
    assert(actualStructure.chapters.exists(chapter => chapter.id.get === "chapterId2"))
    assert(actualStructure.chapters.exists(chapter => chapter.id.get === "chapterId3"))

    val chapter1 = actualStructure.chapters.find(_.id.get == "chapterId1").get
    val chapter2 = actualStructure.chapters.find(_.id.get == "chapterId2").get
    val chapter3 = actualStructure.chapters.find(_.id.get == "chapterId3").get

    assert(chapter1.chapters.isEmpty)
    assert(3 === chapter1.sections.size)

    assert(3 === chapter2.chapters.size)
    assert(3 === chapter2.sections.size)

    assert(chapter3.chapters.isEmpty)
    assert(3 === chapter3.sections.size)

    assert(chapter2.chapters.exists(chapter => chapter.id.get === "chapterId4"))
    assert(chapter2.chapters.exists(chapter => chapter.id.get === "chapterId5"))
    assert(chapter2.chapters.exists(chapter => chapter.id.get === "chapterId6"))

    val chapter4 = chapter2.chapters.find(_.id.get === "chapterId4").get
    val chapter5 = chapter2.chapters.find(_.id.get === "chapterId5").get
    val chapter6 = chapter2.chapters.find(_.id.get === "chapterId6").get
    val chapter7 = chapter6.chapters.find(_.id.get === "chapterId7")
    assert(3 === chapter4.sections.size)
    assert(3 === chapter5.sections.size)
    assert(1 === chapter6.chapters.size)
    assert(0 === chapter6.sections.size)
    assert(chapter7.isDefined)
  }

  test("That we retrieve handbooks, chapters and sections correctly after a set date") {
    testInFrozenTime() {
      val oldestDate = DateTime.now
      val middleDate = oldestDate.plusDays(5)
      val newestDate = oldestDate.plusDays(10)
      val handbook = createHandbook(Option(handbookId)).copy(updatedDate = Option(oldestDate))
      val chapters = createChapters().map(chapter => chapter.copy(updatedDate = Option(newestDate)))
      val sectionsForChapter1 = createSections("chapterId1").map(section => section.copy(updatedDate = Option(oldestDate)))
      val sectionsForChapter2 = createSections("chapterId2").map(section => section.copy(updatedDate = Option(newestDate)))
      val sectionsForChapter3 = createSections("chapterId3").map(section => section.copy(updatedDate = Option(newestDate)))
      val sectionsForChapter4 = createSections("chapterId4").map(section => section.copy(updatedDate = Option(newestDate)))
      val sectionsForChapter5 = createSections("chapterId5").map(section => section.copy(updatedDate = Option(oldestDate)))
      val allSections = List(sectionsForChapter1, sectionsForChapter2, sectionsForChapter3, sectionsForChapter4, sectionsForChapter5).flatten

      when(componentRegistry.localHandbookService.retrieveHandbooksForExternalOrganization(externalOrgId)).thenReturn(List(handbook))
      when(componentRegistry.localHandbookService.retrieveChaptersForHandbook(handbookId)).thenReturn(chapters)
      when(componentRegistry.localHandbookService.retrieveSectionsForHandbook(handbookId)).thenReturn(allSections)

      val changedEntitiesAfterDate = service.retrieveChangedEntitiesAfterDateForOrg(externalOrgId, middleDate)
      val (handbooksAfterDate, chaptersAfterDate, sectionsAfterDate) = changedEntitiesAfterDate

      // Assert general equality
      assert(handbooksAfterDate.isEmpty)
      assert(chaptersAfterDate.nonEmpty)
      assert(sectionsAfterDate.nonEmpty)

      // Chapter testing
      assert(chaptersAfterDate.exists(chapter => chapter.id === Option("chapterId1")))
      assert(chaptersAfterDate.exists(chapter => chapter.id === Option("chapterId2")))
      assert(chaptersAfterDate.exists(chapter => chapter.id === Option("chapterId3")))
      assert(chaptersAfterDate.exists(chapter => chapter.id === Option("chapterId4")))
      assert(chaptersAfterDate.exists(chapter => chapter.id === Option("chapterId5")))


      // Section testing

      // chapter1 has oldestDate and should not be in the list of sections
      assert(sectionsAfterDate.forall(section => section.id !== Option("chapterId1:sectionId1")))
      assert(sectionsAfterDate.forall(section => section.id !== Option("chapterId1:sectionId2")))
      assert(sectionsAfterDate.forall(section => section.id !== Option("chapterId1:sectionId3")))

      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId2:sectionId1")))
      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId2:sectionId2")))
      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId2:sectionId3")))

      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId3:sectionId1")))
      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId3:sectionId2")))
      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId3:sectionId3")))

      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId4:sectionId1")))
      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId4:sectionId2")))
      assert(sectionsAfterDate.exists(section => section.id === Option("chapterId4:sectionId3")))

      // chapter5 has oldestDate and should not be in the list of sections
      assert(sectionsAfterDate.forall(section => section.id !== Option("chapterId5:sectionId1")))
      assert(sectionsAfterDate.forall(section => section.id !== Option("chapterId5:sectionId2")))
      assert(sectionsAfterDate.forall(section => section.id !== Option("chapterId5:sectionId3")))

      // chapter6 has no sections so it should not be in the list of sections
      assert(!sectionsAfterDate.exists(_.id.contains("chapterId6")))
    }
  }

  def createExpectedStructure(): TreeStructureHandbook = {
    service.constructTreeStructureHandbook(createHandbook(Some(handbookId)), Nil)
  }

  def createHandbook(id: Option[String]): Handbook = {
    Handbook(
      id,
      "handbookTitle",
      None,
      externalOrgId
    )
  }

  def createChapters(): List[Chapter] = {
    List(
      createSingleChapter(handbookId, None, Option("chapterId1")),
      createSingleChapter(handbookId, None, Option("chapterId2")),
      createSingleChapter(handbookId, None, Option("chapterId3")),
      createSingleChapter(handbookId, Option("chapterId2"), Option("chapterId4")),
      createSingleChapter(handbookId, Option("chapterId2"), Option("chapterId5")),
      createSingleChapter(handbookId, Option("chapterId2"), Option("chapterId6")),
      createSingleChapter(handbookId, Option("chapterId6"), Option("chapterId7"))
    )
  }

  def createSections(parentId: String): List[Section] = {
    List(
      createSingleSection(handbookId, parentId, Option(s"$parentId:sectionId1")),
      createSingleSection(handbookId, parentId, Option(s"$parentId:sectionId2")),
      createSingleSection(handbookId, parentId, Option(s"$parentId:sectionId3"))
    )
  }

  def createSingleChapter(handbookId: String, parentId: Option[String], id: Option[String]): Chapter = {
    Chapter(
      id,
      "chapterTitle",
      None,
      None,
      handbookId,
      parentId,
      None
    )
  }

  def createSingleSection(handbookId: String, parentId: String, id: Option[String]): Section = {
    Section(
      id,
      "sectionTitle",
      None,
      None,
      None,
      handbookId,
      parentId,
      None
    )
  }
}
