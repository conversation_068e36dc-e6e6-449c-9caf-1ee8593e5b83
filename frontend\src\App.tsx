import { useEffect, useState, Suspense, useCallback, useMemo } from "react";
import { Provider } from "react-redux";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { ToastContainer } from "@/shared/components/Toast";
import { PersistGate } from "redux-persist/integration/react";

import { EditorApp } from "./app/editor";
import { PublicApp } from "./app/public";

import { store, persistor } from "./store";
import { AppErrorBoundaryWrapper } from "./shared/components/ErrorBoundary";

import { I18nProvider } from "./libs/i18n/I18nProvider";
import { SessionExpiryModal } from "./components/SessionExpiryModal";
import type { AppType } from "@/enums/handbook";
import { Spinner } from "./shared/components/Spinner";

import "react-toastify/dist/ReactToastify.css";
import "kf-bui/kf-bui.css";
import "./App.css";

declare global {
  interface Window {
    __BASENAME__?: string;
    __PRELOADED_SESSION_STATE__?: string;
  }
}

function detectAppType(): AppType {
  const path = window.location.pathname;

  if (path.startsWith("/editor")) {
    return "editor";
  }

  if (path.startsWith("/public")) {
    return "public";
  }

  return "editor";
}

function App() {
  const [appType, setAppType] = useState<AppType>("editor");
  const [isLoading, setIsLoading] = useState(true);

  const getBasename = useCallback((): string => {
    if (process.env.NODE_ENV === "development") {
      return appType === "editor" ? "/" : "/public";
    }

    const globalBasename = window.__BASENAME__ || "/";
    return globalBasename.endsWith("/") ? globalBasename : `${globalBasename}/`;
  }, [appType]);

  useEffect(() => {
    const detectedAppType = detectAppType();
    setAppType(detectedAppType);
    setIsLoading(false);
  }, []);

  const loadingComponent = useMemo(
    () => (
      <div className="full-screen-center">
        <Spinner text="Laster..." />
      </div>
    ),
    []
  );

  const basename = useMemo(() => getBasename(), [getBasename]);

  if (isLoading) {
    return loadingComponent;
  }

  const createRouter = (appType: AppType, basename: string) => {
    if (appType === "editor") {
      return createBrowserRouter(
        [
          {
            path: "*",
            element: <EditorApp />,
          },
        ],
        { basename }
      );
    } else if (appType === "public") {
      return createBrowserRouter(
        [
          {
            path: "*",
            element: <PublicApp />,
          },
        ],
        { basename }
      );
    }
    return null;
  };

  const router = createRouter(appType, basename);

  const AppWrapper = ({
    router,
  }: {
    router: NonNullable<ReturnType<typeof createRouter>>;
  }) => (
    <AppErrorBoundaryWrapper>
      <Provider store={store}>
        <PersistGate loading={loadingComponent} persistor={persistor}>
          <I18nProvider>
            <Suspense fallback={loadingComponent}>
              <RouterProvider router={router} />
            </Suspense>
            <ToastContainer />
            <SessionExpiryModal />
          </I18nProvider>
        </PersistGate>
      </Provider>
    </AppErrorBoundaryWrapper>
  );

  if ((appType === "editor" || appType === "public") && router) {
    return <AppWrapper router={router} />;
  }

  return null;
}

export default App;
