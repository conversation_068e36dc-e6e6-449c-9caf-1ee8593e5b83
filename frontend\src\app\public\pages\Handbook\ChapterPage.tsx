import React, { useRef } from "react";
import { useParams } from "react-router-dom";
import { Icon, Title, Group, Button } from "kf-bui";

import { ChildrenList } from "../../components/ChildrenList";
import { AttachmentPopup } from "../../components/AttachmentPopup";
import { useAttachments } from "../../hooks/useAttachments";
import { useRootChapterContent } from "../../hooks/useRootChapterContent";
import type { Chapter, Section } from "@/types";

interface ChapterPageProps {
  chapters: Chapter[];
  sections: Section[];
  onSectionEnter?: (section: Section) => void;
  onSectionLeave?: (section: Section) => void;
  waypointsEnabled?: boolean;
}

export const ChapterPage: React.FC<ChapterPageProps> = ({
  chapters,
  sections,
  onSectionEnter,
  onSectionLeave,
  waypointsEnabled = true
}) => {
  const { chapterId } = useParams<{ chapterId: string }>();
  const wrapperRef = useRef<HTMLDivElement>(null);

  const { 
    rootChapter, 
    rootChapterId, 
    directChildren 
  } = useRootChapterContent(chapterId, chapters, sections);

  const {
    showAttachments,
    attachmentCount,
    handleShowAttachments,
  } = useAttachments("chapter", rootChapterId!, wrapperRef);

  const children = directChildren.map((child) => {
    const isChapter = chapters.some(c => c.id === child.id);
    return {
      ...child,
      type: isChapter ? "CHAPTER" as const : "SECTION" as const
    } as (Chapter | Section) & { type: "CHAPTER" | "SECTION" };
  });

  if (!rootChapter) {
    return (
      <div style={{ textAlign: "center", padding: "2rem" }}>
        Chapter not found
      </div>
    );
  }

  return (
    <>
      <Group className="content-header">
        <Title id={`content-${rootChapter.id}`} className="chapter-title">
          <Icon icon="RegBookmark" size="medium" /> {rootChapter.title}
        </Title>

        {attachmentCount > 0 && (
          <div className="public-attachment-container chapter-attachments">
            <Button
              className="public-attachment-btn"
              onClick={() => handleShowAttachments(!showAttachments)}
              icon="Paperclip"
            />

            {showAttachments && (
              <div ref={wrapperRef}>
                <AttachmentPopup
                  sectionId={rootChapter.id!}
                  type="chapter"
                  onClose={() => handleShowAttachments(false)}
                />
              </div>
            )}
          </div>
        )}
      </Group>

      <ChildrenList
        children={children}
        chapters={chapters}
        sections={sections}
        onSectionEnter={onSectionEnter}
        onSectionLeave={onSectionLeave}
        waypointsEnabled={waypointsEnabled}
      />
    </>
  );
};
