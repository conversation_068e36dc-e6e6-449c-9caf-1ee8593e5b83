package no.kf.handboker.service.handbookdownload

import java.io.ByteArrayOutputStream

import com.lowagie.text.pdf.BaseFont
import org.xhtmlrenderer.pdf.ITextRenderer

object PdfConverter {
  def html2Pdf(html: String): Array[Byte] = {
    val output: ByteArrayOutputStream = new ByteArrayOutputStream

    val renderer: ITextRenderer = new ITextRenderer()
    renderer.getFontResolver.addFont("Georgia.ttf", BaseFont.IDENTITY_H, true)
    renderer.getFontResolver.addFont("Arial.ttf", BaseFont.IDENTITY_H, true)

    renderer.setDocumentFromString(html)
    renderer.layout()
    renderer.createPDF(output)
    renderer.finishPDF()
    output.flush()
    output.close()

    output.toByteArray
  }

}
