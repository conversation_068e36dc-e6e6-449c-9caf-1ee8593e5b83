package no.kf.handboker.service

import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.model.local.{ChangeNotification, Chapter, Handbook, Section}
import org.junit.runner.RunWith
import org.mockito.Matchers._
import org.mockito.Mockito._
import _root_.no.kf.exception.UserFriendlyException
import org.scalatest.Matchers._
import _root_.no.kf.util.Logging
import _root_.no.kf.db.IDGenerator
import _root_.no.kf.handboker.model.central.CentralHandbook
import _root_.no.kf.handboker.model.LDAPUser
import org.joda.time.DateTime
import org.scalatest.{BeforeAndAfterEach, FunSuite}
import org.scalatest.junit.JUnitRunner
import org.scalatest.mockito.MockitoSugar

@RunWith(classOf[JUnitRunner])
class LocalHandbookServiceTest extends FunSuite with DefaultTestDI with MockitoSugar with BeforeAndAfterEach with Logging {

  override val componentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val localHandbookService = new LocalHandbookServiceImpl
  }

  val centralHandbookService = componentRegistry.centralHandbookService
  val localHandbookService = componentRegistry.localHandbookService
  val handbookRepository = componentRegistry.handbookRepository
  val handbookVersionRepository = componentRegistry.localHandbookVersionRepository
  val searchIndexService = componentRegistry.searchIndexService


  override def beforeEach() {
    reset(handbookRepository)
    reset(componentRegistry.subscriptionService)
    reset(componentRegistry.centralAccessService)
    reset(componentRegistry.centralHandbookService)
  }

  test("That persistHandbook calls the right repo method") {
    val handbook = Handbook(None, "title", None, "9900")
    when(handbookRepository.persistHandbook(any())).thenReturn(handbook)
    localHandbookService.persistHandbook(handbook)
    verify(handbookRepository).persistHandbook(any())
  }

  test("That persistChapter calls the right repo method and detection method") {
    val handbook = Handbook(None, "title", None, "9900")
    val chapter = Chapter(None, "title", None, None, "handbookId", None, Some(1))
    when(handbookRepository.retrieveHandbook(any())).thenReturn(Option(handbook))
    when(handbookRepository.persistChapter(any(), any())).thenReturn(chapter.copy(id = Some("id")))
    when(componentRegistry.subscriptionService.persistChangeNotification(any(), any(), any())).thenReturn(mock[ChangeNotification])
    localHandbookService.persistChapter(chapter)
    verify(handbookRepository).persistChapter(chapter)
  }

  test("That persistSection calls the right repo method and detection method") {
    val handbook = Handbook(None, "title", None, "9900")
    val section = Section(None, "title", None, None, None, "handbookId", "parentId", Some(1))
    when(handbookRepository.retrieveHandbook(any())).thenReturn(Option(handbook))
    when(handbookRepository.persistSection(any(), any())).thenReturn(section.copy(Some("id")))
    when(componentRegistry.subscriptionService.persistChangeNotification(any(), any() ,any())).thenReturn(mock[ChangeNotification])
    localHandbookService.persistSection(section, Some("user"))
    verify(handbookRepository).persistSection(section)
  }

  test("That retrieveHandbook calls the right repo method") {
    when(handbookRepository.retrieveHandbook("1")).thenReturn(mock[Option[Handbook]])
    localHandbookService.retrieveHandbook("1")
    verify(handbookRepository).retrieveHandbook("1")
  }

  test("That retrieveChapter calls the right repo method") {
    when(handbookRepository.retrieveChapter("1")).thenReturn(mock[Option[Chapter]])
    localHandbookService.retrieveChapter("1")
    verify(handbookRepository).retrieveChapter("1")
  }

  test("That retrieveSection calls the right repo method") {
    when(handbookRepository.retrieveSection("1")).thenReturn(mock[Option[Section]])
    localHandbookService.retrieveSection("1")
    verify(handbookRepository).retrieveSection("1")
  }

  test("That retreiveChaptersForHandbook calls the right repo method") {
    when(handbookRepository.retrieveChaptersForHandbook("1")).thenReturn(mock[List[Chapter]])
    localHandbookService.retrieveChaptersForHandbook("1")
    verify(handbookRepository).retrieveChaptersForHandbook("1")
  }

  test("That retrieveHandbooksForExternalOrganization calls the right repo method") {
    when(handbookRepository.retrieveHandbooksForExternalOrganization("1")).thenReturn(mock[List[Handbook]])
    localHandbookService.retrieveHandbooksForExternalOrganization("1")
    verify(handbookRepository).retrieveHandbooksForExternalOrganization("1")
  }

  // TODO
  ignore("That retrieveHandbooksWithChaptersAndSectionsWithoutTextForExternalOrganization calls the right repo methods") {
    val handbookList = (1 to 2).map(_ => mock[Handbook]).toList
    when(handbookList(0).id).thenReturn(Some("1"))
    when(handbookList(1).id).thenReturn(Some("2"))
    val chapterList1 = (1 to 3).map(_ => mock[Chapter]).toList
    val chapterList2 = (1 to 3).map(_ => mock[Chapter]).toList
    val sectionList1 = (1 to 5).map(_ => mock[Section]).toList
    val sectionList2 = (1 to 5).map(_ => mock[Section]).toList

    //Sanity
    assert(chapterList1 != chapterList2)
    assert(sectionList1 != sectionList2)

    when(handbookRepository.retrieveHandbooksForExternalOrganization("9999")).thenReturn(handbookList)
    when(handbookRepository.retrieveChaptersForHandbook("1")).thenReturn(chapterList1)
    when(handbookRepository.retrieveChaptersForHandbook("2")).thenReturn(chapterList2)
    when(handbookRepository.retrieveSectionsWithoutTextForHandbook("1")).thenReturn(sectionList1)
    when(handbookRepository.retrieveSectionsWithoutTextForHandbook("2")).thenReturn(sectionList2)

    val (handbooks, chapters, sections) = localHandbookService.retrieveHandbooksWithChaptersAndSectionsWithoutTextForExternalOrganization("9999", user = LDAPUser("<EMAIL>", Some("mock mockesen"), "9900" :: "9999" :: Nil, None, true, false, true) )
    verify(handbookList(0)).id
    verify(handbookList(1)).id
    verify(handbookRepository).retrieveHandbooksForExternalOrganization("9999")
    verify(handbookRepository).retrieveChaptersForHandbook("1")
    verify(handbookRepository).retrieveChaptersForHandbook("2")
    verify(handbookRepository).retrieveSectionsWithoutTextForHandbook("1")
    verify(handbookRepository).retrieveSectionsWithoutTextForHandbook("2")

    assert(handbookList === handbooks)
    assert((chapterList1:::chapterList2).toSet === chapters.toSet)
    assert((sectionList1:::sectionList2).toSet === sections.toSet)
  }

  test("That deleteHandbook calls the right repo method") {
    class ThisTestException extends RuntimeException
    when(handbookRepository.deleteHandbook("1")).thenThrow(new ThisTestException)
    intercept[ThisTestException] {
      localHandbookService.deleteHandbook("1")
    }
    verify(handbookRepository).deleteHandbook("1")
  }

  test("That deleteChapter calls the right repo method") {
    class ThisTestException extends RuntimeException
    when(handbookRepository.deleteChapter("1")).thenThrow(new ThisTestException)
    when(handbookRepository.retrieveChapter(anyString()))thenReturn Some(Chapter(Some("lc1"), "new", None, None, "lh1", None, None))
    when(handbookRepository.retrieveChildren(anyString(), anyBoolean())).thenReturn((List(), List()))
    intercept[ThisTestException] {
      localHandbookService.deleteChapter("1")
    }
    verify(handbookRepository).deleteChapter("1")
  }

  test("That deleteSection calls the right repo method") {
    class ThisTestException extends RuntimeException
    when(handbookRepository.deleteSection("1")).thenThrow(new ThisTestException)
    when(handbookRepository.retrieveSection(anyString()))thenReturn Some(Section(Some("id"), "title", Some("text"), None, None, "handbookid", "parent", None))
    intercept[ThisTestException] {
      localHandbookService.deleteSection("1")
    }
    verify(handbookRepository).deleteSection("1")
  }

  test("That persisting of child elements are only done when persisting a handbook/chapter based on central content") {
    val externalOrgId = "9900"
    val handBook = Handbook(Some("lh1"), "new", None, externalOrgId)
    val chapter = Chapter(Some("lc1"), "new", None, None, "lh1", None, None)
    localHandbookService.ifBasedOnCentralContentPersistAllChildrenToChapter(chapter, Option(externalOrgId), Option("<EMAIL>"))

    verify(componentRegistry.centralHandbookService, never()).retrieveChaptersAndSectionsWithText(anyString())
    verify(handbookRepository, never()).persistChapter(any(), any())
    verify(handbookRepository, never()).persistSection(any(), any())
  }

  // TODO - local handbooks are now based on published central handbook versions
  ignore("That we can persist all children of a handbook") {
    val centralContent = generateCentralChaptersAndSections()
    val localContent = generateLocalChaptersAndSection()

    val handbookBasedOnCentralContent = Handbook(Some("lh1"), "new", Some("ch1"), "9900")
    when(componentRegistry.centralHandbookService.retrieveChaptersAndSectionsWithText(handbookBasedOnCentralContent.importedHandbookId.get)).
      thenReturn(centralContent)
    localContent._1.map(chap => when(handbookRepository.persistChapter(chap.copy(id = None))).thenReturn(chap))
    val centralHandbook = CentralHandbook(Some("id"), "sentralhåndbok", Some("versjon 1"))
    when(componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(anyString())).thenReturn(Some(centralHandbook))

    localHandbookService.persistChapterAndSectionsFromCentralHandbook(handbookBasedOnCentralContent, centralHandbook, None, Option("<EMAIL>"))

    localContent._1.map(chap => verify(handbookRepository, times(1)).persistChapter(chap.copy(id = None)))
    localContent._2.map(section => verify(handbookRepository, times(1)).persistSection(section.copy(id = None)))
  }

  // TODO - local handbooks are now based on published central handbook versions
  ignore("That we can persist all children of a chapter") {
    val externalOrgId = "9900"
    val centralContent = generateCentralChaptersAndSections()
    val localContent = generateLocalChaptersAndSection()

    val chapterBasedOnCentralContent = localContent._1.find(_.id.contains("lc2")).get
    when(componentRegistry.centralHandbookService.retrieveChaptersAndSectionsWithText(chapterBasedOnCentralContent.importedHandbookId.get)).
      thenReturn(centralContent)

    val localCc3 = localContent._1.find(_.id.contains("lc3")).get
    val localCc5 = localContent._1.find(_.id.contains("lc5")).get

    val localCs2 = localContent._2.find(_.id.contains("ls2")).get
    val localCs3 = localContent._2.find(_.id.contains("ls3")).get

    when(handbookRepository.persistChapter(localCc3.copy(id = None))).thenReturn(localCc3)
    when(handbookRepository.persistChapter(localCc5.copy(id = None))).thenReturn(localCc5)
    when(handbookRepository.persistSection(localCs2.copy(id = None))).thenReturn(localCs2)
    when(handbookRepository.persistSection(localCs3.copy(id = None))).thenReturn(localCs3)
    when(handbookRepository.retrieveHandbook(anyString())).thenReturn(Some(Handbook(Some("id"), "title", None, externalOrgId)))

    localHandbookService.ifBasedOnCentralContentPersistAllChildrenToChapter(chapterBasedOnCentralContent, Option(externalOrgId), Option("<EMAIL>"))

    verify(handbookRepository, times(1)).persistChapter(localCc3.copy(id = None))
    verify(handbookRepository, times(1)).persistChapter(localCc5.copy(id = None))
    verify(handbookRepository, times(2)).persistChapter(anyObject())

    verify(handbookRepository, times(1)).persistSection(localCs2.copy(id = None))
    verify(handbookRepository, times(1)).persistSection(localCs3.copy(id = None))
    verify(handbookRepository, times(2)).persistSection(anyObject())

    verify(componentRegistry.subscriptionService, times(4)).persistChangeNotification(anyString(), anyObject(), anyObject())
  }

  test("That an exception is not thrown when getting central access for a new and an old handbook (with access)") {
    val handbookWithId = Handbook(Option("handbookId1"), "handbookTitle1", Option("importedHandbookId1"), "9900")
    val handbookWithoutId = Handbook(None, "handbookTitle2", Option("importedHandbookId2"), "9999")

    when(componentRegistry.centralAccessService.hasAccess(handbookWithId.importedHandbookId.get, handbookWithId.externalOrgId)).thenReturn(true)
    when(componentRegistry.centralAccessService.hasAccess(handbookWithoutId.importedHandbookId.get, handbookWithoutId.externalOrgId)).thenReturn(true)

    noException should be thrownBy {
      componentRegistry.localHandbookService.throwIfNoCentralAccess(handbookWithId.id, handbookWithId.importedHandbookId, handbookWithId.externalOrgId)
    }
    noException should be thrownBy {
      componentRegistry.localHandbookService.throwIfNoCentralAccess(handbookWithoutId.id, handbookWithoutId.importedHandbookId, handbookWithoutId.externalOrgId)
    }
  }

  test("That an exception is thrown when getting central access for a new handbook (no access)") {
    val handbookWithoutId = Handbook(None, "handbookTitle2", Option("importedHandbookId2"), "9999")
    when(componentRegistry.centralAccessService.hasAccess(handbookWithoutId.importedHandbookId.get, handbookWithoutId.externalOrgId)).thenReturn(false)

    intercept[UserFriendlyException] {
      componentRegistry.localHandbookService.throwIfNoCentralAccess(handbookWithoutId.id, handbookWithoutId.importedHandbookId, handbookWithoutId.externalOrgId)
    }
  }

  test("That an exception is not thrown when getting central access to an old handbook (with removed access)") {
    val handbookWithId = Handbook(Option("handbookId1"), "handbookTitle1", Option("importedHandbookId1"), "9900")

    when(componentRegistry.centralAccessService.hasAccess(handbookWithId.importedHandbookId.get, handbookWithId.externalOrgId)).thenReturn(false)

    noException should be thrownBy {
      componentRegistry.localHandbookService.throwIfNoCentralAccess(handbookWithId.id, handbookWithId.importedHandbookId, handbookWithId.externalOrgId)
    }
  }


  test("That we call the right method on retrieving all external org ids") {
    val extOrgId1 = "9900"
    val extOrgId2 = "9999"

    when(componentRegistry.handbookRepository.retrieveAllExternalOrgIds()).thenReturn(List(extOrgId1, extOrgId2))

    val ids = componentRegistry.localHandbookService.retrieveAllExternalOrgIds()

    verify(componentRegistry.handbookRepository, times(1)).retrieveAllExternalOrgIds()
    assert(ids.size === 2)
    assert(ids.contains(extOrgId1))
    assert(ids.contains(extOrgId2))
  }

  test("Test that we return an empty list if there are no central setions with the combination of centralSectionid parentId and centralHandbookId") {
    when(componentRegistry.centralHandbookService.retrieveCentralSectionByCentralIdAndParentId(any(), any(), any(), any())).thenReturn(None)
    val res = localHandbookService.retrieveSectionsBasedOnCentralSectionIdParentIdAndHandbookId("", "", "")
    assert(res === Nil)
  }

  test("That we call handbook repository with the id of the object returned by the central service") {
    val id = "SUPER UNIQUE ... id"
    when(componentRegistry.centralHandbookService.retrieveCentralSectionByCentralIdAndParentId(any(), any(), any(), any())).thenReturn(Some(Section(Some(id), "", None, None, None, "", "", None)))
    when(componentRegistry.handbookRepository.retrieveSectionsBasedOnCentralSection(id, includeDeleted = true)).thenReturn(Nil)

    val res = localHandbookService.retrieveSectionsBasedOnCentralSectionIdParentIdAndHandbookId("", "", "", includeDeleted = true)
    verify(componentRegistry.handbookRepository, times(1)).retrieveSectionsBasedOnCentralSection(id, includeDeleted = true)
    assert(res === Nil)
  }

  test("That we call central service with the correct arguments") {
    val centralId = "central ID"
    val parentId = "parent ID"
    val handbookId = "handbook ID"
    val htmlFlag = false

    when(componentRegistry.centralHandbookService.retrieveCentralSectionByCentralIdAndParentId(centralId, parentId, handbookId, htmlFlag)).thenReturn(None)
    localHandbookService.retrieveSectionsBasedOnCentralSectionIdParentIdAndHandbookId(centralId, parentId, handbookId, htmlFlag)
    verify(componentRegistry.centralHandbookService, times(1)).retrieveCentralSectionByCentralIdAndParentId(centralId, parentId, handbookId, htmlFlag)

  }

  test("That includeDeleted flag is carried on to repo call when we want to includeDeleted") {
    val id = "SUPER UNIQUE ... id"
    when(componentRegistry.centralHandbookService.retrieveCentralSectionByCentralIdAndParentId(any(), any(), any(), any())).thenReturn(Some(Section(Some(id), "", None, None, None, "", "", None)))
    localHandbookService.retrieveSectionsBasedOnCentralSectionIdParentIdAndHandbookId("", "", "", includeDeleted = true)
    verify(componentRegistry.handbookRepository, times(1)).retrieveSectionsBasedOnCentralSection(id, includeDeleted = true)
  }

  test("That includeDeleted flag is carried on to repo call when we do not want to includeDeleted") {
    val id = "SUPER UNIQUE ... id"
    when(componentRegistry.centralHandbookService.retrieveCentralSectionByCentralIdAndParentId(any(), any(), any(), any())).thenReturn(Some(Section(Some(id), "", None, None, None, "", "", None)))
    localHandbookService.retrieveSectionsBasedOnCentralSectionIdParentIdAndHandbookId("", "", "", includeDeleted = false)
    verify(componentRegistry.handbookRepository, times(1)).retrieveSectionsBasedOnCentralSection(id, includeDeleted = false)
  }

  test("That retrieve setions with imported handbook id without html passes the correct arguments the correct number of times") {
    val importedHandbookId1 = IDGenerator.generateUniqueId
    val importedHandbookId2 = IDGenerator.generateUniqueId
    localHandbookService.retrieveSectionsWithImportedHandbookId(importedHandbookId1, false)
    verify(handbookRepository, times(1)).retrieveSectionsWithImportedHandbookId(importedHandbookId1, false)
    localHandbookService.retrieveSectionsWithImportedHandbookId(importedHandbookId2, true)
    verify(handbookRepository, times(1)).retrieveSectionsWithImportedHandbookId(importedHandbookId2, true)
  }


  test("That delete section and chapter clear pendingChanges and pendingDeletion") {
    val chapter = Chapter(Some("id"), "title", None, None, "hanbookid", None, None)
    when(handbookRepository.retrieveChapter(anyString()))thenReturn Some(chapter.copy(pendingChange = true, pendingDeletion = true))
    when(handbookRepository.retrieveChildren(anyString(), anyBoolean())).thenReturn((List(), List()))
    localHandbookService.deleteChapter(chapter.id.get, None)
    verify(handbookRepository, times(1)).persistChapter(chapter.copy(pendingChange = false, pendingDeletion = false))
    val section = Section(Some("id"), "title", Some("text"), None, None, "handbookid", "parent", None)
    when(handbookRepository.retrieveSection(anyString()))thenReturn Some(section.copy(pendingTitleChange = true, pendingDeletion = true))
    localHandbookService.deleteSection(section.id.get, None)
    verify(handbookRepository, times(1)).persistSection(section.copy(pendingTitleChange = false, pendingDeletion = false))
  }

  test("That we get the right versions of chapters and sections on a certain date") {
    val mainSections = List(Section(Some("ccfa76e0-032f-494f-aeaa-ab73ab8bb140"), "s1", None, None, None, "hid", "pid", Some(0),
      createdDate = Some(DateTime.now.withDate(2020, 11, 20).withTime(10, 55, 34, 412)),
      updatedDate = Some(DateTime.now.withDate(2020, 11, 30).withTime(13, 20, 32, 261)),
      textUpdatedDate = Some(DateTime.now.withDate(2020, 11, 30).withTime(13, 20, 32, 360)))
    )
    val sectionVersions2020_11_25 = List(
      Section(Some("506df245-ae39-4519-94fd-b11fea4794b5"), "s1", None, None, None, "hid", "pid", Some(0),
        versionOf = mainSections.head.id,
        createdDate = Some(DateTime.now.withDate(2020, 11, 20).withTime(10, 55, 34, 412)),
        updatedDate = Some(DateTime.now.withDate(2020, 11, 20).withTime(10, 55, 34, 412)),
        textUpdatedDate = Some(DateTime.now.withDate(2020, 11, 20).withTime(10, 55, 34, 412)))
    )

    val theDate = Some(DateTime.now.withDate(2020, 11, 25).withTime(23, 59, 59, 999))
    when(handbookRepository.retrieveDeletedAndNotDeletedChaptersForHandbook("hid")) thenReturn List()
    when(handbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook("hid")) thenReturn mainSections
    when(handbookVersionRepository.retrieveSectionVersionsInHandbook("hid", theDate.get, withText = true)) thenReturn sectionVersions2020_11_25
    when(handbookVersionRepository.retrieveChapterVersionsInHandbook("hid", theDate.get)) thenReturn List()
    val result = localHandbookService.retrieveChapterAndSectionVersions("hid", theDate)
    assert(result._2.nonEmpty)
    assert(result._2.size == 1)
    assert(result._2.head.updatedDate.contains(DateTime.now.withDate(2020, 11, 20).withTime(10, 55, 34, 412)))
  }

  def generateCentralChaptersAndSections(): (List[Chapter], List[Section]) = {
    List(
      Chapter(Some("cc1"), "chapter1", Some("cc1"), Some("ch1"), "ch1", None, Some(0)),
      Chapter(Some("cc2"), "chapter2", Some("cc2"), Some("ch1"), "ch1", None, Some(1)),
      Chapter(Some("cc4"), "chapter3", Some("cc4"), Some("ch1"), "ch1", None, Some(2)),

      Chapter(Some("cc3"), "chapter4", Some("cc3"), Some("ch1"), "ch1", Some("cc2"), Some(0)),
      Chapter(Some("cc5"), "chapter5", Some("cc5"), Some("ch1"), "ch1", Some("cc3"), Some(0))
    ) -> List(
      Section(Some("cs1"), "section1", Some("text1"), Some("cs1"), Some("ch1"), "ch1", "cc1", Some(0)),
      Section(Some("cs2"), "section2", Some("text2"), Some("cs2"), Some("ch1"), "ch1", "cc2", Some(1)),
      Section(Some("cs3"), "section3", Some("text3"), Some("cs3"), Some("ch1"), "ch1", "cc3", Some(1)),
      Section(Some("cs4"), "section4", Some("text4"), Some("cs4"), Some("ch1"), "ch1", "cc4", Some(0))
    )
  }

  def generateLocalChaptersAndSection(): (List[Chapter], List[Section]) = {
    List(
      Chapter(Some("lc1"), "chapter1", Some("cc1"), Some("ch1"), "lh1", None, Some(0)),
      Chapter(Some("lc2"), "chapter2", Some("cc2"), Some("ch1"), "lh1", None, Some(1)),
      Chapter(Some("lc4"), "chapter3", Some("cc4"), Some("ch1"), "lh1", None, Some(2)),

      Chapter(Some("lc3"), "chapter4", Some("cc3"), Some("ch1"), "lh1", Some("lc2"), Some(0)),
      Chapter(Some("lc5"), "chapter5", Some("cc5"), Some("ch1"), "lh1", Some("lc3"), Some(0))
    ) -> List(
      Section(Some("ls1"), "section1", Some("text1"), Some("cs1"), Some("ch1"), "lh1", "lc1", Some(0)),
      Section(Some("ls2"), "section2", Some("text2"), Some("cs2"), Some("ch1"), "lh1", "lc2", Some(1)),
      Section(Some("ls3"), "section3", Some("text3"), Some("cs3"), Some("ch1"), "lh1", "lc3", Some(1)),
      Section(Some("ls4"), "section4", Some("text4"), Some("cs4"), Some("ch1"), "lh1", "lc4", Some(0))
    )
  }
}
