import React, { useRef } from "react";
import { Title, Icon, Section, Group, Column, Button } from "kf-bui";
import parse from "html-react-parser";

import { AttachmentPopup } from "../AttachmentPopup";
import { useAttachments } from "../../hooks/useAttachments";
import { useFetchPublicSectionQuery } from "@/store/services/handbook/publicHandbookApi";
import { cleanHtml } from "@/utils/htmlParser";
import { Waypoint } from "../../hooks/useWaypoint";
import type { Section as SectionType } from "@/types";
import { Spinner } from "@/shared/components/Spinner";

interface SectionComponentProps {
  section: SectionType;
  onSectionEnter?: (section: SectionType) => void;
  onSectionLeave?: (section: SectionType) => void;
  waypointsEnabled?: boolean;
}

export const SectionComponent: React.FC<SectionComponentProps> = ({
  section,
  onSectionEnter,
  onSectionLeave,
  waypointsEnabled = true
}) => {
  const wrapperRef = useRef<HTMLDivElement>(null);

  const { showAttachments, attachmentCount, handleShowAttachments } =
    useAttachments("section", section.id!, wrapperRef);

  const { data: fetchedSection, isLoading: isSectionLoading } =
    useFetchPublicSectionQuery(section.id!, {
      skip: !!section.text,
    });

  const sectionData = fetchedSection || section;

  return (
    <div className="public-section-component">
      
      <Section>
        <hr />
        <Group className="content-header">
          <Title 
            id={`content-${section.id}`} 
            size="5"
          >
            <Icon icon="RegFileLines" className="section-icon" />
            {section.title}
          </Title>

          {attachmentCount > 0 && (
            <div className="public-attachment-container">
              <Button
                className="public-attachment-btn"
                onClick={() => {
                  handleShowAttachments(!showAttachments);
                }}
                icon="Paperclip"
              />

              {showAttachments && (
                <div ref={wrapperRef}>
                  <AttachmentPopup
                    sectionId={section.id!}
                    type="section"
                    onClose={() => handleShowAttachments(false)}
                  />
                </div>
              )}
            </div>
          )}
        </Group>

        {/* Waypoint wraps content area like legacy - critical for accurate detection */}
        <Waypoint
          onEnter={() => onSectionEnter?.(section)}
          onLeave={() => onSectionLeave?.(section)}
          disabled={!waypointsEnabled}
          triggerOffset={20} // Reduced offset for better accuracy
        >
          <Column className="section-content content">
            {isSectionLoading ? (
              <div className="loading-container">
                <Spinner />
              </div>
            ) : (
              parse(cleanHtml(sectionData.text || ""))
            )}
          </Column>
        </Waypoint>
      </Section>
    </div>
  );
};