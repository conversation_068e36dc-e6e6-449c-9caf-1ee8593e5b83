* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  min-height: 100vh;
}

.pending-timestamp {
  font-size: 0.875rem;
  color: #666;
  margin-top: 4px;
  margin-left: 24px;
}

.central-hb-banner {
  background-color: #c5dbff;
  color: #050037 !important;
}

.central-hb-banner.hero {
  background-color: #c5dbff;
}

.central-hb-banner .title,
.central-hb-banner .subtitle {
  color: #050037 !important;
}

i.pendingChange {
  margin-left: 5px;
  color: #fb8200;
}

/* Spinner animation for pending publication buttons */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Target spinner SVG in the pending publication button */
.pending-publication-button svg {
  animation: spin 1s linear infinite;
}

