import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { toast } from "@/shared/components/Toast";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  setPendingHandbooks,
  setCurrentlyViewedHandbook,
  setGlobalPollingActive,
  removePendingHandbook,
  selectPendingHandbooks,
  selectCurrentlyViewedHandbookId,
  selectShouldShowGlobalPolling,
  selectPendingHandbooksExcludingCurrent,
} from "@/store/slices/pendingPublicationsSlice";
import {
  useDiscoverAllPendingPublicationsQuery,
  useCheckPendingPublicationsQuery,
} from "./centralHandbookApi";

const extractHandbookIdFromPath = (pathname: string): string | null => {
  const match = pathname.match(/\/central-editor\/([^/]+)/);
  return match ? match[1] : null;
};

export const useGlobalPendingPublications = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const [hasInitialDiscovery, setHasInitialDiscovery] = useState(false);

  const pendingHandbooks = useAppSelector(selectPendingHandbooks);
  const currentlyViewedHandbookId = useAppSelector(
    selectCurrentlyViewedHandbookId
  );
  const shouldStartPolling = useAppSelector(selectShouldShowGlobalPolling);
  const pendingHandbooksExcludingCurrent = useAppSelector(
    selectPendingHandbooksExcludingCurrent
  );

  const isInCentralEditor = location.pathname.startsWith("/central-editor");

  useEffect(() => {
    const handbookId = extractHandbookIdFromPath(location.pathname);
    dispatch(setCurrentlyViewedHandbook(handbookId));
  }, [location.pathname, dispatch]);

  const {
    data: discoveredPendingHandbooks = [],
    error: discoveryError,
    isLoading: isDiscovering,
    isFetching: isDiscoveryFetching,
  } = useDiscoverAllPendingPublicationsQuery(undefined, {
    skip: !isInCentralEditor || hasInitialDiscovery,
    refetchOnMountOrArgChange: false,
  });

  useEffect(() => {
    if (
      !isDiscovering &&
      !isDiscoveryFetching &&
      !discoveryError &&
      isInCentralEditor &&
      !hasInitialDiscovery
    ) {
      dispatch(setPendingHandbooks(discoveredPendingHandbooks));
      setHasInitialDiscovery(true);
    }
  }, [
    discoveredPendingHandbooks,
    isDiscovering,
    isDiscoveryFetching,
    discoveryError,
    isInCentralEditor,
    hasInitialDiscovery,
    dispatch,
  ]);

  useEffect(() => {
    if (!isInCentralEditor && hasInitialDiscovery) {
      setHasInitialDiscovery(false);
    }
  }, [isInCentralEditor, hasInitialDiscovery]);

  const {
    data: pendingStatusResults = [],
    error: pollingError,
    isLoading: isPolling,
  } = useCheckPendingPublicationsQuery(pendingHandbooksExcludingCurrent, {
    pollingInterval:
      shouldStartPolling &&
      pendingHandbooksExcludingCurrent.length > 0 &&
      hasInitialDiscovery
        ? 10000
        : 0,
    skipPollingIfUnfocused: true,
    refetchOnMountOrArgChange: false,
    skip: !hasInitialDiscovery || pendingHandbooksExcludingCurrent.length === 0,
  });

  useEffect(() => {
    if (
      !isPolling &&
      !pollingError &&
      pendingStatusResults.length > 0 &&
      hasInitialDiscovery
    ) {
      const completedHandbooks = pendingStatusResults.filter(
        (result) => !result.isPending
      );


      completedHandbooks.forEach((completed) => {
        dispatch(removePendingHandbook(completed.handbookId));

        if (completed.handbookId !== currentlyViewedHandbookId) {
          toast.success(`Endringer publisert: ${completed.handbookTitle}`);
        }
      });
    }
  }, [
    pendingStatusResults,
    isPolling,
    pollingError,
    dispatch,
    currentlyViewedHandbookId,
    hasInitialDiscovery,
  ]);

  useEffect(() => {
    dispatch(setGlobalPollingActive(shouldStartPolling && hasInitialDiscovery));
  }, [shouldStartPolling, hasInitialDiscovery, dispatch]);


  return {
    pendingHandbooks,
    currentlyViewedHandbookId,
    isLoading: isDiscovering || isPolling,
    error: discoveryError || pollingError,
    shouldStartPolling: shouldStartPolling && hasInitialDiscovery,
    hasInitialDiscovery,
  };
};

export const useIsHandbookPending = (
  handbookId: string | undefined
): boolean => {
  const pendingHandbooks = useAppSelector(selectPendingHandbooks);

  if (!handbookId) return false;

  return pendingHandbooks.some(
    (handbook) => handbook.handbookId === handbookId
  );
};

export const usePendingPublicationsCount = (): number => {
  const pendingHandbooks = useAppSelector(selectPendingHandbooks);
  const currentlyViewedHandbookId = useAppSelector(
    selectCurrentlyViewedHandbookId
  );

  return pendingHandbooks.filter(
    (handbook) => handbook.handbookId !== currentlyViewedHandbookId
  ).length;
};
