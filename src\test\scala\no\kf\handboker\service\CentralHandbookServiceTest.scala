package no.kf.handboker.service

import no.kf.api.{Product, Taxonomy}
import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import no.kf.handboker.model.local.Chapter
import org.joda.time.DateTime
import org.json4s.ext.JodaTimeSerializers
import org.json4s.jackson.Serialization.write
import org.json4s.{DefaultFormats, Formats}
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfterEach, FunSuite}

class CentralHandbookServiceTest extends FunSuite with DefaultTestDI with BeforeAndAfterEach {

  override val componentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val centralHandbookService = new CentralHandbookServiceImpl
  }

  private val repo = componentRegistry.centralHandbookRepository
  private val service = componentRegistry.centralHandbookService
  private val accessService = componentRegistry.centralAccessService

  override def beforeEach() {
    reset(repo, accessService)
  }

  test("That retriving central handbook converts correctly / is retrieved correctly") {

  }

  test("That we retrieve central handbook by id without html") {
    when(repo.retrieveCentralHandbook("id")).thenReturn(None)
    service.retrieveCentralHandbook("id")
    verify(repo, times(1)).retrieveCentralHandbook("id")
  }

  test("That retriving central handbook by id converts correctly") {
    when(repo.retrieveCentralHandbook("id")).thenReturn(Some(centralHandbook))
    val res = service.retrieveCentralHandbook("id", true)

    assert(res !== None)
    val handbook = res.head
    assert(handbook.id.get === centralHandbook.id)
    assert(handbook.title === centralHandbook.title)

    verify(repo, times(1)).retrieveCentralHandbook("id")
  }

  test("That retrieving central handbooks by external org id returns the correct handbooks") {
    /*when(repo.retrieveCentralHandbooks(false)).thenReturn(List(centralHandbook.copy(id = "id1"), centralHandbook.copy(id = "id2"), centralHandbook.copy(id = "id3")))
    when(accessService.retrieveOrgAccesses("externalOrgId")).thenReturn(List("id1", "id3"))

    val res = service.retrieveCentralHandbooks("externalOrgId")

    assert(res.length === 2)
    assert(res.flatMap(_.id).toSet ===  List("id1", "id3").toSet)*/
  }

  test("That retriving central chapter converts correctly") {
    val chapter = centralChapters.head
    when(repo.retrieveCentralChapter("chapId", "handId")).thenReturn(Some(chapter))

    val res = service.retrieveCentralChapter("chapId", "handId").getOrElse(fail())

    assert(res.id.get === chapter.id)
    assert(res.handbookId === "handId")
    assert(res.importedHandbookId.get === "handId")
    assert(res.importedHandbookChapterId.get === chapter.id)
    assert(res.parentId === chapter.parentId)
    assert(res.title === chapter.title)
    assert(res.localChange === false)
    assert(res.pendingChange === false)
    assert(res.isDeleted === None)
    assert(res.sortOrder.get === chapter.sortOrder)
  }

  test("That retriving central section passes the same withHtml flag to repo") {
    when(repo.retrieveCentralSection("secId")).thenReturn(None)
    service.retrieveCentralSection("secId")
    verify(repo, times(1)).retrieveCentralSection("secId")

    when(repo.retrieveCentralSection("secId")).thenReturn(None)
    service.retrieveCentralSection("secId")
    verify(repo, times(1)).retrieveCentralSection("secId")
  }

  test("That retrieving central section converts correctly") {
    val section = centralSections.head.copy(id = Some("someUUIDsectionId"), sortOrder = 861923)
    when(repo.retrieveCentralSection("secId")).thenReturn(Some(section))

    val res = service.retrieveCentralSection("secId").getOrElse(fail())

    assert(res.id === section.id)
    assert(res.title === section.title)
    assert(res.parentId === section.parentId)
    assert(res.handbookId === "handId")
    assert(res.importedHandbookId.get === "handId")
    assert(res.importedHandbookSectionId === section.id)
    assert(res.localTitleChange === false)
    assert(res.pendingTitleChange === false)
    assert(res.isBasedOnCentralContent === true)
    assert(res.text === section.html)
    assert(res.sortOrder.get === section.sortOrder)
  }

  test("That retrieve chapters and sections without test calls repo with html flag set to false") {

  }

  test("That retrieve chapters and sections without test calls repo with html flag set to true") {
    when(repo.retrieveCentralHandbook("handId")).thenReturn(None)
    service.retrieveChaptersAndSectionsWithText("handId")
    verify(repo, times(1)).retrieveCentralHandbook("handId")
  }

  test("That retrieveImportedHandbook calls repo and returns result") {
    when(repo.retrieveCentralHandbook("handId")).thenReturn(Some(centralHandbook))
    val res = service.retrieveCentralHandbook("handId", false).getOrElse(fail())
    assert(res === centralHandbook)
  }

  test("That retrieveCentralSectionByCentralIdAndParentId calls repo with the correct arguments") {
    val centralId = "central section ID"
    val parentId = "central parent ID"
    val handbookId = "handbook ID"
    val htmlFlag = true
    when(repo.retrieveCentralSection(centralId)).thenReturn(None)
    service.retrieveCentralSectionByCentralIdAndParentId(centralId, parentId, handbookId, htmlFlag)
    verify(repo, times(1)).retrieveCentralSection(centralId)
  }

  test("That we can delete a handbook with all its chapters and functions") {
    val handbookId = "handbook ID"
    val chapterId = "central chapter ID"

    val chaptersToDelete = List(CentralChapter(Some(chapterId),"title", None, handbookId, None, Some(DateTime.now), Some(DateTime.now), None, Some("CreatedBy"), Some("UpdatedBy"), 0))

    when(repo.deleteCentralHandbook(handbookId)).thenReturn()
    when(repo.retrieveChaptersByHandbookId(handbookId)).thenReturn(chaptersToDelete)

    service.deleteCentralHandbook(handbookId)

    verify(repo,times(1)).deleteCentralHandbook(handbookId)
    verify(repo,times(1)).retrieveChaptersByHandbookId(handbookId)
  }

  private lazy val centralSections = List(
    CentralSection(None, "central section 1", "centralChapterId1", "centralSectionId1", Some("some html"), None, Some(DateTime.now.minusDays(10)), Some(DateTime.now.minusDays(20)), Some(DateTime.now), Some(DateTime.now.minusDays(5)), Some(DateTime.now.minusDays(3)), Some("CreatedBy"), Some("UpdatedBy"), Some("TitleUpdatedBy"), Some("HtmlUpdatedBy"), 1212),
    CentralSection(None, "central section 2", "centralChapterId1", "centralSectionId2", Some("some html"), None, Some(DateTime.now.minusDays(10)), Some(DateTime.now.minusDays(20)), Some(DateTime.now), Some(DateTime.now.minusDays(5)), Some(DateTime.now.minusDays(3)), Some("CreatedBy"), Some("UpdatedBy"), Some("TitleUpdatedBy"), Some("HtmlUpdatedBy"), 137),
    CentralSection(None, "central section 3", "centralChapterId2", "centralSectionId3", Some("some html"), None, Some(DateTime.now.minusDays(10)), Some(DateTime.now.minusDays(20)), Some(DateTime.now), Some(DateTime.now.minusDays(5)), Some(DateTime.now.minusDays(3)), Some("CreatedBy"), Some("UpdatedBy"), Some("TitleUpdatedBy"), Some("HtmlUpdatedBy"), 8212345),
    CentralSection(None, "central section 4", "centralChapterId2", "centralSectionId4", Some("some html"), None, Some(DateTime.now.minusDays(10)), Some(DateTime.now.minusDays(20)), Some(DateTime.now), Some(DateTime.now.minusDays(5)), Some(DateTime.now.minusDays(3)), Some("CreatedBy"), Some("UpdatedBy"), Some("TitleUpdatedBy"), Some("HtmlUpdatedBy"), 12))
  private lazy val centralChapters = List(
    CentralChapter(Some("centralChapterId1"), "central chapter 1", None, "centralHandbookId", None, Some(DateTime.now), Some(DateTime.now), None, Some("CreatedBy"), Some("UpdatedBy"), 91238),
    CentralChapter(Some("centralChapterId2"), "central chapter 2", Some("centralChapterId1"), "centralHandbookId", None, Some(DateTime.now), Some(DateTime.now), None, Some("CreatedBy"), Some("UpdatedBy"), 71623))
  private lazy val centralHandbook = CentralHandbook(Some("centralHandbookId"), "handbook", None, Some(DateTime.now), Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy"))
}
