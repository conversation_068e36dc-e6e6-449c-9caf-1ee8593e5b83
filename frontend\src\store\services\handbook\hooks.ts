import { useMemo } from "react";
import { useGetLocalHandbooksQuery } from "./localHandbookApi";
import { useSession } from "../session/hooks";
import { 
  selectPendingCount, 
  selectPendingItems,
  selectLocalHandbooks,
  selectLocalChapters,
  selectLocalSections
} from "./selectors";


// Hook to get the count of pending changes
export const usePendingCount = () => {
  const { hasOrganization } = useSession();
  const { data } = useGetLocalHandbooksQuery(undefined, {
    skip: !hasOrganization,
  });
  
  return useMemo(() => selectPendingCount(data), [data]);
};

// Hook to get all items with pending changes, grouped by handbook
export const usePendingItems = () => {
  const { hasOrganization } = useSession();
  const { data } = useGetLocalHandbooksQuery(undefined, {
    skip: !hasOrganization,
  });
  
  return useMemo(() => selectPendingItems(data), [data]);
};

// Hook to get just the handbooks array from the main query
export const useLocalHandbooks = () => {
  const { hasOrganization } = useSession();
  const { data, ...rest } = useGetLocalHandbooksQuery(undefined, {
    skip: !hasOrganization,
  });
  
  const handbooks = useMemo(() => selectLocalHandbooks(data), [data]);
  
  return { data: handbooks, ...rest };
};

//
// Hook to get just the chapters array from the main query.
// Replaces the removed useGetLocalChaptersQuery hook.
// Only fetches data when user has an organization.
export const useLocalChapters = () => {
  const { hasOrganization } = useSession();
  const { data, ...rest } = useGetLocalHandbooksQuery(undefined, {
    skip: !hasOrganization,
  });
  
  const chapters = useMemo(() => selectLocalChapters(data), [data]);
  
  return { data: chapters, ...rest };
};

//
// Hook to get just the sections array from the main query.
// Replaces the removed useGetLocalSectionsQuery hook.
// Only fetches data when user has an organization.
export const useLocalSections = () => {
  const { hasOrganization } = useSession();
  const { data, ...rest } = useGetLocalHandbooksQuery(undefined, {
    skip: !hasOrganization,
  });
  
  const sections = useMemo(() => selectLocalSections(data), [data]);
  
  return { data: sections, ...rest };
};