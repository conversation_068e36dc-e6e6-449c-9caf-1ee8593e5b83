import React, { Component, type ReactNode } from "react";
import { reportComponentError } from "@/shared/utils/errorReporting";
import type {
  ErrorBoundaryState,
  ErrorBoundaryProps,
  ErrorFallbackProps,
} from "@/shared/types/errorBoundary";

export abstract class BaseErrorBoundary extends Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  private resetTimeoutId?: NodeJS.Timeout;

  constructor(props: ErrorBoundaryProps) {
    super(props);

    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void {
    this.setState({
      errorInfo,
    });

    reportComponentError(error, errorInfo, {
      category: this.getErrorCategory(),
      level: this.getErrorLevel(),
      showToast: this.props.showToast,
      context: {
        componentStack: errorInfo.componentStack || undefined,
        ...(this.props.name ? { component: this.props.name } : {}),
        ...(this.props.level ? { boundaryLevel: this.props.level } : {}),
      },
    });

    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps): void {
    const { resetKeys } = this.props;
    const { hasError } = this.state;

    if (hasError && resetKeys && prevProps.resetKeys) {
      const hasResetKeyChanged = resetKeys.some(
        (resetKey, idx) => prevProps.resetKeys![idx] !== resetKey
      );

      if (hasResetKeyChanged) {
        this.resetErrorBoundary();
      }
    }
  }

  componentWillUnmount(): void {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  private resetErrorBoundary = (): void => {
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  private handleRetry = (): void => {
    this.resetErrorBoundary();

    this.resetTimeoutId = setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  protected abstract getErrorCategory():
    | "ui"
    | "api"
    | "auth"
    | "data"
    | "network"
    | "unknown";
  protected abstract getErrorLevel(): "low" | "medium" | "high" | "critical";
  protected abstract renderFallback(props: ErrorFallbackProps): ReactNode;

  render(): ReactNode {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback: CustomFallback } = this.props;

    if (hasError && error && errorInfo) {
      const fallbackProps: ErrorFallbackProps = {
        error,
        errorInfo,
        resetError: this.resetErrorBoundary,
        retry: this.handleRetry,
      };

      if (CustomFallback) {
        return <CustomFallback {...fallbackProps} />;
      }

      return this.renderFallback(fallbackProps);
    }

    return children;
  }
}
