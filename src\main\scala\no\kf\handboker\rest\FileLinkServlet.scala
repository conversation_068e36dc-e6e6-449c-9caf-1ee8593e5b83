package no.kf.handboker.rest

import no.kf.handboker.model.local.FileLinksInput
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.support.JsonSupport
import org.scalatra.ScalatraServlet
import no.kf.rest.ScalatraExceptions

class FileLinkServlet extends ScalatraServlet with JsonSupport with SessionSupport {

  private lazy val service= componentRegistry.fileLinkService

  get("/section/:ownerId") {
    val ownerId = extractRequiredParam("ownerId")
    service.retrieveFileLinks(ownerId, "SECTION")
  }

  get("/chapter/:ownerId") {
    val ownerId = extractRequiredParam("ownerId")
    service.retrieveFileLinks(ownerId, "CHAPTER")
  }

  get("/section/:ownerId/count") {
    val ownerId = extractRequiredParam("ownerId")
    service.retrieveFileLinkCount(ownerId, "SECTION")
  }

  get("/chapter/:ownerId/count") {
    val ownerId = extractRequiredParam("ownerId")
    service.retrieveFileLinkCount(ownerId, "CHAPTER")
  }

  post("/?") {
    val fileLinksInput = parsedBody.extract[FileLinksInput]
    service.persistFileLinks(fileLinksInput, currentUser.email)
  }

  delete("/:linkId") {
    val linkId = extractRequiredParam("linkId")

    service.deleteFileLink(linkId)
  }
}