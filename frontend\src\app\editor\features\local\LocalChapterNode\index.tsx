import React from "react";
import { Icon, Tree } from "kf-bui";
import { useLocation } from "react-router-dom";
import type { LocalChapterWithChildren } from "@/store/services/handbook/utils";
import {
  sortChaptersAndSections,
  isChapter,
} from "@/store/services/handbook/utils";
import type { LocalTreeNodeWithChildren, Section, Chapter } from "@/types";
import { useAppSelector } from "@/store";
import { selectSelectedLocalItem } from "@/store/slices/localTreeSlice";
import { shouldDisableLocalNode } from "../../../utils/localMoveValidation";
import { LocalSectionNode } from "../LocalSectionNode";

interface LocalChapterNodeProps {
  chapter: LocalChapterWithChildren;
  moving?: boolean;
  onSetSelectedItem?: (item: LocalTreeNodeWithChildren) => void;
  disabled?: boolean;
  movedItem?: Chapter | Section;
}

export const LocalChapterNode: React.FC<LocalChapterNodeProps> = ({
  chapter,
  moving = false,
  onSetSelectedItem,
  disabled = false,
  movedItem,
}) => {
  const location = useLocation();
  const childChapters = chapter.chapters || [];
  const sections = chapter.sections || [];
  const selectedLocalItem = useAppSelector(selectSelectedLocalItem);

  const isActive = React.useMemo(() => {
    if (moving) return false;

    const currentPath = location.pathname;
    const chapterPath = `/chapter/${chapter.id}`;

    if (currentPath.includes(chapterPath)) {
      return true;
    }

    const childSectionIds = sections.map((s) => s.id);
    const isChildSectionActive = childSectionIds.some((sectionId) =>
      currentPath.includes(`/section/${sectionId}`)
    );

    return isChildSectionActive;
  }, [location.pathname, chapter.id, sections, moving]);

  const sortedItems = sortChaptersAndSections(childChapters, sections);

  const isNodeDisabled = movedItem ? shouldDisableLocalNode(chapter, movedItem, disabled) : false;

  const items = sortedItems.map((item) => {
    if (isChapter(item)) {
      return (
        <LocalChapterNode
          key={item.id}
          chapter={item as LocalChapterWithChildren}
          onSetSelectedItem={onSetSelectedItem}
          disabled={isNodeDisabled}
          moving={moving}
          movedItem={movedItem}
        />
      );
    } else {
      return (
        <LocalSectionNode
          key={item.id}
          section={item as Section}
          moving={moving}
          onSetSelectedItem={onSetSelectedItem}
          disabled={isNodeDisabled}
          movedItem={movedItem}
        />
      );
    }
  });

  const getLocalChapterUrl = () => {
    return `/editor/${chapter.handbookId}/chapter/${chapter.id}/`;
  };

  if (moving && movedItem) {
    const isSelected = selectedLocalItem?.id === chapter.id;
    const isBeingMoved = chapter.id === movedItem.id;
    const isParentOfMoved = movedItem.parentId === chapter.id;

    return (
      <Tree.Item
        items={items}
        key={chapter.id}
        id={chapter.id!}
        disabled={isNodeDisabled}
        onClick={() => onSetSelectedItem?.(chapter)}
        style={{
          opacity: isNodeDisabled ? 0.5 : 1,
          cursor: isNodeDisabled ? 'not-allowed' : 'pointer',
          fontWeight: (isBeingMoved || isSelected) ? 600 : undefined,
          color: isBeingMoved ? '#1976d2' : (isSelected ? '#2e7d32' : (isParentOfMoved ? '#666' : undefined)),
          backgroundColor: isBeingMoved ? '#e3f2fd' : (isSelected ? '#e8f5e8' : (isParentOfMoved ? '#f5f5f5' : undefined)),
          padding: (isBeingMoved || isSelected || isParentOfMoved) ? '2px 4px' : undefined,
          borderRadius: (isBeingMoved || isSelected || isParentOfMoved) ? '4px' : undefined,
          border: isSelected ? '2px solid #2e7d32' : (isBeingMoved ? '2px solid #1976d2' : undefined),
        }}
      >
        <Icon icon="RegBookmark" size="small" style={{ marginRight: "4px" }} />
        {chapter.title}
        {isBeingMoved && <span style={{ marginLeft: '8px', fontSize: '12px', color: '#1976d2' }}>← flytter</span>}
        {isSelected && <span style={{ marginLeft: '8px', fontSize: '12px', color: '#2e7d32' }}>← valgt som mål</span>}
      </Tree.Item>
    );
  }
  
  if (moving) {
    return (
      <Tree.Item
        items={items}
        key={chapter.id}
        id={chapter.id!}
        disabled={disabled}
        onClick={() => onSetSelectedItem?.(chapter)}
      >
        <Icon icon="RegBookmark" size="small" style={{ marginRight: "4px" }} />
        {chapter.title}
      </Tree.Item>
    );
  }

  return (
    <Tree.ItemLink
      to={getLocalChapterUrl()}
      items={items}
      key={chapter.id}
      id={chapter.id!}
      style={isActive ? { fontWeight: "600", color: "#050037" } : undefined}
    >
      <Icon icon="RegBookmark" size="small" style={{ marginRight: "4px" }} />
      {chapter.title}
    </Tree.ItemLink>
  );
};
