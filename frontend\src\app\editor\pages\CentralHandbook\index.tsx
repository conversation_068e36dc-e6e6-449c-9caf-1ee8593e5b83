import { useState } from "react";
import { Routes, Route } from "react-router-dom";
import {
  Container,
  Title,
  Subtitle,
  ImageHero,
  Hero,
  Column,
  Button,
  Columns,
  Section,
} from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { CentralHandbookModal } from "../../features/handbook/CentralHandbookModal";
import { CentralHandbookTree } from "../../features/central";
import { CentralHandbookScreen, ChaptersRouter } from "../../features";
import { CentralSectionRouter } from "../../features/section/CentralSectionRouter";
import { LinkCollectionPage } from "../../features/links/LinkCollectionPage";
import { NoSelectionScreen } from "../../features/handbook/NoSelectionScreen";

export const CentralHandbook = () => {
  const t = usePrefixedTranslation("editor");

  document.title = `${t("containers.CentralHandbookEditorPage.title")}  - KF Sentrale Hå<PERSON>ø<PERSON>`;

  const [showCreateCentralHandbookModal, setShowCreateCentralHandbookModal] =
    useState<boolean>(false);

  return (
    <>
      <ImageHero color="primary" className="central-hb-banner">
        <Hero.Body>
          <Container>
            <Columns>
              <Column>
                <Title>{t("containers.CentralHandbookEditorPage.title")}</Title>
                <Subtitle>
                  {t("containers.CentralHandbookEditorPage.header")}
                </Subtitle>
              </Column>
              <Column narrow>
                <Button
                  inverted
                  color="primary"
                  onClick={() => setShowCreateCentralHandbookModal(true)}
                  icon="plus"
                >
                  {t("components.NoSelection.createButton")}
                </Button>

                {showCreateCentralHandbookModal && (
                  <CentralHandbookModal
                    isOpen={showCreateCentralHandbookModal}
                    type="create"
                    onHide={(val: boolean) =>
                      setShowCreateCentralHandbookModal(val)
                    }
                  />
                )}
              </Column>
            </Columns>
          </Container>
        </Hero.Body>
      </ImageHero>

      <Section>
        <Container>
          <Columns>
            <Column size="1/3">
              <Routes>
                <Route
                  path=":handbookId/chapter/:chapterId/move"
                  element={<CentralHandbookTree moving />}
                />
                <Route
                  path=":handbookId/section/:sectionId/move"
                  element={<CentralHandbookTree moving />}
                />
                <Route
                  path="*"
                  element={<CentralHandbookTree moving={false} />}
                />
              </Routes>
            </Column>
            <Column size="2/3">
              <Routes>
                <Route index element={<NoSelectionScreen />} />
                <Route path=":handbookId" element={<CentralHandbookScreen />} />
                <Route
                  path=":handbookId/chapter/*"
                  element={<ChaptersRouter />}
                />
                <Route
                  path=":handbookId/section/*"
                  element={<CentralSectionRouter />}
                />
                <Route
                  path=":handbookId/links"
                  element={<LinkCollectionPage />}
                />
              </Routes>
            </Column>
          </Columns>
        </Container>
      </Section>
    </>
  );
};
