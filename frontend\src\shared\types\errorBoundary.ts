import type { ReactNode, ErrorInfo } from "react";

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export interface ErrorFallbackProps {
  error: Error;
  errorInfo: ErrorInfo;
  resetError: () => void;
  retry?: () => void;
}

export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  resetOnPropsChange?: boolean;
  resetKeys?: Array<string | number | boolean | null | undefined>;
  level?: "app" | "route" | "feature" | "form";
  name?: string;
  showToast?: boolean;
}

export interface BaseErrorFallbackProps {
  title?: string;
  message?: string;
  showRetry?: boolean;
  showReset?: boolean;
  showDetails?: boolean;
  actions?: Array<{
    label: string;
    onClick: () => void;
    color?: "primary" | "white" | "danger";
  }>;
}
