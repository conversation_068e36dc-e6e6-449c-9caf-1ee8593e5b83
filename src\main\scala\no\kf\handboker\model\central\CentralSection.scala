package no.kf.handboker.model.central

import org.joda.time.DateTime
/*
  sortOrder,
  created/updatedDate and
  created/updatedBy are set as default None/0
  because they are set in repository level
 */
case class CentralSection(id: Option[String],
                          title: String,
                          parentId: String,
                          centralHandbookId: String,
                          html: Option[String],
                          versionOf: Option[String] = None,
                          createdDate: Option[DateTime] = None,
                          registeredDate: Option[DateTime] = None,
                          updatedDate: Option[DateTime] = None,
                          titleUpdatedDate: Option[DateTime] = None,
                          htmlUpdatedDate: Option[DateTime] = None,
                          createdBy: Option[String] = None,
                          updatedBy: Option[String] = None,
                          titleUpdatedBy: Option[String] = None,
                          htmlUpdatedBy: Option[String] = None,
                          sortOrder: Int = 0)
