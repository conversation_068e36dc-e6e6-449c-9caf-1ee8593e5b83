import java.util.concurrent.Executors
import javax.servlet.ServletContext
import no.kf.config.RunMigrationToBrukerAdm
import no.kf.handboker.batch.BatchJobs
import no.kf.handboker.config.{RunBatchJobs, RunMigrationScripts, SwaggerUiHost}
import no.kf.handboker.rest._
import no.kf.handboker.{ComponentRegistry, ProductionRegistry}
import no.kf.util.Logging
import org.scalatra.LifeCycle

class ScalatraBootstrap extends LifeCycle with Logging {
  lazy val componentRegistry: ComponentRegistry = ProductionRegistry.componentRegistry
  lazy val runMigrationScripts: Boolean = componentRegistry.settings.settingFor(RunMigrationScripts).toBoolean
  lazy val runMigrationToBrukerAdm: Boolean = componentRegistry.settings.settingFor(RunMigrationToBrukerAdm).toBoolean
  lazy val runBatchJobs: Boolean = componentRegistry.settings.settingFor(RunBatchJobs).toBoolean
  lazy val swaggerUiPath: String = componentRegistry.settings.settingFor(SwaggerUiHost)

  implicit val swagger: HandbookSwagger = new HandbookSwagger

  override def init(context: ServletContext) {

    context.initParameters("org.scalatra.environment") = "production"

    context.initParameters("org.scalatra.cors.allowedOrigins") = swaggerUiPath
    context.initParameters("org.scalatra.cors.allowedMethods") = "GET"
    context.initParameters("org.scalatra.cors.allowedHeaders") = "Content-Type"
    context.initParameters("org.scalatra.cors.preflightMaxAge") = "1800"
    context.initParameters("org.scalatra.cors.allowCredentials") = "true"

    context mount(new MainServlet, "/*")
    context mount(new SessionServlet, "/session/*")

    context mount(new LocalHandbookServlet, "/handbooks/local/*")
    context mount(new CentralHandbookServlet, "/handbooks/central/*")
    context mount(new HandbookLinkServlet, "/handbooks/links/*")
    context mount(new FileLinkServlet, "/handbooks/file-links/*")
    context mount(new ReadingLinkServlet, "/handbooks/readinglink/*")

    context mount(new ReportServlet, "/handbooks/download/*")


    context mount(new ImageServlet, "/images/*")

    context mount(new ImageUploadServlet, "/image-upload/*")

    context mount(new FileServlet, "/files/*")

    context mount(new SearchServlet, "/search/")

    context mount(new HealthCheckServlet, "/helsesjekk/*")

    context mount(new OrganizationDataServlet, "/organization-data/*")


    // The "public" ip filtered handbook servlet
    context mount(new PublicIpFilteredHandbookServlet, "/public/*")

    // Handbook API
    context mount(new HandbookApiServlet, "/api/*", "handbook")

    context mount(new ResourcesApp, "/api-docs")

    wireUpBatchJobs()
    indexElasticSearch()
  }

  private def wireUpBatchJobs() = {
    if (runBatchJobs) {
      log.info("Will initialize batch jobs")
      BatchJobs.initializeBatchCronJobs
      log.info("Finished initializing batch jobs")
    }else{
      log.warn("RunBatchJobs set to false, will not initialize batch jobs")
    }
  }

  private def indexElasticSearch() = {
      Executors.newSingleThreadExecutor().execute(() => {
        try {
          componentRegistry.searchIndexBuilderService.indexDocuments()
        } catch {
          case e: Exception => log.error("Exception during document indexing", e)
        }
      })
  }
}
