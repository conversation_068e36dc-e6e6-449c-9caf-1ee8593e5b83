import React from "react";
import { Level, Heading, FormattedDate } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { PendingElement } from "@/types";

interface CentralMetadataElement {
  createdBy?: string;
  createdDate?: string;
  updatedBy?: string;
  updatedDate?: string;
  textUpdatedBy?: string;
  textUpdatedDate?: string;
}

interface CentralMetadataProps {
  element: PendingElement | CentralMetadataElement;
  prepend?: React.ReactNode;
  append?: React.ReactNode;
}

export const CentralMetadata: React.FC<CentralMetadataProps> = ({
  element,
  prepend,
  append,
}) => {
  const t = usePrefixedTranslation("editor.components.Metadata");

  return (
    <Level style={{ fontSize: "0.8em" }} className="metadata">
      {prepend && <Level.Item textCentered>{prepend}</Level.Item>}

      <Level.Item textCentered>
        <div>
          <Heading>{t("created")}</Heading>
          <p>{element.createdBy || "Ukjent"}</p>
          {element.createdDate && <FormattedDate value={element.createdDate} />}
        </div>
      </Level.Item>

      <Level.Item textCentered>
        <div>
          <Heading>{t("updated")}</Heading>
          <p>{element.updatedBy || "Ukjent"}</p>
          {element.updatedDate && <FormattedDate value={element.updatedDate} />}
        </div>
      </Level.Item>

      {"textUpdatedBy" in element && element.textUpdatedBy && (
        <Level.Item textCentered>
          <div>
            <Heading>{t("textUpdated")}</Heading>
            <p>{element.textUpdatedBy || "Ukjent"}</p>
            <FormattedDate value={element.textUpdatedDate || ""} />
          </div>
        </Level.Item>
      )}

      {append && <Level.Item textCentered>{append}</Level.Item>}
    </Level>
  );
};