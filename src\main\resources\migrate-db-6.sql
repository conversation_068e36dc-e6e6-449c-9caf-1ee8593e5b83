CREATE TABLE handbook_link_collection(id VARCHAR(37), title VA<PERSON><PERSON>R(2000) NOT NULL, handbook_id VARCHAR(37) NOT NULL, sort_order VARCHAR(37), PRIMARY KEY(id))
ALTER TABLE handbook_link_collection ADD CONSTRAINT fk_handbook_link_collection_handbook_id FOREIGN KEY(handbook_id) REFERENCES handbook(id)

CREATE TABLE handbook_link(id VARCHAR(37), title VARCHAR(2000) NOT NULL, url VARCHAR(2000) NOT NULL, sort_order int NOT NULL, handbook_link_collection_id VARCHAR(37) NOT NULL, PRIMARY KEY(id))
ALTER TABLE handbook_link ADD CONSTRAINT fk_handbook_link_handbook_link_collection_id FOREIGN KEY(handbook_link_collection_id) REFERENCES handbook_link_collection(id)
