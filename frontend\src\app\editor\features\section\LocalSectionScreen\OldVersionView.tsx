import { Fragment, useState, useEffect } from "react";
import { Icon, Title, Field, Select, FormattedDate } from "kf-bui";
import { FormattedMessage } from "react-intl";
import { useGetLocalSectionVersionQuery } from "@/store/services/handbook/localHandbookApi";
import type { Section } from "@/types";
import { Wysiwyg } from "../../shared/Wysiwyg";
import { LocalMetadata as Metadata } from "../../local/LocalMetadata";

interface OldVersionViewProps {
  versions: Section[];
}

const getLatestUpdate = (section: Section) => {
  const date = new Date(section.updatedDate || "");
  const textDate = new Date(section.textUpdatedDate || "");
  return date > textDate ? date : textDate;
};

export const OldVersionView = ({
  versions,
}: OldVersionViewProps) => {
  const [selectedVersionId, setSelectedVersionId] = useState<string | null>(
    null
  );

  const { data: selectedVersion, error: selectedVersionError } =
    useGetLocalSectionVersionQuery(selectedVersionId!, {
      skip: !selectedVersionId,
    });

  const sortedVersions = [...versions].sort(
    (v1, v2) => getLatestUpdate(v2).getTime() - getLatestUpdate(v1).getTime()
  );

  const handleVersionChange = (versionFromList: Section) => {
    setSelectedVersionId(versionFromList.id!);
  };

  const versionRenderer = (version: Section) => {
    const latestUpdate =
      new Date(version.updatedDate || "") >
      new Date(version.textUpdatedDate || "")
        ? { date: version.updatedDate, user: version.updatedBy }
        : { date: version.textUpdatedDate, user: version.textUpdatedBy };

    return (
      <Fragment>
        <FormattedDate value={latestUpdate.date || new Date()} format="yyyy.MM.dd-HH:mm" />
        <span style={{ marginLeft: "5px" }}>
          oppdatert av {latestUpdate.user}
        </span>
      </Fragment>
    );
  };

  useEffect(() => {
    if (selectedVersionError) {
      console.error("Error loading selected version:", selectedVersionError);
    }
  }, [selectedVersionError]);

  if (!versions.length) {
    return <p>Det er ingen eldre versjoner av dette avsnittet</p>;
  }

  return (
    <div>
      <h2
        style={{ fontWeight: "bold", fontSize: "1.5em", marginBottom: "0.5em" }}
      >
        <FormattedMessage id="editor.containers.SectionPage.components.OldVersionView.title" />
      </h2>
      <Fragment>
        <Field style={{ marginBottom: "1em" }}>
          <FormattedMessage id="editor.containers.SectionPage.components.OldVersionView.pickVersion" />
          <Select
            id="sectionVersion"
            name="handbookVersion"
            aria-label="Velg versjon av håndboken du vil eksportere"
            options={sortedVersions}
            value={selectedVersion || null}
            onChange={handleVersionChange}
            valueKey="id"
            labelKey="updatedDate"
            optionRenderer={versionRenderer}
            valueRenderer={versionRenderer}
          />
        </Field>
        {selectedVersion && (
          <Fragment>
            <Title>
              <Icon
                icon="RegFileLines"
                size="medium"
                style={{ marginRight: "1rem" }}
              />
              <span>{selectedVersion.title}</span>
            </Title>
            <hr />
            <Metadata element={selectedVersion} />
            <hr />
            <label
              htmlFor="old-section-text"
              style={{ fontWeight: "bold", fontSize: "large" }}
            >
              Avsnittstekst
            </label>
            <Wysiwyg
              id="old-section-text"
              value={selectedVersion.text || ""}
              disabled={true}
              menubar=""
              toolbar={false}
            />
          </Fragment>
        )}
      </Fragment>
    </div>
  );
};
