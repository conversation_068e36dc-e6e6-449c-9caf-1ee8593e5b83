/* Base Link Styles */
a {
  color: #0000ff;
  cursor: pointer;
  text-decoration: none;
  -webkit-transition: none 86ms ease-out;
  transition: none 86ms ease-out;
}

footer a,
footer a:hover {
  color: #0000ff;
  text-decoration: underline;
}

/* <PERSON><PERSON> Settings Button */
.cookie-settings-btn {
  background-color: transparent;
  border: none;
  color: #0000ff;
  text-decoration: underline;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
  padding: 0;
}

.card.comment .card-header {
  padding: 8px;
  justify-content: space-between;
}

.card.comment .card-footer-item {
  padding: 8px;
}

.comment-edited-by {
  font-weight: 600;
}

.pending-sections.card-content {
  padding-top: 0;
}

.pending-item {
  padding-top: 8px;
  padding-bottom: 8px;
}

.title {
  font-weight: 600 !important;
}

.link-collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px;
  gap: 12px;

  .title {
    font-size: 20px !important;
  }
}

.link-collection-form {
  margin-top: 1.5rem;
}

/* Toast Customizations */
.Toastify__toast--success {
  background-color: #050037 !important;
  color: #ffffff !important;
}

.Toastify__toast--success .Toastify__close-button {
  color: #ffffff !important;
}

.Toastify__toast--error {
  background-color: #ff3246 !important;
  color: #ffffff !important;
}

.Toastify__toast--error .Toastify__close-button {
  color: #ffffff !important;
}

.full-screen-center {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* Error and Info States */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
  gap: 1rem;
  text-align: center;
}

.error-message {
  color: #666;
  font-size: 14px;
}

.retry-button {
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.retry-button:hover {
  background-color: #0056b3;
}

/* Layout Helpers */
.app-layout {
  display: flex;
  flex-direction: column;
}

.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1 0 auto;
}

/* Loading States */
.suspense-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.external-link-radio-month {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;

  label {
    display: flex;
    align-items: center;
  }
}

.subscribe-label {
  flex-direction: column;
}
