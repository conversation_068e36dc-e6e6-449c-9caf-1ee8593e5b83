import React, { useState, useCallback, type ReactNode } from "react";
import { <PERSON><PERSON>, Button, Content, Checkbox } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

interface DeleteModalProps {
  isOpen: boolean;
  onHide: () => void;
  onDelete: (keepLocal?: boolean) => void;
  title: string;
  text: ReactNode;
  keepButton?: boolean;
}

/**
 * DeleteModal - Generic delete confirmation modal for truly shared usage
 * This component is used by components that are genuinely shared between flows
 * (like PendingChangeWarning). For flow-specific delete modals, use
 * CentralDeleteModal or LocalDeleteModal instead.
 */
export const DeleteModal: React.FC<DeleteModalProps> = ({
  isOpen,
  onHide,
  onDelete,
  title,
  text,
  keepButton = false,
}) => {
  const t = usePrefixedTranslation("editor.components.DeleteModal");
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [confirmed, setConfirmed] = useState<boolean>(false);

  const handleDeleteClick = useCallback(
    (keepLocal?: boolean) => {
      setSubmitting(true);
      onDelete(keepLocal);
    },
    [onDelete]
  );

  const handleConfirmChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmed(e.target.checked);
  };

  // While we are submitting, the onHide is a noop so we can't
  // close the modal until the server has responded
  const handleHide = submitting ? () => {} : onHide;

  return (
    <Modal isOpen={isOpen} onClose={handleHide}>
      <Modal.Header onClose={handleHide}>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Content>{text}</Content>
        <Checkbox checked={confirmed} onChange={handleConfirmChange}>
          Bekreft sletting
        </Checkbox>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={handleHide} disabled={submitting}>
          {t("cancelButton")}
        </Button>
        {keepButton && (
          <Button
            onClick={() => handleDeleteClick(true)}
            outlined
            disabled={submitting}
            loading={submitting}
          >
            Behold lokalt innhold
          </Button>
        )}
        <Button
          onClick={() => handleDeleteClick(false)}
          disabled={!confirmed || submitting}
          color="danger"
          outlined
          loading={submitting}
        >
          {t("deleteButton")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
