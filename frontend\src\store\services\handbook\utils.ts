import type {
  CentralHandbook,
  CentralChapter,
  CentralSection,
  Handbook,
  Chapter,
  Section,
} from "@/types";

// Extended types for tree structure with children - Central
export interface CentralHandbookWithChildren extends CentralHandbook {
  chapters: CentralChapterWithChildren[];
}

export interface CentralChapterWithChildren extends CentralChapter {
  chapters: CentralChapterWithChildren[];
  sections: CentralSection[];
}

// Extended types for tree structure with children - Local
export interface LocalHandbookWithChildren extends Handbook {
  chapters: LocalChapterWithChildren[];
}

export interface LocalChapterWithChildren extends Chapter {
  chapters: LocalChapterWithChildren[];
  sections: Section[];
}

// Type guard to check if an item is a chapter
export function isChapter(item: any): item is Chapter | CentralChapter {
  // Prioritize explicit type field
  if (item.type === "CHAPTER") {
    return true;
  }
  if (item.type === "SECTION") {
    return false;
  }
  
  // Fallback logic for items without explicit type (legacy data)
  return (
    item.hasOwnProperty("localChange") &&
    !item.hasOwnProperty("localTitleChange")
  );
}

// Type guard to check if an item is a section
export function isSection(item: any): item is Section | CentralSection {
  // Prioritize explicit type field
  if (item.type === "SECTION") {
    return true;
  }
  if (item.type === "CHAPTER") {
    return false;
  }
  
  // Fallback logic for items without explicit type (legacy data)
  return (
    item.hasOwnProperty("localTitleChange") ||
    item.hasOwnProperty("pendingTitleChange") ||
    item.hasOwnProperty("html")
  );
}

// Sort chapters and sections together by sortOrder for tree display
export function sortChaptersAndSections<
  TChapter extends { id?: string; sortOrder?: number },
  TSection extends { id?: string; sortOrder?: number },
>(chapters: TChapter[], sections: TSection[]): (TChapter | TSection)[] {
  const combined = [
    ...chapters.filter((chapter) => chapter.id),
    ...sections.filter((section) => section.id),
  ];

  return combined.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
}

// Transform flat API responses into hierarchical tree structure
export function transformToTreeStructure(
  handbooks: CentralHandbook[],
  chapters: CentralChapter[],
  sections: CentralSection[]
): CentralHandbookWithChildren[] {
  // Filter out entities without IDs (shouldn't happen in practice, but for type safety)
  const validHandbooks = handbooks.filter((h) => h.id);
  const validChapters = chapters.filter((c) => c.id);
  const validSections = sections.filter((s) => s.id);

  // Create a map for quick lookup of chapters by handbook ID
  const chaptersByHandbook = new Map<string, CentralChapter[]>();
  validChapters.forEach((chapter) => {
    const handbookChapters =
      chaptersByHandbook.get(chapter.centralHandbookId) || [];
    handbookChapters.push(chapter);
    chaptersByHandbook.set(chapter.centralHandbookId, handbookChapters);
  });

  // Create a map for quick lookup of sections by parent ID (chapter ID)
  const sectionsByParent = new Map<string, CentralSection[]>();
  validSections.forEach((section) => {
    const parentSections = sectionsByParent.get(section.parentId) || [];
    parentSections.push(section);
    sectionsByParent.set(section.parentId, parentSections);
  });

  // Create a map for quick lookup of child chapters by parent ID
  const chaptersByParent = new Map<string, CentralChapter[]>();
  validChapters.forEach((chapter) => {
    if (chapter.parentId) {
      const parentChapters = chaptersByParent.get(chapter.parentId) || [];
      parentChapters.push(chapter);
      chaptersByParent.set(chapter.parentId, parentChapters);
    }
  });

  //
  // Recursively build chapter tree with nested chapters and sections
  function buildChapterTree(
    chapter: CentralChapter
  ): CentralChapterWithChildren {
    const childChapters = chaptersByParent.get(chapter.id!) || [];
    const chapterSections = sectionsByParent.get(chapter.id!) || [];

    return {
      ...chapter,
      chapters: childChapters
        .map(buildChapterTree)
        .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)),
      sections: chapterSections.sort(
        (a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)
      ),
    };
  }

  // Transform handbooks with their chapter trees
  return validHandbooks.map((handbook) => {
    const handbookChapters = chaptersByHandbook.get(handbook.id!) || [];

    // Only include root-level chapters (chapters without parentId)
    const rootChapters = handbookChapters.filter(
      (chapter) => !chapter.parentId
    );

    return {
      ...handbook,
      chapters: rootChapters
        .map(buildChapterTree)
        .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)),
    };
  });
}

// Generic transformation functions that work with both Central and Local data

// Transform Local handbooks into tree structure
export function transformLocalToTreeStructure(
  handbooks: Handbook[],
  chapters: Chapter[],
  sections: Section[]
): LocalHandbookWithChildren[] {
  // Filter out entities without IDs
  const validHandbooks = handbooks.filter((h) => h.id);
  const validChapters = chapters.filter((c) => c.id);
  const validSections = sections.filter((s) => s.id);

  // Create a map for quick lookup of chapters by handbook ID
  const chaptersByHandbook = new Map<string, Chapter[]>();
  validChapters.forEach((chapter) => {
    const handbookChapters = chaptersByHandbook.get(chapter.handbookId) || [];
    handbookChapters.push(chapter);
    chaptersByHandbook.set(chapter.handbookId, handbookChapters);
  });

  // Create a map for quick lookup of sections by parent ID (chapter ID)
  const sectionsByParent = new Map<string, Section[]>();
  validSections.forEach((section) => {
    const parentSections = sectionsByParent.get(section.parentId) || [];
    parentSections.push(section);
    sectionsByParent.set(section.parentId, parentSections);
  });

  // Create a map for quick lookup of child chapters by parent ID
  const chaptersByParent = new Map<string, Chapter[]>();
  validChapters.forEach((chapter) => {
    if (chapter.parentId) {
      const parentChapters = chaptersByParent.get(chapter.parentId) || [];
      parentChapters.push(chapter);
      chaptersByParent.set(chapter.parentId, parentChapters);
    }
  });

  //
  // Recursively build chapter tree with nested chapters and sections
  function buildChapterTree(chapter: Chapter): LocalChapterWithChildren {
    const childChapters = chaptersByParent.get(chapter.id!) || [];
    const chapterSections = sectionsByParent.get(chapter.id!) || [];

    return {
      ...chapter,
      chapters: childChapters
        .map(buildChapterTree)
        .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)),
      sections: chapterSections.sort(
        (a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)
      ),
    };
  }

  // Transform handbooks with their chapter trees
  return validHandbooks.map((handbook) => {
    const handbookChapters = chaptersByHandbook.get(handbook.id!) || [];

    // Only include root-level chapters (chapters without parentId)
    const rootChapters = handbookChapters.filter(
      (chapter) => !chapter.parentId
    );

    return {
      ...handbook,
      chapters: rootChapters
        .map(buildChapterTree)
        .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)),
    };
  });
}
