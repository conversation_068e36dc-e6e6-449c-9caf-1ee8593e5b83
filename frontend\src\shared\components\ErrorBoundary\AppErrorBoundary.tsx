import React, { type ReactNode } from "react";
import { BaseErrorBoundary } from "./BaseErrorBoundary";
import { AppErrorFallback } from "./ErrorFallback";
import type {
  ErrorBoundaryProps,
  ErrorFallbackProps,
} from "@/shared/types/errorBoundary";

export class AppErrorBoundary extends BaseErrorBoundary {
  protected getErrorCategory():
    | "ui"
    | "api"
    | "auth"
    | "data"
    | "network"
    | "unknown" {
    return "ui";
  }

  protected getErrorLevel(): "low" | "medium" | "high" | "critical" {
    return "critical";
  }

  protected renderFallback(props: ErrorFallbackProps): ReactNode {
    return <AppErrorFallback {...props} />;
  }
}

interface AppErrorBoundaryWrapperProps
  extends Omit<ErrorBoundaryProps, "level"> {}

export const AppErrorBoundaryWrapper: React.FC<AppErrorBoundaryWrapperProps> = (
  props
) => (
  <AppErrorBoundary
    {...props}
    level="app"
    name="AppErrorBoundary"
    showToast={false}
  />
);
