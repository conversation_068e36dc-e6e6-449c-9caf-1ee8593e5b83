package no.kf.handboker.repository

import no.kf.db.{IDGenerator, UpsertSupport}
import no.kf.db.RichSQL._
import no.kf.handboker.model.local.{Link, LinkCollection}
import no.kf.util.Logging

trait HandbookLinkRepositoryComponent {
  this: DbConnectionManagerComponent
    with HandbookRepositoryComponent =>

  val handbookLinkRepository: HandbookLinkRepository

  object HandbookLinkCollectionTableDef {
    val handbookLinkCollectionTableName = "handbook_link_collection"

    val fieldId = "id"
    val fieldTitle = "title"
    val fieldHandbookId = "handbook_id"
    val fieldSortOrder = "sort_order"

    val fields = List(fieldId, fieldTitle, fieldHandbookId, fieldSortOrder)
  }

  object HandbookLinkTableDef {
    val handbookLinkTableName = "handbook_link"

    val fieldId = "id"
    val fieldTitle = "title"
    val fieldUrl = "url"
    val fieldSortOrder = "sort_order"
    val fieldHandbookLinkCollectionId = "handbook_link_collection_id"

    val fields = List(fieldId, fieldTitle, fieldUrl, fieldSortOrder, fieldHandbookLinkCollectionId)
    val selectFields = fields.filterNot(_ == fieldHandbookLinkCollectionId)
  }

  class HandbookLinkRepositoryImpl extends HandbookLinkRepository with UpsertSupport with Logging {

    override def retrieveLinkCollectionsForHandbook(handbookId: String): List[LinkCollection] = {
      import HandbookLinkCollectionTableDef._
      val sql = s"SELECT ${fields.mkString(", ")} FROM $handbookLinkCollectionTableName WHERE $fieldHandbookId = ?"
      val linkCollections = connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<! populateLinkCollectionWithoutLinks
      }
      linkCollections.map(linkCollection => linkCollection.copy(links = retrieveLinksByLinkCollectionId(linkCollection.id.get)))
    }

    private def retrieveLinksByLinkCollectionId(handbookLinkCollectionId: String): List[Link] = {
      import HandbookLinkTableDef._
      val sql = s"SELECT ${selectFields.mkString(", ")} FROM $handbookLinkTableName WHERE $fieldHandbookLinkCollectionId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookLinkCollectionId <<! populateLink
      }
    }

    override def persistLinkCollection(linkCollection: LinkCollection): LinkCollection = {
      val persistedLinkCollection = if (linkCollection.id.isDefined) {
        updateLinkCollection(linkCollection)
      } else {
        insertLinkCollection(linkCollection)
      }

      persistedLinkCollection.copy(links = linkCollection.links.map(persistLink(_, persistedLinkCollection.id.get)))
    }

    private def insertLinkCollection(linkCollection: LinkCollection): LinkCollection = {
      import HandbookLinkCollectionTableDef._
      val sql = s"INSERT INTO $handbookLinkCollectionTableName (${fields.mkString(",")}) VALUES (${#?(fields)})"
      val id = IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) << id << linkCollection.title << linkCollection.handbookId << linkCollection.sortOrder <<!
      }
      linkCollection.copy(id = Some(id))
    }

    private def updateLinkCollection(linkCollection: LinkCollection): LinkCollection = {
      import HandbookLinkCollectionTableDef._
      val sql = s"UPDATE $handbookLinkCollectionTableName SET $fieldTitle = ?, $fieldSortOrder = ? WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << linkCollection.title << linkCollection.sortOrder << linkCollection.id <<!
      }
      linkCollection
    }

    override def deleteLinkCollection(linkCollectionId: String): Unit = {
      import HandbookLinkCollectionTableDef.{fieldId, handbookLinkCollectionTableName}
      import HandbookLinkTableDef.{handbookLinkTableName, fieldHandbookLinkCollectionId}

      val linkSql = s"DELETE FROM $handbookLinkTableName WHERE $fieldHandbookLinkCollectionId = ?"
      val linkCollectionSql = s"DELETE FROM $handbookLinkCollectionTableName WHERE $fieldId = ?"

      connectionManager.doWithConnection {
        _.ps(linkSql) << linkCollectionId <<!
      }
      connectionManager.doWithConnection {
        _.ps(linkCollectionSql) << linkCollectionId <<!
      }
    }

    override def persistLink(link: Link, linkCollectionId: String): Link = {
      if (link.id.isDefined) {
        updateLink(link)
      } else {
        insertLink(link, linkCollectionId)
      }
    }

    private def insertLink(link: Link, linkCollectionId: String): Link = {
      import HandbookLinkTableDef._
      val sql = s"INSERT INTO $handbookLinkTableName (${fields.mkString(",")}) VALUES (${#?(fields)})"
      val id = IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) << id << link.title << link.url << link.sortOrder << linkCollectionId <<!
      }
      link.copy(id = Some(id))
    }

    private def updateLink(link: Link): Link = {
      import HandbookLinkTableDef._
      val sql = s"UPDATE $handbookLinkTableName SET $fieldTitle = ?, $fieldUrl = ?, $fieldSortOrder = ? WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << link.title << link.url << link.sortOrder << link.id <<!
      }
      link
    }

    override def deleteLink(linkId: String): Unit = {
      import HandbookLinkTableDef._
      val sql = s"DELETE FROM $handbookLinkTableName WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << linkId <<!
      }
    }

    private def populateLinkCollectionWithoutLinks(rs: RichResultSet): LinkCollection = LinkCollection(rs, rs, rs, rs, Nil)
    private def populateLink(rs: RichResultSet): Link = Link(rs, rs, rs, rs)
  }
}

trait HandbookLinkRepository {
  def retrieveLinkCollectionsForHandbook(handbookId: String): List[LinkCollection]
  def persistLinkCollection(linkCollection: LinkCollection): LinkCollection
  def deleteLinkCollection(linkCollectionId: String): Unit

  def persistLink(link: Link, linkCollectionId: String): Link
  def deleteLink(linkId: String): Unit
}
