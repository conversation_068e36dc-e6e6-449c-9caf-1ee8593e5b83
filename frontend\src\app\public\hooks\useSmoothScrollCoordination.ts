import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Scroll state inspired by legacy's brilliant 3-state system:
 * - idle: Normal operation, waypoints active (like legacy's userHasScrolled: true)
 * - programmatic: Smooth scroll in progress, waypoints disabled (like legacy's userHasScrolled: false)
 * - settling: Brief pause after scroll completes before re-enabling waypoints
 */
type ScrollState = 'idle' | 'programmatic' | 'settling';

interface SmoothScrollOptions {
  offset?: number;
  duration?: number;
  onComplete?: () => void;
}

/**
 * Advanced smooth scroll coordination system inspired by legacy's scroll tracking
 * 
 * This system perfectly coordinates between:
 * 1. Programmatic smooth scrolling (navigation clicks)
 * 2. User manual scrolling  
 * 3. Waypoint detection enabling/disabling
 * 
 * Key innovations over legacy:
 * - Smooth scrolling with precise completion detection
 * - Temporal separation: waypoints never conflict with navigation
 * - Performance optimized: minimal re-renders, passive listeners
 */
export const useSmoothScrollCoordination = () => {
  const [scrollState, setScrollState] = useState<ScrollState>('idle');
  const activeScrollRef = useRef<{
    targetY: number;
    startTime: number;
    onComplete?: () => void;
  } | null>(null);
  
  const monitoringTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const settlingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Precise smooth scroll completion detection
   * Uses multiple techniques:
   * 1. Target position proximity check
   * 2. Scroll velocity stability detection  
   * 3. Maximum duration timeout (failsafe)
   */
  const monitorScrollCompletion = useCallback(() => {
    const scrollInfo = activeScrollRef.current;
    if (!scrollInfo) return;

    const currentY = window.scrollY;
    const distanceToTarget = Math.abs(currentY - scrollInfo.targetY);
    const timeElapsed = Date.now() - scrollInfo.startTime;
    
    // Completion criteria (more sophisticated than legacy)
    const isAtTarget = distanceToTarget <= 5; // 5px tolerance
    const hasTimedOut = timeElapsed > 3000; // 3s max duration (failsafe)
    
    if (isAtTarget || hasTimedOut) {
      // Scroll completed - enter settling phase
      setScrollState('settling');
      
      // Clear active scroll tracking
      const onComplete = scrollInfo.onComplete;
      activeScrollRef.current = null;
      
      // Brief settling period before re-enabling waypoints
      // This prevents immediate waypoint firing from final scroll position
      settlingTimeoutRef.current = setTimeout(() => {
        setScrollState('idle');
        onComplete?.();
      }, 150); // 150ms settling time
      
      return;
    }
    
    // Continue monitoring
    monitoringTimeoutRef.current = setTimeout(monitorScrollCompletion, 50);
  }, []);

  /**
   * Smooth scroll to element with precise coordination
   * Directly inspired by legacy's scrollIntoView pattern but with smooth animation
   */
  const smoothScrollToElement = useCallback((elementId: string, options: SmoothScrollOptions = {}) => {
    const { offset = 100, onComplete } = options;
    
    const element = document.getElementById(`content-${elementId}`);
    if (!element) {
      onComplete?.();
      return;
    }

    // Clear any existing scroll monitoring
    if (monitoringTimeoutRef.current) {
      clearTimeout(monitoringTimeoutRef.current);
      monitoringTimeoutRef.current = null;
    }
    if (settlingTimeoutRef.current) {
      clearTimeout(settlingTimeoutRef.current);
      settlingTimeoutRef.current = null;
    }

    // Calculate precise scroll target
    const elementRect = element.getBoundingClientRect();
    const targetY = Math.max(0, window.scrollY + elementRect.top - offset);
    
    // Check if scroll is actually needed
    const currentY = window.scrollY;
    if (Math.abs(currentY - targetY) <= 5) {
      // Already at target - no scroll needed
      onComplete?.();
      return;
    }

    // Enter programmatic scroll state (disables waypoints)
    setScrollState('programmatic');
    
    // Track scroll operation
    activeScrollRef.current = {
      targetY,
      startTime: Date.now(),
      onComplete
    };

    // Initiate smooth scroll
    window.scrollTo({
      top: targetY,
      behavior: 'smooth'
    });

    // Start monitoring completion
    setTimeout(monitorScrollCompletion, 100); // Small delay to let scroll start
  }, [monitorScrollCompletion]);

  /**
   * Legacy-inspired instant scroll (for compatibility)
   */
  const instantScrollToElement = useCallback((elementId: string, options: SmoothScrollOptions = {}) => {
    const { offset = 100, onComplete } = options;
    
    const element = document.getElementById(`content-${elementId}`);
    if (!element) {
      onComplete?.();
      return;
    }

    const elementRect = element.getBoundingClientRect();
    const targetY = Math.max(0, window.scrollY + elementRect.top - offset);
    
    // Instant scroll (like legacy)
    window.scrollTo({
      top: targetY,
      behavior: 'auto'
    });
    
    // Brief programmatic state to prevent waypoint conflicts
    setScrollState('programmatic');
    setTimeout(() => {
      setScrollState('idle');
      onComplete?.();
    }, 50); // Very brief for instant scroll
  }, []);

  /**
   * Check if user is manually scrolling (not programmatically)
   * Inspired by legacy's userHasScrolled detection
   */
  const isUserScrolling = scrollState === 'idle';

  /**
   * Check if waypoints should be enabled
   * Only active during idle state (like legacy's conditional waypoint updates)
   */
  const waypointsEnabled = scrollState === 'idle';

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (monitoringTimeoutRef.current) {
        clearTimeout(monitoringTimeoutRef.current);
      }
      if (settlingTimeoutRef.current) {
        clearTimeout(settlingTimeoutRef.current);
      }
    };
  }, []);

  return {
    // State information
    scrollState,
    isUserScrolling,
    waypointsEnabled,
    
    // Navigation functions
    smoothScrollToElement,
    instantScrollToElement,
    
    // For debugging/monitoring
    isScrolling: scrollState !== 'idle'
  };
};