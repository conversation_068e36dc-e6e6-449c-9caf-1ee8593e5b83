package no.kf.handboker.util

import java.io.InputStream

import org.owasp.encoder.Encode

import scala.io.Source
import scala.xml.{Elem, Node, Unparsed, XML}


/**
 * Inject data on the window object in a <script></script> tag
 *
 * The newly added script tag will be added to the body element, before any other script tag found in the body.
 * This is because bundled JS is often found at the bottom of the body, and we want our script code to get parsed by the browser first.
 *
 * Because this is Scala, the HTML we process here MUST be well formed XHTML
 *
 * See http://redux.js.org/docs/recipes/ServerRendering.html#inject-initial-component-html-and-state
 */
class SSRStateInjector(val xhtmlRoot: Node) {

  private var toAdd: Seq[(String, String)] = Seq()
  private var toAddTracking: Seq[String] = Seq()

  def this(xhtml: String) {
    this(XML.loadString(SSRStateInjector.ensureXhtml(xhtml)))
  }

  def this(xhtml: InputStream) {
    this(Source.fromInputStream(xhtml).getLines().mkString)
  }

  /**
   * Add a variable string. If the string contains any single or double quotes, make sure to escape them
   * Param string is encoded and wrapped in quotes to protect against XSS.
   * @param variable
   * @param string
   * @return this
   */
  def addString(variable: String, string: String): SSRStateInjector = {
    // Add single quotes around the string so JavaScript parses it correctly
    toAdd = toAdd :+ (variable, s"'${Encode.forJavaScript(string)}'")
    this
  }

  /**
   * Add a variable object. For instance, use org.json4s.jackson.Serialization.write to serialize a case class
   * Param json is encoded and wrapped in quotes to protect against XSS.
   * @param variable
   * @param json
   * @return this
   */
  def addObject(variable: String, json: String): SSRStateInjector = {
    // Add quotes around the JSON we can JSON.parse it, or else it wont be recognized as JSON
    toAdd = toAdd :+ (variable, s""""${Encode.forJavaScript(json)}"""")
    this
  }

  /**
   * Add a variable object. For instance, use org.json4s.jackson.Serialization.write to serialize a case class
   * Param json is encoded and wrapped in quotes to protect against XSS.
   * @param variable
   * @param json
   * @return this
   */
  def addScript(variable: String, json: String): SSRStateInjector = {
    // Add quotes around the JSON we can JSON.parse it, or else it wont be recognized as JSON
    toAdd = toAdd :+ (variable, s""""${Encode.forJavaScript(json)}"""")
    this
  }

  /**
   * Adds a tracking code object. Currently using Matomo.
   * @param snippet
   * @return this
   */
  def addTrackingCode(snippet: String): SSRStateInjector = {
    toAddTracking = toAddTracking :+ snippet
    this
  }

  /**
   * Parse the XHTML and add the variables inside a <script /> tag. The variables will be defined on the global window object
   * @return a new Node with a <script /> added to the HTML
   */
  def toNode(): Node = {
    addNode(xhtmlRoot, createScriptTag())
  }

  /**
   * Parse the XHTML and add the variables inside a <script /> tag. The variables will be defined on the global window object
   * @return a String with a <script /> added to the HTML
   */
  override def toString(): String = toNode.toString()

  // We use Unparsed here, otherwise, Scala XML will escape stuff, and we don't want any JSON we add to be escaped!
  private def createScriptTag(): Node = {
    <script>
      {toAdd.map(t => {
        Unparsed(s"window.${t._1} = ${t._2}\n")
      })}
      {toAddTracking.map(t => {
        Unparsed(s"$t\n")
      })}
    </script>
  }

  private def addNode(root: Node, elemToAdd: Node): Node = root match {
    case elem: Elem if elem.label == "body" => {
      // We want to insert our node before the script bundle, so it gets parsed by the browser before the bundle
      val bundleIndex = elem.child.indexWhere(_.label == "script")
      val child = if (bundleIndex >= 0) {
        val (before, after) = elem.child.splitAt(bundleIndex)
        before.map(addNode(_, elemToAdd)) ++ elemToAdd ++ after.map(addNode(_, elemToAdd))
      } else {
        elem.child.map(addNode(_, elemToAdd)) :+ elemToAdd
      }
      elem.copy(child = child)
    }
    // Script tags shouldn't be self closing as that causes problems for the browsers, ie. we want <script></script> even if the content is empty
    case elem: Elem if elem.label == "script" => elem.copy(minimizeEmpty = false)
    case elem: Elem => elem.copy(child = elem.child.map(addNode(_, elemToAdd)))
    case other => other
  }
}

object SSRStateInjector {
  def ensureXhtml(xhtml: String): String = {
    xhtml.replaceAll("(?i:doctype)", "DOCTYPE")
  }
}
