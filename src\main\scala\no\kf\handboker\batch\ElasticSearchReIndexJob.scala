package no.kf.handboker.batch

import no.kf.handboker.ProductionRegistry
import org.quartz.{DisallowConcurrentExecution, Job, JobExecutionContext, JobExecutionException}
import no.kf.util.Logging

@DisallowConcurrentExecution
class ElasticSearchReIndexJob extends Job with Logging {

  lazy val searchService = ProductionRegistry.componentRegistry.searchService
  lazy val localHandbookService = ProductionRegistry.componentRegistry.localHandbookService

  override def execute(context: JobExecutionContext): Unit = {
    val jobName = "Elastic Search Re Index job"

    try {
      log.info("Starting " + jobName)

      val externalOrgIds = localHandbookService.retrieveAllExternalOrgIds()
      externalOrgIds.foreach(id => log.info(id))
      externalOrgIds.foreach(id => searchService.doReindex(id))

      log.info("Finished running " + jobName)
    } catch {
      case e: Exception =>
        log.error("Error occurred during " + jobName, e)
        throw new JobExecutionException("Error occurred during " + jobName, e, false)
    }
  }

}
