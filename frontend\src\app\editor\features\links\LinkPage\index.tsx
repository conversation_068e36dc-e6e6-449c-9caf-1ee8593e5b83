import { useParams } from "react-router-dom";
import { Title } from "kf-bui";
import { DndContext, closestCenter } from "@dnd-kit/core";
import { usePrefixedTranslation } from "@/libs/i18n";

import {
  useGetLinkCollectionsQuery,
  useSaveLinkCollectionMutation,
  useDeleteLinkCollectionMutation,
  useSaveLinkMutation,
  useDeleteLinkMutation,
} from "@/store/services/handbook/localHandbookApi";
import type { LinkCollection, Link } from "@/types";
import { LinkCollectionCard } from "../LinkCollectionCard";
import { LinkCollectionForm } from "../LinkCollectionForm";
import { Spinner } from "@/shared/components/Spinner";

export const LinkPage = () => {
  const { handbookId } = useParams() as { handbookId: string };
  const t = usePrefixedTranslation("editor.containers.LinkPage");

  const {
    data: linkCollections = [],
    error: linkCollectionsError,
    isLoading: isLoadingLinkCollections,
    refetch: refetchLinkCollections,
  } = useGetLinkCollectionsQuery(handbookId);

  const [saveLinkCollection] = useSaveLinkCollectionMutation();
  const [deleteLinkCollection] = useDeleteLinkCollectionMutation();
  const [saveLink] = useSaveLinkMutation();
  const [deleteLink] = useDeleteLinkMutation();

  const handleSaveLinkCollection = async (linkCollection: LinkCollection) => {
    try {
      await saveLinkCollection(linkCollection).unwrap();
      refetchLinkCollections();
    } catch (error) {
      console.error("Error saving link collection:", error);
    }
  };

  const handleDeleteLinkCollection = async (linkCollectionId: string) => {
    try {
      await deleteLinkCollection(linkCollectionId).unwrap();
      refetchLinkCollections();
    } catch (error) {
      console.error("Error deleting link collection:", error);
    }
  };

  const handleSaveLink = async (link: Link, linkCollectionId: string) => {
    try {
      await saveLink({ link, linkCollectionId }).unwrap();
      refetchLinkCollections();
    } catch (error) {
      console.error("Error saving link:", error);
    }
  };

  const handleDeleteLink = async (linkId: string) => {
    try {
      await deleteLink(linkId).unwrap();
      refetchLinkCollections();
    } catch (error) {
      console.error("Error deleting link:", error);
    }
  };

  if (isLoadingLinkCollections) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (linkCollectionsError) {
    console.error("Error loading link collections:", linkCollectionsError);
    return <div>Error loading link collections</div>;
  }

  return (
    <DndContext collisionDetection={closestCenter}>
      <div>
        <Title>Lenkesamling</Title>
        {linkCollections.length < 1 && (
          <LinkCollectionForm
            handbookId={handbookId}
            sortOrder={linkCollections.length}
            onSave={handleSaveLinkCollection}
          />
        )}

        {linkCollections.map((linkCollection) => (
          <LinkCollectionCard
            key={linkCollection.id}
            linkCollection={linkCollection}
            onSaveLink={handleSaveLink}
            onDeleteLink={handleDeleteLink}
            onDeleteLinkCollection={handleDeleteLinkCollection}
            onSaveLinkCollection={handleSaveLinkCollection}
            refreshLinkCollections={refetchLinkCollections}
          />
        ))}

        {linkCollections.length === 0 && <span>{t("noLinkCollections")}</span>}
      </div>
    </DndContext>
  );
};
