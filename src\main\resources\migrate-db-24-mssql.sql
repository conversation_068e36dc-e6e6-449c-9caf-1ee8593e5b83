-- Migration script to add separate title and HTML update tracking fields to central_handbooks.section table
-- KFAVT-139: HB - Not correct change dates displayed in the change handling view
ALTER TABLE central_handbooks.section ADD title_updated_date BIGINT;
ALTER TABLE central_handbooks.section ADD html_updated_date BIGINT;
ALTER TABLE central_handbooks.section ADD title_updated_by VARCHAR(200);
ALTER TABLE central_handbooks.section ADD html_updated_by VARCHAR(200);

-- Update all columns in a single statement
UPDATE central_handbooks.section SET title_updated_date = updated_date, html_updated_date = updated_date, title_updated_by = updated_by, html_updated_by = updated_by;

