<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
    <Properties>
        <!-- The actual value of logFileDir will eventually be set a by a Java System property in ComponentRegistry, while it is undefined, this is used -->
        <Property name="logFileDir">.</Property>
    </Properties>
    <Appenders>
        <RollingFile name="FILE" fileName="${sys:logFileDir}/handboker-application.log" filePattern="${sys:logFileDir}/handboker-application-%d{yyyy-MM-dd}.log" append="true">
            <PatternLayout pattern="%-5p %d{ISO8601} ${applicationVersion} [%tid] \t%c: %m%n"/>
            <Policies>
                <!-- The TimeBasedTriggeringPolicy causes a rollover once the date/time pattern no longer applies to the active file -->
                <TimeBasedTriggeringPolicy />
            </Policies>
        </RollingFile>
        <Console name="STDOUT" target="SYSTEM_OUT">
            <PatternLayout pattern="%-5p %d{ISO8601} ${applicationVersion} [%tid] \t%c: %m%n"/>
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="no.kf" level="info" additivity="false">
            <AppenderRef ref="FILE"/>
            <AppenderRef ref="STDOUT" />
        </Logger>
        <Logger name="no.kf.db" level="warn" additivity="false">
            <AppenderRef ref="FILE"/>
            <AppenderRef ref="STDOUT" />
        </Logger>
        <Logger name="org.mortbay.log" level="error" additivity="false">
            <AppenderRef ref="STDOUT"/>
        </Logger>
        <Root level="warn">
            <AppenderRef ref="FILE" />
            <AppenderRef ref="STDOUT" />
        </Root>
    </Loggers>
</Configuration>
