# Deep Dive Database Analysis - Complete Schema & Data Processing Flow

## Overview
This document provides a comprehensive analysis of the Håndbøker application's database schema and how data flows through the system during various operations. We'll examine both the local and central handbook storage, their relationships, and the complete data processing lifecycle.

## 1. Complete Database Schema Analysis

### 1.1 Schema Structure Overview

```
DATABASE ARCHITECTURE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                    DEFAULT SCHEMA (Local Data)                     │   │
│  │                                                                     │   │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐             │   │
│  │  │  handbook   │───►│handbookchapter│───►│handbooksection│          │   │
│  │  │             │    │             │    │             │             │   │
│  │  │ Local org   │    │ Local org   │    │ Local org   │             │   │
│  │  │ handbooks   │    │ chapters    │    │ sections    │             │   │
│  │  └─────────────┘    └─────────────┘    └─────────────┘             │   │
│  │                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                 CENTRAL_HANDBOOKS SCHEMA                           │   │
│  │                                                                     │   │
│  │  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐             │   │
│  │  │  handbook   │───►│   chapter   │───►│   section   │             │   │
│  │  │             │    │             │    │             │             │   │
│  │  │ Central     │    │ Central     │    │ Central     │             │   │
│  │  │ templates   │    │ templates   │    │ templates   │             │   │
│  │  └─────────────┘    └─────────────┘    └─────────────┘             │   │
│  │                                                                     │   │
│  │  ┌─────────────────────────────────────────────────────────────┐   │   │
│  │  │              handbook_structure                             │   │   │
│  │  │          (JSON tree structure)                              │   │   │
│  │  └─────────────────────────────────────────────────────────────┘   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                    METADATA TABLES                                 │   │
│  │                                                                     │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │   │
│  │  │handboker_meta_  │  │ User/Session    │  │ Search/Index    │     │   │
│  │  │     data        │  │    Tables       │  │    Tables       │     │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘     │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.2 Local Handbook Schema (Default Schema)

#### 1.2.1 Core Tables Structure

```sql
-- System metadata table
CREATE TABLE handboker_meta_data (
    id SMALLINT,                    -- Always 1
    version INTEGER NOT NULL,       -- Current DB version (24)
    PRIMARY KEY (id)
)

-- Main handbook table (organization-specific handbooks)
CREATE TABLE handbook (
    id VARCHAR(37),                 -- UUID primary key
    title VARCHAR(255) NOT NULL,    -- Handbook title
    importedhandbook_id VARCHAR(100), -- Reference to central handbook
    external_org_id VARCHAR(200) NOT NULL, -- Organization identifier
    manualmerge SMALLINT NOT NULL,  -- Manual merge required flag
    pendingchanges SMALLINT NOT NULL, -- Has pending changes flag
    pending_changes_updated_date BIGINT NOT NULL, -- When pending changes occurred
    updated_date BIGINT NOT NULL,   -- Last modification timestamp
    created_date BIGINT NOT NULL,   -- Creation timestamp
    updated_by VARCHAR(100),        -- User who last updated
    created_by VARCHAR(100),        -- User who created
    deleted SMALLINT NOT NULL,      -- Soft delete flag
    PRIMARY KEY(id)
)

-- Chapter table (hierarchical structure within handbooks)
CREATE TABLE handbookchapter (
    id VARCHAR(37),                 -- UUID primary key
    title VARCHAR(255) NOT NULL,    -- Chapter title
    importedhandbookchapter_id VARCHAR(100), -- Reference to central chapter
    importedhandbook_id VARCHAR(100), -- Reference to central handbook
    handbook_id VARCHAR(37) NOT NULL, -- Parent handbook
    parent_chapter_id VARCHAR(37),  -- Parent chapter (for sub-chapters)
    orderindex SMALLINT,            -- Sort order within parent
    manualmerge SMALLINT NOT NULL,  -- Manual merge required flag
    pendingchanges SMALLINT NOT NULL, -- Has pending changes flag
    pending_changes_updated_date BIGINT NOT NULL,
    updated_date BIGINT NOT NULL,
    created_date BIGINT NOT NULL,
    updated_by VARCHAR(100),
    created_by VARCHAR(100),
    deleted SMALLINT NOT NULL,
    PRIMARY KEY(id),
    FOREIGN KEY (handbook_id) REFERENCES handbook(id),
    FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)
)

-- Section table (actual content within chapters)
CREATE TABLE handbooksection (
    id VARCHAR(37),                 -- UUID primary key
    title VARCHAR(255) NOT NULL,    -- Section title
    html CLOB,                      -- Section content (HTML)
    importedhandbooksection_id VARCHAR(100), -- Reference to central section
    importedhandbook_id VARCHAR(100), -- Reference to central handbook
    handbook_id VARCHAR(37) NOT NULL, -- Parent handbook
    parent_chapter_id VARCHAR(37) NOT NULL, -- Parent chapter
    orderindex SMALLINT,            -- Sort order within chapter
    manualmerge SMALLINT NOT NULL,  -- Manual merge required flag
    pendingchanges SMALLINT NOT NULL, -- Has pending changes flag
    pending_changes_updated_date BIGINT NOT NULL,
    updated_date BIGINT NOT NULL,
    created_date BIGINT NOT NULL,
    updated_by VARCHAR(100),
    created_by VARCHAR(100),
    deleted SMALLINT NOT NULL,
    PRIMARY KEY(id),
    FOREIGN KEY (handbook_id) REFERENCES handbook(id),
    FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)
)
```

#### 1.2.2 Local Schema Relationships

```
LOCAL SCHEMA RELATIONSHIPS
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  handbook (Organization's handbooks)                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ id: "hb-123"                                                        │   │
│  │ title: "Safety Manual - Org 9900"                                  │   │
│  │ external_org_id: "9900"                                            │   │
│  │ importedhandbook_id: "ch-456" ← Links to central handbook          │   │
│  │ pendingchanges: 1 ← Has unresolved central changes                 │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│                                    │ 1:N                                    │
│                                    ▼                                        │
│  handbookchapter (Chapters within handbook)                                │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ id: "ch-789"                                                        │   │
│  │ title: "Emergency Procedures"                                       │   │
│  │ handbook_id: "hb-123" ← Foreign key to handbook                    │   │
│  │ parent_chapter_id: NULL ← Top-level chapter                        │   │
│  │ importedhandbookchapter_id: "cc-101" ← Links to central chapter    │   │
│  │ orderindex: 1 ← First chapter                                      │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│                                    │ 1:N                                    │
│                                    ▼                                        │
│  handbooksection (Content sections)                                        │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ id: "sec-456"                                                       │   │
│  │ title: "Fire Safety Protocol"                                       │   │
│  │ html: "<p>In case of fire...</p>"                                   │   │
│  │ handbook_id: "hb-123" ← Foreign key to handbook                    │   │
│  │ parent_chapter_id: "ch-789" ← Foreign key to chapter               │   │
│  │ importedhandbooksection_id: "cs-202" ← Links to central section    │   │
│  │ orderindex: 1 ← First section in chapter                           │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.3 Central Handbook Schema

#### 1.3.1 Central Tables Structure

```sql
-- Central handbook templates (master copies)
CREATE TABLE central_handbooks.handbook (
    central_id VARCHAR(100),        -- Central handbook identifier
    title VARCHAR(2000) NOT NULL,  -- Handbook title
    updated_date BIGINT NOT NULL,  -- Last modification timestamp
    created_date BIGINT NOT NULL,  -- Creation timestamp
    version_of VARCHAR(100),       -- Previous version reference
    created_by VARCHAR(200),       -- User who created
    updated_by VARCHAR(200),       -- User who last updated
    PRIMARY KEY(central_id)
)

-- Central chapter templates
CREATE TABLE central_handbooks.chapter (
    central_id VARCHAR(100),        -- Central chapter identifier
    title VARCHAR(2000) NOT NULL,  -- Chapter title
    central_parent_id VARCHAR(100), -- Parent chapter (for sub-chapters)
    central_handbook_id VARCHAR(100) NOT NULL, -- Parent handbook
    updated_date BIGINT NOT NULL,  -- Last modification timestamp
    created_date BIGINT NOT NULL,  -- Creation timestamp
    version_of VARCHAR(100),       -- Previous version reference
    created_by VARCHAR(200),       -- User who created
    updated_by VARCHAR(200),       -- User who last updated
    sort_order INTEGER,            -- Sort order within parent
    PRIMARY KEY(central_id)
)

-- Central section templates (with enhanced change tracking)
CREATE TABLE central_handbooks.section (
    id VARCHAR(100),               -- Section identifier
    central_id VARCHAR(37),        -- Central section identifier
    title VARCHAR(2000) NOT NULL, -- Section title
    central_handbook_id VARCHAR(100) NOT NULL, -- Parent handbook
    central_parent_id VARCHAR(100) NOT NULL, -- Parent chapter
    html CLOB NOT NULL,           -- Section content (HTML)
    status VARCHAR(200) NOT NULL, -- Publication status
    issuer VARCHAR(200),          -- Content issuer
    document_type VARCHAR(200),   -- Document classification
    created_date BIGINT NOT NULL,
    registered_date BIGINT NOT NULL,
    updated_date BIGINT NOT NULL,
    -- Enhanced change tracking (added in migration 24)
    title_updated_date BIGINT,    -- When title was last changed
    html_updated_date BIGINT,     -- When content was last changed
    title_updated_by VARCHAR(200), -- Who last changed title
    html_updated_by VARCHAR(200), -- Who last changed content
    created_by VARCHAR(200),
    updated_by VARCHAR(200),
    version_of VARCHAR(100),      -- Previous version reference
    sort_order INTEGER,           -- Sort order within chapter
    PRIMARY KEY(id)
)

-- Handbook structure cache (JSON tree representation)
CREATE TABLE central_handbooks.handbook_structure (
    central_handbook_id VARCHAR(100), -- Handbook identifier
    structure_json CLOB NOT NULL,     -- Complete tree structure as JSON
    PRIMARY KEY(central_handbook_id)
)
```

#### 1.3.2 Central Schema Relationships

```
CENTRAL SCHEMA RELATIONSHIPS
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  central_handbooks.handbook (Master templates)                             │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ central_id: "ch-456"                                                │   │
│  │ title: "Standard Safety Manual"                                     │   │
│  │ version_of: "ch-123" ← Previous version                            │   │
│  │ updated_date: 1640995200000                                         │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│                                    │ 1:N                                    │
│                                    ▼                                        │
│  central_handbooks.chapter (Master chapters)                               │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ central_id: "cc-101"                                                │   │
│  │ title: "Emergency Procedures"                                       │   │
│  │ central_handbook_id: "ch-456" ← Foreign key to handbook            │   │
│  │ central_parent_id: NULL ← Top-level chapter                        │   │
│  │ sort_order: 1                                                       │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│                                    │ 1:N                                    │
│                                    ▼                                        │
│  central_handbooks.section (Master content)                                │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ id: "cs-202"                                                        │   │
│  │ title: "Fire Safety Protocol"                                       │   │
│  │ html: "<p>Standard fire safety procedures...</p>"                   │   │
│  │ central_handbook_id: "ch-456" ← Foreign key to handbook            │   │
│  │ central_parent_id: "cc-101" ← Foreign key to chapter               │   │
│  │ title_updated_date: 1640995200000 ← Title change tracking          │   │
│  │ html_updated_date: 1640995300000 ← Content change tracking         │   │
│  │ sort_order: 1                                                       │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│  central_handbooks.handbook_structure (Cached tree)                        │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │ central_handbook_id: "ch-456"                                       │   │
│  │ structure_json: {                                                   │   │
│  │   "id": "ch-456",                                                   │   │
│  │   "title": "Standard Safety Manual",                               │   │
│  │   "chapters": [                                                     │   │
│  │     {                                                               │   │
│  │       "id": "cc-101",                                               │   │
│  │       "title": "Emergency Procedures",                             │   │
│  │       "sections": [...]                                            │   │
│  │     }                                                               │   │
│  │   ]                                                                 │   │
│  │ }                                                                   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 1.4 Cross-Schema Relationships

```
LOCAL ↔ CENTRAL RELATIONSHIPS
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  LOCAL HANDBOOK                    CENTRAL HANDBOOK                        │
│  ┌─────────────────────┐           ┌─────────────────────┐                 │
│  │ handbook            │           │ central_handbooks.  │                 │
│  │                     │           │ handbook            │                 │
│  │ id: "hb-123"        │           │                     │                 │
│  │ importedhandbook_id │──────────►│ central_id: "ch-456"│                 │
│  │ external_org_id:    │           │ title: "Safety..."  │                 │
│  │   "9900"            │           │                     │                 │
│  └─────────────────────┘           └─────────────────────┘                 │
│                                                                             │
│  ┌─────────────────────┐           ┌─────────────────────┐                 │
│  │ handbookchapter     │           │ central_handbooks.  │                 │
│  │                     │           │ chapter             │                 │
│  │ id: "ch-789"        │           │                     │                 │
│  │ importedhandbook    │──────────►│ central_id: "cc-101"│                 │
│  │ chapter_id: "cc-101"│           │ title: "Emergency..." │                │
│  │ handbook_id: "hb-123"│          │                     │                 │
│  └─────────────────────┘           └─────────────────────┘                 │
│                                                                             │
│  ┌─────────────────────┐           ┌─────────────────────┐                 │
│  │ handbooksection     │           │ central_handbooks.  │                 │
│  │                     │           │ section             │                 │
│  │ id: "sec-456"       │           │                     │                 │
│  │ importedhandbook    │──────────►│ id: "cs-202"        │                 │
│  │ section_id: "cs-202"│           │ title: "Fire..."    │                 │
│  │ handbook_id: "hb-123"│          │ html: "<p>..."      │                 │
│  └─────────────────────┘           └─────────────────────┘                 │
│                                                                             │
│  SYNCHRONIZATION FLAGS:                                                     │
│  • pendingchanges: 1 = Central content has changed                         │
│  • manualmerge: 1 = Local modifications conflict with central changes      │
│  • pending_changes_updated_date: When central change was detected          │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. Data Processing Flow Analysis

### 2.1 Handbook Creation Flow

#### 2.1.1 Creating a New Local Handbook

```
NEW LOCAL HANDBOOK CREATION
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ STEP 1: USER INITIATES CREATION                                            │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ POST /handbooks/local/create                                        │   │
│ │ {                                                                   │   │
│ │   "title": "Custom Safety Manual",                                 │   │
│ │   "description": "Organization-specific safety procedures",        │   │
│ │   "basedOnCentral": "ch-456"  // Optional central template         │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 2: SERVLET PROCESSING                                                 │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ LocalHandbookServlet.post("/create") {                              │   │
│ │   val externalOrgId = currentExternalOrganizationId                 │   │
│ │   val title = extractRequiredParam("title")                        │   │
│ │   val basedOnCentral = params.get("basedOnCentral")                 │   │
│ │                                                                     │   │
│ │   val service = componentRegistry.localHandbookService              │   │
│ │   val newHandbook = service.createHandbook(                         │   │
│ │     title, externalOrgId, basedOnCentral                           │   │
│ │   )                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 3: SERVICE LAYER PROCESSING                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ LocalHandbookService.createHandbook() {                             │   │
│ │                                                                     │   │
│ │   inTransaction {                                                   │   │
│ │     // 1. Create handbook record                                   │   │
│ │     val handbookId = UUID.randomUUID().toString                    │   │
│ │     val now = System.currentTimeMillis()                           │   │
│ │                                                                     │   │
│ │     val sql = """                                                   │   │
│ │       INSERT INTO handbook (                                       │   │
│ │         id, title, external_org_id, importedhandbook_id,           │   │
│ │         manualmerge, pendingchanges,                               │   │
│ │         pending_changes_updated_date, updated_date,                │   │
│ │         created_date, created_by, updated_by, deleted              │   │
│ │       ) VALUES (?, ?, ?, ?, 0, 0, ?, ?, ?, ?, ?, 0)                │   │
│ │     """                                                             │   │
│ │                                                                     │   │
│ │     connection.prepareStatement(sql).execute(                      │   │
│ │       handbookId, title, externalOrgId, basedOnCentral,            │   │
│ │       now, now, now, currentUser, currentUser                      │   │
│ │     )                                                               │   │
│ │                                                                     │   │
│ │     // 2. If based on central, copy structure                      │   │
│ │     if (basedOnCentral.isDefined) {                                │   │
│ │       copyFromCentralHandbook(handbookId, basedOnCentral.get)      │   │
│ │     }                                                               │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 4: COPYING FROM CENTRAL (if applicable)                               │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ copyFromCentralHandbook(localHandbookId, centralHandbookId) {       │   │
│ │                                                                     │   │
│ │   // 1. Get central handbook structure                             │   │
│ │   val centralStructure = centralHandbookService                     │   │
│ │     .getHandbookStructure(centralHandbookId)                       │   │
│ │                                                                     │   │
│ │   // 2. Copy chapters                                              │   │
│ │   centralStructure.chapters.foreach { centralChapter =>            │   │
│ │     val localChapterId = UUID.randomUUID().toString                │   │
│ │     insertChapter(                                                  │   │
│ │       localChapterId, centralChapter.title,                        │   │
│ │       localHandbookId, centralChapter.id,                          │   │
│ │       centralHandbookId                                             │   │
│ │     )                                                               │   │
│ │                                                                     │   │
│ │     // 3. Copy sections                                            │   │
│ │     centralChapter.sections.foreach { centralSection =>            │   │
│ │       val localSectionId = UUID.randomUUID().toString              │   │
│ │       insertSection(                                                │   │
│ │         localSectionId, centralSection.title,                      │   │
│ │         centralSection.html, localHandbookId,                      │   │
│ │         localChapterId, centralSection.id,                         │   │
│ │         centralHandbookId                                           │   │
│ │       )                                                             │   │
│ │     }                                                               │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 Central Handbook Publishing Flow

#### 2.2.1 Publishing New Central Version

```
CENTRAL HANDBOOK PUBLISHING
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ STEP 1: CONTENT AUTHOR PUBLISHES                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ POST /handbooks/central/publish                                     │   │
│ │ {                                                                   │   │
│ │   "handbookId": "ch-456",                                          │   │
│ │   "changes": [                                                      │   │
│ │     {                                                               │   │
│ │       "type": "section_update",                                    │   │
│ │       "sectionId": "cs-202",                                       │   │
│ │       "newTitle": "Updated Fire Safety Protocol",                  │   │
│ │       "newHtml": "<p>New fire safety procedures...</p>"            │   │
│ │     }                                                               │   │
│ │   ]                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ This creates a NEW VERSION of the central handbook                 │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 2: VERSION CREATION                                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ CentralHandbookService.publishNewVersion() {                        │   │
│ │                                                                     │   │
│ │   inTransaction {                                                   │   │
│ │     val newCentralId = generateNewCentralId() // "ch-789"          │   │
│ │     val now = System.currentTimeMillis()                           │   │
│ │                                                                     │   │
│ │     // 1. Create new handbook version                              │   │
│ │     INSERT INTO central_handbooks.handbook (                       │   │
│ │       central_id, title, version_of, updated_date,                 │   │
│ │       created_date, updated_by                                     │   │
│ │     ) VALUES (                                                      │   │
│ │       newCentralId, updatedTitle, "ch-456", now, now, currentUser  │   │
│ │     )                                                               │   │
│ │                                                                     │   │
│ │     // 2. Copy all chapters with new IDs                          │   │
│ │     copyChaptersToNewVersion(oldCentralId, newCentralId)           │   │
│ │                                                                     │   │
│ │     // 3. Copy all sections with changes applied                   │   │
│ │     copySectionsWithChanges(oldCentralId, newCentralId, changes)   │   │
│ │                                                                     │   │
│ │     // 4. Update handbook structure cache                          │   │
│ │     updateHandbookStructureCache(newCentralId)                     │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ STEP 3: SECTION COPYING WITH CHANGE TRACKING                               │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ copySectionsWithChanges() {                                         │   │
│ │                                                                     │   │
│ │   oldSections.foreach { oldSection =>                              │   │
│ │     val change = changes.find(_.sectionId == oldSection.id)        │   │
│ │     val newSectionId = generateNewSectionId()                      │   │
│ │                                                                     │   │
│ │     val (newTitle, titleUpdated) = change match {                  │   │
│ │       case Some(c) if c.newTitle != oldSection.title =>            │   │
│ │         (c.newTitle, now)                                          │   │
│ │       case _ => (oldSection.title, oldSection.titleUpdatedDate)    │   │
│ │     }                                                               │   │
│ │                                                                     │   │
│ │     val (newHtml, htmlUpdated) = change match {                    │   │
│ │       case Some(c) if c.newHtml != oldSection.html =>              │   │
│ │         (
</augment_code_snippet>