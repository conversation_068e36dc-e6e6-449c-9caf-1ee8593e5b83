# Central vs Local Handbook Database Structure

## Database Schema Visualization

```
┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                                         │
│  CENTRAL HANDBOOK DATABASE SCHEMA                                                                       │
│                                                                                                         │
│  ┌───────────────────────────────────┐                                                                  │
│  │ central_handbooks.handbook        │                                                                  │
│  ├───────────────────────────────────┤                                                                  │
│  │ central_id       VARCHAR(100) PK  │                                                                  │
│  │ title            VARCHAR(2000)    │                                                                  │
│  │ updated_date     BIGINT           │◄─────┐                                                           │
│  │ created_date     BIGINT           │      │                                                           │
│  │ updated_by       VARCHAR(100)     │      │                                                           │
│  │ created_by       VARCHAR(100)     │      │                                                           │
│  │ version_of       VARCHAR(100) FK  │──────┘                                                           │
│  └───────────────────────────────────┘                                                                  │
│           │                                                                                             │
│           │                                                                                             │
│           ▼                                                                                             │
│  ┌───────────────────────────────────┐                                                                  │
│  │ central_handbooks.chapter         │                                                                  │
│  ├───────────────────────────────────┤                                                                  │
│  │ central_id       VARCHAR(100) PK  │                                                                  │
│  │ title            VARCHAR(2000)    │                                                                  │
│  │ central_parent_id VARCHAR(100) FK │───┐                                                              │
│  │ central_handbook_id VARCHAR(100)  │   │                                                              │
│  │ updated_date     BIGINT           │◄──┼──┐                                                           │
│  │ created_date     BIGINT           │   │  │                                                           │
│  │ updated_by       VARCHAR(100)     │   │  │                                                           │
│  │ created_by       VARCHAR(100)     │   │  │                                                           │
│  │ version_of       VARCHAR(100) FK  │───┘  │                                                           │
│  │ sort_order       INT              │      │                                                           │
│  └───────────────────────────────────┘      │                                                           │
│           │                                  │                                                           │
│           │                                  │                                                           │
│           ▼                                  │                                                           │
│  ┌───────────────────────────────────┐      │                                                           │
│  │ central_handbooks.section         │      │                                                           │
│  ├───────────────────────────────────┤      │                                                           │
│  │ id               VARCHAR(100) PK  │      │                                                           │
│  │ central_id       VARCHAR(37)      │      │                                                           │
│  │ title            VARCHAR(2000)    │      │                                                           │
│  │ central_handbook_id VARCHAR(100)  │      │                                                           │
│  │ central_parent_id VARCHAR(100) FK │──────┘                                                           │
│  │ html             CLOB/VARCHAR(max)│                                                                  │
│  │ status           VARCHAR(200)     │                                                                  │
│  │ issuer           VARCHAR(200)     │                                                                  │
│  │ document_type    VARCHAR(200)     │                                                                  │
│  │ created_date     BIGINT           │                                                                  │
│  │ registered_date  BIGINT           │                                                                  │
│  │ updated_date     BIGINT           │◄─────┐                                                           │
│  │ updated_by       VARCHAR(100)     │      │                                                           │
│  │ created_by       VARCHAR(100)     │      │                                                           │
│  │ version_of       VARCHAR(100) FK  │──────┘                                                           │
│  │ sort_order       INT              │                                                                  │
│  └───────────────────────────────────┘                                                                  │
│                                                                                                         │
│  ┌───────────────────────────────────┐                                                                  │
│  │ central_handbooks.handbook_structure │                                                               │
│  ├───────────────────────────────────┤                                                                  │
│  │ central_handbook_id VARCHAR(100) PK │                                                                │
│  │ structure_json    CLOB/VARCHAR(max) │                                                                │
│  └───────────────────────────────────┘                                                                  │
│                                                                                                         │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                                         │
│  LOCAL HANDBOOK DATABASE SCHEMA                                                                         │
│                                                                                                         │
│  ┌───────────────────────────────────┐                                                                  │
│  │ handbook                          │                                                                  │
│  ├───────────────────────────────────┤                                                                  │
│  │ id               VARCHAR(37) PK   │                                                                  │
│  │ title            VARCHAR(255)     │                                                                  │
│  │ importedhandbook_id VARCHAR(100) │◄─────┐                                                           │
│  │ external_org_id  VARCHAR(200)     │      │                                                           │
│  │ manualmerge      SMALLINT         │      │ References central_handbooks.handbook.central_id          │
│  │ pendingchanges   SMALLINT         │      │                                                           │
│  │ pending_changes_updated_date BIGINT│      │                                                           │
│  │ updated_date     BIGINT           │      │                                                           │
│  │ created_date     BIGINT           │      │                                                           │
│  │ updated_by       VARCHAR(100)     │      │                                                           │
│  │ created_by       VARCHAR(100)     │      │                                                           │
│  │ deleted          SMALLINT         │      │                                                           │
│  └───────────────────────────────────┘      │                                                           │
│           │                                  │                                                           │
│           │                                  │                                                           │
│           ▼                                  │                                                           │
│  ┌───────────────────────────────────┐      │                                                           │
│  │ chapter                           │      │                                                           │
│  ├───────────────────────────────────┤      │                                                           │
│  │ id               VARCHAR(37) PK   │      │                                                           │
│  │ title            VARCHAR(255)     │      │                                                           │
│  │ parent_id        VARCHAR(37) FK   │───┐  │                                                           │
│  │ handbook_id      VARCHAR(37) FK   │   │  │                                                           │
│  │ importedhandbook_id VARCHAR(100) │◄──┼──┘                                                           │
│  │ importedhandbook_chapter_id VARCHAR(100)│ References central_handbooks.chapter.central_id            │
│  │ local_change     SMALLINT         │   │                                                              │
│  │ pending_change   SMALLINT         │   │                                                              │
│  │ updated_date     BIGINT           │   │                                                              │
│  │ created_date     BIGINT           │   │                                                              │
│  │ updated_by       VARCHAR(100)     │   │                                                              │
│  │ created_by       VARCHAR(100)     │   │                                                              │
│  │ deleted          SMALLINT         │   │                                                              │
│  │ sort_order       INT              │   │                                                              │
│  └───────────────────────────────────┘   │                                                              │
│           │                              │                                                              │
│           │                              │                                                              │
│           ▼                              │                                                              │
│  ┌───────────────────────────────────┐   │                                                              │
│  │ section                           │   │                                                              │
│  ├───────────────────────────────────┤   │                                                              │
│  │ id               VARCHAR(37) PK   │   │                                                              │
│  │ title            VARCHAR(255)     │   │                                                              │
│  │ parent_id        VARCHAR(37) FK   │───┘                                                              │
│  │ handbook_id      VARCHAR(37) FK   │                                                                  │
│  │ importedhandbook_id VARCHAR(100) │ References central_handbooks.handbook.central_id                  │
│  │ importedhandbook_section_id VARCHAR(100)│ References central_handbooks.section.central_id            │
│  │ text             CLOB/VARCHAR(max)│                                                                  │
│  │ local_title_change SMALLINT       │                                                                  │
│  │ local_text_change SMALLINT        │                                                                  │
│  │ pending_change   SMALLINT         │                                                                  │
│  │ updated_date     BIGINT           │                                                                  │
│  │ created_date     BIGINT           │                                                                  │
│  │ updated_by       VARCHAR(100)     │                                                                  │
│  │ created_by       VARCHAR(100)     │                                                                  │
│  │ deleted          SMALLINT         │                                                                  │
│  │ sort_order       INT              │                                                                  │
│  └───────────────────────────────────┘                                                                  │
│                                                                                                         │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Example Data Flow

### Central Handbook Example

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  CENTRAL HANDBOOK DATA EXAMPLE                                              │
│                                                                             │
│  central_handbooks.handbook                                                 │
│  ┌───────────┬─────────────┬────────────┬────────────┬───────────┐         │
│  │ central_id│ title       │updated_date│created_date│ version_of│         │
│  ├───────────┼─────────────┼────────────┼────────────┼───────────┤         │
│  │ ch-123    │ HMS Handbook│ 1623456789 │ 1623456789 │ NULL      │ v1.0    │
│  │ ch-456    │ HMS Handbook│ 1623556789 │ 1623556789 │ ch-123    │ v1.1    │
│  │ ch-789    │ HMS Handbook│ 1623656789 │ 1623656789 │ ch-456    │ v1.2    │
│  └───────────┴─────────────┴────────────┴────────────┴───────────┘         │
│                                                                             │
│  central_handbooks.chapter                                                  │
│  ┌───────────┬─────────────┬───────────────┬──────────────────┬───────────┐│
│  │ central_id│ title       │central_parent_│central_handbook_ │ version_of││
│  │           │             │id             │id                │           ││
│  ├───────────┼─────────────┼───────────────┼──────────────────┼───────────┤│
│  │ cc-100    │ Introduction│ NULL          │ ch-123           │ NULL      ││
│  │ cc-101    │ Safety Rules│ NULL          │ ch-123           │ NULL      ││
│  │ cc-102    │ Procedures  │ cc-101        │ ch-123           │ NULL      ││
│  │           │             │               │                  │           ││
│  │ cc-200    │ Introduction│ NULL          │ ch-456           │ cc-100    ││
│  │ cc-201    │ Safety Rules│ NULL          │ ch-456           │ cc-101    ││
│  │ cc-202    │ Procedures  │ cc-201        │ ch-456           │ cc-102    ││
│  │ cc-203    │ New Chapter │ NULL          │ ch-456           │ NULL      ││
│  └───────────┴─────────────┴───────────────┴──────────────────┴───────────┘│
│                                                                             │
│  central_handbooks.section                                                  │
│  ┌───────────┬─────────────┬───────────────┬──────────────────┬───────────┐│
│  │ id        │ title       │central_parent_│central_handbook_ │ html      ││
│  │           │             │id             │id                │           ││
│  ├───────────┼─────────────┼───────────────┼──────────────────┼───────────┤│
│  │ cs-100    │ Purpose     │ cc-100        │ ch-123           │ <p>...</p>││
│  │ cs-101    │ General     │ cc-101        │ ch-123           │ <p>...</p>││
│  │ cs-102    │ Equipment   │ cc-101        │ ch-123           │ <p>...</p>││
│  │           │             │               │                  │           ││
│  │ cs-200    │ Purpose     │ cc-200        │ ch-456           │ <p>...</p>││
│  │ cs-201    │ General     │ cc-201        │ ch-456           │ <p>...</p>││
│  │ cs-202    │ Equipment   │ cc-201        │ ch-456           │ <p>...</p>││
│  │ cs-203    │ New Section │ cc-203        │ ch-456           │ <p>...</p>││
│  └───────────┴─────────────┴───────────────┴──────────────────┴───────────┘│
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Local Handbook Example

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  LOCAL HANDBOOK DATA EXAMPLE (Organization "9900")                          │
│                                                                             │
│  handbook                                                                   │
│  ┌───────────┬─────────────┬────────────────┬───────────────┬─────────────┐│
│  │ id        │ title       │importedhandbook│external_org_id│pendingchanges││
│  │           │             │_id             │               │             ││
│  ├───────────┼─────────────┼────────────────┼───────────────┼─────────────┤│
│  │ lh-123    │ HMS Handbook│ ch-456         │ 9900          │ 1           ││
│  │ lh-456    │ Local Only  │ NULL           │ 9900          │ 0           ││
│  └───────────┴─────────────┴────────────────┴───────────────┴─────────────┘│
│                                                                             │
│  chapter                                                                    │
│  ┌───────────┬─────────────┬────────┬──────────┬────────────────┬─────────┐│
│  │ id        │ title       │parent_ │handbook_ │importedhandbook│local_   ││
│  │           │             │id      │id        │_chapter_id     │change   ││
│  ├───────────┼─────────────┼────────┼──────────┼────────────────┼─────────┤│
│  │ lc-100    │ Introduction│ NULL   │ lh-123   │ cc-200         │ 0       ││
│  │ lc-101    │ Safety Rules│ NULL   │ lh-123   │ cc-201         │ 0       ││
│  │ lc-102    │ Procedures  │ lc-101 │ lh-123   │ cc-202         │ 0       ││
│  │ lc-103    │ New Chapter │ NULL   │ lh-123   │ cc-203         │ 0       ││
│  │ lc-104    │ Local Rules │ NULL   │ lh-123   │ NULL           │ 1       ││
│  │           │             │        │          │                │         ││
│  │ lc-200    │ Local Only  │ NULL   │ lh-456   │ NULL           │ 1       ││
│  │ lc-201    │ Imported Ch │ NULL   │ lh-456   │ cc-200         │ 0       ││
│  └───────────┴─────────────┴────────┴──────────┴────────────────┴─────────┘│
│                                                                             │
│  section                                                                    │
│  ┌───────────┬─────────────┬────────┬──────────┬────────────────┬─────────┐│
│  │ id        │ title       │parent_ │handbook_ │importedhandbook│local_   ││
│  │           │             │id      │id        │_section_id     │text_    ││
│  │           │             │        │          │                │change   ││
│  ├───────────┼─────────────┼────────┼──────────┼────────────────┼─────────┤│
│  │ ls-100    │ Purpose     │ lc-100 │ lh-123   │ cs-200         │ 0       ││
│  │ ls-101    │ General     │ lc-101 │ lh-123   │ cs-201         │ 1       ││
│  │ ls-102    │ Equipment   │ lc-101 │ lh-123   │ cs-202         │ 0       ││
│  │ ls-103    │ New Section │ lc-103 │ lh-123   │ cs-203         │ 0       ││
│  │ ls-104    │ Local Info  │ lc-104 │ lh-123   │ NULL           │ 1       ││
│  │           │             │        │          │                │         ││
│  │ ls-200    │ Local Only  │ lc-200 │ lh-456   │ NULL           │ 1       ││
│  │ ls-201    │ Imported Sec│ lc-201 │ lh-456   │ cs-200         │ 1       ││
│  └───────────┴─────────────┴────────┴──────────┴────────────────┴─────────┘│
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Key Differences Explained

### 1. Versioning Approach

**Central Handbooks:**
- Complete copies for each version
- Linear version history via `version_of` references
- Each handbook version has its own chapters and sections
- Publishing creates a new complete version

**Local Handbooks:**
- No explicit versioning table
- References to central content via `importedhandbook_id` fields
- Local modifications tracked via `local_change` flags
- Pending changes tracked via `pending_change` flags

### 2. Content Ownership

**Central Handbooks:**
- All content created and managed centrally
- Content organized in a strict hierarchy
- Changes affect all subscribing organizations

**Local Handbooks:**
- Mix of imported and local-only content
- Can modify imported content (tracked by flags)
- Can add local-only content (no central reference)
- Each organization has independent modifications

### 3. Synchronization Flags

**Central Handbooks:**
- No synchronization flags (source of truth)
- Version chain tracks history

**Local Handbooks:**
- `importedhandbook_id`: Links to central source
- `local_change`: Indicates local modifications
- `pending_change`: Indicates unresolved central changes
- `local_title_change`/`local_text_change`: Tracks specific modifications

### 4. Database Schema Differences

**Central Handbooks:**
- Schema in `central_handbooks` namespace/schema
- Uses `central_id` as primary key
- Contains `version_of` for version chain

**Local Handbooks:**
- Schema in default namespace/schema
- Uses local `id` as primary key
- Contains multiple reference fields to central content
- Contains multiple flags for tracking modifications

## Example Scenarios

### Scenario 1: Publishing a Central Handbook

When a central handbook is published:
1. Create a new version in `central_handbooks.handbook`
2. Copy all chapters and sections with new IDs
3. Set `version_of` to reference original IDs
4. Update all local handbooks that reference the previous version

### Scenario 2: Local Modification

When a local organization modifies content:
1. Update the content in the local tables
2. Set `local_change` flag to 1
3. For sections, set specific `local_title_change` or `local_text_change` flags

### Scenario 3: Synchronization with Conflicts

When central content changes that has local modifications:
1. Set `pending_change` flag to 1 in local tables
2. User must manually resolve the conflict
3. After resolution, clear `pending_change` flag
4. If keeping local changes, maintain `local_change` flag