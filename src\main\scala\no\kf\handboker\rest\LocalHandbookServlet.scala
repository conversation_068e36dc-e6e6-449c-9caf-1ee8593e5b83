package no.kf.handboker.rest

import no.kf.exception.UserFriendlyException
import no.kf.handboker.model.local.{Chapter, Comment, Handbook, LocalEditor, Section}
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.ScalatraExceptions
import no.kf.rest.support.{ExternalOrgIdExtractionSupport, JsonSupport}
import org.joda.time.DateTime
import org.scalatra.ScalatraServlet

class LocalHandbookServlet extends ScalatraServlet with SessionSupport with JsonSupport with ExternalOrgIdExtractionSupport {
  lazy val handbookService = componentRegistry.localHandbookService
  lazy val centralHandbookService = componentRegistry.centralHandbookService
  lazy val subscriptionService = componentRegistry.subscriptionService
  lazy val versionService = componentRegistry.localHandbookVersionService

  /**
    * Get all handbooks, chapters and sections (without text) for an external org
    */
  get("/?") {
    val handbooks = handbookService.retrieveHandbooksWithChaptersAndSectionsWithoutTextForExternalOrganization(currentExternalOrganizationId, currentUser)
    Map(
      "handbooks" -> handbooks._1,
      "chapters" -> handbooks._2,
      "sections" -> handbooks._3
    )
  }

  get("/comments/:handbookId/?") {
    val handbookId = extractRequiredParam("handbookId")

    handbookService.retrieveComments(handbookId)
  }
  post("/comments/?") {
    val comment = parsedBody.extract[Comment]

    handbookService.persistComment(comment)
  }
  delete("/comments/:id/?") {
    val id = extractRequiredParam("id")

    handbookService.deleteComment(id)
    204
  }

  get("/editors/:handbookId/?") {
    val handbookId = extractRequiredParam("handbookId")
    handbookService.retrieveLocalEditors(handbookId)
  }

  delete("/editors/:id/?") {
    val id = extractRequiredParam("id")
    handbookService.deleteLocalEditor(id)
    204
  }

  post("/editors/?") {
    val localEditor = parsedBody.extract[LocalEditor]
    handbookService.insertLocalEditor(localEditor)
  }

  /**
    * Create a new local Handbook
    */
  post("/handbook/?") {
    val handbook = parsedBody.extractWithExternalOrgId[Handbook]
    val currentUserEmail = Option(currentUser.email)
    if (handbook.importedHandbookId.isDefined) {
      val latestVersion = centralHandbookService.retrieveLatestHandbookVersion(handbook.importedHandbookId.get)
      if (latestVersion.isEmpty) throw new UserFriendlyException("Sentral håndbok har ingen publisert versjon. Kan ikke opprette lokal håndbok.", Some("Håndbok kunne ikke opprettes"))

      val newHandbook = handbookService.persistHandbook(handbook.copy(createdBy = Some("KF"), updatedBy = Some("KF")))
      handbookService.persistChapterAndSectionsFromCentralHandbook(newHandbook, latestVersion.get, Option(currentExternalOrganizationId), currentUserEmail)
      newHandbook.id.foreach(createLocalEditor)
      newHandbook
    } else {
      val newHandbook = handbookService.persistHandbook(handbook.copy(updatedBy = currentUserEmail, createdBy = currentUserEmail))
      newHandbook.id.foreach(createLocalEditor)
      newHandbook
    }
  }

  /**
    * Update a local handbook
    */
  post("/handbook/:id/?") {
    val handbook = parsedBody.extract[Handbook]
    throwIfNotCurrentExternalOrganization(handbook.externalOrgId)
    val currentUserEmail = Option(currentUser.email)
    handbookService.persistHandbook(handbook.copy(updatedBy = currentUserEmail))
  }

  /**
    * Create a new chapter
    */
  post("/chapter/?") {
    val chapter = parsedBody.extract[Chapter]
    val currentUserEmail = Option(currentUser.email)
    checkAccess(chapter, currentExternalOrganizationId)
    handbookService.persistChapterWithChildren(chapter.copy(updatedBy = currentUserEmail, createdBy = currentUserEmail), currentUserEmail, Option(currentExternalOrganizationId))
  }

  /**
    * Update a chapter
    */
  post("/chapter/:id/:centralUpdate/?") {
    val chapter = parsedBody.extract[Chapter]
    val currentUserEmail = Option(currentUser.email)
    val centralUpdate = extractRequiredParam("centralUpdate")
    persistChapterIfAccess(chapter.copy(updatedBy = currentUserEmail), currentExternalOrganizationId, centralUpdate.equalsIgnoreCase("KF"))
  }

  /**
    * Create a new section
    */
  post("/section/?") {
    val section = parsedBody.extract[Section]
    val currentUserEmail = Option(currentUser.email)
    persistSectionIfAccess(section.copy(updatedBy = currentUserEmail, createdBy = currentUserEmail), currentUserEmail, currentExternalOrganizationId)
  }

  /**
    * Update a section
    */
  post("/section/:id/:centralUpdate/:centralTextUpdate/?") {
    val section = parsedBody.extract[Section]
    val currentUserEmail = Option(currentUser.email)
    val centralUpdate = extractRequiredParam("centralUpdate")
    val centralTextUpdate = extractRequiredParam("centralTextUpdate")
    persistSectionIfAccess(section, currentUserEmail, currentExternalOrganizationId, centralUpdate.equalsIgnoreCase("KF"), centralTextUpdate.equalsIgnoreCase("KF"))
  }

  def persistChapterIfAccess(chapter: Chapter, externalOrgId: String, centralUpdate: Boolean = false): Chapter = {
    val handbookId = handbookService.retrieveHandbook(chapter.handbookId).map(_.externalOrgId).getOrElse(throw new Exception("Chapter requires a valid handbookId - no handbook found with id: " + chapter.handbookId))
    throwIfNotCurrentExternalOrganization(handbookId)
    handbookService.throwIfNoCentralAccess(chapter.id, chapter.importedHandbookId, externalOrgId)
    handbookService.persistChapter(chapter, Option(externalOrgId), centralUpdate)
  }


  def checkAccess(chapter: Chapter, externalOrgId: String) = {
    val handbookId = handbookService.retrieveHandbook(chapter.handbookId).map(_.externalOrgId).getOrElse(throw new Exception("Chapter requires a valid handbookId - no handbook found with id: " + chapter.handbookId))
    throwIfNotCurrentExternalOrganization(handbookId)
  }


  def persistSectionIfAccess(section: Section, currentUserId: Option[String], externalOrgId: String, centralUpdate: Boolean = false, centralTextUpdate: Boolean = false): Section = {
    val handbookId = handbookService.retrieveHandbook(section.handbookId).map(_.externalOrgId).getOrElse(throw new Exception("Section requires a valid handbookId - no handbook found with id: " + section.handbookId))
    throwIfNotCurrentExternalOrganization(handbookId)
    handbookService.throwIfNoCentralAccess(section.id, section.importedHandbookId, externalOrgId)
    handbookService.persistSection(section, currentUserId, Option(externalOrgId), centralUpdate, centralTextUpdate)
  }

  def throwIfNotCurrentExternalOrganization(entityExternalOrganization: String) = {
    if (entityExternalOrganization != currentExternalOrganizationId) {
      ScalatraExceptions.conflict(Some("The current logged in external organization should match the handbook's external organization"))
    }
  }


  /**
    * Delete a local Handbook
    */
  delete("/:id/?") {
    val id = extractRequiredParam("id")
    val externalOrgId = Option(currentExternalOrganizationId)
    handbookService.deleteHandbook(id, externalOrgId)
    204
  }

  /**
    * Get a section (with text)
    */
  get("/section/:id/?") {
    val id = extractRequiredParam("id")
    handbookService.retrieveSection(id).getOrElse(ScalatraExceptions.notFound())
  }

  /**
    * Get all section versions (without text)
    */
  get("/section/versions/:id/?") {
    val id = extractRequiredParam("id")
    versionService.retrieveSectionVersions(id)
  }

  /**
    * Get a section version (with text)
    */
  get("/section/version/:versionId/?") {
    val versionId = extractRequiredParam("versionId")

    versionService.retrieveSectionVersion(versionId).getOrElse(ScalatraExceptions.notFound())
  }

  /**
    * Get a chapter
    */
  get("/chapter/:id/?") {
    val id = extractRequiredParam("id")
    handbookService.retrieveChapter(id).getOrElse(ScalatraExceptions.notFound())
  }

  /**
    * Get all chapter versions
    */
  get("/chapter/versions/:id/?") {
    val id = extractRequiredParam("id")
    versionService.retrieveChapterVersions(id)
  }

  /**
    * Get a chapter version
    */
  get("/chapter/version/:versionId/?") {
    val versionId = extractRequiredParam("versionId")
    versionService.retrieveChapterVersion(versionId).getOrElse(ScalatraExceptions.notFound())
  }

  /**
    * Get a handbook
    */
  get("/handbook/:id/?") {
    val id = extractRequiredParam("id")
    handbookService.retrieveHandbook(id).getOrElse(ScalatraExceptions.notFound())
  }

  /**
    * Delete a local handbook chapter
    */

  delete("/chapter/:id/?") {
    val id = extractRequiredParam("id")
    val externalOrgId = Option(currentExternalOrganizationId)
    handbookService.deleteChapter(id, externalOrgId)
    204
  }

  /**
    * Delete a local handbook section
    */
  delete("/section/:id/?") {
    val id = extractRequiredParam("id")
    val externalOrgId = Option(currentExternalOrganizationId)
    handbookService.deleteSection(id, externalOrgId)
    204
  }
  get("/section/:id/?") {
    val id = extractRequiredParam("id")
    handbookService.retrieveSection(id).getOrElse(ScalatraExceptions.notFound())
  }

  /**
    * Updates the sort order on the same level in a handbook
    */
  post("/sort/?") {
    val sortOrder = parsedBody.extract[List[String]]
    handbookService.persistSortOrder(sortOrder)
    204
  }

  get("/subscriptions/?") {
    subscriptionService.retrieveHandbookIdsForUserInExternalOrg(currentUser.email, currentExternalOrganizationId)
  }

  /**
    * Add a subscription to the handbook for the current logged in user
    */
  post("/subscribe/:handbookId/?") {
    val handbook = handbookService.retrieveHandbook(extractRequiredParam("handbookId")).getOrElse(ScalatraExceptions.notFound())
    if (handbook.externalOrgId != currentExternalOrganizationId) {
      ScalatraExceptions.conflict()
    }
    subscriptionService.subscribe(currentUser.email, handbook)
    204
  }

  /**
    * Remove the subscription to the handbook for the current logged in user
    */
  post("/unsubscribe/:handbookId/?") {
    val handbook = handbookService.retrieveHandbook(extractRequiredParam("handbookId")).getOrElse(ScalatraExceptions.notFound())
    if (handbook.externalOrgId != currentExternalOrganizationId) {
      ScalatraExceptions.conflict()
    }
    subscriptionService.unsubscribe(currentUser.email, handbook)
    204
  }

  private def createLocalEditor(handbookId: String) = {
    handbookService.insertLocalEditor(LocalEditor(id = None, handbookId = handbookId, rightsHolder = currentUser.email, addedBy = currentUser.email, addedDate = DateTime.now))
  }
}
