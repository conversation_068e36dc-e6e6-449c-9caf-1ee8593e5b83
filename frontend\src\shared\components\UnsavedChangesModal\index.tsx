import { Button, Modal } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

interface UnsavedChangesModalProps {
  isOpen: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  message?: string;
  title?: string;
}

export const UnsavedChangesModal: React.FC<UnsavedChangesModalProps> = ({
  isOpen,
  onCancel,
  onConfirm,
  message,
  title,
}) => {
  const t = usePrefixedTranslation("editor.containers.EditSection");

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onCancel}>
      <Modal.Header>
        <Modal.Title>
          {title || t("leaveEditorConfirmationModalTitle")}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p>{message || t("leaveEditorConfirmationMessage")}</p>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={onCancel}>{t("leaveEditorConfirmationCancel")}</Button>
        <Button
          color="danger"
          outlined
          className="modal-leave-button"
          onClick={onConfirm}
        >
          {t("leaveEditorConfirmationLeave")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
