package no.kf.handboker.rest

import org.scalatra.ScalatraServlet
import org.scalatra.swagger.{ApiInfo, NativeSwaggerBase, Swagger}


class ResourcesApp(implicit val swagger: Swagger) extends ScalatraServlet with NativeSwaggerBase

object  HandbookSwagger{
  val Info: ApiInfo = ApiInfo(
    "The Handbook API",
    "Docs for the Handbook API",
    "termsOfService_url_here",
    "contact_name__can_be_url__here",
    "license_name__here",
    "http://license_url__here.com")
}

class HandbookSwagger extends  Swagger(Swagger.SpecVersion, "1", HandbookSwagger.Info)
