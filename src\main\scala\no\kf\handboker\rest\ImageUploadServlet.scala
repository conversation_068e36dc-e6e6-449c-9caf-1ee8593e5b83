package no.kf.handboker.rest

import no.kf.exception.UserFriendlyException
import no.kf.handboker.model.Image
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.support.{ExternalOrgIdExtractionSupport, JsonSupport}
import org.scalatra.ScalatraServlet
import org.scalatra.servlet.FileUploadSupport

class ImageUploadServlet extends ScalatraServlet with SessionSupport with JsonSupport with ExternalOrgIdExtractionSupport with FileUploadSupport {

  lazy val imageService = componentRegistry.imageService

  post("/?") {
    getImage match {
      case None => None
      case Some(image) => "location" -> imageService.persistImage(image)
    }
  }

  def getImage: Option[Image] = {
    if (fileParams.get("file").isEmpty) return None

    val file = fileParams("file")

    val contentType = file.contentType.getOrElse({
      throw new UserFriendlyException("Ukjent filtype.")
    })

    Some(Image(contentType.split("/").last, file.get()))
  }

}
