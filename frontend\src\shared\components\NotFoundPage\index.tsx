import React from "react";
import { Section, Container, Title, Subtitle } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

export const NotFoundPage: React.FC = () => {
  const t = usePrefixedTranslation("common.components.NotFoundPage");

  return (
    <Section>
      <Container>
        <Title>404</Title>
        <Subtitle>{t("header")}</Subtitle>
        <p>{t("message")}</p>
      </Container>
    </Section>
  );
};