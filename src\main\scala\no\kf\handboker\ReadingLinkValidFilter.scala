package no.kf.handboker

import javax.servlet.http.{HttpServletRequest, HttpServletResponse}

import javax.servlet._
import no.kf.util.Logging

class ReadingLinkValidFilter extends Filter with Logging {

  lazy val componentRegistry: ComponentRegistry = ProductionRegistry.componentRegistry

  override def init(filterConfig: FilterConfig): Unit = {}

  override def doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain): Unit = {

    val httpServletRequest = request.asInstanceOf[HttpServletRequest]
    val httpServletResponse = response.asInstanceOf[HttpServletResponse]

    val query = httpServletRequest.getPathInfo.replaceAll("/readinglink/", "")

    val link = componentRegistry.readingLinkService.retrieveReadingLink(query)
    log.error(s"Found link in filter: $link")
    if(link.isDefined){
      val valid = componentRegistry.readingLinkService.isLinkValid(link.get)
      if(!valid){
        httpServletResponse.sendRedirect("/handboker/410")
        log.error(s"Utgått lenke ble brukt: $link")
      }else{
        chain.doFilter(request, response)
      }
    }else{
      httpServletResponse.sendRedirect("/handboker/410")
      log.error(s"Ugyldig lenke ble brukt: $link")
    }
  }

  override def destroy(): Unit = {}
}