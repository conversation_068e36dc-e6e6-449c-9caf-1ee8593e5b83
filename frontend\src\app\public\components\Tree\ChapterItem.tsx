import React, { useMemo } from "react";
import { <PERSON> } from "react-router-dom";
import { Icon } from "kf-bui";
import { SectionItem } from "./SectionItem";
import { isRootChapter } from "../../utils/handbookNavigation";
import type { Chapter, Section } from "@/types";

interface ChapterItemProps {
  chapter: Chapter;
  chapters: Chapter[];
  sections: Section[];
  externalOrgId: string;
  handbookId: string;
  activeSections: string[];
  expandedChapters: string[];
  onChapterExpand: (item: Chapter) => void;
  onChapterNavigate: (item: Chapter) => void;
  onSectionNavigate: (sectionId: string) => void;
  depth?: number;
}

export const ChapterItem: React.FC<ChapterItemProps> = ({
  chapter,
  chapters,
  sections,
  externalOrgId,
  handbookId,
  activeSections,
  expandedChapters,
  onChapterExpand,
  onChapterNavigate,
  onSectionNavigate,
  depth = 0,
}) => {
  const childChapters = chapters
    .filter((c) => c.parentId === chapter.id)
    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const childSections = sections
    .filter((s) => s.parentId === chapter.id)
    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const allChildren = [
    ...childChapters.map((c) => ({ ...c, itemType: "chapter" as const })),
    ...childSections.map((s) => ({ ...s, itemType: "section" as const })),
  ].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const isExpanded = expandedChapters.includes(chapter.id!);
  const hasChildren = allChildren.length > 0;

  const chapterLink = `/${externalOrgId}/${handbookId}/chapter/${chapter.id}`;

  const isRoot = useMemo(
    () => isRootChapter(chapter.id!, chapters),
    [chapter.id, chapters]
  );

  const hasActiveChild = useMemo(() => {
    const childSectionIds = childSections.map((s) => s.id);
    if (childSectionIds.some((id) => activeSections.includes(id!))) {
      return true;
    }

    const checkChapterRecursively = (chapterId: string): boolean => {
      const sectionsInChapter = sections.filter(
        (s) => s.parentId === chapterId
      );
      if (sectionsInChapter.some((s) => activeSections.includes(s.id!))) {
        return true;
      }

      const childChaps = chapters.filter((c) => c.parentId === chapterId);
      return childChaps.some((childChap) =>
        checkChapterRecursively(childChap.id!)
      );
    };

    return childChapters.some((childChapter) =>
      checkChapterRecursively(childChapter.id!)
    );
  }, [childSections, childChapters, chapters, sections, activeSections]);

  const handleExpandClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChapterExpand(chapter);
  };

  const handleChapterClick = (e: React.MouseEvent) => {
    e.preventDefault();

    if (isRoot) {
      onChapterNavigate(chapter);
    } else {
      const firstSection = childSections.length > 0 ? childSections[0] : null;

      if (firstSection) {
        onSectionNavigate(firstSection.id!);
      } else {
        onChapterNavigate(chapter);
      }
    }
  };

  return (
    <div className={`tree-item ${hasActiveChild ? "has-active-child" : ""}`}>
      <div
        className="tree-item-content"
        style={
          {
            "--tree-depth-padding": `${depth * 1.5}rem`,
          } as React.CSSProperties
        }
      >
        {hasChildren && (
          <button
            className={`tree-expand-button ${isExpanded ? "expanded" : ""}`}
            onClick={handleExpandClick}
          >
            <Icon
              icon={isExpanded ? "chevron-down" : "chevron-right"}
              size="small"
            />
          </button>
        )}
        <Link
          to={chapterLink}
          className={`tree-item-link ${isRoot ? "root-chapter" : "nested-chapter"} ${hasActiveChild ? "has-active-child" : ""}`}
          onClick={handleChapterClick}
        >
          <Icon icon="RegBookmark" size="small" className="tree-item-icon" />
          {chapter.title}
        </Link>
      </div>

      {isExpanded && hasChildren && (
        <div className="tree-children">
          {allChildren.map((child) =>
            child.itemType === "chapter" ? (
              <ChapterItem
                key={child.id}
                chapter={child as Chapter}
                chapters={chapters}
                sections={sections}
                externalOrgId={externalOrgId}
                handbookId={handbookId}
                activeSections={activeSections}
                expandedChapters={expandedChapters}
                onChapterExpand={onChapterExpand}
                onChapterNavigate={onChapterNavigate}
                onSectionNavigate={onSectionNavigate}
                depth={depth + 1}
              />
            ) : (
              <SectionItem
                key={child.id}
                section={child as Section}
                externalOrgId={externalOrgId}
                handbookId={handbookId}
                activeSections={activeSections}
                onSectionNavigate={onSectionNavigate}
                depth={depth + 1}
              />
            )
          )}
        </div>
      )}
    </div>
  );
};
