import { useState, useCallback, useEffect, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import type { Chapter, Section } from '@/types';
import { 
  getParentChapterIds, 
  getFirstSectionOfChapter,
  getRootChapterId,
  isRootChapter
} from '../utils/handbookNavigation';
import { useSmoothScrollCoordination } from './useSmoothScrollCoordination';

/**
 * Legacy-inspired navigation state
 * Directly mirrors the successful legacy HandbookPage state structure
 */
interface NavigationState {
  activeSections: string[];        // Currently visible sections (legacy: activeSections)
  expandedChapters: string[];      // Expanded tree nodes (legacy: expandedChapters)
  clickedSection: string | null;   // User explicitly clicked (legacy: clickedSection)
}


/**
 * Modern handbook navigation with legacy-inspired architecture
 * 
 * This hook recreates the successful patterns from legacy HandbookPage:
 * 1. Clear separation between URL state and UI state
 * 2. Smart expansion management (additive, not replacive)
 * 3. Clicked section tracking for proper prioritization
 * 4. Smooth scroll coordination with waypoint management
 * 
 * Key improvements over legacy:
 * - Smooth scrolling with perfect coordination
 * - React hooks instead of class component lifecycle
 * - TypeScript for better type safety
 * - Modern React patterns while preserving legacy logic
 */
export const useHandbookNavigationLegacy = (
  chapters: Chapter[], 
  sections: Section[]
) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Smooth scroll coordination system
  const { 
    smoothScrollToElement, 
    waypointsEnabled,
    isUserScrolling
  } = useSmoothScrollCoordination();
  
  // Core navigation state (mirrors legacy exactly)
  const [state, setState] = useState<NavigationState>({
    activeSections: [],
    expandedChapters: [],
    clickedSection: null
  });

  /**
   * Parse current URL state (enhanced for root chapter handling)
   * Recreates legacy's componentWillReceiveProps URL parsing logic
   */
  const urlState = useMemo(() => {
    const queryParams = new URLSearchParams(location.search);
    const sectionId = queryParams.get('id');
    const chapterMatch = location.pathname.match(/\/chapter\/([^/]+)$/);
    const currentChapter = chapterMatch?.[1] || null;
    
    // CRITICAL: Always resolve to root chapter (legacy behavior)
    const rootChapter = currentChapter && chapters.length 
      ? getRootChapterId(currentChapter, chapters) 
      : null;
    
    return {
      currentChapter,
      rootChapter,
      sectionId,
      hasExplicitSection: Boolean(sectionId)
    };
  }, [location.search, location.pathname, chapters]);

  /**
   * Get first section under chapter (legacy algorithm)
   * Direct port of legacy's getFirstSectionUnderChapter function
   */
  const getFirstSectionUnderChapter = useCallback((chapterId: string) => {
    return getFirstSectionOfChapter(chapterId, sections, chapters);
  }, [sections, chapters]);

  /**
   * Get parent chapters for section (legacy algorithm)
   * Direct port of legacy's getParentChapters function
   */
  const getParentChapters = useCallback((section: Section): string[] => {
    return getParentChapterIds(section.id!, chapters, sections);
  }, [chapters, sections]);

  /**
   * Update expansion path (legacy algorithm with nested chapter improvements)
   * Enhanced port of legacy's updatePath function - key to preventing tree collapse
   * Now handles deeply nested chapter structures better
   */
  const updatePath = useCallback((activeSections: string[], currentExpandedChapters: string[]): string[] => {
    // Process all active sections to maintain proper expansion
    const allPathsToExpand: string[] = [];
    
    for (const sectionId of activeSections) {
      const activeSection = sections.find(s => s.id === sectionId);
      if (activeSection) {
        const pathToActiveSection = getParentChapters(activeSection);
        allPathsToExpand.push(...pathToActiveSection);
      }
    }
    
    // CRITICAL: Always expand ALL parent paths for ALL active sections
    // This prevents tree collapse when scrolling through nested content
    const newExpandedChapters = [...new Set([...currentExpandedChapters, ...allPathsToExpand])];
    
    return newExpandedChapters;
  }, [sections, getParentChapters]);

  /**
   * Create chapter structure (legacy chapterStructure equivalent)
   * Flattened, sorted list of all chapters and sections for ordering reference
   */
  const chapterStructure = useMemo(() => {
    const allItems: Array<{id: string, sortOrder?: number, parentId?: string, type: 'chapter' | 'section'}> = [];
    
    // Add all chapters
    chapters.forEach(chapter => {
      allItems.push({
        id: chapter.id!,
        sortOrder: chapter.sortOrder,
        parentId: chapter.parentId,
        type: 'chapter'
      });
    });
    
    // Add all sections
    sections.forEach(section => {
      allItems.push({
        id: section.id!,
        sortOrder: section.sortOrder,
        parentId: section.parentId,
        type: 'section'
      });
    });
    
    // Sort by parent hierarchy and sortOrder - recursive structure flattening
    const sortItemsRecursively = (items: typeof allItems, parentId?: string): typeof allItems => {
      const itemsAtLevel = items
        .filter(item => item.parentId === parentId)
        .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
      
      const result: typeof allItems = [];
      for (const item of itemsAtLevel) {
        result.push(item);
        // Recursively add children
        result.push(...sortItemsRecursively(items, item.id));
      }
      
      return result;
    };
    
    const sortedItems = sortItemsRecursively(allItems);
    return sortedItems.map(item => item.id);
  }, [chapters, sections]);

  /**
   * Sort sections (legacy algorithm)
   * Direct port of legacy's sortSections function using chapterStructure
   */
  const sortSections = useCallback((activeSections: string[]): string[] => {
    const clickedSection = state.clickedSection;
    
    const sortedActiveSections = [...new Set(
      activeSections.filter(sectionId => sectionId !== clickedSection)
    )].sort((a, b) => chapterStructure.indexOf(a) - chapterStructure.indexOf(b));
    
    // Clicked section always goes first (legacy behavior)
    if (clickedSection) {
      return [clickedSection, ...sortedActiveSections];
    }
    
    return sortedActiveSections;
  }, [chapterStructure, state.clickedSection]);

  /**
   * Add section to active list (legacy setSection)
   * Direct port of legacy's addSection function with improved prioritization
   */
  const addSection = useCallback((section: Section) => {
    if (!waypointsEnabled) return; // Don't update during programmatic scroll
    
    setState(prevState => {
      const newActiveSections = [
        section.id!,
        ...prevState.activeSections.filter(id => id !== section.id!)
      ];
      
      // Legacy behavior: Only sort when not user scrolling
      const sortedActiveSections = !isUserScrolling 
        ? sortSections(newActiveSections)
        : newActiveSections;
      const updatedPath = updatePath(sortedActiveSections, prevState.expandedChapters);
      
      return {
        ...prevState,
        activeSections: sortedActiveSections,
        expandedChapters: updatedPath
      };
    });
  }, [waypointsEnabled, sortSections, updatePath, isUserScrolling]);

  /**
   * Remove section from active list (legacy removeSection)
   * Direct port of legacy's removeSection function
   */
  const removeSection = useCallback((section: Section) => {
    if (!waypointsEnabled) return; // Don't update during programmatic scroll
    
    setState(prevState => {
      const activeSections = prevState.activeSections.filter(id => id !== section.id!);
      const expandedChapters = updatePath(activeSections, prevState.expandedChapters);
      
      return {
        ...prevState,
        activeSections,
        expandedChapters
      };
    });
  }, [waypointsEnabled, updatePath]);

  /**
   * Handle URL changes (legacy componentWillReceiveProps)
   * Recreates the exact logic from legacy's URL change handling
   */
  useEffect(() => {
    if (!chapters.length || !sections.length) return;
    
    const { currentChapter, sectionId } = urlState;
    
    if (!currentChapter) return;
    
    // Determine the target section (legacy logic)
    let targetSection: string | null = null;
    
    if (sectionId) {
      // Explicit section ID in URL
      const section = sections.find(s => s.id === sectionId);
      if (section) {
        targetSection = section.id!;
      }
    }
    
    if (!targetSection) {
      // No explicit section - find first section in chapter (legacy behavior)
      const firstSection = getFirstSectionUnderChapter(currentChapter);
      if (firstSection) {
        targetSection = firstSection.id!;
      }
    }
    
    if (targetSection) {
      // Update state (enhanced legacy pattern with proper expansion)
      setState(prevState => {
        // Use updatePath to ensure proper expansion logic
        const updatedExpandedChapters = updatePath([targetSection!], prevState.expandedChapters);
        
        return {
          ...prevState,
          activeSections: [targetSection!],
          clickedSection: targetSection,
          expandedChapters: updatedExpandedChapters
        };
      });
      
      // Legacy behavior: Scroll if there's an explicit section ID in URL
      if (sectionId) {
        // Scroll when URL has ?id= parameter (from search, tree clicks, direct links)
        smoothScrollToElement(targetSection);
      }
      // No scrolling for direct chapter navigation without ID (legacy behavior)
    }
  }, [
    location.pathname, 
    location.search, 
    chapters, 
    sections, 
    urlState,
    getFirstSectionUnderChapter, 
    updatePath, 
    smoothScrollToElement
  ]);

  /**
   * Handle chapter expansion (arrow clicks - pure UI)
   */
  const handleChapterExpand = useCallback((chapter: Chapter) => {
    const chapterId = chapter.id!;
    
    setState(prevState => {
      const isExpanded = prevState.expandedChapters.includes(chapterId);
      
      if (isExpanded) {
        // Collapse - remove this chapter and all its children
        const childChapterIds = chapters
          .filter(c => c.parentId === chapterId)
          .map(c => c.id!);
        const toRemove = [chapterId, ...childChapterIds];
        
        return {
          ...prevState,
          expandedChapters: prevState.expandedChapters.filter(id => !toRemove.includes(id))
        };
      } else {
        // Expand - add to expanded list
        return {
          ...prevState,
          expandedChapters: [...prevState.expandedChapters, chapterId]
        };
      }
    });
  }, [chapters]);

  /**
   * Handle chapter navigation (title clicks)
   * CRITICAL: Different behavior for root vs nested chapters (legacy behavior)
   */
  const handleChapterNavigate = useCallback((chapter: Chapter) => {
    const chapterId = chapter.id!;
    const isRoot = isRootChapter(chapterId, chapters);
    
    if (isRoot) {
      // This IS a root chapter - navigate with URL change (legacy behavior)
      const firstSection = getFirstSectionUnderChapter(chapterId);
      if (firstSection) {
        const basePath = location.pathname.split('/chapter/')[0];
        const targetUrl = `${basePath}/chapter/${chapterId}?id=${firstSection.id}`;
        navigate(targetUrl);
      }
    } else {
      // This is a NESTED chapter - just scroll, no URL change (legacy behavior)
      const firstSection = getFirstSectionUnderChapter(chapterId);
      if (firstSection) {
        smoothScrollToElement(firstSection.id!);
      } else {
        // No sections in this chapter, scroll to chapter title itself
        smoothScrollToElement(chapterId);
      }
    }
  }, [chapters, location.pathname, navigate, getFirstSectionUnderChapter, smoothScrollToElement]);

  /**
   * Handle section navigation (tree section clicks)
   * CRITICAL: Always navigate to ROOT chapter, not direct parent (legacy behavior)
   */
  const handleSectionNavigate = useCallback((sectionId: string) => {
    const section = sections.find(s => s.id === sectionId);
    if (!section || !section.parentId) return;
    
    // CRITICAL: Always navigate to ROOT chapter, not direct parent
    const rootChapterId = getRootChapterId(section.parentId, chapters);
    const basePath = location.pathname.split('/chapter/')[0];
    const targetUrl = `${basePath}/chapter/${rootChapterId}?id=${sectionId}`;
    navigate(targetUrl);
  }, [sections, chapters, location.pathname, navigate]);

  /**
   * Navigate to first chapter (legacy firstChapter function)
   */
  const navigateToFirstChapter = useCallback(() => {
    const rootChapters = chapters
      .filter(c => !c.parentId)
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    
    if (rootChapters[0]) {
      const basePath = location.pathname.split('/chapter/')[0];
      const targetUrl = `${basePath}/chapter/${rootChapters[0].id}`;
      navigate(targetUrl);
    }
  }, [chapters, location.pathname, navigate]);

  return {
    // State (for components to read)
    activeSections: state.activeSections,
    expandedChapters: state.expandedChapters,
    currentChapter: urlState.currentChapter,
    activeSection: state.activeSections[0] || null,
    waypointsEnabled,
    
    // Actions (for components to call)
    handleChapterExpand,
    handleChapterNavigate, 
    handleSectionNavigate,
    addSection,
    removeSection,
    navigateToFirstChapter
  };
};