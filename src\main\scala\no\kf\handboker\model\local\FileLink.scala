package no.kf.handboker.model.local
import org.joda.time.DateTime

case class FileLink(id: Option[String],
                    title: String,
                    belongsTo: String,
                    ownerId: String,
                    url: String,
                    sortOrder: Int,
                    size: Double,
                    createdDate: Option[DateTime] = None,
                    updatedDate: Option[DateTime] = None,
                    createdBy: Option[String] = None,
                    updatedBy: Option[String] = None )
