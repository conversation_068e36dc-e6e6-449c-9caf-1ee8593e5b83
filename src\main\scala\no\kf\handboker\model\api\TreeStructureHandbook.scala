package no.kf.handboker.model.api

import no.kf.handboker.model.local.HandbookProperties
import org.joda.time.DateTime

case class TreeStructureHandbook(
                                  id: Option[String],
                                  title: String,
                                  importedHandbookId: Option[String],
                                  externalOrgId: String,
                                  chapters: List[TreeStructureChapter], // New attribute compared to HandbookProperties
                                  override val localChange: Boolean = false,
                                  override val isPublic: Boolean = false,
                                  override val pendingChange: Boolean = false,
                                  override val pendingChangeUpdatedDate: Option[DateTime] = None,
                                  override val updatedDate: Option[DateTime] = None,
                                  override val createdDate: Option[DateTime] = None,
                                  override val updatedBy: Option[String] = None,
                                  override val createdBy: Option[String] = None) extends HandbookProperties

