import React, { useState, useEffect, useCallback } from "react";
import { Link, useParams, useNavigate } from "react-router-dom";
import { Button, Column, Icon, Columns, Title, Group, Menu } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Spinner } from "@/shared/components/Spinner";
import { CentralDeleteButton as DeleteButton } from "../../central/CentralDeleteButton";
import { CentralMetadata as Metadata } from "../../central/CentralMetadata";
import { CentralSortChildrenScreen as SortChildrenScreen } from "../../central/CentralSortChildrenScreen";
import { NoSelectionScreen } from "../../handbook/NoSelectionScreen";
import {
  useGetCentralChaptersQuery,
  useGetCentralSectionsQuery,
  useDeleteCentralChapterMutation,
  useSortCentralItemsMutation,
  useGetReadingLinksQuery,
} from "@/store/services/handbook/centralHandbookApi";
import { toast } from "@/shared/components/Toast";

export const CentralChapterScreen: React.FC = () => {
  const t = usePrefixedTranslation("editor.containers.ChapterSelection");
  const navigate = useNavigate();
  const { chapterId } = useParams<{ chapterId: string }>();
  const [isSorting, setIsSorting] = useState<boolean>(false);

  const { data: chapters = [], isLoading: isLoadingChapters } = useGetCentralChaptersQuery();
  const { data: sections = [], isLoading: isLoadingSections } = useGetCentralSectionsQuery();
  const { data: readingLinks = [], refetch: refetchReadingLinks } =
    useGetReadingLinksQuery();
  const [deleteCentralChapter] = useDeleteCentralChapterMutation();
  const [sortCentralItems, { isLoading: isSortingItems }] =
    useSortCentralItemsMutation();

  const centralChapter = chapters.find((chapter) => chapter.id === chapterId);
  
  // Only include chapters and sections from the same handbook as the current chapter
  const currentHandbookId = centralChapter?.centralHandbookId;
  
  const childChapters = chapters.filter(
    (chapter) => 
      chapter.parentId === chapterId && 
      chapter.centralHandbookId === currentHandbookId
  );
  const childSections = sections.filter(
    (section) => 
      section.parentId === chapterId && 
      section.centralHandbookId === currentHandbookId
  );

  const children = [
    ...childChapters.map((ch) => ({ ...ch, type: "CHAPTER" as const })),
    ...childSections.map((sec) => ({ ...sec, type: "SECTION" as const })),
  ].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const toggleSort = useCallback(() => {
    setIsSorting((prev) => !prev);
  }, []);

  useEffect(() => {
    setIsSorting(false);
  }, [chapterId, currentHandbookId]);

  useEffect(() => {
    refetchReadingLinks();
  }, [refetchReadingLinks]);

  const handleSortChildren = useCallback(async (itemIds: string[]) => {
    try {
      await sortCentralItems(itemIds).unwrap();
      toast.success("Lagret sortering.");
      setIsSorting(false);
    } catch (error) {
      console.error("Error sorting items:", error);
      toast.error(t("editor.error.sortFailed"));
    }
  }, [sortCentralItems, t]);

  const handleDeleteChapter = useCallback(async () => {
    if (!centralChapter?.id) return;

    try {
      await deleteCentralChapter(centralChapter.id).unwrap();
      toast.success(t("editor.success.chapterDeleted"));
      navigate(`/central-editor/${centralChapter.centralHandbookId}/`);
    } catch (error) {
      console.error("Error deleting chapter:", error);
      toast.error(t("editor.error.chapterDeleteFailed"));
    }
  }, [deleteCentralChapter, centralChapter?.id, centralChapter?.centralHandbookId, navigate, t]);

  const renderChildren = useCallback(() => {
    if (isSorting) {
      return (
        <SortChildrenScreen
          items={children}
          onCancel={() => setIsSorting(false)}
          sortFunction={handleSortChildren}
          isSaving={isSortingItems}
        />
      );
    }

    if (!children.length) {
      return <div style={{ marginTop: "1rem" }}>{t("noChildren")}</div>;
    }

    return (
      <Menu>
        <Menu.List>
          {children.map((child) => (
            <Menu.Item
              key={child.id}
              as={Link}
              to={`/central-editor/${child.centralHandbookId}/${
                child.type === "CHAPTER" ? "chapter" : "section"
              }/${child.id}/`}
            >
              <Icon
                icon={
                  child.type === "CHAPTER" ? "RegBookmark" : "RegFileLines"
                }
                size="small"
                style={{ marginRight: "4px" }}
              />
              {child.title}
            </Menu.Item>
          ))}
        </Menu.List>
      </Menu>
    );
  }, [
    children,
    isSorting,
    isSortingItems,
    handleSortChildren,
    t,
  ]);

  if (!chapterId) {
    return <NoSelectionScreen />;
  }

  if (isLoadingChapters || isLoadingSections) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (!centralChapter) {
    if (!isLoadingChapters) {
      toast.error(t("editor.error.chapterNotFound", { id: chapterId }));
    }
    return <NoSelectionScreen />;
  }

  return (
    <>
      <Columns>
        <Column>
          <Title>
            <Icon
              icon="RegBookmark"
              size="medium"
              style={{ marginRight: "1rem" }}
            />
            <span>{centralChapter.title}</span>
          </Title>
        </Column>
      </Columns>

      <Columns responsive="desktop">
        <Column>
          <Group>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralChapter.centralHandbookId}/chapter/${centralChapter.id}/edit`}
              size="small"
              icon="pencil"
            >
              {t("editButton")}
            </Button>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralChapter.centralHandbookId}/chapter/add-new?parent=${centralChapter.id}`}
              size="small"
              icon="plus"
            >
              {t("newChapter")}
            </Button>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralChapter.centralHandbookId}/section/add-new/?parent=${centralChapter.id}`}
              size="small"
              icon="plus"
            >
              {t("newSection")}
            </Button>
          </Group>
        </Column>

        <Column narrow>
          <Group>
            <Button
              control
              active={isSorting}
              disabled={children.length <= 1}
              onClick={toggleSort}
              size="small"
            >
              {t("sortButton")}
            </Button>
            <Button
              control
              as={Link}
              to={`/central-editor/${centralChapter.centralHandbookId}/chapter/${centralChapter.id}/move`}
              size="small"
            >
              {t("moveButton")}
            </Button>
            <DeleteButton
              toDelete={{
                id: centralChapter.id!,
                title: centralChapter.title,
                type: "CHAPTER" as const,
              }}
              onDelete={handleDeleteChapter}
              readlinkExists={readingLinks && readingLinks.length > 0}
            />
          </Group>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <hr />
          <Metadata
            element={{
              ...centralChapter,
              handbookId: centralChapter.centralHandbookId,
              localChange: false,
              pendingChange: false,
              pendingDeletion: false,
              isDeleted: false,
            }}
          />
          <hr style={{ marginBottom: "0" }} />
        </Column>
      </Columns>

      {renderChildren()}
    </>
  );
};
