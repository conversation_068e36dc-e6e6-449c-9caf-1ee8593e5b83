import { useState, useRef } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
  type DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button, Card, Column, Icon, Columns, Group } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

// Type for sortable items (chapters and sections have id and title)
interface SortableItem {
  id: string;
  title: string;
  type?: string;
}

interface CentralSortChildrenScreenProps {
  items: Array<{ id?: string; title: string; type?: string }>;
  isSaving: boolean;
  onCancel: () => void;
  sortFunction: (itemIds: string[]) => Promise<void>;
}

interface CentralSortableItemProps {
  id: string;
  item: SortableItem;
  disabled: boolean;
}

/**
 * CentralSortChildrenScreen - Sort children component exclusively for Central Editor
 * This component handles sorting operations within the central editor.
 * Used exclusively in the Central Editor flow (/central-editor/)
 */
const CentralSortableItem = ({
  id,
  item,
  disabled,
}: CentralSortableItemProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id, disabled });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getItemIcon = () => {
    if (item.type === "CHAPTER" || item.type === "CENTRAL_CHAPTER") {
      return "RegBookmark";
    }
    return "RegFileLines";
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <Card
        style={{
          marginBottom: "0.5rem",
          cursor: disabled ? "default" : "grab",
        }}
      >
        <Card.Content>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="GripLines"
              size="small"
              style={{
                marginRight: "0.5rem",
                color: "#9b9b9b",
                cursor: disabled ? "default" : "grab",
              }}
            />
            <Icon
              icon={getItemIcon()}
              size="small"
              style={{ marginRight: "0.5rem" }}
            />
            <span>{item.title}</span>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
};

export const CentralSortChildrenScreen = ({
  items,
  onCancel,
  sortFunction,
  isSaving,
}: CentralSortChildrenScreenProps) => {
  const t = usePrefixedTranslation("editor.components.SortChildren");

  const validItems: SortableItem[] = items
    .filter((item) => item.id)
    .map((item) => ({
      id: item.id!,
      title: item.title,
      type: item.type,
    }));

  const [sortedItems, setSortedItems] = useState(validItems);
  const [activeId, setActiveId] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setSortedItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);

        return arrayMove(items, oldIndex, newIndex);
      });
    }

    setActiveId(null);
  };

  const handleSave = async () => {
    try {
      const itemIds = sortedItems.map((item) => item.id);
      await sortFunction(itemIds);
    } catch (error) {
      console.error("Error in central sort function:", error);
    }
  };

  const activeItem = activeId
    ? sortedItems.find((item) => item.id === activeId)
    : null;

  return (
    <>
      <Columns>
        <Column>
          <div ref={containerRef} style={{ position: "relative" }}>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              modifiers={[
                (args) => {
                  const { transform } = args;
                  if (!containerRef.current) return transform;

                  const containerRect =
                    containerRef.current.getBoundingClientRect();
                  const constrainedTransform = { ...transform };

                  const maxHorizontalOffset = 50;
                  constrainedTransform.x = Math.max(
                    -maxHorizontalOffset,
                    Math.min(maxHorizontalOffset, transform.x)
                  );

                  const verticalBuffer = containerRect.height * 0.5;
                  constrainedTransform.y = Math.max(
                    -containerRect.height - verticalBuffer,
                    Math.min(containerRect.height + verticalBuffer, transform.y)
                  );

                  return constrainedTransform;
                },
              ]}
            >
              <SortableContext
                items={sortedItems.map((item) => item.id)}
                strategy={verticalListSortingStrategy}
              >
                {sortedItems.map((item) => (
                  <CentralSortableItem
                    key={item.id}
                    id={item.id}
                    item={item}
                    disabled={isSaving}
                  />
                ))}
              </SortableContext>
              <DragOverlay>
                {activeItem ? (
                  <Card
                    style={{
                      cursor: "grabbing",
                      boxShadow: "0 5px 15px rgba(0, 0, 0, 0.15)",
                    }}
                  >
                    <Card.Content>
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <Icon
                          icon="GripLines"
                          size="small"
                          style={{
                            marginRight: "0.5rem",
                            color: "#9b9b9b",
                            cursor: "grabbing",
                          }}
                        />
                        <Icon
                          icon={
                            activeItem.type === "CHAPTER" ||
                            activeItem.type === "CENTRAL_CHAPTER"
                              ? "RegBookmark"
                              : "RegFileLines"
                          }
                          size="small"
                          style={{ marginRight: "4px" }}
                        />
                        <span>{activeItem.title}</span>
                      </div>
                    </Card.Content>
                  </Card>
                ) : null}
              </DragOverlay>
            </DndContext>
          </div>
        </Column>
      </Columns>
      <Columns>
        <Column>
          <Group right>
            <Button
              control
              onClick={onCancel}
              disabled={isSaving}
              title={t("cancelButton.title")}
            >
              {t("cancelButton")}
            </Button>
            <Button
              control
              onClick={handleSave}
              color="primary"
              title={t("saveButton.title")}
              loading={isSaving}
            >
              Lagre sentralt
            </Button>
          </Group>
        </Column>
      </Columns>
    </>
  );
};
