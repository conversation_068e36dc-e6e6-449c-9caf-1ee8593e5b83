package no.kf.handboker.service

import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.model.local.{ChangeNotification, Handbook}
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfterEach, FunSuite}
import org.scalatest.junit.JUnitRunner
import org.scalatest.mockito.MockitoSugar

@RunWith(classOf[JUnitRunner])
class SubscriptionServiceTest extends FunSuite with DefaultTestDI with MockitoSugar with BeforeAndAfterEach {

  override val componentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val subscriptionService = new SubscriptionServiceImpl
  }

  override def beforeEach() {
    reset(componentRegistry.subscriptionRepository)
  }

  val email = "<EMAIL>"
  val externalOrgId = "9900"
  val handbook = Handbook(None, "", None, externalOrgId, false, false)


  test("That subscribe calls the right repo method") {
    class ThisTestException extends RuntimeException
    when(componentRegistry.subscriptionRepository.persistSubscription(email, handbook)).thenThrow(new ThisTestException)

    intercept[ThisTestException] {
      componentRegistry.subscriptionService.subscribe(email, handbook)
    }

    verify(componentRegistry.subscriptionRepository).persistSubscription(email, handbook)
  }


  test("That unsubscribe calls the right repo method") {
    class ThisTestException extends RuntimeException
    when(componentRegistry.subscriptionRepository.deleteSubscription(email, handbook)).thenThrow(new ThisTestException)

    intercept[ThisTestException] {
      componentRegistry.subscriptionService.unsubscribe(email, handbook)
    }

    verify(componentRegistry.subscriptionRepository).deleteSubscription(email, handbook)
  }

  test("That retrieving handbookIds for a user in an external organization filters correctly") {
    val handbook2 = Handbook(Option("2"), "", None, externalOrgId)
    val handbook3 = Handbook(Option("3"), "", None, externalOrgId)
    val subscribedHandbookIds = List("1", "2", "4")
    val handbookIdsInExternalOrg = List(handbook2, handbook3)

    when(componentRegistry.subscriptionRepository.retrieveSubscriptionsForUser(email)).thenReturn(subscribedHandbookIds)
    when(componentRegistry.localHandbookService.retrieveHandbooksForExternalOrganization(externalOrgId)).thenReturn(handbookIdsInExternalOrg)

    val handbookIdsForUserInExternalOrg = componentRegistry.subscriptionService.retrieveHandbookIdsForUserInExternalOrg(email, externalOrgId)

    assert(handbookIdsForUserInExternalOrg.size == 1)
  }

  test("That we can retrieve all notifications for emails") {
    /*val handbook1 = Handbook(Some("1"), "Håndbok 1", None, "9900")
    val handbook2 = Handbook(Some("2"), "Håndbok 2", None, "9900")
    val changesForBook1 = List(
      ChangeNotification(Some("1"), "Slettet bok", handbook1, DateTime.now),
      ChangeNotification(Some("2"), "Endret tittel", handbook1, DateTime.now),
      ChangeNotification(Some("3"), "Nytt kapittel", handbook1, DateTime.now)
    )
    val changesForBook2 = List(
      ChangeNotification(Some("4"), "Fyttet kapittel", handbook2, DateTime.now),
      ChangeNotification(Some("5"), "Nytt avsnitt", handbook2, DateTime.now),
      ChangeNotification(Some("6"), "Endret nivå for kapittel", handbook2, DateTime.now)
    )
    val allChanges = changesForBook1 ++ changesForBook2
    val mail1 = "mail1"
    val mail2 = "mail2"

    when(componentRegistry.subscriptionRepository.retrieveAndDeleteAllChangeNotifications).thenReturn(allChanges)
    when(componentRegistry.subscriptionRepository.retrieveSubscriptionsForHandbook(handbook1.id.get)).thenReturn(List(mail1, mail2))
    when(componentRegistry.subscriptionRepository.retrieveSubscriptionsForHandbook(handbook2.id.get)).thenReturn(List(mail2))

    val expected = Map(mail1 -> changesForBook1, mail2 -> allChanges)
    val res = componentRegistry.subscriptionService.retrieveChangesForEmails

    assert(res(mail1).sortBy(_.id) === changesForBook1.sortBy(_.id))
    assert(res(mail2).sortBy(_.id) === allChanges.sortBy(_.id))*/
  }

  test("That we get an empty map if there are no notifications") {
    when(componentRegistry.subscriptionRepository.retrieveAndDeleteAllChangeNotifications).thenReturn(Nil)
    assert(componentRegistry.subscriptionService.retrieveChangesForEmails.isEmpty)
  }



}
