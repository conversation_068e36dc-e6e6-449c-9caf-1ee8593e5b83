package no.kf.handboker.integrationtest

import java.io.File

import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.scalatest.{FunSuite, PrivateMethodTester}

@RunWith(classOf[JUnitRunner])
class LocalHandbookTest extends FunSuite with FullStack with PrivateMethodTester {

  override def handbookConfigDir = new File("src/test/resources/integrationtest")

  val persistCentralHandbookVersion: PrivateMethod[(CentralHandbook, List[CentralChapter], List[CentralSection])] = PrivateMethod[(CentralHandbook, List[CentralChapter], List[CentralSection])]('persistCentralHandbookVersion)
  val synchronizeLocalHandbook = PrivateMethod[Unit]('synchronizeLocalHandbook)

  lazy val synchronizationService = componentRegistry.handbookSynchronizationService
  lazy val centralHandbookRepository = componentRegistry.centralHandbookRepository
  lazy val localHandbookService = componentRegistry.localHandbookService
  lazy val localHandbookRepository = componentRegistry.handbookRepository
  lazy val centralHandbookService = componentRegistry.centralHandbookService
  lazy val reportService = componentRegistry.reportService
  lazy val localHandbookVersionService = componentRegistry.localHandbookVersionService

  test("That we can create a local handbook chapter based on a central chapter") {
    testInTransaction {
      // Publish central centralHandbook
      val (centralHandbook: CentralHandbook, ch1: CentralChapter, ch2: CentralChapter, ch3: CentralChapter, ch21: CentralChapter, ch211: CentralChapter, s21: CentralSection, s22: CentralSection, s211: CentralSection, s23: CentralSection) = createHandbook
      assert(!centralHandbookRepository.isPublished(centralHandbook.id.get))
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "testbruker")

      // Store an unpublished chapter and section
      val ch22 = centralHandbookRepository.persistCentralChapter(CentralChapter(None, "ch22", ch2.id, centralHandbook.id.get, sortOrder = 1), "user")
      val s24 = centralHandbookRepository.persistCentralSection(CentralSection(None, "s24", ch2.id.get, centralHandbook.id.get, Some("s24html"), sortOrder = 2), "user")

      // Create local handbook, and create a central based chapter
      val localHandbook = localHandbookService.persistHandbook(Handbook(None, "lokalhb", None, "9900"))
      localHandbookService.persistChapterWithChildren(Chapter(None, ch2.title, ch2.id, centralHandbook.id, localHandbook.id.get, None, None), Option("<EMAIL>"))

      // Check result
      val localChapters = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters.size == 3)
      assert(List(ch2, ch21, ch211).map(c => c.title).toSet.subsetOf(localChapters.map(cc => cc.title).toSet))
      assert(List(ch2, ch21, ch211).map(c => c.id).toSet.subsetOf(localChapters.map(cc => cc.importedHandbookChapterId).toSet))
      assert(localChapters.find(c => c.title == ch2.title).get.sortOrder.get == 0)
      assert(localChapters.forall(c => c.updatedBy == Option("KF") && c.createdBy == Option("KF")))

      val localSections = localHandbookService.retrieveSectionsForHandbook(localHandbook.id.get)
      assert(localSections.size == 4)
      assert(List(s21, s22, s211, s23).map(c => c.title).toSet.subsetOf(localSections.map(cc => cc.title).toSet))
      assert(List(s21, s22, s211, s23).map(c => c.id).toSet.subsetOf(localSections.map(cc => cc.importedHandbookSectionId).toSet))
      assert(localSections.find(s => s.title == s21.title).get.sortOrder.get == s21.sortOrder)
      assert(localSections.find(s => s.title == s22.title).get.sortOrder.get == s22.sortOrder)
      assert(localSections.find(s => s.title == s23.title).get.sortOrder.get == s23.sortOrder)
      assert(localSections.find(s => s.title == s211.title).get.sortOrder.get == s211.sortOrder)

      // Unpublished chapters and sections must not be copied
      assert(!localSections.exists(s => s.title == s24.title))
      assert(!localChapters.exists(c => c.title == ch22.title))

      // Change central chapter and section. Check that local version is synchronized
      val lch2 = localChapters.find(c => c.title == "ch2").get
      centralHandbookRepository.persistCentralChapter(ch2.copy(title = "ch2 new title"), "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "testbruker")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localChapters2 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(localChapters2.exists(c => c.title == "ch2 new title" && c.id == lch2.id))

      // Delete central chapter. Check that chapter in pure local handbook is deleted
      centralHandbookService.deleteCentralChapter(ch2.id.get)
      synchronizationService invokePrivate persistCentralHandbookVersion(centralHandbook, "testbruker")
      synchronizationService invokePrivate synchronizeLocalHandbook(localHandbook.id.get, centralHandbook.id.get)
      val localChapters3 = localHandbookService.retrieveChaptersForHandbook(localHandbook.id.get)
      assert(!localChapters3.exists(c => c.id == lch2.id))
    }
  }

  test("That retrieving lists of chapter and section versions gives correct result") {
    testInTransaction {
      val hb = localHandbookService.persistHandbook(Handbook(None, "hb", None, "9900"))
      Thread.sleep(10)
      val c1 = localHandbookService.persistChapter(Chapter(None, "c1", None, None, hb.id.get, None, None))
      Thread.sleep(10)
      val s1 = localHandbookService.persistSection(Section(None, "c1s1", Some("text"), None, None, hb.id.get, c1.id.get, None), Some("user"))
      Thread.sleep(10)
      val c2 = localHandbookService.persistChapter(Chapter(None, "c2", None, None, hb.id.get, None, None))
      Thread.sleep(10)
      val c3 = localHandbookService.persistChapter(Chapter(None, "c3", None, None, hb.id.get, None, None))
      Thread.sleep(10)
      val s2 = localHandbookService.persistSection(Section(None, "c3s1", Some("text s2"), None, None, hb.id.get, c3.id.get, None), Some("user"))
      val s3 = localHandbookService.persistSection(Section(None, "c3s2", Some("text s3"), None, None, hb.id.get, c3.id.get, None), Some("user"))

      val list1 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, Some(DateTime.now()))
      assert(list1._2.nonEmpty)
      assert(list1._1.nonEmpty)
      assert(list1._1.size == 3)
      assert(list1._2.size == 3)

      val list1b = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, c1.updatedDate)
      assert(list1b._2.isEmpty)
      assert(list1b._1.nonEmpty)
      assert(list1b._1.size == 1)

      val list2 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, Some(DateTime.now().minusDays(1)))
      assert(list2._2.isEmpty)
      assert(list2._1.isEmpty)

      val s1v1 = localHandbookService.persistSection(s1.copy(title = "c1s1*"), Some("user"))
      val list3 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, s1v1.updatedDate)
      assert(list3._2.nonEmpty)
      assert(list3._1.nonEmpty)
      assert(list3._1.size == 3)
      assert(list3._2.size == 3)
      assert(list3._2.exists(s => s.title == "c1s1*"))
      assert(list3._2.head.text.isDefined)

      val list4 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, c3.updatedDate)
      assert(list4._2.nonEmpty)
      assert(list4._1.nonEmpty)
      assert(list4._1.size == 3)
      assert(list4._2.size == 1)
      assert(list4._2.head.title == "c1s1")
      assert(list4._2.head.text.isDefined)

      localHandbookService.deleteSection(s1.id.get)
      val list5 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, c3.updatedDate)
      assert(list5._2.nonEmpty)
      assert(list5._1.nonEmpty)
      assert(list5._1.size == 3)
      assert(list5._2.size == 1)
      assert(list5._2.head.title == "c1s1")
      assert(list4._2.head.text.isDefined)

      val list6 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, Some(DateTime.now()))
      assert(list6._2.nonEmpty)
      assert(list6._2.size == 2)
      assert(list6._1.nonEmpty)
      assert(list6._1.size == 3)

      val c2v1 = localHandbookService.persistChapter(c2.copy(title = "c2*"))
      val list7 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, Some(DateTime.now()))
      assert(list7._2.nonEmpty)
      assert(list7._2.size == 2)
      assert(list7._1.nonEmpty)
      assert(list7._1.size == 3)
      assert(list7._1.exists(c => c.title == "c2*"))

      val list8 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, c3.updatedDate)
      assert(list8._1.nonEmpty)
      assert(list8._1.size == 3)
      assert(list8._1.exists(c => c.title == "c2"))
      assert(list8._2.nonEmpty)
      assert(list8._2.size == 1)

      val t1 = DateTime.now()
      Thread.sleep(5)
      localHandbookService.deleteChapter(c3.id.get)
      val list9 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, Some(DateTime.now()))
      assert(list9._1.nonEmpty)
      assert(list9._1.size == 2)
      assert(list9._1.exists(c => c.title == "c2*"))
      assert(!list9._1.exists(c => c.title == "c3"))
      assert(list9._2.isEmpty)

      val list10 = localHandbookService.retrieveChapterAndSectionVersions(hb.id.get, Some(t1))
      assert(list10._1.nonEmpty)
      assert(list10._1.size == 3)
      assert(list10._1.exists(c => c.title == "c1"))
      assert(list10._1.exists(c => c.title == "c2*"))
      assert(list10._1.exists(c => c.title == "c3"))
      assert(list10._2.nonEmpty)
      assert(list10._2.size == 2)
      assert(list10._2.head.text.isDefined)

    }
  }

  private def createHandbook = {
    val repository = componentRegistry.centralHandbookRepository
    val handbook = repository.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
    val ch1 = repository.persistCentralChapter(CentralChapter(None, "ch1", None, handbook.id.get), "user")
    val ch2 = repository.persistCentralChapter(CentralChapter(None, "ch2", None, handbook.id.get), "user")
    val ch3 = repository.persistCentralChapter(CentralChapter(None, "ch3", None, handbook.id.get), "user")
    val ch21 = repository.persistCentralChapter(CentralChapter(None, "ch21", ch2.id, handbook.id.get), "user")
    val ch211 = repository.persistCentralChapter(CentralChapter(None, "ch211", ch21.id, handbook.id.get), "user")

    val s21 = repository.persistCentralSection(CentralSection(None, "s21", ch2.id.get, handbook.id.get, Some("s21html")), "user")
    val s22 = repository.persistCentralSection(CentralSection(None, "s22", ch2.id.get, handbook.id.get, Some("s22html")), "user")
    val s23 = repository.persistCentralSection(CentralSection(None, "s23", ch2.id.get, handbook.id.get, Some("s23html")), "user")
    val s211 = repository.persistCentralSection(CentralSection(None, "s211", ch21.id.get, handbook.id.get, Some("s211html")), "user")
    (handbook, ch1, ch2, ch3, ch21, ch211, s21, s22, s211, s23)
  }


}
