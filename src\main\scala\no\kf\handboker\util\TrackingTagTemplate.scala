package no.kf.handboker.util

object TrackingTagTemplate {

  def get(server: String, container: String): String = {
    s"""<!-- Matomo Tag Manager -->
       |var _mtm = window._mtm = window._mtm || [];
       |_mtm.push({'mtm.startTime': (new Date().getTime()), 'event': 'mtm.Start'});
       |var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
       |g.type='text/javascript'; g.async=true; g.src='https://${server}/js/${container}.js'; s.parentNode.insertBefore(g,s);
       |<!-- End Matomo Tag Manager -->""".stripMargin
  }
}
