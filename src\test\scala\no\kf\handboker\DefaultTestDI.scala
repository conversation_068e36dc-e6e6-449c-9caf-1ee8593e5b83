package no.kf.handboker

import java.io.File

import no.kf.config.Settings
import no.kf.db.{DbConnectionManager, ExportAndDeleteRepository}
import no.kf.handboker.service.handbookdownload.ReportService
import no.kf.handboker.repository.{CentralHandbookRepository, CentralNotificationRepository, HandbookLinkRepository, HandbookRepository, LocalHandbookVersionRepository, PublicationRepository, ReadingLinkRepository, SubscriptionRepository}
import no.kf.handboker.service._
import no.kf.handboker.service.api.HandbookApiService
import no.kf.handboker.service.search.{SearchIndexBuilderService, SearchIndexService, SearchService}
import no.kf.test.TestResourceUtil
import org.scalatest.mockito.MockitoSugar

trait DefaultTestDI extends TestResourceUtil {

  class DefaultTestRegistry extends ComponentRegistry with MockitoSugar {
    val dir = new File(getResourcePath("handboker.properties")).getParent
    log.info(getResourcePath("handboker.properties"))
    log.info(new File(getResourcePath("handboker.properties")).getAbsolutePath)

    override lazy val settings: Settings = new AppSettings(dir)
    override lazy val connectionManager: DbConnectionManager = mock[DbConnectionManager]
    override lazy val ldapService: LDAPService = mock[LDAPService]
    override lazy val healthCheckService: HealthCheckService = mock[HealthCheckService]
    override lazy val localHandbookService: LocalHandbookService = mock[LocalHandbookService]
    override lazy val centralHandbookService: CentralHandbookService = mock[CentralHandbookService]
    override lazy val subscriptionService: SubscriptionService = mock[SubscriptionService]
    override lazy val subscriptionRepository: SubscriptionRepository = mock[SubscriptionRepository]
    override lazy val centralAccessService: CentralAccessService = mock[CentralAccessService]
    override lazy val handbookRepository: HandbookRepository = mock[HandbookRepository]
    override lazy val centralHandbookRepository: CentralHandbookRepository = mock[CentralHandbookRepository]
    override lazy val centralNotificationRepository: CentralNotificationRepository = mock[CentralNotificationRepository]
    override lazy val handbookSynchronizationService: HandbookSynchronizationService = mock[HandbookSynchronizationService]
    override lazy val imageService: ImageService = mock[ImageService]
    override lazy val fileService: FileService = mock[FileService]
    override lazy val mailService: MailService = mock[MailService]
    override lazy val searchService: SearchService = mock[SearchService]
    override lazy val searchIndexService: SearchIndexService = mock[SearchIndexService]
    override lazy val searchIndexBuilderService: SearchIndexBuilderService = mock[SearchIndexBuilderService]
    override lazy val handbookApiService: HandbookApiService = mock[HandbookApiService]
    override lazy val exportAndDeleteRepository: ExportAndDeleteRepository = mock[ExportAndDeleteRepository]
    override lazy val handbookLinkRepository: HandbookLinkRepository = mock[HandbookLinkRepository]
    override lazy val handbookLinkService: HandbookLinkService = mock[HandbookLinkService]
    override lazy val reportService: ReportService = mock[ReportService]
    override lazy val publicationRepository: PublicationRepository = mock[PublicationRepository]
    override lazy val readingLinkRepository: ReadingLinkRepository = mock[ReadingLinkRepository]
    override lazy val localHandbookVersionRepository: LocalHandbookVersionRepository = mock[LocalHandbookVersionRepository]
  }

  val componentRegistry: ComponentRegistry = new DefaultTestRegistry

}
