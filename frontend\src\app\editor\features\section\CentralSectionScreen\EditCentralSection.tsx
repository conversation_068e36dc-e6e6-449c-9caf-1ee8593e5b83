import React from "react";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { CentralSectionForm } from "./CentralSectionForm";
import {
  useGetCentralSectionsQuery,
  useCreateCentralSectionMutation,
  useUpdateCentralSectionMutation,
  type UpdateCentralSectionRequest,
} from "@/store/services/handbook/centralHandbookApi";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useAppDispatch } from "@/store";
import { setSelectedCentralItem } from "@/store/slices/centralTreeSlice";
import type { CentralSection } from "@/types";
import { Spinner } from "@/shared/components/Spinner";

interface CentralSectionFormData {
  title: string;
  html: string;
}

export const EditCentralSection: React.FC = () => {
  const t = usePrefixedTranslation("editor.containers.EditSection");
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const dispatch = useAppDispatch();
  const { handbookId, sectionId } = useParams<{
    handbookId: string;
    sectionId?: string;
  }>();

  const parentId = searchParams.get("parent") || "";
  const isEditMode = Boolean(sectionId);

  const { data: sections = [] } = useGetCentralSectionsQuery();
  const [createSection, { isLoading: isCreating }] =
    useCreateCentralSectionMutation();
  const [updateSection, { isLoading: isUpdating }] =
    useUpdateCentralSectionMutation();

  const centralSection = isEditMode
    ? sections.find((section) => section.id === sectionId)
    : undefined;

  const isLoading = isCreating || isUpdating;

  if (isEditMode && !centralSection) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  const onSubmit = async (values: CentralSectionFormData) => {
    try {
      if (isEditMode && centralSection && centralSection.id) {
        const updatedSection: UpdateCentralSectionRequest = {
          ...centralSection,
          id: centralSection.id,
          title: values.title,
          html: values.html,
        };
        await updateSection(updatedSection).unwrap();
        toast.success(t("updateSuccess") || "Avsnitt oppdatert");

        // Update selected item in tree
        const updatedSectionForTree: CentralSection = {
          ...centralSection,
          title: values.title,
          html: values.html,
        };
        dispatch(setSelectedCentralItem(updatedSectionForTree));

        navigate(
          `/central-editor/${centralSection.centralHandbookId}/section/${centralSection.id}/`
        );
      } else {
        if (!handbookId || !parentId) {
          throw new Error("Missing required parameters for section creation");
        }

        const newSectionData = {
          centralHandbookId: handbookId,
          title: values.title,
          html: values.html,
          parentId: parentId,
        };
        const createdSection = await createSection(newSectionData).unwrap();
        toast.success(t("createSuccess") || "Avsnitt opprettet");

        // Update selected item in tree
        dispatch(setSelectedCentralItem(createdSection));

        navigate(`/central-editor/${handbookId}/section/${createdSection.id}/`);
      }
    } catch (error) {
      console.error("Failed to save section:", error);
      const errorMessage = isEditMode
        ? t("updateError") || "Feil ved oppdatering av avsnitt"
        : t("createError") || "Feil ved opprettelse av avsnitt";
      toast.error(errorMessage);
    }
  };

  const getAbortLink = (): string => {
    if (centralSection) {
      return `/central-editor/${centralSection.centralHandbookId}/section/${centralSection.id}/`;
    }
    return `/central-editor/${handbookId}/chapter/${parentId}`;
  };

  return (
    <CentralSectionForm
      centralSection={centralSection}
      onSubmit={onSubmit}
      abortLink={getAbortLink()}
      isLoading={isLoading}
    />
  );
};
