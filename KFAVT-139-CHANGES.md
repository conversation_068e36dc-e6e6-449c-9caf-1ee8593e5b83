# KFAVT-139: HB - Not correct change dates displayed in the change handling view

## Problem
In the KF Handbook Application, when users view the "merge" or "change handling" section, the date shown as the "last central change" for each chapter or section was incorrect. Instead of showing the date when that specific chapter or section was last changed in the central handbook, the application displayed the date of the most recent overall synchronization of the handbook.

## Solution
Added separate tracking for title and HTML update dates in the central handbook sections to provide accurate change tracking.

## Changes Made

### 1. Model Changes
- **File**: `src/main/scala/no/kf/handboker/model/central/CentralSection.scala`
- **Changes**: Added new fields to track title and HTML updates separately:
  - `titleUpdatedDate: Option[DateTime]`
  - `htmlUpdatedDate: Option[DateTime]`
  - `titleUpdatedBy: Option[String]`
  - `htmlUpdatedBy: Option[String]`

### 2. Service Layer Changes
- **File**: `src/main/scala/no/kf/handboker/service/CentralHandbookService.scala`
- **Changes**:
  - Updated `persistCentralSection` method to detect what changed (title vs HTML) and set appropriate update dates
  - Updated `convertToSection` method to map the new fields to local section fields
  - Added logic to preserve existing update dates when only one field changes

### 3. Repository Changes
- **File**: `src/main/scala/no/kf/handboker/repository/CentralHandbookRepository.scala`
- **Changes**:
  - Updated table definition to include new database columns
  - Updated `updateSection` and `insertSection` methods to handle new fields
  - Updated `populateCentralSection` method to read new fields from database

### 4. Database Migration
- **Files**: 
  - `src/main/resources/migrate-db-24.sql`
  - `src/main/resources/migrate-db-24-mssql.sql`
- **Changes**: Added migration scripts to add new columns to `central_handbooks.section` table
- **File**: `src/main/scala/no/kf/handboker/repository/DbConnectionManager.scala`
- **Changes**: Updated version number from 23 to 24

### 5. Test Updates
- **File**: `src/test/scala/no/kf/handboker/service/CentralHandbookServiceTest.scala`
- **Changes**: Updated test data to use new constructor parameters
- **File**: `src/test/scala/no/kf/handboker/service/CentralSectionUpdateTrackingTest.scala`
- **Changes**: Added new test file to verify the update tracking functionality

## API Impact
The existing REST endpoints (`get("/sections/")` and `get("/:handbookId/section/:id/latest/")`) will now return the new fields in the JSON response:
- `titleUpdatedDate`
- `htmlUpdatedDate`
- `titleUpdatedBy`
- `htmlUpdatedBy`

## Backward Compatibility
- The migration script initializes the new fields with existing values for backward compatibility
- Existing API consumers will continue to work, but can now access more granular update information
- The `updatedDate` field is still maintained for overall section updates

## Testing
- Added unit tests to verify that only changed fields get their update dates modified
- Existing tests updated to work with new constructor parameters
- Integration tests should verify that the merge view now shows correct dates

## Deployment Notes
1. Run database migration (version 24) before deploying the application
2. The migration will populate new fields with existing data
3. No downtime required as changes are backward compatible