import { useState, type RefObject } from "react";
import { 
  useGetPublicChapterAttachmentCountQuery,
  useGetPublicSectionAttachmentCountQuery 
} from "@/store/services/handbook/publicHandbookApi";

export interface UseAttachmentsReturn {
  showAttachments: boolean;
  attachmentCount: number;
  handleShowAttachments: (show: boolean) => void;
  handleClickOutside: (event: MouseEvent) => void;
}

export const useAttachments = (
  type: "chapter" | "section",
  id: string,
  wrapperRef: RefObject<HTMLDivElement | null>
): UseAttachmentsReturn => {
  const [showAttachments, setShowAttachments] = useState(false);

  const { data: chapterAttachmentCountData } = useGetPublicChapterAttachmentCountQuery(
    id,
    { skip: type !== "chapter" }
  );

  const { data: sectionAttachmentCountData } = useGetPublicSectionAttachmentCountQuery(
    id,
    { skip: type !== "section" }
  );

  const attachmentCount = type === "chapter" 
    ? (chapterAttachmentCountData?.count || 0)
    : (sectionAttachmentCountData?.count || 0);

  function handleShowAttachments(show: boolean) {
    setShowAttachments(show);
  }

  function handleClickOutside(event: MouseEvent) {
    if (
      wrapperRef.current &&
      event.target instanceof Node &&
      !wrapperRef.current.contains(event.target)
    ) {
      setShowAttachments(false);
    }
  }

  return {
    showAttachments,
    attachmentCount,
    handleShowAttachments,
    handleClickOutside,
  };
};