import React, { type ReactNode } from "react";
import { BaseErrorBoundary } from "./BaseErrorBoundary";
import { FeatureErrorFallback } from "./ErrorFallback";
import type {
  ErrorBoundaryProps,
  ErrorFallbackProps,
} from "@/shared/types/errorBoundary";

export class FeatureErrorBoundary extends BaseErrorBoundary {
  protected getErrorCategory():
    | "ui"
    | "api"
    | "auth"
    | "data"
    | "network"
    | "unknown" {
    const { error } = this.state;

    if (!error) return "unknown";

    const message = error.message.toLowerCase();

    if (
      message.includes("validation") ||
      message.includes("required") ||
      message.includes("invalid")
    ) {
      return "data";
    }

    if (
      message.includes("fetch") ||
      message.includes("api") ||
      message.includes("http")
    ) {
      return "api";
    }

    if (message.includes("network") || message.includes("timeout")) {
      return "network";
    }

    return "ui";
  }

  protected getErrorLevel(): "low" | "medium" | "high" | "critical" {
    const { error } = this.state;

    if (!error) return "low";

    const message = error.message.toLowerCase();

    if (message.includes("validation") || message.includes("required")) {
      return "low";
    }

    if (
      message.includes("fetch") ||
      message.includes("api") ||
      message.includes("network")
    ) {
      return "medium";
    }

    return "low";
  }

  protected renderFallback(props: ErrorFallbackProps): ReactNode {
    const featureName =
      this.props.name?.replace("Feature-", "") || "denne funksjonen";
    return <FeatureErrorFallback {...props} featureName={featureName} />;
  }
}

interface FeatureErrorBoundaryWrapperProps
  extends Omit<ErrorBoundaryProps, "level"> {
  featureName?: string;
}

export const FeatureErrorBoundaryWrapper: React.FC<
  FeatureErrorBoundaryWrapperProps
> = ({ featureName, ...props }) => (
  <FeatureErrorBoundary
    {...props}
    level="feature"
    name={featureName ? `Feature-${featureName}` : "FeatureErrorBoundary"}
    showToast={true}
  />
);
