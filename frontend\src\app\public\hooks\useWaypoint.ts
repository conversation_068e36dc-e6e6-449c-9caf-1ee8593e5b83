import React, { useEffect, useRef, useCallback } from 'react';

interface UseWaypointOptions {
  onEnter: () => void;
  onLeave: () => void;
  rootMargin?: number;
  disabled?: boolean;
  triggerOffset?: number;
}

export const useWaypoint = ({
  onEnter,
  onLeave,
  rootMargin = 0,
  disabled = false,
  triggerOffset = 0
}: UseWaypointOptions) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const isInsideRef = useRef(false);

  const checkPosition = useCallback(() => {
    if (disabled || !elementRef.current) return;

    const element = elementRef.current;
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    
    // More precise legacy-like detection
    // Element is visible when any part is in viewport, with optional margins
    const elementTop = rect.top;
    const elementBottom = rect.bottom;
    const viewportTop = rootMargin;
    const viewportBottom = windowHeight - rootMargin;
    
    // Legacy react-waypoint behavior: element is inside when:
    // - Top edge is above viewport bottom AND bottom edge is below viewport top
    // - But apply triggerOffset to the viewport, not the element
    const effectiveViewportTop = viewportTop + triggerOffset;
    const effectiveViewportBottom = viewportBottom - triggerOffset;
    
    const isVisible = elementTop < effectiveViewportBottom && elementBottom > effectiveViewportTop;
    
    if (isVisible && !isInsideRef.current) {
      isInsideRef.current = true;
      onEnter();
    } else if (!isVisible && isInsideRef.current) {
      isInsideRef.current = false;
      onLeave();
    }
  }, [disabled, onEnter, onLeave, rootMargin, triggerOffset]);

  useEffect(() => {
    if (disabled) {
      isInsideRef.current = false;
      return;
    }

    checkPosition();

    const handleScroll = () => checkPosition();
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleScroll);
    };
  }, [checkPosition, disabled]);

  return elementRef;
};

export interface WaypointProps {
  onEnter: () => void;
  onLeave: () => void;
  children: React.ReactNode;
  disabled?: boolean;
  rootMargin?: number;
  triggerOffset?: number;
}

export const Waypoint: React.FC<WaypointProps> = ({ 
  children, 
  onEnter, 
  onLeave, 
  disabled = false,
  rootMargin = 0,
  triggerOffset = 0
}) => {
  const ref = useWaypoint({ 
    onEnter, 
    onLeave, 
    disabled, 
    rootMargin,
    triggerOffset 
  });

  return React.createElement('div', { ref }, children);
};