import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';
import type { LocalTreeNode } from '@/types';

interface LocalTreeState {
  selectedItem: LocalTreeNode | null;
}

const initialState: LocalTreeState = {
  selectedItem: null,
};

const localTreeSlice = createSlice({
  name: 'localTree',
  initialState,
  reducers: {
    selectLocalItem: (state, action: PayloadAction<LocalTreeNode | null>) => {
      state.selectedItem = action.payload;
    },
    clearLocalSelection: (state) => {
      state.selectedItem = null;
    },
  },
});

export const { selectLocalItem, clearLocalSelection } = localTreeSlice.actions;

// Selectors
export const selectSelectedLocalItem = (state: RootState): LocalTreeNode | null =>
  state.localTree?.selectedItem || null;

export default localTreeSlice.reducer;