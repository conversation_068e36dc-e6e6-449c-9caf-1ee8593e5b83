create table handbook_local_editor(id VARCHAR(37), handbook_id VARCHAR(37) NOT NULL, rights_holder varchar(100) NOT NULL, added_by varchar(100), added_date bigint, PRIMARY KEY(id))
ALTER TABLE handbook_local_editor ADD CONSTRAINT fk_handbook_local_editor FOREI<PERSON><PERSON> KEY (handbook_id) REFERENCES handbook(id)
ALTER TABLE handbook_local_editor ADD CONSTRAINT uc_handbook_local_editor UNIQUE (handbook_id, rights_holder)
