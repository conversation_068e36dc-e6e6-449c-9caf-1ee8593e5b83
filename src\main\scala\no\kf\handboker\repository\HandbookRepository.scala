package no.kf.handboker.repository

import no.kf.db.IDGenerator
import no.kf.db.RichSQL._
import no.kf.exception.UserFriendlyException
import no.kf.handboker.model.local.{Chapter, Comment, Handbook, LocalEditor, Section}
import no.kf.util.Logging
import org.joda.time.DateTime

import scala.annotation.tailrec

trait HandbookRepositoryComponent {
  this: DbConnectionManagerComponent
    with LocalHandbookVersionRepositoryComponent
  =>

  val handbookRepository: HandbookRepository

  object HandbookTableDef {
    val tableName = "handbook"

    val fieldId = "id"
    val fieldTitle = "title"
    val fieldImportedHandbookId = "importedhandbook_id"
    val fieldExternalOrgId = "external_org_id"
    val fieldLocalChange = "local_change"
    val fieldPendingChanges = "pending_change"
    val fieldPendingDeletion = "pending_deletion"
    val fieldIsPublic = "is_public"
    val fieldPendingChangesUpdatedDate = "pending_change_updated_date"
    val fieldUpdatedDate = "updated_date"
    val fieldCreatedDate = "created_date"
    val fieldUpdatedBy = "updated_by"
    val fieldCreatedBy = "created_by"
    val fieldDeleted = "deleted"
    val fieldDeletedDate = "deleted_date"

    val nonIdOrDeleteFields = List(fieldTitle, fieldImportedHandbookId, fieldExternalOrgId, fieldLocalChange, fieldIsPublic, fieldPendingChanges, fieldPendingDeletion, fieldPendingChangesUpdatedDate, fieldUpdatedDate, fieldUpdatedBy)
    val nonDeleteFields = fieldId :: nonIdOrDeleteFields ++ List(fieldCreatedDate, fieldCreatedBy)
    val nonDeleteFieldsNoId = nonIdOrDeleteFields ++ List(fieldCreatedDate, fieldCreatedBy)
    val allFields = nonDeleteFields ++ List(fieldDeleted, fieldDeletedDate)
  }

  object HandbookChapterTableDef {
    val tableName = "handbookchapter"

    val fieldId = "id"
    val fieldTitle = "title"
    val fieldImportedHandbookChapterId = "importedhandbookchapter_id"
    val fieldImportedHandbookId = "importedhandbook_id"
    val fieldHandbookId = "handbook_id"
    val fieldParentChapterId = "parent_chapter_id"
    val fieldOrderindex = "orderindex"
    val fieldLocalChange = "local_change"
    val fieldPendingChanges = "pending_change"
    val fieldPendingDeletion = "pending_deletion"
    val fieldPendingChangesUpdatedDate = "pending_change_updated_date"
    val fieldUpdatedDate = "updated_date"
    val fieldLocalChapterUpdatedDate = "local_chapter_updated_date"
    val fieldCreatedDate = "created_date"
    val fieldUpdatedBy = "updated_by"
    val fieldCreatedBy = "created_by"
    val fieldDeleted = "deleted"
    val fieldDeletedDate = "deleted_date"

    val nonIdOrDeleteFields = List(fieldTitle, fieldImportedHandbookChapterId, fieldImportedHandbookId, fieldHandbookId, fieldParentChapterId, fieldOrderindex, fieldLocalChange, fieldPendingChanges, fieldPendingDeletion, fieldPendingChangesUpdatedDate, fieldUpdatedDate, fieldLocalChapterUpdatedDate, fieldUpdatedBy)
    val nonDeleteFields = fieldId :: nonIdOrDeleteFields ++ List(fieldCreatedDate, fieldCreatedBy)
    val allFields = nonDeleteFields ++ List(fieldDeleted, fieldDeletedDate)
  }

  object HandbookSectionTableDef {
    val tableName = "handbooksection"

    val fieldId = "id"
    val fieldTitle = "title"
    val fieldHtml = "html"
    val fieldImportedHandbookSectionId = "importedhandbooksection_id"
    val fieldImportedHandbookId = "importedhandbook_id"
    val fieldHandbookId = "handbook_id"
    val fieldParentChapterId = "parent_chapter_id"
    val fieldOrderindex = "orderindex"
    val fieldLocalTitleChange = "local_title_change"
    val fieldPendingTitleChange = "pending_title_change"
    val fieldLocalTextChange = "local_text_change"
    val fieldPendingTextChange = "pending_text_change"
    val fieldPendingDeletion = "pending_deletion"
    val fieldPendingChangesUpdatedDate = "pending_change_updated_date"
    val fieldCreatedDate = "created_date"
    val fieldUpdatedDate = "updated_date"
    val fieldTextUpdatedDate = "text_updated_date"
    val fieldUpdatedBy = "updated_by"
    val fieldTextUpdatedBy = "text_updated_by"
    val fieldCreatedBy = "created_by"
    val fieldDeleted = "deleted"
    val fieldDeletedDate = "deleted_date"

    val nonIdOrDeleteFields = List(fieldTitle, fieldHtml, fieldImportedHandbookSectionId, fieldImportedHandbookId, fieldHandbookId, fieldParentChapterId, fieldOrderindex, fieldLocalTitleChange, fieldPendingTitleChange, fieldLocalTextChange, fieldPendingTextChange, fieldPendingDeletion, fieldPendingChangesUpdatedDate, fieldUpdatedDate, fieldTextUpdatedDate, fieldUpdatedBy, fieldTextUpdatedBy)
    val nonDeleteFields = fieldId :: nonIdOrDeleteFields ++ List(fieldCreatedDate, fieldCreatedBy)
    val noHtmlField = nonDeleteFields.filterNot(_.matches(s"$fieldHtml"))
    val allFields = nonDeleteFields ++ List(fieldDeleted, fieldDeletedDate)
  }

  object HandbookCommentTableDef {
    val tableName = "handbook_comment"

    val fieldId = "id"
    val fieldText = "text"
    val fieldEditedBy = "edited_by"
    val fieldEditedDate = "edited_date"
    val fieldHandbookId = "handbook_id"

    val updateFields = List(fieldText, fieldEditedBy, fieldEditedDate)
    val allFields = List(fieldId, fieldText, fieldEditedBy, fieldEditedDate, fieldHandbookId)
  }

  object HandbookLocalEditorTableDef {
    val tableName = "handbook_local_editor"

    val fieldId = "id"
    val fieldHandbookId = "handbook_id"
    val fieldRightsHolder = "rights_holder"
    val fieldAddedBy = "added_by"
    val fieldAddedDate = "added_date"

    val allFields = List(fieldId, fieldHandbookId, fieldRightsHolder, fieldAddedBy, fieldAddedDate)
  }

  class HandbookRepositoryImpl extends HandbookRepository {
    override def persistHandbook(handbook: Handbook): Handbook = {
      handbook.id match {
        case None => insertHandbook(handbook)
        case Some(_) => updateHandbook(handbook)
      }
    }

    override def retrieveHandbook(id: String): Option[Handbook] = {
      val select = selectString(HandbookTableDef.tableName, HandbookTableDef.fieldId, HandbookTableDef.nonDeleteFields, HandbookTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << id <<! populateHandbook
      }.headOption
    }

    override def retrieveExternalOrganizationIdForHandbook(handbookId: String): String = {
      val select = selectString(HandbookTableDef.tableName, HandbookTableDef.fieldId, List(HandbookTableDef.fieldExternalOrgId), HandbookTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << handbookId <<# populateString
      }.get
    }

    override def retrieveHandbooksForExternalOrganization(externalOrgId: String): List[Handbook] = {
      val select = selectString(HandbookTableDef.tableName, HandbookTableDef.fieldExternalOrgId, HandbookTableDef.nonDeleteFields, HandbookTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << externalOrgId <<! populateHandbook
      }.toList
    }

    override def retrieveHandbooksForExternalOrganizationRestricted(externalOrgId: String, userEmail: String): List[Handbook] = {
      import HandbookTableDef._
      val select = s"SELECT ${tableName}.$fieldId, ${nonDeleteFieldsNoId.mkString(",")} " +
        s"FROM ${tableName} " +
        s"INNER JOIN ${HandbookLocalEditorTableDef.tableName} " +
        s"ON ${tableName}.${fieldId} = ${HandbookLocalEditorTableDef.tableName}.${HandbookLocalEditorTableDef.fieldHandbookId} " +
        s"WHERE $fieldDeleted = 0 AND ${tableName}.${fieldExternalOrgId} = ? " +
        s"AND ${HandbookLocalEditorTableDef.tableName}.${HandbookLocalEditorTableDef.fieldRightsHolder} = ?"
      connectionManager.doWithConnection {
        _.ps(select) << externalOrgId << userEmail <<! populateHandbook
      }.toList
    }

    override def deleteHandbook(id: String) = {
      val deleteComments = s"DELETE FROM ${HandbookCommentTableDef.tableName} WHERE ${HandbookCommentTableDef.fieldHandbookId} = ?"
      connectionManager.doWithConnection {
        _.ps(deleteComments) << id <<!
      }
      val timestamp = Some(DateTime.now)
      val deleteSections = deleteString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldDeleted, HandbookSectionTableDef.fieldHandbookId, HandbookSectionTableDef.fieldDeletedDate)
      connectionManager.doWithConnection {
        _.ps(deleteSections) << timestamp << id <<!
      }
      val deleteChapters = deleteString(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldDeleted, HandbookChapterTableDef.fieldHandbookId, HandbookChapterTableDef.fieldDeletedDate)
      connectionManager.doWithConnection {
        _.ps(deleteChapters) << timestamp << id <<!
      }
      val deleteHandbook = deleteString(HandbookTableDef.tableName, HandbookTableDef.fieldDeleted, HandbookTableDef.fieldId, HandbookTableDef.fieldDeletedDate)
      connectionManager.doWithConnection {
        _.ps(deleteHandbook) << timestamp << id <<!
      }
    }

    override def retrieveChaptersForHandbook(handbookId: String): List[Chapter] = {
      val select = selectString(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldHandbookId, HandbookChapterTableDef.nonDeleteFields, HandbookChapterTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << handbookId <<! populateChapter
      }.toList
    }

    override def retrieveSectionsForHandbook(handbookId: String): List[Section] = {
      val select = selectString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldHandbookId, HandbookSectionTableDef.nonDeleteFields, HandbookSectionTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << handbookId <<! populateSection
      }.toList
    }

    override def retrieveSectionsWithoutTextForHandbook(handbookId: String): List[Section] = {
      val select = selectString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldHandbookId, HandbookSectionTableDef.noHtmlField, HandbookSectionTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << handbookId <<! populateSectionWithoutText
      }.toList
    }

    override def retrieveAllExternalOrgIds(): List[String] = {
      val select = s"SELECT DISTINCT ${HandbookTableDef.fieldExternalOrgId} FROM ${HandbookTableDef.tableName} WHERE ${HandbookTableDef.fieldDeleted} = 0"
      connectionManager.doWithConnection {
        _.ps(select) <<! populateString
      }.toList
    }

    override def retrieveChildren(sectionOrChapterId: String, withHtml: Boolean = false): (List[Section], List[Chapter]) = {
      val sectionSelect = s"SELECT ${if (withHtml) HandbookSectionTableDef.nonDeleteFields.mkString(",") else HandbookSectionTableDef.noHtmlField.mkString(",")} FROM ${HandbookSectionTableDef.tableName} WHERE ${HandbookSectionTableDef.fieldParentChapterId} = ?"
      val chapterSelect = s"SELECT ${HandbookChapterTableDef.nonDeleteFields.mkString(",")} FROM ${HandbookChapterTableDef.tableName} WHERE ${HandbookChapterTableDef.fieldParentChapterId} = ?"

      val sections = connectionManager.doWithConnection {
        _.ps(sectionSelect) << sectionOrChapterId <<! (if (withHtml) populateSection else populateSectionWithoutText)
      }
      val chapters = connectionManager.doWithConnection {
        _.ps(chapterSelect) << sectionOrChapterId <<! populateChapter
      }

      (sections, chapters)
    }

    def insertHandbook(handbook: Handbook): Handbook = {
      val insert = insertString(HandbookTableDef.tableName, HandbookTableDef.allFields)
      val timestamp = Some(DateTime.now)
      val newId = IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(insert) <<
          newId <<
          handbook.title <<
          handbook.importedHandbookId <<
          handbook.externalOrgId <<
          handbook.localChange <<
          handbook.isPublic <<
          handbook.pendingChange <<
          handbook.pendingDeletion <<
          timestamp <<
          timestamp <<
          handbook.updatedBy <<
          timestamp <<
          handbook.createdBy <<
          false <<
          0 <<!
      }
      handbook.copy(id = Option(newId), pendingChangeUpdatedDate = timestamp, updatedDate = timestamp, createdDate = timestamp)
    }


    def updateHandbook(handbook: Handbook): Handbook = {
      val update = updateString(HandbookTableDef.tableName, HandbookTableDef.nonIdOrDeleteFields, HandbookTableDef.fieldId)
      val timestamp = Some(DateTime.now)
      connectionManager.doWithConnection {
        _.ps(update) <<
          handbook.title <<
          handbook.importedHandbookId <<
          handbook.externalOrgId <<
          handbook.localChange <<
          handbook.isPublic <<
          handbook.pendingChange <<
          handbook.pendingDeletion <<
          handbook.pendingChangeUpdatedDate <<
          timestamp <<
          handbook.updatedBy <<
          handbook.id <<!
      }
      handbook.copy(updatedDate = timestamp)
    }

    override def persistChapter(chapter: Chapter, computeSortOrder: Boolean = true): Chapter = {
      // Make sure we haven't set just one of the imported fields
      if (List(chapter.importedHandbookChapterId, chapter.importedHandbookId).count(_.isDefined) == 1) {
        throw new RuntimeException(s"Must define neither or both imported handbook fields. importedHandbookChapterId = ${chapter.importedHandbookChapterId} and importedHandbookId = ${chapter.importedHandbookId}")
      }

      chapter.id match {
        case None => insertChapter(chapter, computeSortOrder)
        case Some(_) => updateChapter(chapter)
      }
    }

    override def retrieveChapter(id: String): Option[Chapter] = {
      val select = selectString(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldId, HandbookChapterTableDef.nonDeleteFields, HandbookChapterTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << id <<! populateChapter
      }.headOption
    }

    override def deleteChapter(id: String) = {
      val deleteBaseChapter = deleteString(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldDeleted, HandbookChapterTableDef.fieldId, HandbookChapterTableDef.fieldDeletedDate)
      val timestamp = DateTime.now
      connectionManager.doWithConnection {
        _.ps(deleteBaseChapter) << timestamp << id <<!
      }

      //Delete all child-chapters by deleting as long as there are chapters that have deleted parents.
      val deleteChapters = deleteIfParentChapterDeletedString(HandbookChapterTableDef.tableName, HandbookChapterTableDef.fieldDeleted, HandbookChapterTableDef.fieldParentChapterId, HandbookChapterTableDef.fieldDeletedDate, timestamp)

      def deleteAndCount = connectionManager.doWithConnection {
        _.ps(deleteChapters).executeUpdate
      }

      while (deleteAndCount > 0) {
        //No operation here except logging. The while-statement itself executes deleteAndCount until no more chapters need to be deleted.
        log.trace("Deleting sub-chapters...")
      }

      //Delete all sections that now have a deleted parent. Sections aren't nested, so we only need to do this once.
      val deleteSections = deleteIfParentChapterDeletedString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldDeleted, HandbookSectionTableDef.fieldParentChapterId, HandbookChapterTableDef.fieldDeletedDate, timestamp)
      connectionManager.doWithConnection {
        _.ps(deleteSections) <<!
      }
    }

    def insertChapter(chapter: Chapter, computeSortOrder: Boolean = true): Chapter = {
      val insert = insertString(HandbookChapterTableDef.tableName, HandbookChapterTableDef.allFields)
      val newId = IDGenerator.generateUniqueId
      val timestamp = Some(DateTime.now)
      val sortOrder = if (computeSortOrder) {
        retrieveLastSortindexForParent(chapter)
      } else {
        chapter.sortOrder.get
      }

      connectionManager.doWithConnection {
        _.ps(insert) <<
          newId <<
          chapter.title <<
          chapter.importedHandbookChapterId <<
          chapter.importedHandbookId <<
          chapter.handbookId <<
          chapter.parentId <<
          sortOrder <<
          chapter.localChange <<
          chapter.pendingChange <<
          chapter.pendingDeletion <<
          timestamp <<
          timestamp << // updated_date
          timestamp << // local_chapter_updated_date
          chapter.updatedBy <<
          timestamp << // created_date
          chapter.createdBy <<
          false <<
          0 <<!
      }
      chapter.copy(id = Option(newId), pendingChangeUpdatedDate = timestamp, updatedDate = timestamp, localChapterUpdatedDate = timestamp, createdDate = timestamp, sortOrder = Some(sortOrder))
    }

    def retrieveLastSortindexForParent(chapter: Chapter): Int = {
      if (chapter.parentId.isEmpty) retrieveLatestIndexForHandbookRootItems(chapter.handbookId) else retrieveLatestIndexForChapterItems(chapter.parentId.get)
    }


    private def retrieveLatestIndexForHandbookRootItems(handbookId: String): Int = {
      import HandbookChapterTableDef._

      val sql = s"SELECT MAX($fieldOrderindex), COUNT(*) FROM $tableName WHERE $fieldHandbookId = ? AND $fieldParentChapterId IS NULL"
      val (max, count) = connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<# populateMaxCount
      }.get

      if (count == 0) {
        count
      } else {
        max + 1
      }
    }

    private def retrieveLatestIndexForChapterItems(parentId: String): Int = {
      import HandbookChapterTableDef._
      import HandbookSectionTableDef.{fieldParentChapterId => sectionParentId, fieldOrderindex => sectionSortOrder, tableName => sectionTableName}

      val sql = s"SELECT MAX($fieldOrderindex), COUNT(*) FROM (" +
        s"SELECT $fieldOrderindex FROM $tableName WHERE $fieldParentChapterId = ? " +
        s"UNION SELECT $sectionSortOrder FROM $sectionTableName WHERE $sectionParentId = ?" +
        ") as subquery"
      val (max, count) = connectionManager.doWithConnection {
        _.ps(sql) << parentId << parentId <<# populateMaxCount
      }.get

      if (count == 0) {
        count
      } else {
        max + 1
      }
    }

    private def populateMaxCount(rs: RichResultSet): (Int, Int) = (rs, rs)

    def updateChapter(chapterToPersist: Chapter): Chapter = updateChapter(chapterToPersist, calculateAndPersistMovementToNewHandbook = true)

    private def updateChapter(chapterToPersist: Chapter, calculateAndPersistMovementToNewHandbook: Boolean): Chapter = {
      val chapterId = chapterToPersist.id.getOrElse {
        log.error(s"Attempted to update chapter without ID: ${chapterToPersist.title}")
        throw new IllegalArgumentException("Cannot update chapter: missing chapter ID")
      }

      val oldChapter = retrieveChapter(chapterId).getOrElse {
        log.error(s"Attempted to update non-existent chapter: $chapterId")
        throw new IllegalStateException(s"Cannot update chapter $chapterId: the chapter is already deleted in the database.")
      }

      // If the parentId of a chapter changes. Make sure it is a valid move
      if (oldChapter.parentId != chapterToPersist.parentId) {

        // We should not be able to set the chapter as a direct child of it self
        if (chapterToPersist.parentId == chapterToPersist.id) {
          throw new UserFriendlyException("Cannot set a chapter as a child of itself")
        }

        // Traverse up along all the way to the root chapter, if we the same chapter we are updating in that path we are creating an illegal cycle
        @tailrec
        def isDescendantOfItself(parentId: Option[String]): Boolean = {
          val parent = parentId.flatMap(retrieveChapter(_))
          if (parent.exists(_.id == chapterToPersist.id)) {
            return true
          } else if (parent.isEmpty) {
            return false
          }
          isDescendantOfItself(parent.get.parentId)
        }

        if (isDescendantOfItself(chapterToPersist.parentId)) {
          throw new UserFriendlyException("Cannot set a chapter as a descendant of itself")
        }
      }

      val update = updateString(HandbookChapterTableDef.tableName, HandbookChapterTableDef.nonIdOrDeleteFields, HandbookChapterTableDef.fieldId)
      val timestamp = Some(DateTime.now)
      connectionManager.doWithConnection {
        _.ps(update) <<
          chapterToPersist.title <<
          chapterToPersist.importedHandbookChapterId <<
          chapterToPersist.importedHandbookId <<
          chapterToPersist.handbookId <<
          chapterToPersist.parentId <<
          chapterToPersist.sortOrder <<
          chapterToPersist.localChange <<
          chapterToPersist.pendingChange <<
          chapterToPersist.pendingDeletion <<
          chapterToPersist.pendingChangeUpdatedDate <<
          timestamp <<
          chapterToPersist.localChapterUpdatedDate << // Keep existing localChapterUpdatedDate value
          chapterToPersist.updatedBy <<
          chapterToPersist.id <<!
      }

      // If we moved the chapter to another handbook, we need to move all it's descendants as well
      if (oldChapter.handbookId != chapterToPersist.handbookId && calculateAndPersistMovementToNewHandbook) {

        //Chapters first
        val allChaptersInOldHandbook = retrieveDeletedAndNotDeletedChaptersForHandbook(oldChapter.handbookId)
        var moved: Set[Chapter] = Set(oldChapter)
        var tryToAddMore: Boolean = true
        while (tryToAddMore) {
          val candidates = allChaptersInOldHandbook.filterNot(otherChapter => moved.exists(_.id == otherChapter.id)).filter(otherChapter => moved.exists(_.id == otherChapter.parentId)).toSet
          tryToAddMore = candidates.size > 0
          moved = moved ++ candidates
        }
        moved = moved - oldChapter
        moved = moved.map(_.copy(handbookId = chapterToPersist.handbookId)).map(alsomoved => updateChapter(alsomoved, calculateAndPersistMovementToNewHandbook = false))

        //Then sections
        (moved + chapterToPersist).foreach(chap => persistNewHandbookForSectionsWithParent(chapterToPersist.handbookId, chap.id.get, chapterToPersist.updatedBy))
      }

      // Only update updatedDate, keep existing localChapterUpdatedDate value
      chapterToPersist.copy(updatedDate = timestamp)
    }


    def persistNewHandbookForSectionsWithParent(newHandbookId: String, parentId: String, updatedBy: Option[String]) = {
      val update = updateString(HandbookSectionTableDef.tableName, List(HandbookSectionTableDef.fieldHandbookId, HandbookSectionTableDef.fieldUpdatedDate, HandbookSectionTableDef.fieldUpdatedBy), HandbookSectionTableDef.fieldParentChapterId)
      val timestamp = Some(DateTime.now)
      connectionManager.doWithConnection {
        _.ps(update) <<
          newHandbookId <<
          timestamp <<
          updatedBy <<
          parentId <<!
      }
    }

    override def persistSection(section: Section, computeSortOrder: Boolean = true): Section = {
      // Make sure we haven't set just one of the imported fields
      if (List(section.importedHandbookSectionId, section.importedHandbookId).count(_.isDefined) == 1) {
        throw new RuntimeException(s"Must define neither or both imported handbook fields. importedHandbookSectionId = ${section.importedHandbookSectionId} and importedHandbookId = ${section.importedHandbookId}")
      }
      section.id match {
        case None => insertSection(section, computeSortOrder)
        case Some(_) => updateSection(section)
      }
    }

    override def retrieveSection(id: String): Option[Section] = {
      val select = selectString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldId, HandbookSectionTableDef.nonDeleteFields, HandbookSectionTableDef.fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << id <<! populateSection
      }.headOption
    }

    override def retrieveSectionsWithImportedHandbookId(importedHandbookId: String, includeDeleted: Boolean): List[Section] = {
      import HandbookSectionTableDef._
      val sql = s"SELECT ${nonDeleteFields.mkString(",")} FROM $tableName WHERE $fieldImportedHandbookId = ?${if (includeDeleted) "" else s" AND $fieldDeleted = 0"}"
      connectionManager.doWithConnection {
        _.ps(sql) << importedHandbookId <<! populateSection
      }
    }

    override def deleteSection(id: String) = {
      val delete = deleteString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.fieldDeleted, HandbookSectionTableDef.fieldId, HandbookSectionTableDef.fieldDeletedDate)
      val timestamp = Some(DateTime.now)
      connectionManager.doWithConnection {
        _.ps(delete) << timestamp << id <<!
      }
    }

    def insertSection(section: Section, computeSortOrder: Boolean = true): Section = {
      val insert = insertString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.allFields)
      val timestamp = Some(DateTime.now)
      val newId = IDGenerator.generateUniqueId
      val sortOrder = if (computeSortOrder) retrieveLatestIndexForChapterItems(section.parentId) else section.sortOrder.get

      connectionManager.doWithConnection {
        _.ps(insert) <<
          newId <<
          section.title <<
          section.text <<
          section.importedHandbookSectionId <<
          section.importedHandbookId <<
          section.handbookId <<
          section.parentId <<
          sortOrder <<
          section.localTitleChange <<
          section.pendingTitleChange <<
          section.localTextChange <<
          section.pendingTextChange <<
          section.pendingDeletion <<
          timestamp <<
          timestamp <<
          timestamp <<
          section.updatedBy <<
          section.textUpdatedBy <<
          timestamp <<
          section.createdBy <<
          false <<
          0 <<!
      }
      section.copy(id = Option(newId), pendingChangeUpdatedDate = timestamp, updatedDate = timestamp, textUpdatedDate = timestamp, createdDate = timestamp, sortOrder = Some(sortOrder))
    }

    def updateSection(section: Section): Section = {
      val update = updateString(HandbookSectionTableDef.tableName, HandbookSectionTableDef.nonIdOrDeleteFields, HandbookSectionTableDef.fieldId)

      connectionManager.doWithConnection {
        _.ps(update) <<
          section.title <<
          section.text <<
          section.importedHandbookSectionId <<
          section.importedHandbookId <<
          section.handbookId <<
          section.parentId <<
          section.sortOrder <<
          section.localTitleChange <<
          section.pendingTitleChange <<
          section.localTextChange <<
          section.pendingTextChange <<
          section.pendingDeletion <<
          section.pendingChangeUpdatedDate <<
          section.updatedDate <<
          section.textUpdatedDate <<
          section.updatedBy <<
          section.textUpdatedBy <<
          section.id <<!
      }
      section
    }

    override def persistSortOrder(sortOrder: List[String]): Unit = {
      def updateSql(tableName: String, fieldOrderIndex: String, idField: String) = s"UPDATE $tableName SET $fieldOrderIndex = ? WHERE $idField = ?"

      def tryUpdatingChapter(id: String, orderIndex: Int): Boolean = {
        import HandbookChapterTableDef.{fieldId, fieldOrderindex, tableName}
        val sql = updateSql(tableName, fieldOrderindex, fieldId)
        connectionManager.doWithConnection {
          _.ps(sql) << orderIndex << id <<!!
        } == 1
      }

      def tryUpdatingSection(id: String, orderIndex: Int): Boolean = {
        import HandbookSectionTableDef.{fieldId, fieldOrderindex, tableName}
        val sql = updateSql(tableName, fieldOrderindex, fieldId)
        connectionManager.doWithConnection {
          _.ps(sql) << orderIndex << id <<!!
        } == 1
      }

      sortOrder.zipWithIndex.foreach {
        case (id, i) =>
          if (!tryUpdatingChapter(id, i)) {
            tryUpdatingSection(id, i)
          }
      }
    }

    def insertComment(comment: Comment) = {
      import HandbookCommentTableDef._

      val insert = insertString(tableName, allFields)
      val newId = IDGenerator.generateUniqueId

      connectionManager.doWithConnection {
        _.ps(insert) <<
          newId <<
          comment.text <<
          comment.editedBy <<
          comment.editedDate <<
          comment.handbookId <<!
      }
      comment.copy(id = Option(newId))
    }

    def updateComment(comment: Comment) = {
      import HandbookCommentTableDef._

      val update = updateString(tableName, updateFields, fieldId)

      connectionManager.doWithConnection {
        _.ps(update) <<
          comment.text <<
          comment.editedBy <<
          comment.editedDate <<
          comment.id.get <<!
      }
      comment
    }

    override def persistComment(comment: Comment): Comment = {
      comment.id match {
        case None => insertComment(comment)
        case Some(_) => updateComment(comment)
      }
    }

    override def retrieveComments(handbookId: String): List[Comment] = {
      import HandbookCommentTableDef._

      val select = s"select ${allFields.mkString(",")} from ${tableName} where $fieldHandbookId = ?"
      val comments = connectionManager.doWithConnection {
        _.ps(select) << handbookId <<! populateComment
      }
      comments
    }

    override def deleteComment(id: String) = {
      import HandbookCommentTableDef._

      val delete = s"DELETE FROM $tableName WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(delete) << id <<!
      }
    }

    override def retrieveLocalEditors(handbookId: String): List[LocalEditor] = {
      import HandbookLocalEditorTableDef._

      val select = s"select ${allFields.mkString(",")} from ${tableName} where $fieldHandbookId = ?"
      val localEditors = connectionManager.doWithConnection {
        _.ps(select) << handbookId <<! populateLocalEditor
      }
      localEditors
    }

    override def retrieveAllLocalEditors(): List[LocalEditor] = {
      import HandbookLocalEditorTableDef._

      val select = s"select ${allFields.mkString(",")} from ${tableName}"
      val localEditors = connectionManager.doWithConnection {
        _.ps(select) <<! populateLocalEditor
      }
      localEditors
    }

    override def insertLocalEditor(localEditor: LocalEditor): LocalEditor = {
      import HandbookLocalEditorTableDef._
      val insert = insertString(tableName, allFields)
      val newId = IDGenerator.generateUniqueId
      val timestamp = DateTime.now
      connectionManager.doWithConnection {
        _.ps(insert) <<
          newId <<
          localEditor.handbookId <<
          localEditor.rightsHolder <<
          localEditor.addedBy <<
          timestamp <<!
      }
      localEditor.copy(id = Option(newId), addedDate = timestamp)
    }

    override def deleteLocalEditor(id: String) = {
      import HandbookLocalEditorTableDef._

      val delete = s"DELETE FROM $tableName WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(delete) << id <<!
      }
    }

    override def retrieveHandbooksBasedOnCentralHandbookId(centralHandbookId: String): List[Handbook] = {
      import HandbookTableDef._
      import HandbookChapterTableDef.{tableName => chapterTable, fieldHandbookId => chapterHandbookId, fieldImportedHandbookId => chapterImportedHandbookId}
      import HandbookSectionTableDef.{tableName => sectionTable, fieldHandbookId => sectionHandbookId, fieldImportedHandbookId => sectionImportedHandbookId}
      log.info(s"Retrieving handbooks based on central handbook ID: $centralHandbookId")

      // First query: Get handbooks directly based on central handbook
      val select = selectString(tableName, fieldImportedHandbookId, nonDeleteFields, fieldDeleted)
      val centralBasedHandbooks = connectionManager.doWithConnection {
        _.ps(select) << centralHandbookId <<! populateHandbook
      }
      log.info(s"##########retrieveHandbooksBasedOnCentralHandbookId Executed Select1 Query : $select#############")

      // Second query: Get handbooks with chapters or sections referencing the central handbook
      val select2 =
        s"""
        SELECT hb.${nonDeleteFields.mkString(", hb.")}
        FROM $tableName hb
        WHERE hb.$fieldDeleted = 0
        AND (hb.$fieldImportedHandbookId IS NULL OR hb.$fieldImportedHandbookId <> ?)
        AND (
          EXISTS (
            SELECT 1
            FROM $chapterTable s_chap
            WHERE s_chap.$chapterImportedHandbookId = ?
            AND s_chap.$chapterHandbookId = hb.$fieldId
          )
          OR
          EXISTS (
            SELECT 1
            FROM $sectionTable s_sec
            WHERE s_sec.$sectionImportedHandbookId = ?
            AND s_sec.$sectionHandbookId = hb.$fieldId
          )
        )
      """
      log.info(s">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Going to execute local handbooks with cental items Query : $select2 <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
      val start = System.currentTimeMillis()
      val localHandbooksWithCentraItems = connectionManager.doWithConnection {
        _.ps(select2) << centralHandbookId << centralHandbookId << centralHandbookId <<! populateHandbook
      }
      val duration = System.currentTimeMillis() - start
      log.info(s">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Found ${localHandbooksWithCentraItems.size} local handbooks with central items in $duration ms <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")

      centralBasedHandbooks ::: localHandbooksWithCentraItems
    }

    override def retrieveSectionsBasedOnCentralSection(centralSectionId: String, includeDeleted: Boolean): List[Section] = {
      import HandbookSectionTableDef._
      val sql = s"SELECT ${allFields.mkString(",")} FROM $tableName WHERE $fieldImportedHandbookSectionId = ?${if (includeDeleted) "" else s" AND $fieldDeleted = 0"}"
      connectionManager.doWithConnection {
        _.ps(sql) << centralSectionId <<! populateSection
      }
    }

    override def retrieveChaptersBasedOnCentralChapter(centralChapterId: String, includeDeleted: Boolean): List[Chapter] = {
      import HandbookChapterTableDef._
      val sql = s"SELECT ${allFields.mkString(",")} FROM $tableName WHERE $fieldImportedHandbookChapterId = ?${if (includeDeleted) "" else s" AND $fieldDeleted = 0"}"
      connectionManager.doWithConnection {
        _.ps(sql) << centralChapterId <<! populateChapter
      }
    }

    override def removeCentralContentFromChapter(chapterId: String) {
      import HandbookChapterTableDef._
      val update = updateString(tableName, List(fieldImportedHandbookId, fieldImportedHandbookChapterId, fieldPendingChanges, fieldLocalChange), fieldId)
      connectionManager.doWithConnection {
        _.ps(update) << Option.empty[String] << Option.empty[String] << false << false << chapterId <<!
      }
    }

    override def removeCentralContentFromSection(sectionId: String) {
      import HandbookSectionTableDef._
      val update = updateString(tableName, List(fieldImportedHandbookId, fieldImportedHandbookSectionId, fieldPendingTitleChange, fieldLocalTitleChange, fieldPendingTextChange, fieldLocalTextChange), fieldId)
      connectionManager.doWithConnection {
        _.ps(update) << Option.empty[String] << Option.empty[String] << false << false << false << false << sectionId <<!
      }
    }

    override def setNewTitleOnHandbooks(newTitle: String, handbookIds: List[String]) {
      if (handbookIds.nonEmpty) {
        val sql = updateRangeString(HandbookTableDef.tableName, HandbookTableDef.fieldTitle, HandbookTableDef.fieldId, handbookIds.size)
        connectionManager.doWithConnection {
          _.ps(sql) << newTitle <<~ handbookIds <<!
        }
      }
    }

    override def setPendingChangesFlagToTrueOnHandbooks(handbookIds: List[String]) {
      if (handbookIds.nonEmpty) {
        val sql = updateRangeString(HandbookTableDef.tableName, HandbookTableDef.fieldPendingChanges, HandbookTableDef.fieldId, handbookIds.size)
        connectionManager.doWithConnection {
          _.ps(sql) << true <<~ handbookIds <<!
        }
      }
    }

    override def retrieveDeletedAndNotDeletedChaptersForHandbook(handbookId: String): List[Chapter] = {
      import HandbookChapterTableDef._
      val select = s"SELECT ${allFields.mkString(",")} FROM $tableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(select) << handbookId <<! populateChapterWithDelete
      }
    }

    override def retrieveDeletedAndNotDeletedChaptersBasedOnCentralHandbook(centralHandbookId: String): List[Chapter] = {
      import HandbookChapterTableDef._
      val select = s"SELECT ${allFields.mkString(",")} FROM $tableName WHERE $fieldImportedHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(select) << centralHandbookId <<! populateChapterWithDelete
      }
    }

    override def retrieveDeletedAndNotDeletedSectionsForHandbook(handbookId: String): List[Section] = {
      import HandbookSectionTableDef._
      val select = s"SELECT ${allFields.mkString(",")} FROM $tableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(select) << handbookId <<! populateSectionWithDelete
      }
    }

    override def retrieveSectionsWithTextsBasedOnCentralHandbook(centralHandbookId: String): List[Section] = {
      import HandbookSectionTableDef._
      val select = selectString(tableName, centralHandbookId, nonDeleteFields, fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << centralHandbookId <<! populateSection
      }
    }

    override def retrieveChaptersByChapterId(chapterId: String): List[Chapter] = {
      import HandbookChapterTableDef._
      val select = selectString(tableName, fieldParentChapterId, nonDeleteFields, fieldDeleted)
      connectionManager.doWithConnection {
        _.ps(select) << chapterId <<! populateChapter
      }
    }

    private def selectString(tableName: String, fieldId: String, nonDeleteFields: List[String], fieldDeleted: String) = s"${genericSelectString(tableName, nonDeleteFields, fieldDeleted)} and ${fieldId} = ?"

    private def genericSelectString(tableName: String, nonDeleteFields: List[String], fieldDeleted: String, isDeleted: Int = 0) = s"select ${nonDeleteFields.mkString(",")} from ${tableName} where $fieldDeleted = $isDeleted"

    private def insertString(tableName: String, allFields: List[String]) = s"insert into ${tableName} ${allFields.mkString("(", ",", ")")} values ${allFields.map(_ => "?").mkString("(", ",", ")")}"

    private def updateRangeString(tableName: String, fieldToUpdate: String, fieldId: String, numIds: Int) = s"update $tableName set $fieldToUpdate=? where $fieldId in ${(1 to numIds).map(_ => "?").mkString("(", ",", ")")}"

    private def updateString(tableName: String, nonIdOrDeleteFields: List[String], fieldId: String) = s"update ${tableName} set ${nonIdOrDeleteFields.mkString("", "=?,", "=?")} where ${fieldId} = ?"

    private def deleteString(tableName: String, fieldDeleted: String, fieldId: String, fieldDeletedDate: String) = s"update ${tableName} set $fieldDeleted = 1, ${fieldDeletedDate} = ? where $fieldId = ?"

    private def deleteIfParentChapterDeletedString(tableName: String, fieldDeleted: String, fieldParentChapterId: String, fieldDeletedDate: String, timestamp: DateTime) = s"update ${tableName} set $fieldDeleted = 1, ${fieldDeletedDate} = ${timestamp.getMillis} where  $fieldDeleted = 0 and $fieldParentChapterId in (${genericSelectString(HandbookChapterTableDef.tableName, List(HandbookChapterTableDef.fieldId), HandbookChapterTableDef.fieldDeleted, isDeleted = 1)})"

    private def odt(t: Option[Long]): Option[DateTime] = t.map(new DateTime(_))

    private def dt(t: Option[Long]): DateTime = new DateTime(t.get)

    private def populateString(rs: RichResultSet): String = rs

    private def populateHandbook(rs: RichResultSet): Handbook = Handbook(rs, rs, rs, rs, rs, rs, rs, rs, pendingChangeUpdatedDate = odt(rs), updatedDate = odt(rs), updatedBy = rs, createdDate = odt(rs), createdBy = rs)

    private def populateChapter(rs: RichResultSet): Chapter = Chapter(rs, rs, rs, rs, rs, rs, rs, rs, rs, rs,
      pendingChangeUpdatedDate = odt(rs),
      updatedDate = odt(rs),
      localChapterUpdatedDate = odt(rs),
      updatedBy = rs,
      createdDate = odt(rs),
      createdBy = rs)

    private def populateChapterWithDelete(rs: RichResultSet): Chapter = Chapter(rs, rs, rs, rs, rs, rs, rs, rs, rs, rs,
      pendingChangeUpdatedDate = odt(rs),
      updatedDate = odt(rs),
      localChapterUpdatedDate = odt(rs),
      updatedBy = rs,
      createdDate = odt(rs),
      createdBy = rs,
      isDeleted = rs,
      deletedDate = odt(rs))

    private def populateSection(rs: RichResultSet): Section = Section(rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, pendingChangeUpdatedDate = odt(rs), updatedDate = odt(rs), textUpdatedDate = odt(rs), updatedBy = rs, textUpdatedBy = rs, createdDate = odt(rs), createdBy = rs)

    private def populateSectionWithDelete(rs: RichResultSet): Section = Section(rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, pendingChangeUpdatedDate = odt(rs), updatedDate = odt(rs), textUpdatedDate = odt(rs), updatedBy = rs, textUpdatedBy = rs, createdDate = odt(rs), createdBy = rs, isDeleted = rs, deletedDate = odt(rs))

    private def populateSectionWithoutText(rs: RichResultSet): Section = Section(rs, rs, None, rs, rs, rs, rs, rs, rs, rs, rs, rs, rs, pendingChangeUpdatedDate = odt(rs), updatedDate = odt(rs), textUpdatedDate = odt(rs), updatedBy = rs, textUpdatedBy = rs, createdDate = odt(rs), createdBy = rs)

    private def populateComment(rs: RichResultSet): Comment = Comment(id = rs, text = rs, editedBy = rs, editedDate = dt(rs), handbookId = rs)

    private def populateLocalEditor(rs: RichResultSet): LocalEditor = LocalEditor(id = rs, handbookId = rs, rightsHolder = rs, addedBy = rs, addedDate = dt(rs))
  }
}

trait HandbookRepository {
  def persistHandbook(handbook: Handbook): Handbook

  def persistChapter(chapter: Chapter, computeSortOrder: Boolean = true): Chapter

  def persistSection(section: Section, computeSortOrder: Boolean = true): Section

  def retrieveHandbook(id: String): Option[Handbook]

  def retrieveChapter(id: String): Option[Chapter]

  def retrieveSection(id: String): Option[Section]

  def retrieveSectionsWithImportedHandbookId(importedHandbookId: String, includeDeleted: Boolean): List[Section]

  def retrieveChaptersForHandbook(handbookId: String): List[Chapter]

  def retrieveSectionsForHandbook(handbookId: String): List[Section]

  def retrieveSectionsWithoutTextForHandbook(handbookId: String): List[Section]

  def retrieveAllExternalOrgIds(): List[String]

  def retrieveChildren(sectionOrChapterId: String, withHtml: Boolean = false): (List[Section], List[Chapter])

  def retrieveLastSortindexForParent(chapter: Chapter): Int

  def deleteHandbook(id: String)

  def deleteChapter(id: String)

  def deleteSection(id: String)

  def retrieveExternalOrganizationIdForHandbook(handbookId: String): String

  def retrieveHandbooksForExternalOrganization(externalOrgId: String): List[Handbook]

  def retrieveHandbooksForExternalOrganizationRestricted(externalOrgId: String, userId: String): List[Handbook]

  def persistSortOrder(sortOrder: List[String])

  def persistComment(comment: Comment): Comment

  def retrieveComments(handbookId: String): List[Comment]

  def deleteComment(id: String)

  def retrieveLocalEditors(handbookId: String): List[LocalEditor]

  def retrieveAllLocalEditors(): List[LocalEditor]

  def insertLocalEditor(localEditor: LocalEditor): LocalEditor

  def deleteLocalEditor(id: String)

  def retrieveSectionsBasedOnCentralSection(centralSectionId: String, includeDeleted: Boolean): List[Section]

  def retrieveChaptersBasedOnCentralChapter(centralChapterId: String, includeDeleted: Boolean): List[Chapter]

  def removeCentralContentFromChapter(chapterId: String)

  def removeCentralContentFromSection(sectionId: String)

  def setNewTitleOnHandbooks(newTitle: String, handbookIds: List[String])

  def setPendingChangesFlagToTrueOnHandbooks(handbookIds: List[String])

  def retrieveHandbooksBasedOnCentralHandbookId(centralHandbookId: String): List[Handbook]

  def retrieveDeletedAndNotDeletedChaptersForHandbook(handbookId: String): List[Chapter]

  def retrieveDeletedAndNotDeletedChaptersBasedOnCentralHandbook(centralHandbookId: String): List[Chapter]

  def retrieveDeletedAndNotDeletedSectionsForHandbook(handbookId: String): List[Section]

  def retrieveSectionsWithTextsBasedOnCentralHandbook(centralHandbookId: String): List[Section]

  def retrieveChaptersByChapterId(chapterId: String): List[Chapter]
}

