import React from "react";
import { Icon, Tree } from "kf-bui";
import { useLocation } from "react-router-dom";
import type { CentralChapterWithChildren } from "@/store/services/handbook/utils";
import {
  sortChaptersAndSections,
  isChapter,
} from "@/store/services/handbook/utils";
import type {
  CentralChapter,
  CentralSection,
  CentralTreeNodeWithChildren,
} from "@/types";
import { CentralSectionNode } from "../CentralSectionNode";
import { shouldDisableNode } from "../../../utils/moveValidation";
import { useAppSelector } from "@/store";
import { selectSelectedCentralItem } from "@/store/slices/centralTreeSlice";

interface CentralChapterNodeProps {
  chapter: CentralChapterWithChildren;
  moving?: boolean;
  onSetSelectedItem?: (item: CentralTreeNodeWithChildren) => void;
  disabled?: boolean;
  movedItem?: CentralChapter | CentralSection;
}

export const CentralChapterNode: React.FC<CentralChapterNodeProps> = ({
  chapter,
  moving = false,
  onSetSelectedItem,
  disabled = false,
  movedItem,
}) => {
  const location = useLocation();
  const childChapters = chapter.chapters || [];
  const sections = chapter.sections || [];
  const selectedCentralItem = useAppSelector(selectSelectedCentralItem);

  const isActive = React.useMemo(() => {
    if (moving) return false;

    const currentPath = location.pathname;
    const chapterPath = `/chapter/${chapter.id}`;

    if (currentPath.includes(chapterPath)) {
      return true;
    }

    const childSectionIds = sections.map((s) => s.id);
    const isChildSectionActive = childSectionIds.some((sectionId) =>
      currentPath.includes(`/section/${sectionId}`)
    );

    return isChildSectionActive;
  }, [location.pathname, chapter.id, sections, moving]);

  const sortedItems = sortChaptersAndSections(childChapters, sections);

  const items = sortedItems.map((item) => {
    if (isChapter(item)) {
      return (
        <CentralChapterNode
          key={item.id}
          chapter={item as CentralChapterWithChildren}
          onSetSelectedItem={onSetSelectedItem}
          disabled={disabled}
          moving={moving}
          movedItem={movedItem}
        />
      );
    } else {
      return (
        <CentralSectionNode
          key={item.id}
          section={item as CentralSection}
          moving={moving}
          onSetSelectedItem={onSetSelectedItem}
          disabled={disabled}
          movedItem={movedItem}
        />
      );
    }
  });

  const getCentralChapterUrl = () => {
    return `/central-editor/${chapter.centralHandbookId}/chapter/${chapter.id}/`;
  };

  if (moving && movedItem) {
    // Use validation-based disabling with inheritance
    const isDisabled = shouldDisableNode(chapter, movedItem, disabled);
    const isBeingMoved = chapter.id === movedItem.id;
    const isParentOfMoved = movedItem.parentId === chapter.id;
    const isSelected = selectedCentralItem?.id === chapter.id;
    const isDifferentHandbook =
      movedItem && movedItem.centralHandbookId !== chapter.centralHandbookId;

    return (
      <Tree.Item
        items={items}
        key={chapter.id}
        id={chapter.id!}
        disabled={isDisabled}
        onClick={() => onSetSelectedItem?.(chapter)}
        style={{
          opacity:
            isDifferentHandbook || (isDisabled && !isBeingMoved && !isSelected)
              ? 0.5
              : 1,
          cursor: isDisabled ? "not-allowed" : "pointer",
          fontWeight: isBeingMoved || isSelected ? 600 : undefined,
          color: isBeingMoved
            ? "#1976d2"
            : isSelected
              ? "#2e7d32"
              : isParentOfMoved
                ? "#666"
                : undefined,
          backgroundColor: isBeingMoved
            ? "#e3f2fd"
            : isSelected
              ? "#e8f5e8"
              : isParentOfMoved
                ? "#f5f5f5"
                : undefined,
          padding:
            isBeingMoved || isSelected || isParentOfMoved
              ? "2px 4px"
              : undefined,
          borderRadius:
            isBeingMoved || isSelected || isParentOfMoved ? "4px" : undefined,
          border: isSelected
            ? "2px solid #2e7d32"
            : isBeingMoved
              ? "2px solid #1976d2"
              : undefined,
        }}
      >
        <Icon icon="RegBookmark" size="small" style={{ marginRight: "4px" }} />
        {chapter.title}
        {isBeingMoved && (
          <span
            style={{ marginLeft: "8px", fontSize: "12px", color: "#1976d2" }}
          >
            ← flytter
          </span>
        )}
        {isSelected && (
          <span
            style={{ marginLeft: "8px", fontSize: "12px", color: "#2e7d32" }}
          >
            ← valgt som mål
          </span>
        )}
      </Tree.Item>
    );
  }

  return (
    <Tree.ItemLink
      to={getCentralChapterUrl()}
      items={items}
      key={chapter.id}
      id={chapter.id!}
      style={isActive ? { fontWeight: "600", color: "#050037" } : undefined}
    >
      <Icon icon="RegBookmark" size="small" style={{ marginRight: "4px" }} />
      {chapter.title}
    </Tree.ItemLink>
  );
};
