package no.kf.handboker.batch

import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.LDAPSearchBase
import no.kf.handboker.model.LDAPUser
import no.kf.util.Logging
import org.quartz.{DisallowConcurrentExecution, Job, JobExecutionContext, JobExecutionException}

@DisallowConcurrentExecution
class RemoveSubscriptionsBatchJob extends Job with Logging {

  lazy val localHandbookService = ProductionRegistry.componentRegistry.localHandbookService
  lazy val subscriptionService = ProductionRegistry.componentRegistry.subscriptionService
  lazy val ldapService = ProductionRegistry.componentRegistry.ldapService

  override def execute(context: JobExecutionContext): Unit = {
    try {
      log.info("Starting Removing Subscriptions batch job")
      log.info("Removing Subscriptions for Editors & Administrators")
      val ldapEditors = getEditorsForAllOrganizations.getOrElse(throw new JobExecutionException("Unable to retrieve editors (or none) from ldap during RemoveSubscriptions batch job", false))
      val ldapAdministrators = getAdministratorsForAllOrganizations.getOrElse(throw new JobExecutionException("Unable to retrieve administrators (or none) from ldap during RemoveSubscriptions batch job", false))
      val ldapUsers = ldapEditors ++ ldapAdministrators
      //subscriptionService.deleteSubscriptions(ldapUsers) -- This has been already commited by Amila

      log.info(s"Finished running Removing Subscriptions for Editors & Administrators.")
    } catch {
      case e: Exception =>
        log.error("Error occurred during Removing Subscriptions batch job", e)
        throw new JobExecutionException("Error occured during Removing Subscriptions batch job", e, false)
    }
  }

  def getEditorsForAllOrganizations: Option[List[LDAPUser]] = {
    val base = ProductionRegistry.componentRegistry.settings.settingFor(LDAPSearchBase)
    val editorGroupString = s"CN=Håndbøker-Redaktør,OU=Handboker,OU=KFApplications,$base"
    ldapService.findBrukerMemberOf(editorGroupString)
  }

  def getAdministratorsForAllOrganizations: Option[List[LDAPUser]] = {
    val base = ProductionRegistry.componentRegistry.settings.settingFor(LDAPSearchBase)
    val adminGroupString = s"CN=Håndbøker-Administrator,OU=Handboker,OU=KFApplications,$base"
    ldapService.findBrukerMemberOf(adminGroupString)
  }


}
