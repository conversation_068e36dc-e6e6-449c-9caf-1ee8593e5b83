# H<PERSON><PERSON><PERSON><PERSON><PERSON>

## Prerequisites 
 
- Java 8
- Scala 2.12.7
- Node 16 or 12
- Maven

## Getting started

It's recommended to create maven run configurations in intellij to run and build the application.

If you just want to run an already built version, run:
```
jetty:run -s settings.xml
```
For running backend without frontend:
```
jetty:run -P !frontend -s settings.xml -f pom.xml
```
Build the whole application, then run it after:
```
jetty:run clean install -s settings.xml -f pom.xml
```
Build for deliveries, for use on our test server, or just to have a build you can run whenever (see above command):
```
clean install -Pprod -s settings.xml -f pom.xml
```

### Running as a dev

When developing the application, it's recommended to use the run command (see above) without frontend.
This allows for faster testing, as you don't need to rerun for every frontend change, and your build times are way lower.

When running backend without frontend, you need to connect to your application with 
```
localhost:5100
```
Doing so might not have the results you want, since the frontend is not properly loaded (only from a previous build),
but also some applications might not show data as expected (not sure why this is)

To properly see the frontend, you need to use a terminal (the one in intellij works fine), and navigate to /frontend and run the following command:
```
npm start
```
This will start the frontend with auto refresh functionality to automatically refresh when you do any change in the frontend files.
(except when you change .css files). To connect to your frontend, you first have to connect to the backend (the localhost above),
then you can connect to:
```
localhost:3000
```
The reason you have to connect to the backend first is to get a session active by logging in. This process is not possible on the frontend conneciton.


## Public page (publikumssiden)
If you want to access "publikumssiden" for an organization, go to **[delegering-url]/publikum/[org-id]**

There is only one problem with the public page. If you run backend and frontend separate (-P !frontend above),
you have to change the public page url abit.

When you press the "publikumssiden" button, you will be taken to an url looking something like this:
**http://localhost:3000/delegering/publikum/[org-id]** and it might not display anything. If it loads, that is good,
but any changes you make in the public page code is not update here yet (it only displays last full build public page code).

To fix this issue, just delte the **publikum/** part in the url, so it looks like this (from example):
**http://localhost:3000/delegering/[org-id]**

Doing this makes the public page load all new changes you make, but also loads an extra footer, and the leftside
navigation menu. These will not be in the end product, so just ignore them.


## Bypassing CAS (not tested in a long time)
In `src/test/resources/delegering.properties`, change/set/comment out the following values:

    # CasConfig=internal-test
    MockBrukerAdm=true
    UseLDAP=false``
    
## Elasticsearch

Håndbøker uses Elasticsearch for indexing and searching the handbooks.

Install elasticsearch 5.4.* [https://www.elastic.co/products/elasticsearch]

#### OS X
On OS X, install it using Homebrew:
```
$ brew install elasticsearch
```

The config files can then be found in `/usr/local/etc/elasticsearch/`

#### Configuration

Configure `handboker.properties` and `elasticsearch.yml`. The following properties need to match:

**elasticsearch.yml**: *cluster.name*

**kvalitetsstyring.properties**: *ElasticSearchClusterName*

For the wysiwyg spellcheck to work, you will need to fire up the php-server found in the spellcheck project.
To do this, simply fire up the compose file in the spellcheck project using docker-compose up in the root folder of spellcheck.
Alternatively, you could change the 'SpellCheckUrl' to use the testserver spellcheck url, if the spellcheck is deployed on a testserver.
