import nbTranslationMessages from './translations/nb.json';
import nnTranslationMessages from './translations/nn.json';
import type { SessionInfo } from '@/types';

export const SUPPORTED_LOCALES = ['nb', 'nn'] as const;
export type SupportedLocale = 'nb' | 'nn';
export const DEFAULT_LOCALE: SupportedLocale = 'nb';
export type TranslationMessages = Record<string, string>;

export const translationMessages: Record<SupportedLocale, TranslationMessages> = {
  nb: nbTranslationMessages,
  nn: nnTranslationMessages,
};

/**
 * Detects locale with priority matching legacy system:
 * 1. Session user language preference
 * 2. Session organization language preference  
 * 3. Previously saved locale (localStorage)
 * 4. Browser language
 * 5. Default locale
 */
export const detectLocaleWithSession = (session?: SessionInfo | null): SupportedLocale => {
  // 1. Check session user language (highest priority)
  if (session?.user?.language && SUPPORTED_LOCALES.includes(session.user.language as SupportedLocale)) {
    return session.user.language as SupportedLocale;
  }

  // 2. Check session organization language
  if (session?.organization?.language && SUPPORTED_LOCALES.includes(session.organization.language as SupportedLocale)) {
    return session.organization.language as SupportedLocale;
  }

  // 3. Check saved locale (localStorage)
  try {
    const savedLocale = localStorage.getItem('locale') as SupportedLocale;
    if (savedLocale && SUPPORTED_LOCALES.includes(savedLocale)) {
      return savedLocale;
    }
  } catch (error) {
    console.warn('Failed to read locale from localStorage:', error);
  }

  // 4. Check browser language
  try {
    const browserLocale = navigator.language.split('-')[0] as SupportedLocale;
    if (SUPPORTED_LOCALES.includes(browserLocale)) {
      return browserLocale;
    }
  } catch (error) {
    console.warn('Failed to detect browser locale:', error);
  }

  // 5. Fall back to default
  return DEFAULT_LOCALE;
};

/**
 * Legacy-compatible detectLocale function (without session)
 * Used when session is not available yet
 */
export const detectLocale = (): SupportedLocale => {
  return detectLocaleWithSession(null);
};

export const saveLocale = (locale: SupportedLocale): void => {
  localStorage.setItem('locale', locale);
};

export const getMessagesForLocale = (locale: SupportedLocale): TranslationMessages => {
  return translationMessages[locale] || translationMessages[DEFAULT_LOCALE];
};

export const LOCALE_NAMES: Record<SupportedLocale, string> = {
  nb: 'Norsk (Bokmål)',
  nn: 'Norsk (Nynorsk)',
};

export const appLocales = SUPPORTED_LOCALES;
export const formatTranslationMessages = (_locale: string, messages: TranslationMessages) => messages;
