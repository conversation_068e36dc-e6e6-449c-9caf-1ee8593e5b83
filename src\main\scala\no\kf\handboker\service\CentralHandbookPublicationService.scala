package no.kf.handboker.service
import no.kf.db.TransactionManager
import no.kf.handboker.model.Publication
import no.kf.handboker.repository.PublicationRepositoryComponent
import no.kf.handboker.rest.support.SessionSupport
import org.joda.time.DateTime
import org.json4s.ext.JodaTimeSerializers
import org.json4s.{DefaultFormats, Formats}


trait CentralHandbookPublicationServiceComponent extends TransactionManager {
  this: PublicationRepositoryComponent=>

  class CentralHandbookPublicationServiceImpl extends CentralHandbookPublicationService {

    protected implicit val jsonFormats: Formats = DefaultFormats.lossless ++ JodaTimeSerializers.all

    override def createPublication(handbookId: String, publicationDate: DateTime, user: String): Publication = inTransaction {
      val publication = Publication(None, publicationDate,false, DateTime.now, user, handbookId, false)
      publicationRepository.persistPublication(publication)
    }
  }
}

trait CentralHandbookPublicationService {
  def createPublication(handbookId: String, PublicationDate: DateTime, user: String): Publication
}

