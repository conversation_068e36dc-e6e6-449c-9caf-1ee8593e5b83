# Handboker

A React + TypeScript + Vite application.

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Development

#### Running Frontend Only
Start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:3000`

#### Full Development Setup (Frontend + Backend)

1. **Start the backend** (in the project root):
   ```bash
   mvn jetty:run -P !frontend -s settings.xml -f pom.xml
   ```
   Backend will run on `http://localhost:5600`

2. **Start the frontend** (in the `frontend/` directory):
   ```bash
   npm run dev
   ```
   Frontend will run on `http://localhost:3000`

3. **Access the applications**:
   - **Editor App**: `http://localhost:3000/` - Content management interface
   - **Public App**: `http://localhost:3000/public` - Public viewing interface

#### Port Configuration

- **Frontend (Vite)**: Port 3000 (same as original webpack setup)
- **Backend (Jetty)**: Port 5600
- **Proxy Configuration**: All API calls are automatically proxied from frontend to backend

The Vite development server is configured to proxy the following endpoints to the backend:
- `/api/*` - Public and editor API endpoints
- `/session/*` - Authentication and session management
- `/files/*` - File upload/download operations
- `/search/*` - Search functionality
- `/images/*` - Image serving
- `/handboker/*` - Legacy API endpoints

#### Authentication Flow

In development mode:
1. The frontend automatically fetches session data from `/session` endpoint
2. If session loading fails, you'll see a detailed error message with retry option
3. Make sure the backend is running on port 5600 before starting frontend development

### Dual App Architecture

This project contains two applications in one codebase:

- **Editor App**: `http://localhost:5173/` - Content management interface
- **Public App**: `http://localhost:5173/public` - Public viewing interface

The main App.tsx component automatically detects the URL path and renders the appropriate application with the correct store and routing configuration.

### Building for Production

Build the application:
```bash
npm run build
```

Preview the production build:
```bash
npm run preview
```

### Linting

Run the linter:
```bash
npm run lint
```

## Tech Stack

- **React 19** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Redux Toolkit** - State management
- **TinyMCE 6.8.6** - Rich text editor (MIT)
- **ESLint** - Code linting
- **Prettier** - Code formatting

## Third-party Licenses

This project uses the following third-party software:

### TinyMCE 6.8.6
- **License**: MIT (MIT License)
- **Copyright**: Copyright (c) 2024, Ephox Corporation DBA Tiny Technologies, Inc.
- **Website**: https://www.tiny.cloud/
- **Source**: https://github.com/tinymce/tinymce

The full license information can be found in [TINYMCE_LICENSE.md](./TINYMCE_LICENSE.md).

## Project Structure

```
src/
├── App.tsx          # Main application component
├── App.css          # Application styles
├── main.tsx         # Application entry point
└── index.css        # Global styles
```
