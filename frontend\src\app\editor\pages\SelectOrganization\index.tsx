import { useState, useEffect, useMemo } from "react";
import {
  Button,
  Container,
  Control,
  Group,
  Section,
  Title,
  Subtitle,
  Select,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useSession } from "@/store/services/session/hooks";
import { useSetSessionOrganizationMutation } from "@/store/services/session";
import { useLocation, useNavigate } from "react-router-dom";

export const SelectOrganization = () => {
  const { session } = useSession();
  const location = useLocation();
  const history = useNavigate();
  const [setOrganization, { isLoading: isMutationLoading, error }] = useSetSessionOrganizationMutation();

  const t = usePrefixedTranslation("editor.containers.SelectOrganizationPage");
  const tApp = usePrefixedTranslation("editor.containers.App");

  const orgsWithLabel = useMemo(
    () =>
      session?.userOrgsWithAccess.map((org) => ({
        id: org.id,
        label: `${org.name} (${org.id})`,
      })) || [],
    [session?.userOrgsWithAccess]
  );

  const [organizationId, setOrganizationId] = useState(
    session?.organization ? session.organization.id : null
  );
  const [isPending, setIsPending] = useState(false);
  const [hadPreviousOrg] = useState(Boolean(session?.organization));

  useEffect(() => {
    if (session?.organization && session.organization.id === organizationId && isPending) {
      // Organization was successfully changed, navigate to the intended page
      const { from } = location.state || { from: { pathname: "/editor" } };
      setIsPending(false);
      
      // Show success toast if we had a previous organization (indicating a change, not initial selection)
      if (hadPreviousOrg) {
        toast.success(tApp("changedOrganization"));
      }
      
      history(from.pathname, { replace: true });
    }
  }, [session?.organization, history, location.state, organizationId, isPending, tApp, hadPreviousOrg]);

  if (!session) return null;

  const onSelectChange = (newOrganizationId: string) => {
    setOrganizationId(newOrganizationId);
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!organizationId) return;

    setIsPending(true);
    try {
      await setOrganization(organizationId).unwrap();
      // The RTK Query mutation will automatically update the session state
      // and the useEffect will handle navigation
    } catch (error) {
      console.error('Failed to set organization:', error);
      setIsPending(false);
      
      // Show error toast matching legacy behavior
      toast.error(`${tApp("changedOrganizationFail1")}: ${tApp("changedOrganizationFail2")}`);
    }
  };

  const hasPreviousOrg = Boolean(session.organization);

  return (
    <Section>
      <Container>
        <Title>
          {hasPreviousOrg
            ? t("changeOrganizationTitle")
            : t("setOrganizationTitle")}
        </Title>

        {!hasPreviousOrg && <Subtitle>{t("selectOrganization")}</Subtitle>}

        <form onSubmit={handleSubmit}>
          <Group>
            <Control expanded>
              <Select
                aria-label="Organisasjon"
                options={orgsWithLabel}
                valueKey="id"
                onChange={onSelectChange}
                matchProp="label"
                name="organization"
                simpleValue
                value={organizationId}
                disabled={isPending || isMutationLoading}
              />
            </Control>
            <Button
              color="primary"
              loading={isPending || isMutationLoading}
              type="submit"
              disabled={!organizationId || isPending || isMutationLoading}
            >
              {hasPreviousOrg
                ? t("changeOrganizationButton")
                : t("setOrganizationButton")}
            </Button>
          </Group>

          {error && (
            <div style={{
              marginTop: '1rem',
              padding: '0.75rem',
              backgroundColor: '#ffebee',
              border: '1px solid #ffcdd2',
              borderRadius: '4px',
              color: '#d32f2f'
            }}>
              {tApp("changedOrganizationFail2")}
            </div>
          )}
        </form>
      </Container>
    </Section>
  );
};
