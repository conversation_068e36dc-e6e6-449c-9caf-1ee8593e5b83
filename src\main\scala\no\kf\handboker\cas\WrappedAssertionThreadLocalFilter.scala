package no.kf.handboker.cas

import no.kf.config.Settings
import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.{SiteUrl, CasConfig}
import no.kf.web.cas.GenericCasFilter

class WrappedAssertionThreadLocalFilter extends GenericCasFilter {
  lazy val wrapperFilter = assertionFilter
  lazy val casConfigConfigKey = CasConfig
  lazy val siteUrlConfigKey = SiteUrl
  lazy val settings: Settings = ProductionRegistry.componentRegistry.settings

}
