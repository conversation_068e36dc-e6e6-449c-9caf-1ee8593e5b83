declare module 'react-sticky-el' {
  import React from 'react';

  interface StickyProps {
    style?: React.CSSProperties;
    className?: string;
    children?: React.ReactNode;
    mode?: 'top' | 'bottom' | 'both';
    positionRecheckInterval?: number;
    scrollElement?: string | Element;
    boundaryElement?: string | Element;
    hideOnBoundaryHit?: boolean;
    offsetTop?: number;
    offsetBottom?: number;
    onStickyStateChange?: (isSticky: boolean) => void;
    stickyStyle?: React.CSSProperties;
    topOffset?: number;
    bottomOffset?: number;
    disabled?: boolean;
  }

  const Sticky: React.FC<StickyProps>;
  export default Sticky;
}