package no.kf.handboker.cas

import no.kf.config.Settings
import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.{CasConfig, SiteUrl}
import no.kf.web.cas.GenericCasFilter

class SingleSignOutFilter extends GenericCasFilter {
  lazy val wrapperFilter = singleSignOutFilter
  lazy val casConfigConfigKey = CasConfig
  lazy val siteUrlConfigKey = SiteUrl
  lazy val settings: Settings = ProductionRegistry.componentRegistry.settings

}