import React from "react";
import { Icon, Tree } from "kf-bui";
import type { Section, Chapter } from "@/types";
import { useAppSelector } from "@/store";
import { selectSelectedLocalItem } from "@/store/slices/localTreeSlice";
import { shouldDisableLocalNode } from "../../../utils/localMoveValidation";

interface ClickableSectionNodeProps {
  section: Section;
  onSetSelectedItem?: (section: Section) => void;
  movedItem?: Chapter | Section;
  disabled?: boolean;
}

export const ClickableSectionNode: React.FC<ClickableSectionNodeProps> = ({
  section,
  onSetSelectedItem,
  movedItem,
  disabled = false,
}) => {
  const selectedLocalItem = useAppSelector(selectSelectedLocalItem);

  const isDisabled = movedItem
    ? shouldDisableLocalNode(section, movedItem, disabled)
    : false;

  const isSelected = selectedLocalItem?.id === section.id;
  const isBeingMoved = movedItem && section.id === movedItem.id;

  return (
    <Tree.Item
      id={section.id!}
      key={section.id}
      disabled={isDisabled}
      onClick={() => !isDisabled && onSetSelectedItem?.(section)}
      style={{
        opacity: isDisabled && !isBeingMoved && !isSelected ? 0.5 : 1,
        cursor:
          isDisabled && !isBeingMoved && !isSelected
            ? "not-allowed"
            : isBeingMoved
              ? "default"
              : "pointer",
        fontWeight: isBeingMoved || isSelected ? 600 : undefined,
        color: isBeingMoved ? "#1976d2" : isSelected ? "#2e7d32" : undefined,
        backgroundColor: isBeingMoved
          ? "#e3f2fd"
          : isSelected
            ? "#e8f5e8"
            : undefined,
        padding: isBeingMoved || isSelected ? "2px 4px" : undefined,
        borderRadius: isBeingMoved || isSelected ? "4px" : undefined,
        border: isSelected
          ? "2px solid #2e7d32"
          : isBeingMoved
            ? "2px solid #1976d2"
            : undefined,
      }}
    >
      <Icon icon="RegFileLines" size="small" style={{ marginRight: "4px" }} />
      {section.title}
      {isBeingMoved && (
        <span style={{ marginLeft: "8px", fontSize: "12px", color: "#1976d2" }}>
          ← flytter
        </span>
      )}
      {isSelected && (
        <span style={{ marginLeft: "8px", fontSize: "12px", color: "#2e7d32" }}>
          ← valgt som mål
        </span>
      )}
    </Tree.Item>
  );
};
