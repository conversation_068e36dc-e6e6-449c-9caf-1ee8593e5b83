export type {
  BaseEntity,
  CentralHandbook,
  Handbook,
  Chapter,
  Organization,
  LDAPUser,
  SessionInfo,
  SessionGetResponse,
  SessionSetOrgResponse,
  SessionEditorsResponse,
} from "@/types";

export interface SearchResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export type {
  AttachmentFile,
  AttachmentUploadResponse,
  AttachmentCountResponse,
  SaveAttachmentsRequest,
} from "@/types";
