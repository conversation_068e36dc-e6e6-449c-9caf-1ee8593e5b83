package no.kf.handboker.rest

import java.nio.charset.StandardCharsets.UTF_8

import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.support.{ExternalOrgIdExtractionSupport, JsonSupport}
import org.joda.time.DateTimeZone
import org.joda.time.format.ISODateTimeFormat
import org.scalatra.ScalatraServlet
import org.scalatra.servlet.FileUploadSupport
import org.scalatra.util.RicherString.RicherStringImplicitClass

class ReportServlet extends ScalatraServlet with SessionSupport with JsonSupport with ExternalOrgIdExtractionSupport with FileUploadSupport {

  lazy val imageService = componentRegistry.imageService

  get("/central/pdf/:handbookId/?") {

    val handbookId = extractRequiredParam("handbookId")

    val (filename, report) = componentRegistry.reportService.centralHandbookPDF(handbookId, currentExternalOrganization.get.name, currentSession.user.get.fullName.getOrElse(""))

    contentType = "application/pdf"
    response.addHeader("Content-Disposition", getContentDispositionValue(filename))

    report
  }

  get("/local/pdf/:handbookId/:date/?") {

    val handbookId = extractRequiredParam("handbookId")
    val dateAsString = extractRequiredParam("date")
    val parser = ISODateTimeFormat.dateTimeParser()
    val endOfDay = parser.parseDateTime(dateAsString).withZone(DateTimeZone.forID("Europe/Oslo")).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withMillisOfSecond(999)

    val (filename, report) = componentRegistry.reportService.localHandbookPDF(handbookId, currentExternalOrganization.get.name, currentSession.user.get.fullName.getOrElse(""), endOfDay)

    contentType = "application/pdf"
    response.addHeader("Content-Disposition", getContentDispositionValue(filename))

    report
  }

  get("/central/word/:handbookId/?") {
    val handbookId = extractRequiredParam("handbookId")

    val (filename, report) = componentRegistry.reportService.centralHandbookWord(handbookId, currentExternalOrganization.get.name, currentSession.user.get.fullName.getOrElse(""))

    contentType = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    response.addHeader("Content-Disposition", getContentDispositionValue(filename))

    report
  }

  private def getContentDispositionValue(fileName: String): String = {
    val urlEncodedFileName = new RicherStringImplicitClass(fileName).urlEncode(UTF_8)
    s"attachment; filename=${"\""}$fileName${"\""}; filename*=UTF-8''$urlEncodedFileName"
  }

}
