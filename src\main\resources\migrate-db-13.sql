alter table changenotification drop constraint fk_changenotification_handbook
create table central_changenotification(id varchar(37), changedescription varchar(4000), handbook_id varchar(37)constraint fk_changenotification_handbook references handbook, updated bigint not null, chapter_id varchar(37), section_id varchar(37), change_html varchar(4000), concerns_title smallint, deletion smallint)

RENAME COLUMN handbook.manualmerge TO local_change
RENAME COLUMN handbook.pendingchanges TO pending_change
RENAME COLUMN handbook.pending_changes_updated_date TO pending_change_updated_date

RENAME COLUMN handbookchapter.manualmerge TO local_change
RENAME COLUMN handbookchapter.pendingchanges TO pending_change
RENAME COLUMN handbookchapter.pending_changes_updated_date TO pending_change_updated_date

RENAME COLUMN handbooksection.manualmerge TO local_title_change
RENAME COLUMN handbooksection.pendingchanges TO pending_title_change
RENAME COLUMN handbooksection.pending_changes_updated_date TO pending_change_updated_date

ALTER TABLE handbooksection ADD local_text_change SMALLINT
ALTER TABLE handbooksection ADD pending_text_change SMALLINT
