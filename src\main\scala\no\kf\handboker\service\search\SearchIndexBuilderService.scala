package no.kf.handboker.service.search

import no.kf.handboker.service.LocalHandbookServiceComponent
import no.kf.util.Logging

/**
  * SearchIndexBuilderService is triggered to index handbooks, chapters and sections in bulk
  */
trait SearchIndexBuilderServiceComponent {
  this: ElasticClientManagerServiceComponent
    with LocalHandbookServiceComponent
    with SearchIndexServiceComponent =>

  val searchIndexBuilderService: SearchIndexBuilderService

  class SearchIndexBuilderServiceImpl extends SearchIndexBuilderService with Logging {

    override def indexDocuments(): Unit = {
      synchronized {
        val start = System.currentTimeMillis()
        log.info("Building document indexes for external organizations")

        val externalOrgIds = localHandbookService.retrieveAllExternalOrgIds()
        externalOrgIds.foreach(indexDocumentsForExtOrg)

        log.info(s"Completed indexing all documents in ${System.currentTimeMillis() - start}ms")
      }
    }

    override def indexDocumentsForExtOrg(externalOrgId: String): Unit = {
      log.info(s"Indexing documents for externalOrgId $externalOrgId")

      val handbooks = localHandbookService.retrieveHandbooksForExternalOrganization(externalOrgId)
      val chapters = handbooks.flatMap(handbook => localHandbookService.retrieveChaptersForHandbook(handbook.id.get))
      val sections = handbooks.flatMap(handbook => localHandbookService.retrieveSectionsForHandbook(handbook.id.get, false))

      handbooks.foreach(handbook => searchIndexService.indexHandbook(handbook))
      chapters.foreach(chapter => searchIndexService.indexChapter(chapter, externalOrgId))
      sections.foreach(section => searchIndexService.indexSection(section, externalOrgId))

      log.info(s"Completed indexing documents for externalOrgId $externalOrgId")
    }
  }
}

trait SearchIndexBuilderService {
  def indexDocuments()
  def indexDocumentsForExtOrg(externalOrgId: String)
}
