.attachment-popup {
  position: absolute;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  min-width: 200px;
  z-index: 1000;
  top: 100%;
  right: 0;
  margin-top: 8px;
}

.attachment-popup .arrow {
  position: absolute;
  top: -6px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid white;
}

.attachment-popup .arrow::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -6px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #ddd;
  z-index: -1;
}

.attachment-public-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.attachment-link:hover {
  background-color: #f5f5f5;
}

.attachment-link svg {
  flex-shrink: 0;
}

.attachment-link span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachment-loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  gap: 8px;
  color: #666;
}

.attachment-loading-message .loader {
  height: 1.2em;
  width: 1.2em;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #050037;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.attachment-loading-message p {
  margin: 0;
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}