// Session monitoring Web Worker
// Tracks API call timing and notifies main thread of session state changes

let lastApiCallTime = Date.now();
// let sessionTimeout = 120; // minutes
// let warningTime = 100;    // minutes

// one minute
let sessionTimeout = 2;
let warningTime = 1;
let intervalId: ReturnType<typeof setInterval> | null = null;

function startMonitoring() {
  if (intervalId) {
    clearInterval(intervalId);
  }

  console.log(
    `[SessionWorker] Starting monitoring with ${sessionTimeout}min timeout, ${warningTime}min warning`
  );

  intervalId = setInterval(() => {
    const now = Date.now();
    const minutesSinceLastCall = (now - lastApiCallTime) / (1000 * 60);

    if (minutesSinceLastCall >= sessionTimeout) {
      console.log("[SessionWorker] Session expired!");
      postMessage({ type: "EXPIRED" });
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
    } else if (minutesSinceLastCall >= warningTime) {
      const remainingMinutes = sessionTimeout - minutesSinceLastCall;
      postMessage({ type: "WARNING", remainingMinutes });
    }
  }, 1000);
}

self.addEventListener("message", (event) => {
  const {
    type,
    timestamp,
    sessionTimeout: newSessionTimeout,
    warningTime: newWarningTime,
  } = event.data;

  switch (type) {
    case "INIT":
      sessionTimeout = newSessionTimeout;
      warningTime = newWarningTime;
      lastApiCallTime = timestamp;
      startMonitoring();
      break;

    case "API_CALL": {
      const wasInWarningOrExpired =
        (Date.now() - lastApiCallTime) / (1000 * 60) >= warningTime;
      lastApiCallTime = timestamp;

      if (wasInWarningOrExpired) {
        postMessage({ type: "ACTIVE" });
      }
      break;
    }

    case "STOP":
      console.log("[SessionWorker] Stopping monitoring");
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
      break;
  }
});

export {};
