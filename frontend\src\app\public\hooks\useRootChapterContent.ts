import { useMemo } from "react";
import type { Chapter, Section } from "@/types";
import { getRootChapterId } from "../utils/handbookNavigation";

export const useRootChapterContent = (
  currentChapterId: string | undefined,
  chapters: Chapter[],
  sections: Section[]
) => {
  return useMemo(() => {
    if (!currentChapterId || !chapters.length) {
      return {
        rootChapter: null,
        rootChapterId: null,
        allDescendants: [],
        directChildren: [],
      };
    }

    const rootChapterId = getRootChapterId(currentChapterId, chapters);
    const rootChapter = chapters.find((c) => c.id === rootChapterId);

    if (!rootChapter) {
      return {
        rootChapter: null,
        rootChapterId,
        allDescendants: [],
        directChildren: [],
      };
    }

    const getAllDescendants = (parentId: string): (Chapter | Section)[] => {
      const childChapters = chapters.filter((c) => c.parentId === parentId);
      const childSections = sections.filter((s) => s.parentId === parentId);

      let result: (Chapter | Section)[] = [...childChapters, ...childSections];

      childChapters.forEach((childChapter) => {
        result.push(...getAllDescendants(childChapter.id!));
      });

      return result;
    };

    const directChildren = [
      ...chapters.filter((c) => c.parentId === rootChapterId),
      ...sections.filter((s) => s.parentId === rootChapterId),
    ].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

    const allDescendants = getAllDescendants(rootChapterId);

    return {
      rootChapter,
      rootChapterId,
      allDescendants,
      directChildren,
    };
  }, [currentChapterId, chapters, sections]);
};
