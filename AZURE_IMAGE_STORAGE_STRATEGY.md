# Azure Image Storage Strategy

## Current Situation Analysis

You're hosting the application on an Azure VM and concerned about:
1. VM memory/storage requirements for images
2. Whether to move images to SQL/MSSQL database instead

## Recommended Approach: Azure-Optimized File Storage

### Phase 1: Optimized File Storage (Immediate)
Keep the current file storage approach but optimize for Azure:

**Azure VM Configuration:**
- **Separate Data Disk**: Mount a separate Azure managed disk for images
- **Disk Type**: Standard SSD (cost-effective) or Premium SSD (high performance)
- **Size**: Start with 128GB, can expand as needed
- **Mount Point**: `/mnt/images` (Linux) or `D:\images` (Windows)

**Benefits:**
- **Scalable Storage**: Can increase disk size without VM downtime
- **Cost Effective**: Storage costs much less than database storage
- **Performance**: Direct file serving remains fast
- **Backup**: Azure disk snapshots for image backups

### Phase 2: Azure Blob Storage (Recommended Long-term)
Migrate to Azure Blob Storage for optimal cloud architecture:

**Implementation:**
```scala
// New Azure Blob Storage Service
class AzureBlobImageService extends ImageService {
  override def persistImage(image: Image): String = {
    // Upload to Azure Blob Storage
    // Return blob URL: https://youraccount.blob.core.windows.net/images/filename.jpg
  }
}
```

**Benefits:**
- **Unlimited Scale**: No VM storage constraints
- **CDN Integration**: Azure CDN for global image delivery
- **Cost Effective**: Pay only for storage used
- **High Availability**: Built-in redundancy
- **No VM Impact**: Images don't consume VM resources

### Phase 3: Database Storage (If Required)
Only consider database storage if you have specific requirements:

## Database Storage Implementation

If you decide to store images in the database, here's how to implement it:

### Database Schema Changes

**SQL Server:**
```sql
-- Update welcome_page_customization table
ALTER TABLE welcome_page_customization 
ADD welcome_image_data VARBINARY(MAX),
    welcome_image_filename VARCHAR(255),
    welcome_image_content_type VARCHAR(100),
    welcome_image_size INT;

-- Keep welcome_image for backward compatibility during transition
```

**PostgreSQL:**
```sql
-- Update welcome_page_customization table
ALTER TABLE welcome_page_customization 
ADD welcome_image_data BYTEA,
    welcome_image_filename VARCHAR(255),
    welcome_image_content_type VARCHAR(100),
    welcome_image_size INTEGER;
```

### Code Changes Required

**Model Updates:**
```scala
case class WelcomePageCustomization(
  // ... existing fields ...
  welcomeImage: Option[String] = None, // Keep for backward compatibility
  welcomeImageData: Option[Array[Byte]] = None,
  welcomeImageFilename: Option[String] = None,
  welcomeImageContentType: Option[String] = None,
  welcomeImageSize: Option[Int] = None
)
```

**Service Layer:**
```scala
class DatabaseImageService extends ImageService {
  override def persistImage(image: Image): String = {
    // Store in database instead of file system
    val imageId = IDGenerator.generateUniqueId
    // Save to database with VARBINARY/BYTEA
    s"/handboker/images/db/$imageId"
  }
  
  override def retrieveImage(imageId: String): Array[Byte] = {
    // Retrieve from database
  }
}
```

**New Image Servlet:**
```scala
class DatabaseImageServlet extends ScalatraServlet {
  get("/db/:imageId") {
    val imageId = params("imageId")
    val imageData = welcomePageService.getImageData(imageId)
    
    contentType = imageData.contentType
    imageData.data
  }
}
```

## Cost Comparison (Azure)

### File Storage Costs (Current + Azure Disk)
- **128GB Standard SSD**: ~$19/month
- **VM Impact**: Minimal
- **Backup**: Disk snapshots ~$6/month
- **Total**: ~$25/month

### Azure Blob Storage Costs
- **Storage**: $0.0184/GB/month (Hot tier)
- **100GB images**: ~$1.84/month
- **Transactions**: Minimal cost
- **CDN**: Optional, ~$0.087/GB transfer
- **Total**: ~$2-5/month

### Database Storage Costs
- **Azure SQL Database**: Storage costs 2-3x higher
- **100GB images in DB**: ~$50-100/month additional
- **Performance Impact**: May require higher tier
- **Total**: ~$75-150/month additional

## Performance Comparison

### File Storage (Current)
- **Image Serving**: Direct file read (~1-5ms)
- **Database Impact**: None
- **Concurrent Users**: Limited by disk I/O

### Azure Blob Storage
- **Image Serving**: HTTP request to blob (~10-50ms)
- **Database Impact**: None
- **Concurrent Users**: Virtually unlimited
- **CDN**: Global edge caching (~1-10ms)

### Database Storage
- **Image Serving**: Database query + data transfer (~50-200ms)
- **Database Impact**: High (memory, I/O, CPU)
- **Concurrent Users**: Limited by database performance
- **Backup Time**: Significantly increased

## Recommendation

**For Azure VM hosting, I recommend:**

1. **Short-term**: Keep file storage, add separate Azure managed disk
2. **Medium-term**: Migrate to Azure Blob Storage
3. **Avoid**: Database storage unless you have specific compliance requirements

**Why avoid database storage:**
- **Cost**: 10-20x more expensive in Azure
- **Performance**: Slower image serving
- **Scalability**: Database becomes bottleneck
- **Complexity**: More complex backup/restore procedures

## Implementation Priority

1. **Immediate**: Configure separate Azure disk for images
2. **Next Sprint**: Implement Azure Blob Storage service
3. **Future**: Add CDN integration for global performance

This approach gives you the best of both worlds: cost-effective storage with cloud scalability.