import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query";

export const API_BASE_URLS = {
  HANDBOOKS: "/handbooks",
  SESSION: "/session",
  FILES: "/files",
  IMAGES: "/images",
  SEARCH: "/search",
} as const;

const sharedBaseQuery = fetchBaseQuery({
  baseUrl: "",
  credentials: "same-origin",
  prepareHeaders: (headers, { endpoint }) => {
    const xsrfCookie = document.cookie
      .split("; ")
      .find((row) => row.startsWith("XSRF-TOKEN="));

    if (xsrfCookie) {
      const xsrfToken = xsrfCookie.split("=")[1];
      if (xsrfToken) {
        headers.set("X-XSRF-TOKEN", decodeURIComponent(xsrfToken));
      }
    }

    if (!headers.has("content-type") && endpoint !== "uploadFile") {
      headers.set("content-type", "application/json");
    }

    return headers;
  },
});

const publicBaseQuery = fetchBaseQuery({
  baseUrl: "",
  credentials: "omit",
  prepareHeaders: (headers, { endpoint }) => {
    if (!headers.has("content-type") && endpoint !== "uploadFile") {
      headers.set("content-type", "application/json");
    }
    return headers;
  },
});

export function createBaseQueryWithCsrf(): BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> {
  return async (args, api, extraOptions) => {
    const result = await sharedBaseQuery(args, api, extraOptions);

    if (!result.error) {
      window.dispatchEvent(new CustomEvent('api-call-success'));
    } else if (result.error.status === 401) {
      console.warn("Authentication required - session may have expired");
    } else if (result.error.status === 403) {
      console.warn("CSRF token validation failed");
    }

    return result;
  };
}

export function createPublicBaseQuery(): BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> {
  return async (args, api, extraOptions) => {
    const result = await publicBaseQuery(args, api, extraOptions);
    
    if (result.error && result.error.status === 403) {
      console.warn("Access denied - this handbook may not be public or IP restricted");
    }

    return result;
  };
}

export const baseApi = createApi({
  reducerPath: "api",
  baseQuery: createBaseQueryWithCsrf(),
  tagTypes: [
    "Session",
    "Organization",
    "User",
    "Handbook",
    "CentralHandbook",
    "Chapter",
    "CentralChapter",
    "Section",
    "CentralSection",
    "Comment",
    "Editor",
    "Link",
    "LinkCollection",
    "File",
    "Image",
    "Search",
    "Publication",
    "PendingPublications",
    "Attachment",
    "AttachmentCount",
    "ReadingLink",
  ],
  endpoints: () => ({}),
});

export const publicApi = createApi({
  reducerPath: "publicApi",
  baseQuery: createPublicBaseQuery(),
  tagTypes: [
    "Handbook",
    "Chapter", 
    "Section",
    "Search",
    "Attachment",
    "AttachmentCount",
    "ReadingLink",
  ],
  endpoints: () => ({}),
});

export type BaseApi = typeof baseApi;
export type PublicApi = typeof publicApi;
