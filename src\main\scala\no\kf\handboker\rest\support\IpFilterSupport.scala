package no.kf.handboker.rest.support

import no.kf.config.{AccessKey, BrukerAdmBaseUrl, MockBrukerAdm}
import no.kf.cache.CachedWebGetter
import no.kf.util.JavaUtils.stringToOption
import no.kf.rest.support.JsonSupport

trait IpFilterSupport {
  this: RegistrySupport with JsonSupport =>

  lazy val baseUrl = componentRegistry.settings.settingFor(BrukerAdmBaseUrl)
  lazy val accessKey = componentRegistry.settings.settingFor(AccessKey)

  lazy val ipAccessUrl = s"$baseUrl/open/services/organization-id/#{IP}/accesskey/$accessKey"
  lazy val mockBrukerAdm: Boolean = componentRegistry.settings.settingFor(MockBrukerAdm).toBoolean


  def getOrganizationsForIp: List[String] = {
    if (mockBrukerAdm) {
      log.warn("Using mock data because MockBrukerAdm is true, should only be active in test!")
      List("9900", "1002", "9999", "1622", "0301", "1657", "3805")
    } else {
      val ip = getIp
      log.debug(s"Looking up ip-address for access: $ip")
      val url = ipAccessUrl.replace("#{IP}", ip)
      CachedWebGetter.cachedHttpGet(url).map(parseAsList).getOrElse(Nil)
    }
  }

  private def getIp: String = {
    stringToOption(request.getHeader("X-Forwarded-For")).getOrElse(request.getRemoteAddr).split(",").head.trim
  }

  private def parseAsList(jsonList: String): List[String] = {
    parse(jsonList).extract[List[String]]
  }
}
