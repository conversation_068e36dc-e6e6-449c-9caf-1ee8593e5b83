import { baseApi, API_BASE_URLS } from "@/store/api";
import type {
  SessionInfo,
  LDAPUser,
  SessionGetResponse,
  SessionSetOrgResponse,
  SessionEditorsResponse,
} from "@/types";

export const sessionApi = baseApi.injectEndpoints({
  overrideExisting: process.env.NODE_ENV === "development",
  endpoints: (builder) => ({
    getSession: builder.query<SessionGetResponse, void>({
      query: () => ({
        url: `${API_BASE_URLS.SESSION}`,
        method: "GET",
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      }),
      providesTags: ["Session"],
      keepUnusedDataFor: 0,
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      transformResponse: (response: SessionInfo): SessionGetResponse => {
        return response;
      },
      transformErrorResponse: (response: { status: string | number }) => {
        if (response.status === 401) {
          return {
            error: "Authentication required",
            message: "Please log in to access this application",
          };
        }
        return {
          error: "Session loading failed",
          message: "Unable to load session information",
        };
      },

      onQueryStarted: async (_arg, { queryFulfilled, dispatch }) => {
        try {
          if (
            typeof window !== "undefined" &&
            window.__PRELOADED_SESSION_STATE__
          ) {
            try {
              const preloadedSession = JSON.parse(
                window.__PRELOADED_SESSION_STATE__
              );
              delete window.__PRELOADED_SESSION_STATE__;

              dispatch(
                sessionApi.util.upsertQueryData(
                  "getSession",
                  undefined,
                  preloadedSession
                )
              );
              return;
            } catch (error) {
              console.warn("Failed to parse preloaded session state:", error);
            }
          }

          await queryFulfilled;
        } catch (error) {
          console.error("Session fetch failed:", error);
        }
      },
    }),

    setSessionOrganization: builder.mutation<SessionSetOrgResponse, string>({
      query: (externalOrgId) => ({
        url: `${API_BASE_URLS.SESSION}/${externalOrgId}/`,
        method: "POST",
      }),
      invalidatesTags: ["Organization"],
      transformResponse: (response: SessionInfo): SessionSetOrgResponse => {
        return response;
      },

      onQueryStarted: async (_externalOrgId, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;

          dispatch(
            sessionApi.util.upsertQueryData("getSession", undefined, data)
          );

          dispatch(
            sessionApi.util.invalidateTags([
              "Organization",
              "Handbook",
              "CentralHandbook",
              "Editor",
              "User",
            ])
          );
        } catch (error) {
          console.error("Failed to change organization:", error);
        }
      },
    }),

    getSessionEditors: builder.query<SessionEditorsResponse, string>({
      query: (externalOrgId) => ({
        url: `${API_BASE_URLS.SESSION}/editors/${externalOrgId}/`,
        method: "GET",
      }),
      providesTags: (_result, _error, externalOrgId) => [
        { type: "User", id: `editors-${externalOrgId}` },
        "User",
      ],
      transformResponse: (response: LDAPUser[]): SessionEditorsResponse => {
        return response;
      },
    }),
  }),
});

export const {
  useGetSessionQuery,
  useSetSessionOrganizationMutation,
  useGetSessionEditorsQuery,
  useLazyGetSessionQuery,
  useLazyGetSessionEditorsQuery,
} = sessionApi;
