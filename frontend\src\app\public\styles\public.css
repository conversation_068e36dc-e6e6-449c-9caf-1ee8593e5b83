body {
  overflow: auto;
}

footer a,
footer a:hover {
  color: #0000ff;
  text-decoration: underline;
}

.public-navbar {
  border-bottom: 1px solid #e9ecef;
  background-color: #fff;
}

.navbar-banner {
  flex-grow: 1;
  text-align: center;
}

/* Header/Hero Styles */
.public-hero {
  min-height: 120px;
  background-color: #4673b2;
  color: #fff;
}

.public-hero .hero-body {
  padding: 3rem 1.5rem;
}

.public-hero .hero-footer {
  padding: 0.75rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.handbook-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.handbook-title {
  margin: 0 !important;
  color: white;
  font-size: 1.75rem;
  font-weight: 600;
}

.organization-subtitle {
  margin: 0 !important;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-weight: 400;
}

.organization-logo {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.organization-logo img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 4px;
}

.search-input-container {
  margin-left: auto;
}

.search-input-container .input {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.search-input-container .input:focus {
  background: white;
  border-color: #3273dc;
  box-shadow: 0 0 0 0.125em rgba(50, 115, 220, 0.25);
}

/* Navigation Tabs */
.public-tabs {
  margin: 0;
}

.public-tabs .tabs-tab {
  border-color: rgba(255, 255, 255, 0.3) !important;
}

.public-tabs .tabs-tab.is-active {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.public-tabs .tabs-tab a {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.public-tabs .tabs-tab.is-active a {
  color: white;
}

.hero.is-info .tabs.is-boxed li.is-active a {
  color: #050037 !important;
}

/* Dropdown Links */
.links-dropdown {
  position: relative;
  display: inline-block;
}

.links-dropdown button {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem 0.75rem;
}

.links-dropdown button:hover {
  color: white;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  min-width: 200px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
}

.dropdown-section-title {
  padding: 0.75rem 1rem;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  font-size: 0.9rem;
}

.dropdown-link {
  display: block;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #007bff;
  transition: background-color 0.2s ease;
}

.dropdown-link:hover {
  background: #f8f9fa;
  text-decoration: none;
}

/* Loading states */
.loading-placeholder {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

.public-chapter-component,
.public-section-component {
  .chapter-icon {
    justify-content: flex-start;

    svg {
      width: 20px;
    }
  }

  .section-icon {
    margin-right: 4px;
    justify-content: flex-start;

    svg {
      width: 18px;
    }
  }

  .section {
    padding: 0 1.5rem;
  }
}

.public-section-component {
  margin-top: 1.5rem;

  .content-header {
    align-items: center;
    margin-bottom: 0;

    .title {
      flex-grow: 1;
      margin: 0;
      display: flex;
      align-items: center;
    }
  }
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .public-hero .hero-body {
    padding: 1rem 0;
  }

  .handbook-title {
    font-size: 1.5rem;
  }

  .organization-subtitle {
    font-size: 1rem;
  }

  .search-input-container {
    max-width: 100%;
    margin-left: 0;
    margin-top: 1rem;
  }

  .public-tabs {
    font-size: 0.9rem;
  }

  .dropdown-menu {
    left: -50px;
    right: -50px;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .handbook-title {
    font-size: 1.25rem;
  }

  .organization-logo {
    width: 40px;
    height: 40px;
  }

  .search-input-container .input {
    font-size: 16px;
  }
}

.public-attachment-container {
  position: relative;
}

.tree-item {
  transition: all 0.2s ease;

  .tree-item-content a {
    width: 100%;
    padding: 0.4rem 0.4rem 0.4rem 0.5rem;
    font-size: 1rem;
  }
}

.tree-item.active .tree-item-content a {
  background-color: rgba(0, 123, 255, 0.1);
  border-radius: 4px;
  font-weight: 600;
}

.tree-item.has-active-child > .tree-item-content a {
  font-weight: 500;
  color: #4673b2;
}

.tree-item-content a:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  transition: background-color 0.15s ease;
}

.section-item.active .tree-item-content a {
  background-color: #4673b2;
  padding-left: 8px;
}

.tree-children {
  overflow: hidden;
  transition:
    max-height 0.3s ease,
    opacity 0.2s ease;
}

.tree-item.section-item {
  font-size: 0.9em;
}

.tree-item.section-item .tree-item-content {
  opacity: 0.85;
}

.tree-item-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 2px;
  padding-left: var(--tree-depth-padding, 0);
}

.tree-expand-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
    transition: background-color 0.15s ease;
  }

  .icon {
    transition: transform 0.2s ease;
  }

  &.expanded .icon {
    transform: rotate(360deg);
  }
}

.tree-item-link {
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tree-item-icon {
  margin-right: 4px;
}

.tree-item.auto-expanded > .tree-item-content {
  background-color: rgba(0, 123, 255, 0.05);
  border-radius: 4px;
}

html {
  scroll-behavior: smooth;
}

.content-header.field.is-grouped {
  justify-content: space-between;
}

.button.public-attachment-btn.button {
  border-color: #ffffff;
  transition: ease-in-out all 0.2s;
  border: none;
  background: none;
  padding: 6px 16px;
  cursor: pointer;
  box-shadow: none;
  border-radius: 4px;
}

.button.public-attachment-btn.button svg {
  height: 24px;
  width: auto;
}

.button.public-attachment-btn.button:hover {
  border-color: #050037;
  background-color: #050037;
  color: #ffffff;
}

.chapter-title {
  margin-bottom: 0 !important;
  display: flex;
  align-items: center;
  gap: 6px;

  .icon {
    justify-content: flex-start;
  }
}

.chapter-attachments {
  margin-left: 1rem;
  right: 26px;
}

.section-content {
  margin-right: 0;
}

.attachment-popup {
  position: absolute;
  right: 0;
  top: 50px;
  background: #ffffff;
  border: 1px solid #cccccc;
  max-width: 350px;
  min-width: 200px;
  text-align: center;
  border-radius: 6px;
  z-index: 1000;
  min-height: 41px;
}

.attachment-popup-content {
  padding: 0;
}

.attachment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.attachment-header h4 {
  margin: 0;
  font-size: 1rem;
}

.attachment-list {
  max-height: 300px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #eee;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-info {
  flex-grow: 1;
}

.attachment-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.attachment-size {
  font-size: 0.875rem;
  color: #666;
}

/* Legacy Attachment Popup Styles */
.attachment-link {
  padding: 8px 16px;
  display: block;
  text-align: left;
  transition: ease-in-out 0.1s;
  background-color: transparent;
  outline: none;
  border: none;
  width: 100%;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 15px;
  color: #050037;
}

.attachment-link:hover {
  background-color: #050037;
  color: #ffffff;
}

.attachment-loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 100px;
  flex-direction: column;
  gap: 8px;
}

.attachment-link svg {
  width: 25px;
  height: 25px;
  min-width: 25px;
  max-width: 25px;
}

.attachment-link span {
  width: max-content;
}

.attachment-loading-message .icon {
  background-color: #b4c6e7;
  border-radius: 50%;
  padding: 16px;
  color: #4673b2;
}

.attachment-popup .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
  top: calc((0.5rem + 1px) * -1);
  right: 7px;
}

.attachment-popup .arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-bottom-color: #a7a7a7;
}

.attachment-popup .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  top: 1px;
  border-bottom-color: #fff;
}

.attachment-public-content {
  max-height: 205px;
  overflow-y: auto;
  border-radius: 6px;
}

.attachment-popup ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.attachment-popup ::-webkit-scrollbar-track {
  background: #ffffff;
}

.attachment-popup ::-webkit-scrollbar-thumb {
  background: #e6e6e6;
  border-radius: 9px;
}

.attachment-popup ::-webkit-scrollbar-thumb:hover {
  background: #d1d1d1;
}

.attachment-popup ::-webkit-scrollbar-thumb:active {
  background: #d1d1d1;
}

/* Tree Styles */
.tree-children {
  margin-left: 1rem;
}

.has-active-child > a {
  font-weight: 500;
}

/* Content Styles */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.chapter-title {
  margin: 0;
}

.section-content {
  padding: 1rem 0;
}

/* Mobile Tree Styles */
@media (max-width: 768px) {
  .mobile-tree-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    z-index: 1000;
    overflow: auto;
    padding: 1rem;
  }
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .attachment-popup {
    max-width: none;
  }

  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Legacy CSS compatibility */
.chapter-attachments .public-attachment-btn {
  margin-left: 0.5rem;
}

/* Loading Spinner */
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #666;
  font-style: italic;
}

/* Footer adjustments */
.kf-footer {
  margin-top: auto;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* Cookie Consent - Legacy Styles */
.toggle-cookies-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
  width: fit-content;
  transition: background-color 0.1s ease;
  padding-right: 6px;
  border-radius: 4px;
}

.toggle-cookies-container:hover {
  background-color: #d6d6e5;
}

.toggle-cookies-container i {
  font-size: 14px;
  transition: transform 0.6s ease;
  cursor: pointer;
}

.toggle-cookies-container i.rotate {
  transform: rotate(180deg);
}

.cookies-wrapper {
  overflow: hidden;
  transition: max-height 0.6s ease;
}

.cookie-type-title {
  margin: 24px 0;
  font-size: 16px;
  font-weight: 700;
  text-align: left;
}

.cookie-type-section-container label {
  display: flex;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.cookie-type-section-container label input {
  position: absolute;
  left: -9999px;
}

.cookie-type-section-container label input:checked + span {
  background-color: #050037;
  color: #ffffff;
}

.cookie-type-section-container label input:checked + span:before {
  box-shadow: inset 0 0 0 0.4375em #00005c;
}

.cookie-type-section-container label span {
  display: flex;
  align-items: center;
  border-radius: 99em;
  transition: 0.25s ease;
  padding: 8px 32px;
  justify-content: center;
  background-color: #d9d9d9;
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
  text-align: left;
  color: #021815;
}

.cookie-type-section-container label span:hover {
  background-color: #d6d6e5;
}

.cookie-type-section-container {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 24px;
  justify-content: center;
}

.cookie-desc {
  margin-bottom: 12px;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc {
  text-decoration: underline;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc:hover {
  color: #3273dc;
}

.toggle-cookies-button {
  font-size: 16px;
  font-weight: 700;
  line-height: 19.36px;
  text-align: left;
  background-color: transparent;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #363636;
}

.cookie-settings-btn {
  background-color: transparent;
  border: none;
  color: #0000ff;
  text-decoration: underline;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
}

.hero-foot {
  .tab-button:hover {
    border-bottom-color: transparent;
  }

  .has-dropdown {
    transition: all 0.2s ease-in-out;
    border-start-end-radius: 0.375rem;
    border-start-start-radius: 0.375rem;
    padding: 7px 18px;

    &:hover {
      background-color: rgba(10, 10, 10, 0.3);
    }

    .tab-button {
      &:focus {
        box-shadow: none;
      }
    }

    .links-dropdown {
      .dropdown-menu {
        top: 35px;
        left: -14px;
        z-index: 1000000;
        display: unset;
        background-color: #ffffff;
        min-width: 300px;
        max-width: 500px;
        width: max-content;

        .dropdown-link {
          justify-content: space-between;
          gap: 12px;
          color: #363636;
          border-radius: 0;
          margin-bottom: 0;
          border-bottom: 1px solid #f2f2f2;

          &:hover {
            color: #4673b2;
            background-color: #f5f5f5;
          }

          span {
            white-space: initial;
            text-align: left;
          }

          &:focus {
            border-bottom-color: unset;
            box-shadow: none;
          }
        }
      }

      .dropdown-toggle {
        display: flex;
        align-items: center;
        color: #ffffff;

        .icon {
          margin-right: 0;
          width: auto;
        }
      }
    }
  }
}

.search-result-icon {
  svg {
    width: 20px;
    height: 20px;
  }
}

.mobile-tree-toggle {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mobile-tree-toggle button {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 44px;
  font-size: 1rem;
  font-weight: 500;
}

.mobile-tree-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
}

.mobile-tree-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.mobile-tree-sidebar {
  position: fixed;
  top: 0;
  left: -100%;
  width: 85%;
  max-width: 400px;
  height: 100vh;
  background: white;
  z-index: 10000;
  overflow-y: auto;
  transition: left 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-tree-sidebar.visible {
  left: 0;
}

.mobile-tree-header {
  position: sticky;
  top: 0;
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem;
  z-index: 1;
}

.mobile-tree-close {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.mobile-tree-close button {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  font-size: 1.25rem;
  transition: all 0.2s ease;
}

.mobile-tree-close button:hover {
  background: #e9ecef;
  color: #495057;
}

.mobile-tree-content {
  padding: 0 1rem 2rem;
}

.attachment-link,
.dropdown-link {
  min-height: 44px;
  display: flex;
  align-items: center;
  padding: 0.75rem;
}

.public-hero .hero-body {
  padding: 1.5rem 0;
}

.public-hero .columns {
  gap: 1rem;
  align-items: flex-start;
}

.search-input-container {
  transition: all 0.3s ease;
}

.public-tabs {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.public-tabs::-webkit-scrollbar {
  display: none;
}

.public-tabs .tabs-tab {
  flex-shrink: 0;
  min-width: max-content;
}

.public-tabs .tabs-tab a {
  padding: 0.75rem 1rem;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.dropdown-menu {
  transform: translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;
}

.dropdown-menu.visible {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

@media (max-width: 480px) {
  .public-hero .hero-body {
    padding: 1rem 0;
  }

  .handbook-title {
    font-size: 1.25rem !important;
    line-height: 1.3;
  }

  .organization-subtitle {
    font-size: 0.9rem !important;
  }

  .organization-logo {
    width: 36px !important;
    height: 36px !important;
  }

  .search-input-container {
    margin-top: 1rem;
    width: 100%;
  }

  .search-input-container .input {
    font-size: 16px;
  }

  .public-tabs {
    font-size: 0.85rem;
  }

  .mobile-tree-sidebar {
    width: 90%;
  }

  .attachment-popup {
    max-width: 80vw;
  }

  .section-content {
    font-size: 0.9rem;
  }

  .dropdown-menu {
    left: -1rem;
    right: -1rem;
    min-width: auto;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .public-hero .hero-body {
    padding: 1.5rem 0;
  }

  .handbook-title {
    font-size: 1.5rem !important;
  }

  .organization-subtitle {
    font-size: 1rem !important;
  }

  .search-input-container {
    margin-top: 0.5rem;
  }

  .mobile-tree-sidebar {
    width: 80%;
    max-width: 350px;
  }

  .attachment-popup {
    max-width: 300px;
  }

  .dropdown-menu {
    min-width: 250px;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .public-hero .hero-body {
    padding: 2rem 0;
  }

  .public-hero .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .handbook-title {
    font-size: 1.75rem !important;
  }

  .search-input-container {
    max-width: 350px;
  }

  .section-content {
    font-size: 1rem;
  }
}

@media (min-width: 1025px) {
  .public-hero .hero-body {
    padding: 2.5rem 0;
  }

  .handbook-title {
    font-size: 2rem !important;
  }

  .organization-subtitle {
    font-size: 1.2rem !important;
  }

  .search-input-container {
    max-width: 400px;
  }
}

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .organization-logo img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

@media (max-height: 500px) and (orientation: landscape) {
  .mobile-tree-header {
    padding: 0.5rem 1rem;
  }

  .mobile-tree-close button {
    min-height: 36px;
    min-width: 36px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .mobile-tree-overlay,
  .mobile-tree-sidebar,
  .dropdown-menu,
  .tree-expand-button .icon,
  * {
    transition: none !important;
    animation: none !important;
  }
}

@media (prefers-color-scheme: dark) {
  .mobile-tree-sidebar {
    background: #1a1a1a;
    color: #ffffff;
  }

  .mobile-tree-header {
    border-bottom-color: #333;
    background: #1a1a1a;
  }

  .mobile-tree-close button {
    background: #333;
    border-color: #444;
    color: #ccc;
  }

  .mobile-tree-close button:hover {
    background: #444;
    color: #fff;
  }
}

@media print {
  .mobile-tree-toggle,
  .mobile-tree-overlay,
  .mobile-tree-sidebar,
  .public-hero .hero-footer,
  .search-input-container {
    display: none !important;
  }

  .public-hero {
    background: none !important;
    color: black !important;
    min-height: auto !important;
  }

  .handbook-title,
  .organization-subtitle {
    color: black !important;
  }
}

.mobile-tree-toggle button:focus,
.mobile-tree-close button:focus,
.dropdown-link:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.mobile-tree-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
}

.mobile-tree-loading .spinner {
  margin-right: 0.75rem;
}

.kf-footer {
  margin-top: auto;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 2rem 0;
}

.kf-footer .level {
  margin: 0;
}

.kf-footer .level-item {
  gap: 1rem;
}

.kf-footer .level-item[style*="flex-direction: column"] a,
.kf-footer .level-item[style*="flex-direction: column"] button {
  margin-bottom: 0.5rem;
}

.kf-footer .level-item[style*="flex-direction: column"] a:last-of-type,
.kf-footer .level-item[style*="flex-direction: column"] button:last-child {
  margin-bottom: 0;
}

.kf-footer a {
  color: #0000ff;
  text-decoration: underline;
  transition: color 0.2s ease;
  font-size: 1rem;
  line-height: 1.5;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
}

.kf-footer a:hover {
  color: #0000cc;
  text-decoration: underline;
}

.kf-footer .cookie-settings-btn {
  background-color: transparent;
  border: none;
  color: #0000ff;
  text-decoration: underline;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
  min-height: 44px;
  padding: 0.5rem;
  transition: color 0.2s ease;
}

.kf-footer .cookie-settings-btn:hover {
  color: #0000cc;
}

.kf-footer .level-item:last-child {
  font-size: 1rem;
  font-weight: 500;
  color: #495057;
}

@media (max-width: 480px) {
  .kf-footer {
    padding: 1.5rem 0;
  }

  .kf-footer .level {
    flex-direction: column;
    gap: 1.5rem;
  }

  .kf-footer .level-item {
    width: 100%;
    max-width: none;
    margin: 0;
  }

  .kf-footer .level-item[style*="flex-direction: column"] {
    gap: 0.75rem;
  }

  .kf-footer .level-item[style*="flex-direction: column"] a,
  .kf-footer .level-item[style*="flex-direction: column"] button {
    font-size: 0.9rem;
    padding: 0.75rem 0.5rem;
    margin-bottom: 0.5rem;
  }

  .kf-footer .level-item:last-child {
    font-size: 0.9rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;
    margin-top: 0.5rem;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .kf-footer {
    padding: 1.75rem 0;
  }

  .kf-footer .level {
    flex-direction: column;
    gap: 1.5rem;
  }

  .kf-footer .level-item {
    width: 100%;
    max-width: none;
    margin: 0;
  }

  .kf-footer .level-item[style*="flex-direction: column"] a,
  .kf-footer .level-item[style*="flex-direction: column"] button {
    font-size: 0.95rem;
  }

  .kf-footer .level-item:last-child {
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .kf-footer .level {
    justify-content: space-between;
    align-items: flex-start;
  }

  .kf-footer .level-item[style*="flex-direction: column"] {
    flex: 0 1 auto;
    max-width: 60%;
  }

  .kf-footer .level-item:last-child {
    flex: 0 1 auto;
    max-width: 35%;
    text-align: right;
  }
}

@media (min-width: 1025px) {
  .kf-footer {
    padding: 2rem 0;
  }

  .kf-footer .level {
    justify-content: space-between;
    align-items: center;
  }
}

@media (max-height: 500px) and (orientation: landscape) {
  .kf-footer {
    padding: 1rem 0;
  }

  .kf-footer .level-item[style*="flex-direction: column"] a,
  .kf-footer .level-item[style*="flex-direction: column"] button {
    padding: 0.5rem;
    min-height: 36px;
  }
}

@media (prefers-contrast: high) {
  .kf-footer {
    background-color: #ffffff;
    border-top: 2px solid #000000;
  }

  .kf-footer a,
  .kf-footer .cookie-settings-btn {
    color: #0000ff;
    font-weight: 600;
  }

  .kf-footer a:hover,
  .kf-footer .cookie-settings-btn:hover {
    color: #000000;
    background-color: #ffff00;
  }
}

.kf-footer a:focus,
.kf-footer .cookie-settings-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
  border-radius: 2px;
}

@media print {
  .kf-footer {
    background: none !important;
    border: none !important;
    padding: 1rem 0 !important;
  }

  .kf-footer a,
  .kf-footer .level-item:last-child {
    color: black !important;
  }

  .kf-footer .cookie-settings-btn {
    display: none !important;
  }
}

@media (prefers-reduced-motion: reduce) {
  .kf-footer a,
  .kf-footer .cookie-settings-btn {
    transition: none !important;
  }
}

@media (max-width: 1024px) {
  .public-hero .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .public-tabs button a {
    padding: 10px 18px;
  }
}

@media (max-width: 1024px) {
  .hero-foot {
    & .has-dropdown {
      & .links-dropdown {
        .dropdown-menu {
          display: block !important;
          transform: translateY(-10px);
          opacity: 0;
          visibility: hidden;
          transition: all 0.2s ease;
          pointer-events: none;
          top: 41px;
          left: unset;
          right: -12px;
        }

        .dropdown-menu.visible {
          transform: translateY(0);
          opacity: 1;
          visibility: visible;
          pointer-events: auto;
        }
      }
    }
  }
}
