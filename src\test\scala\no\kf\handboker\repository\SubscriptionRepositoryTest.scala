package no.kf.handboker.repository

import no.kf.handboker.model.User
import no.kf.handboker.model.local.Handbook
import org.apache.derby.shared.common.error.DerbySQLIntegrityConstraintViolationException
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.scalatest.{BeforeAndAfterEach, FunSuite}

@RunWith(classOf[JUnitRunner])
class SubscriptionRepositoryTest extends FunSuite with DbTestHandler with BeforeAndAfterEach {

  val repository = componentRegistry.subscriptionRepository
  val handbookRepo = componentRegistry.handbookRepository

  //  val avvikHandbook = inTransaction { handbookRepo.persistHandbook(Handbook(None, "KF Avvik-Håndbok", None, "9900", false, false)) }
  val userKari = User("id", "karis.mail", None)
  val userOla = User("id", "olas.mail", None)

  transactedTest("that we can store a subscription") {
    val hmsHandbook = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9900", false, false))
    repository.persistSubscription(userKari.email, hmsHandbook)
  }

  transactedTest("that we can retrieve all subscriptions by handbook") {
    val hmsHandbook = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9900", false, false))
    repository.persistSubscription(userKari.email, hmsHandbook)
    repository.persistSubscription(userOla.email, hmsHandbook)

    val res = repository.retrieveSubscriptionsForHandbook(hmsHandbook.id.get)

    assert(res.size === 2)
  }

  transactedTest("that we can delete a subscription") {
    val hmsHandbook = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9900", false, false))
    repository.persistSubscription(userKari.email, hmsHandbook)
    assert(repository.retrieveSubscriptionsForHandbook(hmsHandbook.id.get).size === 1)

    repository.deleteSubscription(userKari.email, hmsHandbook)

    val res = repository.retrieveSubscriptionsForHandbook(hmsHandbook.id.get)
    assert(res.isEmpty)
  }

  transactedTest("That we can retrieve all subscriptions for users") {
    val hmsHandbook1 = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok 1", None, "9900"))
    val hmsHandbook2 = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok 2", None, "9900"))
    val hmsHandbook3 = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok 3", None, "9900"))
    repository.persistSubscription(userKari.email, hmsHandbook1)
    repository.persistSubscription(userKari.email, hmsHandbook2)
    repository.persistSubscription(userOla.email, hmsHandbook3)

    val kariHandbookIds = repository.retrieveSubscriptionsForUser(userKari.email)
    val olaHandbookIds = repository.retrieveSubscriptionsForUser(userOla.email)

    assert(kariHandbookIds.size == 2)
    assert(kariHandbookIds.contains(hmsHandbook1.id.get))
    assert(kariHandbookIds.contains(hmsHandbook2.id.get))
    assert(olaHandbookIds.size == 1)
    assert(olaHandbookIds.contains(hmsHandbook3.id.get))
  }

  transactedTest("That we can't store multiple subscriptions for the same user on the same handbook") {
    val hmsHandbook = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9900"))
    repository.persistSubscription(userKari.email, hmsHandbook)

    intercept[DerbySQLIntegrityConstraintViolationException] {
      repository.persistSubscription(userKari.email, hmsHandbook)
    }
  }


  transactedTest("that we can store a change notification") {
    val hmsHandbook = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9900", false, false))
   // repository.persistChangeNotification("change description", hmsHandbook)
  }

  transactedTest("that we don't store the same change twice") {
    val hmsHandbook = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9900", false, false))
    val changeDescription = "change description"
    //repository.persistChangeNotification(changeDescription, hmsHandbook)
    //repository.persistChangeNotification(changeDescription, hmsHandbook)

    val res = repository.retrieveAndDeleteAllChangeNotifications
    assert(res.size === 1)
  }

  transactedTest("that we can retrieve all change notifications and they get deleted afterwards") {
    val hmsHandbook = handbookRepo.persistHandbook(Handbook(None, "KF HMS-Håndbok", None, "9900", false, false))
    //repository.persistChangeNotification("change description", hmsHandbook)
    //repository.persistChangeNotification("another change description", hmsHandbook)

    val res = repository.retrieveAndDeleteAllChangeNotifications
    assert(res.size === 2)

    val emptyRes = repository.retrieveAndDeleteAllChangeNotifications
    assert(emptyRes.isEmpty)
  }

}
