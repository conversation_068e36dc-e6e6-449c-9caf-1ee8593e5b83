package no.kf.handboker.rest

import no.kf.handboker.rest.support.SessionSupport
import no.kf.handboker.service.search.{SearchService}
import no.kf.rest.support.{ExternalOrgIdExtractionSupport, JsonSupport}
import no.kf.util.StopWatch
import org.scalatra.ScalatraServlet

class SearchServlet extends ScalatraServlet with SessionSupport with JsonSupport with ExternalOrgIdExtractionSupport with StopWatch {

  lazy val searchService: SearchService = componentRegistry.searchService

  get("/?") {
    val query = extractRequiredParam("query")
    val page = extractOptionalInt("page")
    val handbookId = params.get("handbookId")

    log.debug(s"SearchServlet got query $query")

    searchService.doSearch(currentExternalOrganizationId, query, handbookId, page)
  }

  post("/index/?") {
    val externalOrgs = parsedBody.extract[List[String]]
    ifNotThen403(currentUser.globalAdmin, "delete all indexes for multiple orgs")
    log.info(s"Attempting to reset all indexes for $externalOrgs")
    withStopWatch(s"Reset all indexes for $externalOrgs", Warn) {
      val results = externalOrgs.map(org => {
        log.info(s"Attempting to reset all indexes for ${org}")
        searchService.doReindex(org)
      })
      log.info(s"$results")
      List(results.filter(r => r), results.filter(r => !r))
    }
  }

  post("/reset/?") {
    log.info("Attempting to reset all indexes")
    ifNotThen403((currentUser.globalAdmin || currentUser.localAdmin), "delete all indexes")
    searchService.doReindex(currentExternalOrganizationId)
  }
  
}
