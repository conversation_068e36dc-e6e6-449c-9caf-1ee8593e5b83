package no.kf.handboker.repository

import no.kf.db.IDGenerator
import no.kf.db.RichSQL._
import no.kf.util.Logging

trait CentralAccessRepositoryComponent {
  this: DbConnectionManagerComponent =>

  val centralAccessRepository: CentralAccessRepository

  object CentralAccessTableDef {
    val accessTableName = "centralcontentaccess"

    val fieldId = "id"
    val fieldExternalOrgId = "external_org_id"
    val fieldImportedHandbookId = "importedhandbook_id"

    val insertString = List(fieldId, fieldExternalOrgId, fieldImportedHandbookId)
  }

  class CentralAccessRepositoryImpl extends CentralAccessRepository with Logging {

    import CentralAccessTableDef._

    override def persistAccesses(externalOrgId: String, centralHandbookIds: List[String]) {
      // We always overwrite the result, so we delete the previous entries
      deleteAccesses(externalOrgId)

      val sql = s"INSERT INTO $accessTableName ${insertString.mkString("(", ",", ")")} VALUES (?,?,?)"
      connectionManager.doWithConnection {
        conn => {
          val ps = conn.ps(sql)
          centralHandbookIds.foreach(bookId => {
            ps.pos = 1
            ps << IDGenerator.generateUniqueId << externalOrgId << bookId
            ps.addBatch()
          })
          ps.executeBatch()
        }
      }
    }

    override def retrieveAccesses(externalOrgId: String): List[String] = {
      val sql = s"SELECT $fieldImportedHandbookId FROM $accessTableName WHERE $fieldExternalOrgId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << externalOrgId <<! populateString
      }.toList
    }

    def deleteAccesses(externalOrgId: String): Unit = {
      val sql = s"DELETE FROM $accessTableName WHERE $fieldExternalOrgId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << externalOrgId <<!
      }
    }

    private def populateString(rs: RichResultSet): String = rs
  }
}

trait CentralAccessRepository {
  def persistAccesses(externalOrgId: String, centralHandbooks: List[String])
  def retrieveAccesses(externalOrgId: String): List[String]
}
