package no.kf.handboker.rest

import no.kf.handboker.config.HandbookApiKey
import no.kf.handboker.model.api.TreeStructureHandbook
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import no.kf.handboker.rest.support.RegistrySupport
import no.kf.handboker.service.api.HandbookApiService
import no.kf.rest.ScalatraExceptions
import no.kf.rest.support.JsonSupport
import org.joda.time.DateTimeZone
import org.joda.time.format.ISODateTimeFormat
import org.scalatra.ScalatraServlet

//Swagger
import org.scalatra.swagger._
import org.scalatra.swagger.ResponseMessage

class HandbookApiServlet(implicit val swagger: Swagger) extends ScalatraServlet with JsonSupport with RegistrySupport with SwaggerSupport {

  val handbookApiService: HandbookApiService = componentRegistry.handbookApiService
  lazy val organizations = componentRegistry.settings.repeatableSettingTupleFor(HandbookApiKey).toMap.map(_.swap)

  protected val applicationDescription = "The handbook API."

  before() {
    contentType = formats("json")
  }

  val getHandbooks: SwaggerSupportSyntax.OperationBuilder =
    (apiOperation[HandbookList]("getHandbooks")
      summary "Show all handbooks"
      description "Shows all the handbooks in the system."
      responseMessage ResponseMessage(401, "Could not match apiKey to any known organizations (Unauthorized)")
      )

  get("/handbooks", operation(getHandbooks)) {
    val externalOrgId = externalOrgIdOrThrowIfMissingKeyOrNoAccess()

    val handbooks = handbookApiService.retrieveHandbooksWithMetadata(externalOrgId)
    Map("handbooks" -> handbooks)
  }

  val findByHandbookId: SwaggerSupportSyntax.OperationBuilder =
    (apiOperation[SpecificHandbook]("findByHandbookId")
      summary "Finds a handbook by id"
      description "Shows a handbook for a given handbookId"
      parameter pathParam[String]("handbookId").description("HandbookId of the handbook that needs to be fetched")
      responseMessages (
        ResponseMessage(401, "Could not match apiKey to any known organizations (Unauthorized)"),
        ResponseMessage(404, "Unknown handbookId (Not Found)"),
        ResponseMessage(500, "An error occurred. Contact support. (Server Error)")
        )
      )

  get("/handbook/:handbookId", operation(findByHandbookId)) {
    val externalOrgId = externalOrgIdOrThrowIfMissingKeyOrNoAccess()
    val handbookId = extractRequiredParam("handbookId")

    val treeStructure = handbookApiService.retrieveHandbookTreeStructure(handbookId, externalOrgId)
    val result = treeStructure.getOrElse(ScalatraExceptions.notFound(Option(s"Could not create tree-structure for handbook with ID: $handbookId")))
    Map("handbook" -> result)
  }

  val findEditedByDate: SwaggerSupportSyntax.OperationBuilder =
    (apiOperation[EditedList]("findEditedByDate")
      summary "Find handbooks, chapters and sections edited after given date"
      description "Finds all handbooks, chapters and sections that have been edited after the given date. Demands ISO standard as date format: 2015-04-14T11:07:36.639Z"
      parameter pathParam[String]("date").description("Date by which changes must have occurred after")
      responseMessages (
        ResponseMessage(401, "Could not match apiKey to any known organizations (Unauthorized)"),
        ResponseMessage(500, "An error occurred. Contact support. (Server Error)")
        )
      )

  // Demands ISO standard as date format: 2015-04-14T11:07:36.639Z
  get("/edited/:date", operation(findEditedByDate)) {
    val externalOrgId = externalOrgIdOrThrowIfMissingKeyOrNoAccess()
    val dateAsString = extractRequiredParam("date")
    val parser = ISODateTimeFormat.dateTimeParser()
    val dateWithoutTimezone = parser.parseDateTime(dateAsString).withZone(DateTimeZone.forID("Europe/Oslo"))

    val (handbooks, chapters, sections) = handbookApiService.retrieveChangedEntitiesAfterDateForOrg(externalOrgId, dateWithoutTimezone)
    Map("handbooks" -> handbooks,
      "chapters" -> chapters,
      "sections" -> sections
    )
  }

  private def externalOrgIdOrThrowIfMissingKeyOrNoAccess(): String = {
    val apiKey = request.headers.get("accessKey")
    val keyEntry = apiKey.getOrElse(ScalatraExceptions.unauthorizedException("Missing Authorization field in HTTP-Header"))
    val externalOrgId = organizations.getOrElse(keyEntry, ScalatraExceptions.unauthorizedException(s"Could not match apiKey ($keyEntry) to any known organizations"))
    externalOrgId
  }
}

case class EditedList(handbooks: List[Handbook], chapters: List[Chapter], sections: List[Section])
case class HandbookList(handbooks: List[Handbook])
case class SpecificHandbook(handbook: TreeStructureHandbook)

