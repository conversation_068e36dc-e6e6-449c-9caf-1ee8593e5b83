package no.kf.handboker.integrationtest

import java.io.File

import no.kf.db.TransactionManager
import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.WebAppPath
import no.kf.test.TestResourceUtil
import no.kf.util.Logging
import org.scalatest.{BeforeAndAfterEach, Suite}

trait FullStack extends Suite with BeforeAndAfterEach with Logging with TestResourceUtil {

  def handbookConfigDir: File

  System.getProperties.setProperty("handboker.config.dir", handbookConfigDir.getAbsolutePath)

  class IntegrationTestRegistry extends ProductionRegistry with Logging {

    log.info("Starting up")

    def shutDown() {
      connectionManager.releaseAllConnections()
    }
  }

  var componentRegistry: IntegrationTestRegistry = _


  def testInTransaction[A](work: => A) {
    val txManager = new TransactionManager {
      val webAppPath = ProductionRegistry.componentRegistry.settings.settingFor(WebAppPath)
      val connectionManager = componentRegistry.connectionManager
    }
    txManager.inTransaction {
      componentRegistry.connectionManager.doWithConnection {
        con =>
          try {
            work
          }
          finally {
            con.rollback()
            log.info("Rollback performed")
          }
      }
    }
  }


  override def afterEach {
    componentRegistry.shutDown
  }

  override def beforeEach {
    componentRegistry = new IntegrationTestRegistry
  }
}
