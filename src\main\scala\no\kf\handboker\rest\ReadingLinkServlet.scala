package no.kf.handboker.rest

import no.kf.handboker.model.central.ReadingLink
import no.kf.handboker.rest.support.SessionSupport
import no.kf.rest.ScalatraExceptions
import no.kf.rest.support.JsonSupport
import org.scalatra.ScalatraServlet

class ReadingLinkServlet extends ScalatraServlet with SessionSupport with JsonSupport {

  lazy val readingLinkService = componentRegistry.readingLinkService
  lazy val centralAccessService = componentRegistry.centralAccessService

  /**
    * Retrieve valid links for sectionId
    */
  get("/link/:centralSectionId/?") {
    forKFAdminsOnly()
    val sectionId = extractRequiredParam("centralSectionId")
    readingLinkService.retrieveReadingLinkForSection(sectionId)
  }

  /**
    * Create a new reading link for a section
    */
  post("/?") {
    forKFAdminsOnly()
    val readingLink = parsedBody.extract[ReadingLink]
    readingLinkService.persistReadingLink(readingLink)
  }

  /**
    * Delete a reading link for a section
    * */
  delete("/:linkId/?"){
    forKFAdminsOnly()
    val linkId = extractRequiredParam("linkId")
    readingLinkService.deleteReadingLink(linkId)
  }

  /**
    * Delete all expired links
    * */
  delete("/expired/?"){
    forKFAdminsOnly()
    readingLinkService.deleteInvalidLinks()
  }


  def forKFAdminsOnly() = {
    if (!userIsKFAdmin(currentUser)) {
      ScalatraExceptions.forbiddenException("You do not have sufficient privileges")
    }
  }
}