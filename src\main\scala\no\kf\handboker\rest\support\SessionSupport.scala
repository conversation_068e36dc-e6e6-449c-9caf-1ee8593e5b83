package no.kf.handboker.rest.support

import no.kf.config.{AccessKey, ApplicationId, BrukerAdmBaseUrl, MockBrukerAdm}
import no.kf.handboker.config.{LDAPSearchBase, PublicWebAppPath, SpellCheckUrl, UseLDAP, WebAppPath}
import no.kf.rest.support.{JsonSupport, SessionSupport => KfSessionSupport}
import no.kf.handboker.model.{LDAPUser, Organization, SessionInfo}
import no.kf.model.{BrukerAdmExtOrg, Integration}
import no.kf.rest.ScalatraExceptions
import org.scalatra.{ScalatraBase, XsrfTokenSupport}

trait SessionSupport extends KfSessionSupport[LDAPUser, SessionInfo, Organization] with ScalatraBase with RegistrySupport with XsrfTokenSupport with JsonSupport {

  override protected val useLDAP: Boolean = componentRegistry.settings.settingFor(UseLDAP).toBoolean

  lazy val baseUrl = componentRegistry.settings.settingFor(BrukerAdmBaseUrl)
  lazy val appId = componentRegistry.settings.settingFor(ApplicationId)
  lazy val accessKey = componentRegistry.settings.settingFor(AccessKey)

  override lazy val brukerAdmUrl: String = s"$baseUrl/open/services/application-access/v3/$appId/accesskey/$accessKey"
  override lazy val mockBrukerAdm: Boolean = componentRegistry.settings.settingFor(MockBrukerAdm).toBoolean
  override lazy val ldapService = componentRegistry.ldapService

  before() {
    requireCasLogin()
  }

  after("/*") {
    response.setHeader("Cache-Control", "no-cache")
    response.setHeader("X-Frame-Options", "deny")
  }

  override def createSession(): SessionInfo = {
    val user = currentUser
    val ssoLogOutUrl = componentRegistry.globalSettingsApiService.retrieveSsoLogOutUrl
    SessionInfo(
      version,
      Some(user),
      currentExternalOrganization.map(org => s"$baseUrl/open/services/banner/${org.id}"),
      user.globalAdmin,
      currentExternalOrganization,
      getUsersAccessibleExternalOrganizations(),
      s"$baseUrl/open/services/logo/",
      componentRegistry.settings.settingFor(WebAppPath),
      componentRegistry.settings.settingFor(PublicWebAppPath),
      ssoLogOutUrl,
      componentRegistry.settings.settingFor(SpellCheckUrl)
    )
  }

  override def userHasAccess(user: LDAPUser, externalOrganizationId: String): Boolean = {
    user.organizations.contains(externalOrganizationId)
  }

  override protected def transformOrgFromBrukerAdmToApp(orgs: Seq[BrukerAdmExtOrg]): Seq[Organization] = {
    orgs.map(org => Organization(org.id, org.name, org.language, org.integrations))
  }

  override def createExternalOrganization(externalOrgId: String, name: String, language: Option[String], integrations: List[Integration]): Organization = {
    Organization(externalOrgId, name, language, integrations)
  }

  override protected def userIsKFAdmin(user: LDAPUser): Boolean = {
    user.globalAdmin
  }

  override def getUsersAccessibleExternalOrganizations(forceRefresh: Boolean): Seq[Organization] = {
    if (mockBrukerAdm) {
      val mockOrgs = Seq(
        BrukerAdmExtOrg("0220", "Asker", Option("nb"), Nil),
        BrukerAdmExtOrg("9900", "Kommuneforlaget", Option("nb"), Nil),
        BrukerAdmExtOrg("0301", "Oslo", Option("nb"), Nil),
        BrukerAdmExtOrg("9999", "Storevik", Option("nb"), Nil),
        BrukerAdmExtOrg("3805", "Larvik", Option("nb"), Nil)
      )
      transformOrgFromBrukerAdmToApp(mockOrgs)
    } else {
      super.getUsersAccessibleExternalOrganizations(forceRefresh)
    }
  }

  override protected def createUserMock(): LDAPUser = {
    LDAPUser("<EMAIL>", Some("mock mockesen"), "9900" :: "9999" :: "3805" :: Nil, None, true, false, true)
  }

  override protected def externalOrganizationDidChange(previousExternalOrganization: Option[Organization]): Unit = {
  }

  override def userHasSystemAccess(user: LDAPUser): Boolean = {
    user.localUser || user.localAdmin || user.globalAdmin
  }

  def ifNotThen403(func: => Boolean, operation: String): Unit = {
    if (!func) {
      val email = currentUser.email
      ScalatraExceptions.unauthorizedException(s"User with email '$email' not authorized to perform '$operation' operation")
    }
  }

  def getEditorsForOrganization(externalOrganizationId: String): Option[List[LDAPUser]] = {
    val base = componentRegistry.settings.settingFor(LDAPSearchBase)
    val orgGroupString = s"CN=$externalOrganizationId,OU=KFOrganizationAffiliation,$base"
    val editorGroupString = s"CN=Håndbøker-Redaktør,OU=Handboker,OU=KFApplications,$base"
    ldapService.findBrukerMemberOf(orgGroupString, editorGroupString)
  }
}
