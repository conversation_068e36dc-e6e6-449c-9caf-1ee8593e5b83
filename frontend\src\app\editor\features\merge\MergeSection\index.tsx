import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Title,
  Subtitle,
  Field,
  Input,
  Icon,
  Addons,
} from "kf-bui";
import moment from "moment";
import { toast } from "@/shared/components/Toast";
import { Spinner } from "@/shared/components/Spinner";
import { usePrefixedTranslation } from "@/libs/i18n";
import {
  useGetLocalSectionQuery,
  useSaveLocalSectionMutation,
} from "@/store/services/handbook/localHandbookApi";
import { useGetLatestCentralSectionVersionQuery } from "@/store/services/handbook/centralHandbookApi";
import { Wysiwyg, type WysiwygRef } from "../../shared/Wysiwyg";
import { RightRadio } from "../shared/RightRadio";
import type { Section } from "@/types";
import type { PublishedCentralSection } from "@/store/services/handbook/centralHandbookApi";

function mergeSection(
  element: Section,
  centralElement: PublishedCentralSection,
  title: string,
  text: string
): Section {
  return {
    ...element,
    title,
    text,
    pendingTitleChange: false,
    pendingTextChange: false,
    pendingChangeUpdatedDate: new Date().toISOString(),
    localTitleChange: title !== centralElement.title,
    localTextChange: text !== centralElement.text,
  };
}

export const MergeSection = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const t = usePrefixedTranslation("editor.components.MergeSection");

  const [centralSection, setCentralSection] = useState<
    PublishedCentralSection | undefined
  >(undefined);
  const [titleRadio, setTitleRadio] = useState<string | undefined>(undefined);
  const [textRadio, setTextRadio] = useState<string | undefined>(undefined);
  const [newTitle, setNewTitle] = useState<string>("");
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const newWysiwygRef = useRef<WysiwygRef | null>(null);

  const { data: localSection, isLoading: isLoadingLocal } =
    useGetLocalSectionQuery(id!, {
      skip: !id,
    });

  const {
    data: centralSectionData,
    isLoading: isLoadingCentral,
    error: centralError,
  } = useGetLatestCentralSectionVersionQuery(
    {
      handbookId: localSection?.importedHandbookId || "",
      sectionId: localSection?.importedHandbookSectionId || "",
    },
    {
      skip:
        !localSection?.importedHandbookId ||
        !localSection?.importedHandbookSectionId,
    }
  );

  const [saveSection] = useSaveLocalSectionMutation();

  useEffect(() => {
    if (localSection) {
      setTitleRadio(localSection.localTitleChange ? undefined : "central");
      setTextRadio(localSection.localTextChange ? undefined : "central");
      setNewTitle(localSection.title);
    }
  }, [localSection]);

  useEffect(() => {
    if (centralSectionData) {
      setCentralSection(centralSectionData);
    } else if (centralError) {
      toast.error(t("fetchCentralElementFail"));
    }
  }, [centralSectionData, centralError, t]);

  if (isLoadingLocal || isLoadingCentral || !localSection || !centralSection) {
    return (
      <div
        className="loading-container"
        style={{
          alignItems: "center",
          minHeight: "400px",
        }}
      >
        <Spinner size="large" />
      </div>
    );
  }

  const onTitleRadioChange = (value: string | number) => {
    if (value !== titleRadio) {
      setTitleRadio(String(value));
      setNewTitle(localSection.title);
    }
  };

  const onTextRadioChange = (value: string | number) => {
    if (value !== textRadio) {
      setTextRadio(String(value));
      if (newWysiwygRef.current) {
        newWysiwygRef.current.setValue(localSection.text || "");
      }
    }
  };

  const onSave = async () => {
    setIsSaving(true);

    try {
      const centralChange = titleRadio === "central" ? "KF" : "local";
      const centralTextChange = textRadio === "central" ? "KF" : "local";
      const title = titleRadio === "central" ? centralSection.title : newTitle;

      let mergedSection: Section;

      if (textRadio === "central") {
        mergedSection = mergeSection(
          localSection,
          centralSection,
          title,
          centralSection.text || ""
        );
      } else {
        const content = newWysiwygRef.current
          ? await newWysiwygRef.current.uploadImagesAndGetContent()
          : localSection.text || "";
        mergedSection = mergeSection(
          localSection,
          centralSection,
          title,
          content
        );
      }

      await saveSection({
        section: mergedSection,
        centralChange: centralChange === "KF",
        centralTextChange: centralTextChange === "KF",
      }).unwrap();

      navigate(`/editor/${localSection.handbookId}/section/${localSection.id}`);
    } catch (error) {
      console.error("Error merging section:", error);
      toast.error("Feil ved lagring av avsnitt");
    } finally {
      setIsSaving(false);
    }
  };

  const isSaveDisabled =
    !newTitle ||
    isSaving ||
    (localSection.pendingTitleChange && !titleRadio) ||
    (localSection.pendingTextChange && !textRadio);

  return (
    <div>
      <Title>{t("lead.title")}</Title>
      <Subtitle>{t("lead.text")}</Subtitle>
      <hr />

      <Columns>
        <Column className="merge-column">
          <Title size="5">{t("central.header")}</Title>
          <Field />

          <Field>
            <label
              htmlFor="centralTitle"
              style={{ fontWeight: "bold", fontSize: "large" }}
            >
              Tittel
            </label>

            {localSection.pendingTitleChange && (
              <>
                <i
                  className="fa fa-exclamation-triangle pendingChange"
                  aria-hidden="true"
                />
                <RightRadio
                  name="titleRadio"
                  value="central"
                  checked={titleRadio === "central"}
                  onChange={onTitleRadioChange}
                >
                  <span className="conflict-text">
                    {`Bruk sentral tittel. (${t("lastModified")} ${moment(centralSection.updatedDate).format("DD.MM.YYYY")})`}
                  </span>
                </RightRadio>
              </>
            )}

            <Addons>
              <Input
                id="centralTitle"
                placeholder="Tittel"
                value={centralSection.title}
                aria-label={t("central.text")}
                readOnly
                expanded
              />
              <Button
                aria-label="Bruk sentral tittel"
                disabled={
                  newTitle === centralSection.title || titleRadio !== "local"
                }
                onClick={() => setNewTitle(centralSection.title)}
                title="Bruk sentral tittel"
              >
                <Icon icon="copy" size="small" />
              </Button>
            </Addons>
          </Field>

          <label
            htmlFor="central"
            style={{ fontWeight: "bold", fontSize: "large" }}
          >
            Avsnittstekst
          </label>

          {localSection.pendingTextChange && (
            <>
              <i
                className="fa fa-exclamation-triangle pendingChange"
                aria-hidden="true"
              />
              <RightRadio
                name="textRadio"
                value="central"
                checked={textRadio === "central"}
                onChange={onTextRadioChange}
              >
                <span className="conflict-text">
                  {`Bruk sentral avsnittstekst. (${t("lastModified")} ${moment(centralSection.textUpdatedDate).format("DD.MM.YYYY")})`}
                </span>
              </RightRadio>
            </>
          )}

          <Wysiwyg value={centralSection.text || ""} disabled id="central" />
        </Column>

        <Column className="merge-column">
          <Title size="5">{t("local.header")}</Title>

          <Field>
            <label
              htmlFor="localTitle"
              style={{ fontWeight: "bold", fontSize: "large" }}
            >
              Tittel
            </label>

            {localSection.pendingTitleChange && (
              <>
                <RightRadio
                  name="titleRadio"
                  value="local"
                  checked={titleRadio === "local"}
                  onChange={onTitleRadioChange}
                >
                  <span className="conflict-text">
                    {`Bruk lokal tittel. (${t("lastModified")} ${moment(localSection.updatedDate).format("DD.MM.YYYY")})`}
                  </span>
                </RightRadio>
              </>
            )}

            <Addons style={{ margin: 0 }}>
              <Input
                id="localTitle"
                placeholder="Tittel"
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setNewTitle(e.target.value)
                }
                value={newTitle}
                aria-label={t("local.text")}
                expanded
                disabled={titleRadio !== "local" || !titleRadio}
              />
              <Button
                aria-label="Bruk original tittel"
                disabled={newTitle === localSection.title}
                onClick={() => setNewTitle(localSection.title)}
                title="Bruk original tittel"
              >
                <Icon icon="undo" size="small" />
              </Button>
            </Addons>
          </Field>

          <label
            htmlFor="localText"
            style={{ fontWeight: "bold", fontSize: "large" }}
          >
            Avsnittstekst
          </label>

          {localSection.pendingTextChange && (
            <>
              <RightRadio
                name="textRadio"
                value="local"
                checked={textRadio === "local"}
                onChange={onTextRadioChange}
              >
                <span className="conflict-text">
                  {`${t("local.radio")} for avsnittstekst. (${t("lastModified")} ${moment(localSection.textUpdatedDate).format("DD.MM.YYYY")})`}
                </span>
              </RightRadio>
            </>
          )}

          <Wysiwyg
            value={localSection.text || ""}
            id="localText"
            ref={newWysiwygRef}
            disabled={textRadio !== "local" || !textRadio}
          />
        </Column>
      </Columns>

      <hr />

      <Columns responsive="mobile">
        <Column>
          <Button
            as={Link}
            to={`/editor/${localSection.handbookId}/section/${localSection.id}`}
          >
            Avbryt
          </Button>
        </Column>
        <Column narrow>
          <Button
            disabled={isSaveDisabled}
            loading={isSaving}
            onClick={onSave}
            color="primary"
          >
            {t("saveButton")}
          </Button>
        </Column>
      </Columns>
    </div>
  );
};
