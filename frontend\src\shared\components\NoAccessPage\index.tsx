import React from "react";
import { Section, Container, Title, Subtitle } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

export const NoAccessPage: React.FC = () => {
  const t = usePrefixedTranslation("common.components.NoAccessPage");

  return (
    <Section>
      <Container>
        <Title>403</Title>
        <Subtitle>{t("header")}</Subtitle>
        <p>{t("message")}</p>
      </Container>
    </Section>
  );
};