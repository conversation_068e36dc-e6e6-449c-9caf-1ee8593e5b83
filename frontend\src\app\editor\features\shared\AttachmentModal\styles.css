.attachment-modal .modal-card-body {
  padding: 24px 48px;
  border-radius: 16px;
}

.attachment-modal .title {
  text-align: center;
}

.file-upload-wrapper {
  background-color: #ffffff;
  border: 2px dashed #9a9999;
  padding: 30px;
  text-align: center;
  margin-top: 24px;
  margin-bottom: 24px;
  border-radius: 16px;
}

.drag-drop-area {
  cursor: pointer;
}

.drag-drop-area.active {
  border-color: #050037;
  background-color: #f8f8ff;
}

.drag-drop-area p {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  margin: 8px 0;
}

.upload-link {
  text-decoration: underline;
  font-weight: bold;
  color: #050037;
  cursor: pointer;
  border: none;
  background: none;
  padding: 4px 10px;
  font-size: 16px;
  margin-bottom: 10px;
  transition: ease-in-out 0.3s;
  border-radius: 4px;
}

.upload-link:hover {
  background-color: #f7f7f7;
  color: #0000ff;
}

p.or-text {
  margin: 10px 0;
}

.file-list {
  margin-top: 20px;
  max-height: 300px;
  overflow: auto;
  margin-left: -16px;
  margin-right: -16px;
  padding: 0 10px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 6px;
  gap: 12px;
  border-radius: 4px;
  transition: ease-in-out 0.2s;
}

.file-item:hover {
  background-color: #f7f7f7;
}

.attachments-action-wrapper {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}

.attachments-action-wrapper .button {
  border-radius: 40px;
  min-width: 142px;
}

.attachment-publish-btn {
  background-color: #d6f1d0;
  color: #050037;
}

.remove-file-button {
  border: none;
  background: none;
  color: #000000;
  font-weight: bold;
  cursor: pointer;
  padding: 4px;
  font-size: 16px;
  transition: ease-in-out 0.2s;
  border-radius: 4px;
}

.remove-file-button .icon {
  border: 2px solid #000000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.remove-file-button:hover {
  color: #cd5c5c;
}

.remove-file-button:hover .icon {
  border: 2px solid #cd5c5c;
}

.file-list-title h3 {
  font-size: 28px;
  font-weight: 400;
  line-height: 33.89px;
  text-align: center;
  margin: 20px 0;
}

.attachment-file-name {
  font-size: 18px;
  font-weight: 400;
  line-height: 21.78px;
  text-align: left;
  color: #363636;
  margin: 0 0 8px 0;
}

.file-item.new-item {
  background-color: #fafaff;
}

.file-item.new-item * {
  font-weight: 600;
}

.file-more-info {
  font-size: 16px;
  font-style: italic;
  font-weight: 300;
  line-height: 19.36px;
  text-align: left;
  color: #363636;
  margin: 0;
}

.error-message {
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
  margin: 0;
}

.error-message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 28px;
  padding: 0 2rem;
  margin-bottom: 34px;
}

.attachment-file-details {
  flex: 1;
  transition: all ease-in-out 0.2s;
}

.attachment-file-details:hover * {
  font-weight: 500;
  text-decoration: underline;
}

.attachment-modal ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.attachment-modal ::-webkit-scrollbar-track {
  background: #ffffff;
}

.attachment-modal ::-webkit-scrollbar-thumb {
  background: #e6e6e6;
  border-radius: 9px;
}

.attachment-modal ::-webkit-scrollbar-thumb:hover {
  background: #d1d1d1;
}

.attachment-modal ::-webkit-scrollbar-thumb:active {
  background: #d1d1d1;
}

.oversized-file {
  background-color: #f5dede;
}

.oversized-file:hover * {
  font-weight: inherit;
  background-color: #f5dede;
  text-decoration: none;
}

.oversized-file:hover .file-more-info {
  font-weight: 300;
}

.oversized-file:hover .attachment-file-details {
  cursor: default;
}

.oversized-file:hover .attachment-file-name {
  font-weight: 400;
}

.oversized-file:hover .file-oversized-error {
  font-weight: 600;
}

.oversized-file:hover {
  background-color: #f5dede;
}

.file-oversized-error-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
}

.file-oversized-error {
  font-weight: 600;
  font-style: normal;
  color: #b20129;
  margin: 0;
}

.file-oversized-error-wrapper svg {
  width: 18px;
}

.attachment-loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  gap: 12px;
}

.attachment-loading-message .loader {
  height: 1.5em;
  width: 1.5em;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #050037;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.attachment-upload-percentage {
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachment-uploading-progress {
  font-size: 14px;
  color: #666;
  font-style: italic;
  margin: 0;
}

.attachment-count {
  background-color: #d6f1d0;
  margin-left: 5px;
  padding: 1px 4px;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #363636;
  min-width: 20px;
  text-align: center;
}
