package no.kf.handboker.service

import no.kf.api.Product
import no.kf.db.TransactionManager
import no.kf.exception.KfException
import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection, ReadingLink}
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import no.kf.handboker.model.Publication
import no.kf.handboker.repository.{CentralHandbookRepositoryComponent, CentralNotificationRepositoryComponent, HandbookRepositoryComponent, PublicationRepositoryComponent}
import org.json4s.ext.JodaTimeSerializers
import org.json4s.jackson.Serialization.{read, write}
import org.json4s.{DefaultFormats, Formats}

trait CentralHandbookServiceComponent extends TransactionManager {
  this: CentralAccessServiceComponent
    with PublicationRepositoryComponent
    with SubscriptionServiceComponent
    with HandbookRepositoryComponent
    with CentralNotificationRepositoryComponent
    with CentralHandbookRepositoryComponent =>

  val centralHandbookService: CentralHandbookService

  class CentralHandbookServiceImpl extends CentralHandbookService {

    protected implicit val jsonFormats: Formats = DefaultFormats.lossless ++ JodaTimeSerializers.all

    override def retrieveCentralHandbooks(externalOrgId: String): List[CentralHandbook] = {
      val handbooks = retrieveAllCentralHandbooks
      log.debug(s"Handbooks: ${handbooks.map(_.title)}")
      val hasAccessTo = centralAccessService.retrieveOrgAccesses(externalOrgId)
      log.debug(s"have access to: $hasAccessTo")
      handbooks.filter(h => hasAccessTo.contains(h.id.get))
    }

    override def retrieveHandbookReadLink(handbookId: String): List[ReadingLink] = inTransaction{
      println("Test about getting readlink api called")
      centralHandbookRepository.retrieveHandbookReadLinks(handbookId)
    }

    override def getPendingPublication(handbookId: String): List[Publication] = inTransaction{
      publicationRepository.getPendingPublication(handbookId)
    }
    
    override def retrieveAllCentralHandbooks: List[CentralHandbook] = inTransaction {
      centralHandbookRepository.retrieveShallowHandbooks.map(
        handbook => handbook.copy(isPublished = centralHandbookRepository.isPublished(handbook.id.get))
      )
    }

    override def retrieveCentralHandbook(handbookId: String, withHtml: Boolean = true): Option[CentralHandbook] = inTransaction {
      centralHandbookRepository.retrieveCentralHandbook(handbookId).map( importedHandbook =>
        importedHandbook.copy(isPublished = centralHandbookRepository.isPublished(importedHandbook.id.get))
      )
    }

    override def retrieveCentralHandbook(handbookId: String): Option[CentralHandbook] = inTransaction {
      centralHandbookRepository.retrieveCentralHandbook(handbookId)
    }

    override def retrieveCentralChapter(chapterId: String, handbookId: String): Option[Chapter] = inTransaction {
      centralHandbookRepository.retrieveCentralChapter(chapterId, handbookId).map( centralChapter =>
        convertToChapter(centralChapter, Some(handbookId)))
    }

    override def retrieveLatestPublishedCentralChapter(chapterId: String, handbookId: String): Option[Chapter] = inTransaction {
      centralHandbookRepository.retrieveLatestPublishedChapter(chapterId).map( centralChapter =>
        convertToChapter(centralChapter, Some(handbookId)))
    }

    override def retrieveCentralSection(sectionId: String): Option[Section] = inTransaction {
      centralHandbookRepository.retrieveCentralSection(sectionId).map { centralSection => {
        val importedHandbookId = if (centralSection.versionOf.isDefined) Some(centralHandbookRepository.retrieveCentralSection(centralSection.versionOf.get).get.centralHandbookId) else None
        Section(centralSection.id, centralSection.title, centralSection.html, centralSection.versionOf, importedHandbookId, centralSection.centralHandbookId, centralSection.parentId, Some(centralSection.sortOrder))
      }
      }
    }

    override def retrieveLatestPublishedCentralSection(sectionId: String, handbookId: String): Option[Section] = inTransaction {
      centralHandbookRepository.retrieveLatestPublishedSection(sectionId).map { centralSection =>
        convertToSection(centralSection, Some(handbookId))
      }
    }

    override def retrieveCentralSectionByCentralIdAndParentId(centralSectionId: String, parentId: String, handbookId: String, withHtml: Boolean): Option[Section] = inTransaction {
      centralHandbookRepository.retrieveCentralSection(centralSectionId).map { centralSection =>
        convertToSection(centralSection, Some(handbookId))}
    }

    override def retrieveChaptersAndSectionsWithText(handbookId: String): (List[Chapter], List[Section]) = inTransaction {
      val centralHandbook = centralHandbookRepository.retrieveCentralHandbook(handbookId).get
      val (latestPublishedHandbookId, originalHandbookId) = if (centralHandbook.versionOf.isEmpty) {
        (centralHandbookRepository.retrieveLastestVersion(centralHandbook.id.get).get.id.get, centralHandbook.id)
      } else {
        // Make sure we really use the latest published version
        (centralHandbookRepository.retrieveLastestVersion(centralHandbook.versionOf.get).get.id.get, centralHandbook.versionOf)
      }
      val chapters = centralHandbookRepository.retrieveChaptersByHandbookId(latestPublishedHandbookId).map( c => Chapter(c.id, c.title, if (c.versionOf.isEmpty) c.id else c.versionOf, originalHandbookId, handbookId, c.parentId, Option(c.sortOrder)))
      val sections = centralHandbookRepository.retrieveSectionsByHandbookId(latestPublishedHandbookId).map(
        s =>
          Section(s.id, s.title, s.html, if (s.versionOf.isEmpty) s.id else s.versionOf, originalHandbookId, handbookId, s.parentId, Option(s.sortOrder)))
      (chapters, sections)
    }

    override def persistCentralHandbook(handbook: CentralHandbook, currentUser: String): CentralHandbook = inTransaction {
      centralHandbookRepository.persistCentralHandbook(handbook, currentUser: String)
    }

    override def persistCentralChapter(chapter: CentralChapter, currentUser: String): CentralChapter = inTransaction {
      try {
        val centralHandbook = centralHandbookRepository.retrieveCentralHandbook(chapter.centralHandbookId).get

        if(chapter.id.isEmpty){
          val savedChapter =centralHandbookRepository.persistCentralChapter(chapter, currentUser: String)
          subscriptionService.persistChangeNotifications(s"Et nytt kapittel er lagt til: ${chapter.title}", centralHandbook)
          savedChapter
        }else{
          val existingChapter = retrieveCentralChapter(chapter.id.get, chapter.centralHandbookId).get
          val savedChapter = centralHandbookRepository.persistCentralChapter(chapter, currentUser: String)
          if(existingChapter.title != chapter.title){
            //subscriptionService.persistChangeNotifications(s"Kapittel ${existingChapter.title} endret tittel til ${chapter.title}", chapter.centralHandbookId)
            subscriptionService.persistChangeNotifications(s"Kapittel ${existingChapter.title} har endret navn til ${chapter.title}", centralHandbook)
          }
          savedChapter
        }

      } catch {
        case e: Exception =>
          throw new KfException("Kunne ikke lagre sentralt kapittel", e)
      }
    }

    override def persistCentralSection(section: CentralSection, currentUser: String): CentralSection = inTransaction {
      try {
        val centralHandbook = centralHandbookRepository.retrieveCentralHandbook(section.centralHandbookId).get

        if(section.id.isEmpty){
          val savedSection = centralHandbookRepository.persistCentralSection(section, currentUser: String)
          subscriptionService.persistChangeNotifications(s"Et nytt avsnitt er lagt til: ${section.title}", centralHandbook)
          savedSection
        }else{
          val existingCentralSection = centralHandbookRepository.retrieveCentralSection(section.id.get).get
          val existingSection = retrieveCentralSection(section.id.get).get
          log.info(s"##########Going to edit Existing Section of CentralHandbookId: ${existingSection.handbookId}############")
          
          // Determine what changed and set appropriate update dates
          val now = org.joda.time.DateTime.now()
          val titleChanged = existingCentralSection.title != section.title
          val htmlChanged = existingCentralSection.html != section.html
          
          val updatedSection = section.copy(
            titleUpdatedDate = if (titleChanged) Some(now) else existingCentralSection.titleUpdatedDate,
            htmlUpdatedDate = if (htmlChanged) Some(now) else existingCentralSection.htmlUpdatedDate,
            titleUpdatedBy = if (titleChanged) Some(currentUser) else existingCentralSection.titleUpdatedBy,
            htmlUpdatedBy = if (htmlChanged) Some(currentUser) else existingCentralSection.htmlUpdatedBy
          )
          
          val savedSection = centralHandbookRepository.persistCentralSection(updatedSection, currentUser: String)
          
          if(titleChanged && htmlChanged){
            subscriptionService.persistChangeNotifications(s"Det er gjort endringer i teksten i avsnitt: ${existingSection.title}", centralHandbook)
          }else if(titleChanged){
            subscriptionService.persistChangeNotifications(s"Avsnitt ${existingSection.title} har endret navn til ${section.title}", centralHandbook)
          }else if(htmlChanged){
            subscriptionService.persistChangeNotifications(s"Det er gjort endringer i teksten i avsnitt: ${existingSection.title}", centralHandbook)
          }
          savedSection
        }
      } catch {
        case e: Exception =>
          throw new KfException("Kunne ikke lagre sentralt avsnitt", e)
      }
    }

    override def deleteCentralHandbook(handbookId: String): Unit = inTransaction {
      centralHandbookRepository.deleteCentralHandbook(handbookId)
    }

    override def deleteCentralChapter(chapterId: String): Unit = inTransaction {
      val existingChapter = centralHandbookRepository.retrieveCentralChapter(chapterId).get
      val centralHandbook = centralHandbookRepository.retrieveCentralHandbook(existingChapter.centralHandbookId).get
      centralHandbookRepository.deleteCentralChapter(chapterId)
      subscriptionService.persistChangeNotifications(s"Et kapittel er slettet: ${existingChapter.title}", centralHandbook)
    }
    override def deleteCentralSection(sectionId: String): Unit = inTransaction {
      val existingSection = centralHandbookRepository.retrieveCentralSection(sectionId).get
      val centralHandbook = centralHandbookRepository.retrieveCentralHandbook(existingSection.centralHandbookId).get
      centralHandbookRepository.deleteCentralSection(sectionId)
      subscriptionService.persistChangeNotifications(s"Et avsnitt er slettet: ${existingSection.title}", centralHandbook)
    }

    override def retrieveOrCreateCentralContentInstanceId(): String = inTransaction {
      centralHandbookRepository.retrieveOrCreateCentralContentInstanceId()
    }

    override def retrieveCentralSections(handbookId: String): List[CentralSection] = inTransaction {
      centralHandbookRepository.retrieveSectionsInHandbook(handbookId: String)
    }

    override def retrieveCentralChapters(handbookId: String): List[CentralChapter] = inTransaction {
      centralHandbookRepository.retrieveChaptersInHandbook(handbookId: String)
    }

    override def retrieveAllHandbookVersions(centralId: String): List[CentralHandbook] = inTransaction {
      centralHandbookRepository.retrieveAllVersions(centralId)
    }

    override def retrieveLatestHandbookVersion(centralId: String): Option[CentralHandbook] = inTransaction {
      centralHandbookRepository.retrieveLastestVersion(centralId)
    }

    override def sortCentralElements(idsToSort: List[String]): Unit = inTransaction{
      centralHandbookRepository.persistSortOrder(idsToSort)
    }

    private def convertToChapter(centralChapter: CentralChapter, handbookId: Option[String] = None): Chapter = {
      val centralHandbookId = if( handbookId.isDefined ) handbookId.get else centralChapter.centralHandbookId
      Chapter(
        id = centralChapter.id,
        title = centralChapter.title,
        importedHandbookChapterId = centralChapter.id,
        importedHandbookId = Option(centralChapter.centralHandbookId),
        handbookId = centralHandbookId,
        parentId = centralChapter.parentId,
        sortOrder = Some(centralChapter.sortOrder),
        localChange = false,
        pendingChange = false,
        pendingDeletion = false,
        pendingChangeUpdatedDate = None,
        updatedDate = centralChapter.updatedDate,
        centralChapterUpdatedDateBeforePublish = centralChapter.updatedDateBeforePublish,
        localChapterUpdatedDate = None, // This is a new field, set to None for central chapters
        createdDate = centralChapter.createdDate,
        updatedBy = centralChapter.updatedBy,
        createdBy = centralChapter.createdBy,
        isDeleted = None,
        deletedDate = None,
        versionOf = None
      )
    }
    private def convertToSection(centralSection: CentralSection, handbookId: Option[String] = None): Section = {
      val centralHandbookId = if( handbookId.isDefined ) handbookId.get else centralSection.centralHandbookId
      Section(centralSection.id, centralSection.title, centralSection.html, centralSection.id, Some(centralSection.centralHandbookId), centralHandbookId, centralSection.parentId, Some(centralSection.sortOrder), false, false, false, false, false, None, centralSection.titleUpdatedDate.orElse(centralSection.updatedDate), centralSection.htmlUpdatedDate, centralSection.createdDate, centralSection.titleUpdatedBy.orElse(centralSection.updatedBy), centralSection.htmlUpdatedBy, centralSection.createdBy)
    }

    override def retrieveAllCentralSections(): List[CentralSection] = inTransaction {
      centralHandbookRepository.retrieveAllSections()
    }

    override def retrieveAllCentralChapters(): List[CentralChapter] = inTransaction {
      centralHandbookRepository.retrieveAllChapters()
    }
  }
}

trait CentralHandbookService {
  def retrieveAllCentralSections(): List[CentralSection]
  def retrieveAllCentralChapters(): List[CentralChapter]
  
  def retrieveHandbookReadLink(handbookId: String): List[ReadingLink]

  def persistCentralHandbook(handbook: CentralHandbook, currentUser: String): CentralHandbook
  def persistCentralChapter(chapter: CentralChapter, currentUser: String): CentralChapter
  def persistCentralSection(section: CentralSection, currentUser: String): CentralSection

  def deleteCentralHandbook(handbookId: String): Unit
  def deleteCentralChapter(chapterId: String): Unit
  def deleteCentralSection(sectionId: String): Unit

  def retrieveCentralChapters(handbookId: String): List[CentralChapter]
  def retrieveCentralSections(handbookId: String): List[CentralSection]

  def sortCentralElements(idsToSort: List[String])

  def retrieveAllCentralHandbooks: List[CentralHandbook]
  def retrieveCentralHandbooks(externalOrgId: String): List[CentralHandbook]
  def retrieveCentralHandbook(handbookId: String, withHtml: Boolean): Option[CentralHandbook]
  def retrieveCentralHandbook(handbookId: String): Option[CentralHandbook]

  def retrieveCentralChapter(chapterId: String, handbookId: String): Option[Chapter]
  def retrieveCentralSection(sectionId: String): Option[Section]
  def retrieveLatestPublishedCentralChapter(chapterId: String, handbookId: String): Option[Chapter]
  def retrieveLatestPublishedCentralSection(sectionId: String, handbookId: String): Option[Section]

  def retrieveCentralSectionByCentralIdAndParentId(centralSectionId: String, parentId: String, handbookId: String, withHtml: Boolean = true): Option[Section]
  def retrieveChaptersAndSectionsWithText(handbookId: String): (List[Chapter], List[Section])
  def retrieveOrCreateCentralContentInstanceId(): String

  def retrieveLatestHandbookVersion(centralId: String): Option[CentralHandbook]
  def retrieveAllHandbookVersions(centralId: String): List[CentralHandbook]
  def getPendingPublication(handbookId: String): List[Publication]
}
