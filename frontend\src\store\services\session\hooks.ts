import { useGetSessionQuery } from "./sessionApi";
import type { SessionInfo } from "@/types";

export function useSession() {
  const {
    data: session,
    error,
    isLoading,
    isError,
    refetch,
  } = useGetSessionQuery();

  return {
    session: session as SessionInfo | undefined,
    isLoading,
    isError,
    error,
    refetch,
    isAuthenticated: <PERSON><PERSON><PERSON>(session?.user),
    hasOrganization: <PERSON><PERSON><PERSON>(session?.organization),
    isKfAdmin: <PERSON><PERSON><PERSON>(session?.isKfAdmin),
    user: session?.user,
    organization: session?.organization,
    userOrgsWithAccess: session?.userOrgsWithAccess || [],
    bannerUrl: session?.bannerUrl,
    logoUrlStart: session?.logoUrlStart,
    publicBasename: session?.publicBasename,
  };
}

export const useCurrentSession = useSession;
