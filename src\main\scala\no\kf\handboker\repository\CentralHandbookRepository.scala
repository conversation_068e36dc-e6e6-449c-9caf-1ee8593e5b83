package no.kf.handboker.repository

import no.kf.db.RichSQL._
import no.kf.db.{IDGenerator, UpsertSupport}
import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection, ReadingLink}
import no.kf.util.Logging
import org.joda.time.DateTime

import scala.annotation.tailrec

trait CentralHandbookRepositoryComponent {
  this: DbConnectionManagerComponent =>

  val centralHandbookRepository: CentralHandbookRepository

  object CentralHandbookTableDef {
    val tableName = "central_handbooks.handbook"

    val fieldId = "central_id"
    val fieldTitle = "title"
    val fieldVersionOf = "version_of"
    val fieldUpdated = "updated_date"
    val fieldCreated = "created_date"
    val fieldUpdatedBy = "updated_by"
    val fieldCreatedBy = "created_by"

    val fields: List[String] = List(fieldId, fieldTitle, fieldVersionOf, fieldUpdated, fieldCreated, fieldUpdatedBy, fieldCreatedBy)
    val selectFields: List[String] = fields
    val insertFields: List[String] = fields
    val updateFields: List[String] = fields.filterNot(List(fieldId, fieldCreated, fieldCreatedBy, fieldVersionOf).contains)
  }

  object CentralHandbookChapterTableDef {
    val tableName = "central_handbooks.chapter"

    val fieldId = "central_id"
    val fieldTitle = "title"
    val fieldParentId = "central_parent_id"
    val fieldHandbookId = "central_handbook_id"
    val fieldVersionOf = "version_of"
    val fieldUpdated = "updated_date"
    val fieldUpdatedDateBeforePublish = "updated_date_before_publish"
    val fieldSortOrder = "sort_order"
    val fieldCreated = "created_date"
    val fieldCreatedBy = "created_by"
    val fieldUpdatedBy = "updated_by"

    val fields: List[String] = List(fieldId, fieldTitle, fieldParentId, fieldHandbookId, fieldVersionOf, fieldUpdated, fieldUpdatedDateBeforePublish, fieldSortOrder, fieldCreated, fieldUpdatedBy, fieldCreatedBy)
    val selectFields: List[String] = fields
    val insertFields: List[String] = fields
    val updateFields: List[String] = fields.filterNot(List(fieldId, fieldHandbookId, fieldVersionOf, fieldCreated, fieldCreatedBy).contains)
  }

  object CentralHandbookReadLinkTableDef {
    val tableName = "reading_link"

    val fieldId = "id"
    val fieldLink = "link"
    val fieldCentralSectionId = "central_section_id"
    val fieldCreatedDate = "created_date"
    val fieldValidTo = "valid_to"

    val fields: List[String] = List(fieldId, fieldLink, fieldCentralSectionId, fieldCreatedDate, fieldValidTo)
    val selectFields: List[String] = fields
    val insertFields: List[String] = fields
    val updateFields: List[String] = fields.filterNot(List(fieldId, fieldLink, fieldCentralSectionId, fieldCreatedDate).contains)
  }

  object CentralHandbookSectionTableDef {
    val tableName = "central_handbooks.section"

    val fieldId = "id"
    val fieldCentralId = "central_id"
    val fieldTitle = "title"
    val fieldHandbookId = "central_handbook_id"
    val fieldVersionOf = "version_of"
    val fieldParentId = "central_parent_id"
    val fieldHtml = "html"
    val fieldCreated = "created_date"
    val fieldRegistered = "registered_date"
    val fieldUpdated = "updated_date"
    val fieldTitleUpdated = "title_updated_date"
    val fieldHtmlUpdated = "html_updated_date"
    val fieldSortOrder = "sort_order"
    val fieldCreatedBy = "created_by"
    val fieldUpdatedBy = "updated_by"
    val fieldTitleUpdatedBy = "title_updated_by"
    val fieldHtmlUpdatedBy = "html_updated_by"

    val fields: List[String] = List(fieldId, fieldCentralId, fieldTitle, fieldHandbookId, fieldVersionOf, fieldParentId, fieldHtml, fieldCreated, fieldRegistered, fieldUpdated, fieldTitleUpdated, fieldHtmlUpdated, fieldSortOrder, fieldCreatedBy, fieldUpdatedBy, fieldTitleUpdatedBy, fieldHtmlUpdatedBy)
    val selectFields: List[String] = fields
    val insertFields: List[String] = fields
    val updateFields: List[String] = fields.filterNot(List(fieldId, fieldCentralId, fieldHandbookId, fieldVersionOf, fieldCreated, fieldRegistered, fieldCreatedBy).contains)
  }

  object CentralHandbookPublicationTableDef {
    val tableName = "central_handbooks.publication"

    val fieldId = "id"
    val fieldPublicationDate = "publication_date"
    val fieldCreatedDate = "created_date"
    val fieldCreatedBy = "created_by"
    val fieldNotify = "notify_by_email"
    val fieldPublished = "published"
    val fieldHandbookId = "handbook_id"

    val fields: List[String] = List(fieldId, fieldPublicationDate, fieldCreatedDate, fieldCreatedBy, fieldNotify, fieldPublished, fieldHandbookId)
    val selectFields: List[String] = fields
    val insertFields: List[String] = fields
    val updateFields: List[String] = fields.filterNot(List(fieldId, fieldCreatedDate, fieldCreatedBy, fieldHandbookId).contains)
  }

  object CentralHandbookReadingLinkTableDef {
    val tableName = "reading_link"

    val fieldId = "id"
    val fieldLink = "link"
    val fieldCentralSection = "central_section_id"
    val fieldCreatedDate = "created_date"
    val fieldValidTo = "valid_to"

    val fields: List[String] = List(fieldId, fieldLink, fieldCreatedDate, fieldCentralSection, fieldValidTo)
    val selectFields: List[String] = fields
    val insertFields: List[String] = fields
    val updateFields: List[String] = fields.filterNot(List(fieldId, fieldCreatedDate, fieldCentralSection).contains)
  }

  class CentralHandbookRepositoryImpl extends CentralHandbookRepository with UpsertSupport with Logging {

    override def persistCentralHandbook(handbook: CentralHandbook, currentUser: String): CentralHandbook = {
      if (handbook.id.isDefined) {
        updateHandbook(handbook, currentUser)
      } else {
        insertHandbook(handbook, currentUser)
      }
    }

    private def updateHandbook(handbook: CentralHandbook, currentUser: String): CentralHandbook = {
      import CentralHandbookTableDef._
      val sql = s"UPDATE $tableName SET ${updateFields.mkString("", "=?, ", "=?")} WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbook.title << DateTime.now << currentUser << handbook.id <<!!
      }
      retrieveCentralHandbook(handbook.id.get).get
    }

    private def insertHandbook(handbook: CentralHandbook, currentUser: String): CentralHandbook = {
      import CentralHandbookTableDef._
      val handbookId = IDGenerator.generateUniqueId
      val sql = s"INSERT INTO $tableName (${insertFields.mkString(",")}) VALUES(${#?(insertFields)})"
      connectionManager.doWithConnection {
        _.ps(sql) <<
          handbookId <<
          handbook.title <<
          handbook.versionOf <<
          DateTime.now <<
          DateTime.now <<
          currentUser <<
          currentUser <<! // First time the "updater" is the same person creating the object
      }
      retrieveCentralHandbook(handbookId).get
    }

    override def persistCentralChapter(chapter: CentralChapter, currentUser: String): CentralChapter = {
      if (chapter.id.isDefined) {
        updateChapter(chapter, chapter.centralHandbookId, currentUser)
      } else {
        insertChapter(chapter, chapter.centralHandbookId, currentUser)
      }
    }

    override def insertCentralChapterCopy(chapter: CentralChapter, currentUser: String): CentralChapter = {
      insertChapter(chapter, chapter.centralHandbookId, currentUser, computeSortOrder = false)
    }

    private[repository] def updateChapter(chapter: CentralChapter, handbookId: String, currentUser: String): CentralChapter = {
      import CentralHandbookChapterTableDef._
      val sql = s"UPDATE $tableName SET ${updateFields.mkString("", "=?, ", "=?")} WHERE $fieldId = ? AND $fieldHandbookId = ?"
      val currentTime = DateTime.now
      connectionManager.doWithConnection {
        _.ps(sql) <<
          chapter.title <<
          chapter.parentId <<
          currentTime <<
          currentTime << // Update updatedDateBeforePublish for direct updates
          chapter.sortOrder <<
          currentUser <<
          chapter.id <<
          handbookId <<!!
      }
      retrieveCentralChapter(chapter.id.get, handbookId).get
    }

    private[repository] def insertChapter(chapter: CentralChapter, handbookId: String, currentUser: String, computeSortOrder: Boolean = true): CentralChapter = {
      import CentralHandbookChapterTableDef._
      val sql = s"INSERT INTO $tableName (${insertFields.mkString(",")}) VALUES (${#?(insertFields)})"
      val chapterId = IDGenerator.generateUniqueId
      val sortOrder = if (computeSortOrder) {
        if (chapter.parentId.isEmpty) retrieveLatestIndexForHandbookItems(handbookId) else retrieveLatestIndexForChapterItems(chapter.parentId.get)
      } else {
        chapter.sortOrder
      }

      // For new chapters (not copies), set updatedDateBeforePublish to current time
      // For copies (publishing/synchronization), preserve the original updatedDateBeforePublish
      val updatedDateBeforePublish = if (chapter.versionOf.isDefined) {
        // This is a copy operation (publishing/synchronization), preserve original value
        chapter.updatedDateBeforePublish
      } else {
        // This is a new chapter, set to current time
        Some(DateTime.now)
      }

      connectionManager.doWithConnection {
        _.ps(sql) <<
          chapterId <<
          chapter.title <<
          chapter.parentId <<
          handbookId <<
          chapter.versionOf <<
          DateTime.now <<
          updatedDateBeforePublish <<
          sortOrder <<
          DateTime.now <<
          currentUser <<
          currentUser <<! // First time the "updater" is the same person creating the object
      }
      retrieveCentralChapter(chapterId, handbookId).get
    }

    override def persistCentralSection(section: CentralSection, currentUser: String): CentralSection = {
      if (section.id.isDefined) {
        updateSection(section, currentUser)
      } else {
        insertSection(section, currentUser)
      }
    }

    override def insertCentralSectionCopy(section: CentralSection, currentUser: String): CentralSection = {
      insertSection(section, currentUser, computeSortOrder = false)
    }

    private def updateSection(section: CentralSection, currentUser: String): CentralSection = {
      import CentralHandbookSectionTableDef._
      val sql = s"UPDATE $tableName SET ${updateFields.mkString("", "=?, ", "=?")} WHERE $fieldId = ? AND $fieldHandbookId = ?"
      log.info(s"#########Updating Central Section Query : $sql##########")
      val updatedTimestamp = DateTime.now
      connectionManager.doWithConnection {
        _.ps(sql) <<
          section.title <<
          section.parentId <<
          section.html.getOrElse("") <<
          updatedTimestamp <<
          section.titleUpdatedDate <<
          section.htmlUpdatedDate <<
          section.sortOrder <<
          currentUser <<
          section.titleUpdatedBy <<
          section.htmlUpdatedBy <<
          section.id.get <<
          section.centralHandbookId <<!
      }
      log.info(s"#########Updated Central Section of CentralHandbookId : ${section.centralHandbookId}##########")
      section.copy(updatedDate = Some(updatedTimestamp), updatedBy = Some(currentUser))
    }

    private def insertSection(section: CentralSection, currentUser: String, computeSortOrder: Boolean = true): CentralSection = {
      import CentralHandbookSectionTableDef._
      val sql = s"INSERT INTO $tableName (${insertFields.mkString(",")}) VALUES (${#?(insertFields)})"
      val newSectionId = IDGenerator.generateUniqueId
      val finalSortOrder = if (computeSortOrder) retrieveLatestIndexForChapterItems(section.parentId) else section.sortOrder
      val currentTime = DateTime.now

      connectionManager.doWithConnection {
        _.ps(sql) <<
          newSectionId << // fieldId
          newSectionId << // fieldCentralId (assuming central_id gets the same new ID as id)
          section.title <<
          section.centralHandbookId <<
          section.versionOf <<
          section.parentId <<
          section.html.getOrElse("") <<
          currentTime << // fieldCreated
          section.registeredDate << // fieldRegistered
          currentTime << // fieldUpdated
          section.titleUpdatedDate.orElse(Some(currentTime)) << // fieldTitleUpdated
          section.htmlUpdatedDate.orElse(Some(currentTime)) << // fieldHtmlUpdated
          finalSortOrder <<
          currentUser << // fieldCreatedBy
          currentUser << // fieldUpdatedBy
          section.titleUpdatedBy.orElse(Some(currentUser)) << // fieldTitleUpdatedBy
          section.htmlUpdatedBy.orElse(Some(currentUser)) <<! // fieldHtmlUpdatedBy
      }
      log.info(s"#########Inserted Central Section of CentralHandbookId : ${section.centralHandbookId}##########")
      section.copy(
        id = Some(newSectionId),
        createdDate = Some(currentTime),
        updatedDate = Some(currentTime),
        titleUpdatedDate = section.titleUpdatedDate.orElse(Some(currentTime)),
        htmlUpdatedDate = section.htmlUpdatedDate.orElse(Some(currentTime)),
        createdBy = Some(currentUser),
        updatedBy = Some(currentUser),
        titleUpdatedBy = section.titleUpdatedBy.orElse(Some(currentUser)),
        htmlUpdatedBy = section.htmlUpdatedBy.orElse(Some(currentUser)),
        sortOrder = finalSortOrder
      )
    }

    override def retrieveCentralChapter(id: String, handbookId: String): Option[CentralChapter] = {
      import CentralHandbookChapterTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldId = ? AND $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << id << handbookId <<# populateCentralChapter
      }
    }

    override def retrieveCentralChapter(id: String): Option[CentralChapter] = {
      import CentralHandbookChapterTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << id <<# populateCentralChapter
      }
    }

    override def retrieveLatestPublishedChapter(id: String): Option[CentralChapter] = {
      import CentralHandbookChapterTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldVersionOf = ? AND $fieldCreated = (SELECT MAX($fieldCreated) FROM $tableName where $fieldVersionOf = ?)"
      connectionManager.doWithConnection {
        _.ps(sql) << id << id <<# populateCentralChapter
      }
    }

    override def retrieveCentralSection(id: String): Option[CentralSection] = {
      import CentralHandbookSectionTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << id <<# populateCentralSection
      }
    }

    override def retrieveLatestPublishedSection(id: String): Option[CentralSection] = {
      import CentralHandbookSectionTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldVersionOf = ? AND $fieldCreated = (SELECT MAX($fieldCreated) FROM $tableName where $fieldVersionOf = ?)"
      connectionManager.doWithConnection {
        _.ps(sql) << id << id <<# populateCentralSection
      }
    }

    override def retrieveCentralHandbook(id: String): Option[CentralHandbook] = {
      import CentralHandbookTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << id <<# populateCentralHandbook
      }
    }

    override def retrieveShallowHandbooks: List[CentralHandbook] = {
      import CentralHandbookTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldVersionOf IS NULL"
      connectionManager.doWithConnection {
        _.ps(sql) <<! populateCentralHandbook
      }
    }

    private def retrieveLatestIndexForHandbookItems(handbookId: String): Int = {
      import CentralHandbookChapterTableDef._

      val sql = s"SELECT MAX($fieldSortOrder), COUNT(*) FROM $tableName WHERE $fieldHandbookId = ? AND $fieldParentId IS NULL"
      val (max, count) = connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<# populateMaxCount
      }.get

      if (count == 0) {
        count
      } else {
        max + 1
      }
    }

    private def retrieveLatestIndexForChapterItems(parentId: String): Int = {
      import CentralHandbookChapterTableDef._
      import CentralHandbookSectionTableDef.{fieldParentId => sectionParentId, fieldSortOrder => sectionSortOrder, tableName => sectionTableName}

      val sql = s"SELECT MAX($fieldSortOrder), COUNT(*) FROM (" +
        s"SELECT $fieldSortOrder FROM $tableName WHERE $fieldParentId = ? " +
        s"UNION SELECT $sectionSortOrder FROM $sectionTableName WHERE $sectionParentId = ?" +
        ") as subquery"
      val (max, count) = connectionManager.doWithConnection {
        _.ps(sql) << parentId << parentId <<# populateMaxCount
      }.get

      if (count == 0) {
        count
      } else {
        max + 1
      }
    }

    private def populateMaxCount(rs: RichResultSet): (Int, Int) = (rs, rs)

    override def deleteCentralHandbook(handbookId: String): Unit = {
      import CentralHandbookTableDef._
      import CentralHandbookPublicationTableDef.{tableName => pubTable, fieldHandbookId => pubHandbookId}

      //Delete children
      deleteChildrenForHandbook(handbookId)

      //Delete publication
      val handbookPublicationSql = s"DELETE FROM $pubTable WHERE $pubHandbookId = ? "
      connectionManager.doWithConnection {
        _.ps(handbookPublicationSql) << handbookId <<!
      }

      //Delete handbook
      val handbookSql = s"DELETE FROM $tableName WHERE $fieldId = ? "
      connectionManager.doWithConnection {
        _.ps(handbookSql) << handbookId <<!
      }
    }

    override def deleteCentralChapter(chapterId: String): Unit = {
      deleteChapterWithContents(chapterId)
    }

    private[repository] def deleteChapterWithContents(chapterId: String): Unit = {
      @tailrec
      def findChapterAndSubChaptersInDeletableOrder(chapterIdList: List[String], doneChapterIdList: List[String] = Nil): List[String] = {
        chapterIdList match {
          case Nil =>
            doneChapterIdList
          case id :: tail =>
            val subs = retrieveChaptersInChapter(id).map(_.id.get)
            findChapterAndSubChaptersInDeletableOrder(subs ::: tail, id :: doneChapterIdList)
        }
      }

      val toDelete = findChapterAndSubChaptersInDeletableOrder(List(chapterId))
      toDelete.foreach(deleteChapterFunction)
    }

    private def deleteChapterFunction(chapterId: String): Unit = {
      import CentralHandbookChapterTableDef._

      deleteSectionsInChapter(chapterId)

      val sql = s"DELETE FROM $tableName WHERE $fieldId = ? "
      connectionManager.doWithConnection {
        _.ps(sql) << chapterId <<!
      }
    }

    private def deleteSectionsInChapter(chapterId: String): Unit = {
      val sectionsToDelete = retrieveSectionsInChapter(chapterId).map(_.id.get)
      sectionsToDelete.foreach(deleteCentralSection)
    }

    private def retrieveChaptersInChapter(chapterId: String): List[CentralChapter] = {
      import CentralHandbookChapterTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldParentId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << chapterId <<! populateCentralChapter
      }
    }

    private def retrieveSectionsInChapter(chapterId: String): List[CentralSection] = {
      import CentralHandbookSectionTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldParentId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << chapterId <<! populateCentralSection
      }
    }

    override def deleteCentralSection(sectionId: String): Unit = {
      import CentralHandbookSectionTableDef._
      val sql = s"DELETE FROM $tableName WHERE $fieldId = ? "
      connectionManager.doWithConnection {
        _.ps(sql) << sectionId <<!
      }
    }

    private[repository] def deleteChildrenForHandbook(handbookId: String): Unit = {
      import CentralHandbookChapterTableDef._
      import CentralHandbookSectionTableDef.{fieldHandbookId => handbookIdForSection, tableName => sectionTableName, fieldCentralId => centralId}
      import CentralHandbookReadingLinkTableDef.{fieldCentralSection => linkCentralId, tableName => linkTable}

      /*select * from reading_link as rl
          where rl.central_section_id in (
          select central_id from central_handbooks.section as cs
      where cs.central_handbook_id = '94e28a3d-d08a-46d1-9a23-60c4659784c9'
      )*/

      val readingLinkSql = s"DELETE FROM $linkTable WHERE $linkCentralId in (" +
        s"SELECT $centralId FROM $sectionTableName WHERE $handbookIdForSection = ?)"
      connectionManager.doWithConnection(
        _.ps(readingLinkSql) << handbookId <<!
      )

      //Join read_link tabell
      val sectionSql = s"DELETE FROM $sectionTableName WHERE $handbookIdForSection = ?"
      connectionManager.doWithConnection(
        _.ps(sectionSql) << handbookId <<!
      )

      val chapterSql = s"DELETE FROM $tableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(chapterSql) << handbookId <<!
      }
    }

    override def retrieveChaptersInHandbook(handbookId: String): List[CentralChapter] = {
      import CentralHandbookChapterTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<! populateCentralChapter
      }
    }

    override def retrieveSectionsInHandbook(handbookId: String): List[CentralSection] = {
      import CentralHandbookSectionTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<! populateCentralSection
      }
    }

    override def retrieveChaptersByHandbookId(handbookId: String): List[CentralChapter] = {
      import CentralHandbookChapterTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<! populateCentralChapter
      }
    }

    override def retrieveSectionsByHandbookId(handbookId: String, withHtml: Boolean): List[CentralSection] = {
      import CentralHandbookSectionTableDef._
      val sql = s"SELECT ${selectFields.filter(_ != fieldHtml || withHtml).mkString(",")} FROM $tableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<! populateCentralSection
      }
    }

    override def persistSortOrder(sortOrder: List[String]): Unit = {
      def updateSql(tableName: String, fieldSortOrder: String, idField: String) = s"UPDATE $tableName SET $fieldSortOrder = ? WHERE $idField = ?"

      def tryUpdatingChapter(id: String, sortOrder: Int): Boolean = {
        import CentralHandbookChapterTableDef.{fieldId, fieldSortOrder, tableName}
        val sql = updateSql(tableName, fieldSortOrder, fieldId)
        connectionManager.doWithConnection {
          _.ps(sql) << sortOrder << id <<!!
        } == 1
      }

      def tryUpdatingSection(id: String, sortOrder: Int): Boolean = {
        import CentralHandbookSectionTableDef.{fieldId, fieldSortOrder, tableName}
        val sql = updateSql(tableName, fieldSortOrder, fieldId)
        connectionManager.doWithConnection {
          _.ps(sql) << sortOrder << id <<!!
        } == 1
      }

      sortOrder.zipWithIndex.map {
        case (id, i) =>
          if (!tryUpdatingChapter(id, i)) {
            tryUpdatingSection(id, i)
          }
      }
    }

    override def retrieveOrCreateCentralContentInstanceId(): String = {
      val selectSql = s"SELECT instance_id from central_handbooks.instance"
      connectionManager.doWithConnection {
        _.ps(selectSql) <<# populateString
      } match {
        case Some(instanceId) => instanceId
        case None => insertCentralContentInstanceId()
      }
    }

    override def retrieveAllVersions(centralId: String): List[CentralHandbook] = {
      import CentralHandbookTableDef._
      val sql = s"SELECT ${fields.mkString(",")} FROM $tableName WHERE $fieldVersionOf = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << centralId <<! populateCentralHandbook
      }
    }

    override def retrieveLastestVersion(centralId: String): Option[CentralHandbook] = {
      import CentralHandbookTableDef._
      val sql = s"SELECT ${fields.mkString(",")} FROM $tableName WHERE $fieldCreated = (SELECT MAX($fieldCreated) FROM $tableName WHERE $fieldVersionOf = ?)"
      connectionManager.doWithConnection {
        _.ps(sql) << centralId <<# populateCentralHandbook
      }
    }

    override def retrieveSecondLastestVersion(centralId: String): Option[CentralHandbook] = {
      import CentralHandbookTableDef._
      val sql = s"SELECT ${fields.mkString(",")} FROM $tableName WHERE $fieldVersionOf = ?"
      val allVersions = connectionManager.doWithConnection {
        _.ps(sql) << centralId <<! populateCentralHandbook
      }.toList
      if (allVersions.isEmpty || allVersions.size == 1) {
        None
      } else {
        Some(allVersions.sortBy(v => v.createdDate.get.getMillis).init.last)
      }
    }

    override def isPublished(handbookId: String): Boolean = {
      retrieveLastestVersion(handbookId).isDefined
    }

    override def retrieveAllSections(): List[CentralSection] = {
      import CentralHandbookSectionTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldHandbookId IN (SELECT ${CentralHandbookTableDef.fieldId} from ${CentralHandbookTableDef.tableName} WHERE ${CentralHandbookTableDef.fieldVersionOf} IS NULL)"
      connectionManager.doWithConnection {
        _.ps(sql) <<! populateCentralSection
      }
    }

    override def retrieveAllChapters(): List[CentralChapter] = {
      import CentralHandbookChapterTableDef._
      val sql = s"SELECT ${selectFields.mkString(",")} FROM $tableName WHERE $fieldHandbookId IN (SELECT ${CentralHandbookTableDef.fieldId} from ${CentralHandbookTableDef.tableName} WHERE ${CentralHandbookTableDef.fieldVersionOf} IS NULL)"
      connectionManager.doWithConnection {
        _.ps(sql) <<! populateCentralChapter
      }
    }

    private def insertCentralContentInstanceId(): String = {
      val instanceId = IDGenerator.generateUniqueId
      val insertSql = s"INSERT INTO central_handbooks.instance(instance_id) VALUES(?)"
      connectionManager.doWithConnection {
        _.ps(insertSql) << instanceId <<!
      }
      instanceId
    }

    private def populateString(rs: RichResultSet): String = rs

    private def populateCentralHandbook(rs: RichResultSet): CentralHandbook = {
      val id: Option[String] = rs
      val title: String = rs
      val versionOf: Option[String] = rs
      val updatedDate: Option[DateTime] = rs
      val createdDate: Option[DateTime] = rs
      val createdBy: Option[String] = rs
      val updatedBy: Option[String] = rs

      CentralHandbook(id, title, versionOf, createdDate, updatedDate, createdBy, updatedBy, false)
    }

    private def populateCentralChapter(rs: RichResultSet): CentralChapter = {
      val id: Option[String] = rs
      val title: String = rs
      val parentId: Option[String] = rs
      val handbookId: String = rs
      val versionOf: Option[String] = rs
      val updatedDate: Option[DateTime] = rs
      val updatedDateBeforePublish: Option[DateTime] = rs
      val sortOrder: Int = rs
      val createdDate: Option[DateTime] = rs
      val updatedBy: Option[String] = rs
      val createdBy: Option[String] = rs

      CentralChapter(id, title, parentId, handbookId, versionOf, createdDate, updatedDate, updatedDateBeforePublish, createdBy, updatedBy, sortOrder)
    }

    private def populateCentralSection(rs: RichResultSet): CentralSection = {
      val id: Option[String] = rs
      val oldCentralId: String = rs
      val title: String = rs
      val centralHandbookId: String = rs
      val versionOf: Option[String] = rs
      val parentId: String = rs
      val html: Option[String] = rs
      val createdDate: Option[DateTime] = rs
      val registeredDate: Option[DateTime] = rs
      val updatedDate: Option[DateTime] = rs
      val titleUpdatedDate: Option[DateTime] = rs
      val htmlUpdatedDate: Option[DateTime] = rs
      val sortOrder: Int = rs
      val createdBy: Option[String] = rs
      val updatedBy: Option[String] = rs
      val titleUpdatedBy: Option[String] = rs
      val htmlUpdatedBy: Option[String] = rs

      CentralSection(id, title, parentId, centralHandbookId, html, versionOf, createdDate, registeredDate, updatedDate, titleUpdatedDate, htmlUpdatedDate, createdBy, updatedBy, titleUpdatedBy, htmlUpdatedBy, sortOrder)
    }

    private def populateReadLink(rs: RichResultSet): ReadingLink = {
      val id: Option[String] = rs
      val link: Option[String] = rs
      val centralSectionId: String = rs
      val createdDate: Option[DateTime] = rs
      val validTo: DateTime = rs

      ReadingLink(id, link, centralSectionId, createdDate, validTo)
    }

    override def retrieveHandbookReadLinks(handbookId: String): List[ReadingLink] = {
      import CentralHandbookReadingLinkTableDef.{tableName => readLinkTable, fieldCentralSection => readLinkSectionId, selectFields => readLinkSelect}
      import CentralHandbookSectionTableDef.{tableName => sectionTable, fieldId => sectionId, fieldHandbookId => sectionHandbookId}

      val sql = s"SELECT ${readLinkSelect.mkString(",")} FROM $readLinkTable WHERE $readLinkSectionId IN (SELECT ${sectionId} from ${sectionTable} WHERE ${sectionHandbookId} = ?)"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<! populateReadLink
      }
    }

  }

}

trait CentralHandbookRepository {
  def retrieveAllSections(): List[CentralSection]

  def retrieveAllChapters(): List[CentralChapter]

  def retrieveLastestVersion(centralId: String): Option[CentralHandbook]

  def retrieveSecondLastestVersion(centralId: String): Option[CentralHandbook]

  def retrieveAllVersions(centralId: String): List[CentralHandbook]

  def isPublished(get: String): Boolean

  def retrieveHandbookReadLinks(handbookId: String): List[ReadingLink]

  def persistCentralHandbook(handbook: CentralHandbook, currentUser: String): CentralHandbook

  def persistCentralChapter(chapter: CentralChapter, currentUser: String): CentralChapter

  def insertCentralChapterCopy(chapter: CentralChapter, currentUser: String): CentralChapter

  def persistCentralSection(section: CentralSection, currentUser: String): CentralSection

  def insertCentralSectionCopy(section: CentralSection, currentUser: String): CentralSection

  def retrieveCentralHandbook(id: String): Option[CentralHandbook]

  def retrieveCentralChapter(id: String, handbookId: String): Option[CentralChapter]

  def retrieveCentralChapter(id: String): Option[CentralChapter]

  def retrieveLatestPublishedChapter(id: String): Option[CentralChapter]

  def retrieveCentralSection(id: String): Option[CentralSection]

  def retrieveLatestPublishedSection(id: String): Option[CentralSection]

  def deleteCentralHandbook(handbookId: String): Unit

  def deleteCentralChapter(chapterId: String): Unit

  def deleteCentralSection(sectionId: String): Unit

  def persistSortOrder(sortOrder: List[String]): Unit

  def retrieveShallowHandbooks: List[CentralHandbook]

  def retrieveChaptersInHandbook(handbookId: String): List[CentralChapter]

  def retrieveSectionsInHandbook(handbookId: String): List[CentralSection]

  def retrieveChaptersByHandbookId(handbookId: String): List[CentralChapter]

  def retrieveSectionsByHandbookId(handbookId: String, withHtml: Boolean = true): List[CentralSection]

  def retrieveOrCreateCentralContentInstanceId(): String
}
