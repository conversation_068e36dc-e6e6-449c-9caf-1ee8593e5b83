package no.kf.handboker.rest

import scala.xml.XML
import no.kf.config.{ApplicationId, BrukerAdmBaseUrl}
import no.kf.handboker.config.{MatomoContainer, MatomoServer, PublicWebAppPath, WebAppPath}
import no.kf.handboker.rest.support.{IpFilterSupport, RegistrySupport}
import no.kf.handboker.util.{SSRStateInjector, TrackingTagTemplate}
import no.kf.rest.{HistoryApiFallbackServlet, ScalatraExceptions}
import no.kf.rest.support.JsonSupport
import no.kf.util.ApplicationNameReplacer
import java.nio.charset.StandardCharsets.UTF_8

import scala.util.{Failure, Success, Try}

class PublicIpFilteredHandbookServlet extends HistoryApiFallbackServlet with JsonSupport with RegistrySupport with IpFilterSupport {

  lazy val handbookService = componentRegistry.localHandbookService
  lazy val searchService = componentRegistry.searchService
  lazy val organizationService = componentRegistry.externalOrganizationService
  lazy val handbookLinkService = componentRegistry.handbookLinkService
  lazy val readingLinkService = componentRegistry.readingLinkService

  lazy val webAppPath = componentRegistry.settings.settingFor(WebAppPath)
  lazy val publicWebAppPath = componentRegistry.settings.settingFor(PublicWebAppPath)
  lazy val logoUrlStart = s"${componentRegistry.settings.settingFor(BrukerAdmBaseUrl)}/open/services/logo/"
  lazy val bannerUrlStart = s"${componentRegistry.settings.settingFor(BrukerAdmBaseUrl)}/open/services/banner/"

  lazy val appId = componentRegistry.settings.settingFor(ApplicationId)
  lazy val brukerAdmUrl: String = s"$baseUrl/open/services/application-access/v2/$appId/accesskey/$accessKey"

  lazy val matomoServer: String = componentRegistry.settings.settingFor(MatomoServer)
  lazy val matomoContainer: String = componentRegistry.settings.settingFor(MatomoContainer)

  lazy val fileLinkService = componentRegistry.fileLinkService
  lazy val fileService = componentRegistry.fileService

  after("/*") {
    response.setHeader("Cache-Control", "no-cache")
  }

  // TODO: Move all api's used by the frontend to /api/ or something... So we don't have to filter by the accept type
  // We need to send the basename to the client so the links will be correct
  get("/?", request.getHeader("accept").contains("application/json")) {
    Map(
      "basename" -> publicWebAppPath,
      "editorBasename" -> webAppPath,
      "logoUrlStart" -> logoUrlStart,
      "bannerUrlStart" -> bannerUrlStart
    )
  }

  /**
    * Get the handbook with it's chapters and sections
    */
  get("/:externalOrgId/:handbookId/?", request.getHeader("accept").contains("application/json")) {
    val handbook = handbookService.retrieveHandbook(extractRequiredParam("handbookId")).getOrElse(ScalatraExceptions.notFound())
    val externalOrgId = extractRequiredParam("externalOrgId")
    val organization = organizationService.fetchOrganizationsFromBrukerAdm().find(_.id == externalOrgId).getOrElse(ScalatraExceptions.forbiddenException(s"$externalOrgId doesn't have access to Håndbøker"))

    if (!handbook.isPublic) {
      throwUnauthorizedIfInvalidIp(handbook.externalOrgId)
    }

    // Make sure the handbook we got also matches the url param!
    if (handbook.externalOrgId != externalOrgId) {
      ScalatraExceptions.conflict(Option("The parameters didn't match the server's expectations"))
    }

    Map(
      "handbook" -> handbook,
      "chapters" -> handbookService.retrieveChaptersForHandbook(handbook.id.get),
      "sections" -> handbookService.retrieveSectionsForHandbook(handbook.id.get, runImageReplacement = false),
      "organization" -> organization,
      "linkCollections" -> handbookLinkService.retrieveLinkCollectionsForHandbook(handbook.id.get)
    )
  }

  /**
    * Get the full section with text
    */
  get("/section/:sectionId/?") {
    val section = handbookService.retrieveSection(extractRequiredParam("sectionId")).getOrElse(ScalatraExceptions.notFound())
    val handbook = handbookService.retrieveHandbook(section.handbookId).getOrElse(ScalatraExceptions.notFound())

    if (!handbook.isPublic) {
      throwUnauthorizedIfInvalidIp(handbook.externalOrgId)
    }

    section.copy(text = removeHtmlHeader(section.text))
  }

  get("/search/?") {
    val externalOrgId = extractRequiredParam("externalOrgId")
    val query = extractRequiredParam("query")
    val page = extractOptionalInt("page")
    val handbookId = extractRequiredParam("handbookId")

    log.debug(s"SearchServlet got query $query")

    searchService.doSearch(externalOrgId, query, Option(handbookId), page)
  }

  /**
    * Retrieve a section for reading link view
    * */
  get("/linkdata/:linkId/?") {
    val linkId = extractRequiredParam("linkId")
    readingLinkService.retrieveDataForLink(linkId)
  }

  get("/attactment/:fileId") {
    try {
      val fileId = extractRequiredParam("fileId")
      val file = fileService.retrieveFile(fileId)

      contentType = file.contentType.getOrElse("image/png")
      response.addHeader("Content-Disposition", generateContentDispositionValue(file.name))
      file.byteArray
    } catch {
      case e: Exception =>
        ScalatraExceptions.notFound(Some("File not found"))
    }
  }

  get("/section/:ownerId/attachments") {
    val ownerId = extractRequiredParam("ownerId")
    val section = handbookService.retrieveSection(ownerId).getOrElse(ScalatraExceptions.notFound())
    val handbook = handbookService.retrieveHandbook(section.handbookId).getOrElse(ScalatraExceptions.notFound())

    if (!handbook.isPublic) {
      throwUnauthorizedIfInvalidIp(handbook.externalOrgId)
    }

    fileLinkService.retrieveFileLinks(ownerId, "SECTION")
  }

  get("/chapter/:ownerId/attachments") {
    val ownerId = extractRequiredParam("ownerId")
    val chapter = handbookService.retrieveChapter(ownerId).getOrElse(ScalatraExceptions.notFound())
    val handbook = handbookService.retrieveHandbook(chapter.handbookId).getOrElse(ScalatraExceptions.notFound())

    if (!handbook.isPublic) {
      throwUnauthorizedIfInvalidIp(handbook.externalOrgId)
    }
    fileLinkService.retrieveFileLinks(ownerId, "CHAPTER")
  }

  get("/section/:ownerId/attachments/count") {
    val ownerId = extractRequiredParam("ownerId")
    val section = handbookService.retrieveSection(ownerId).getOrElse(ScalatraExceptions.notFound())
    val handbook = handbookService.retrieveHandbook(section.handbookId).getOrElse(ScalatraExceptions.notFound())

    if (!handbook.isPublic) {
      throwUnauthorizedIfInvalidIp(handbook.externalOrgId)
    }
    fileLinkService.retrieveFileLinkCount(ownerId, "SECTION")
  }

  get("/chapter/:ownerId/attachments/count") {
    val ownerId = extractRequiredParam("ownerId")
    val chapter = handbookService.retrieveChapter(ownerId).getOrElse(ScalatraExceptions.notFound())
    val handbook = handbookService.retrieveHandbook(chapter.handbookId).getOrElse(ScalatraExceptions.notFound())

    if (!handbook.isPublic) {
      throwUnauthorizedIfInvalidIp(handbook.externalOrgId)
    }
    fileLinkService.retrieveFileLinkCount(ownerId, "CHAPTER")
  }

  private def removeHtmlHeader(html: Option[String]): Option[String] = {
    Try(html.map(content => (XML.loadString(content) \ "body").head.buildString(stripComments = true))) match {
      case Success(withBodyTag) => removeBodyTag(withBodyTag)
      case Failure(_) => html
    }
  }

  private def removeBodyTag(body: Option[String]): Option[String] = {
    body.map(_.replace("<body>", "").replace("</body>", ""))
  }

  private def throwUnauthorizedIfInvalidIp(handbookExternalId: String) {
    val organizationsForIp = getOrganizationsForIp
    log.debug(s"Ip-address has access to: ${organizationsForIp}")
    if(!organizationsForIp.contains(handbookExternalId)) {
      ScalatraExceptions.forbiddenException("Invalid IP-address")
    }
  }

  def getIndexHtml: String = {
    val html = new SSRStateInjector(servletContext.getResourceAsStream("/public/index.html"))
      .addString("__BASENAME__", publicWebAppPath)
      .addString("__MATOMOBASE__", matomoServer)
      .addTrackingCode(TrackingTagTemplate.get(matomoServer, matomoContainer))
      .toString()
    ApplicationNameReplacer.replaceInLocalAbsoluteHrefAndSrc(html, webAppPath)
  }

  def generateContentDispositionValue(fileName: String): String = {
    val urlEncodedFileName = new org.scalatra.util.RicherString.RicherStringImplicitClass(fileName).urlEncode(UTF_8)
    s"inline; filename=${"\""}$fileName${"\""}; filename*=UTF-8''$urlEncodedFileName"
  }
}
