import React from "react";
import {
  Link,
  useParams,
  useLocation,
  useNavigate,
} from "react-router-dom";
import {
  Nav,
  Container,
  Input,
  Hero,
  Title,
  Subtitle,
  Columns,
  Column,
  ImageContainer,
  Tabs,
  ImageHero,
} from "kf-bui";
import styled from "styled-components";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useAppDispatch, useAppSelector } from "@/store";
import { setSearchQuery } from "@/store/slices/publicSearchSlice";
import {
  useFetchPublicHandbookQuery,
  useFetchPublicConfigQuery,
} from "@/store/services/handbook/publicHandbookApi";

import { DropDownLinkItems } from "../DropDownLinkItems";

const GrowingHero = styled(ImageHero)`
  .container {
    height: unset !important;
  }
  .hero-body {
    height: unset !important;
  }
  .hero-foot {
    height: unset !important;
  }
  height: unset !important;
`;

export const Navbar: React.FC = () => {
  const t = usePrefixedTranslation("public.components.Header");
  const dispatch = useAppDispatch();
  const searchState = useAppSelector((state) => state.publicSearch);

  const location = useLocation();
  const navigate = useNavigate();

  const { externalOrgId, handbookId } = useParams<{
    externalOrgId: string;
    handbookId: string;
  }>();

  const { data: configData } = useFetchPublicConfigQuery();

  const { data: handbookData } = useFetchPublicHandbookQuery(
    { externalOrgId: externalOrgId!, handbookId: handbookId! },
    { skip: !externalOrgId || !handbookId }
  );

  const organization = handbookData?.organization;
  const handbook = handbookData?.handbook;
  const linkCollections = handbookData?.linkCollections || [];

  const logoUrl = organization
    ? configData?.logoUrlStart && configData.logoUrlStart !== ""
      ? `${configData.logoUrlStart}${organization.id}`
      : `/images/logo/${organization.id}`
    : null;
  const bannerUrl = organization
    ? configData?.bannerUrlStart && configData.bannerUrlStart !== ""
      ? `${configData.bannerUrlStart}${organization.id}`
      : `/images/banner/${organization.id}`
    : null;

  const onSearch = (event: React.FormEvent) => {
    event.preventDefault();
    if (!searchState.query.trim()) {
      return;
    }
    if (externalOrgId && handbookId) {
      navigate(`/${externalOrgId}/${handbookId}/search`);
    }
  };

  const onQueryChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(event.target.value));
  };

  return (
    <GrowingHero className="public-hero" color="info" imageUrl={bannerUrl || undefined}>
      <Container>
        {externalOrgId && handbookId ? (
          <>
            <Hero.Body>
              <Columns vCentered>
                {logoUrl && (
                  <Column narrow paddingLess>
                    <div className="organization-logo">
                      <img
                        src={logoUrl}
                        alt="Organisasjonslogo"
                        onError={(e) => {
                          e.currentTarget.style.display = "none";
                        }}
                      />
                    </div>
                  </Column>
                )}
                <Column>
                  <Title className="handbook-title">
                    {handbook?.title || "Laster håndbok..."}
                  </Title>
                  {organization && (
                    <Subtitle className="organization-subtitle">
                      {organization.name}
                    </Subtitle>
                  )}
                </Column>
                <Column
                  as="form"
                  onSubmit={onSearch}
                  className="search-input-container"
                >
                  <Input
                    aria-label="Søk"
                    placeholder="Søk"
                    size="medium"
                    iconLeft="search"
                    iconSize="small"
                    value={searchState.query}
                    onChange={onQueryChange}
                    type="search"
                  />
                </Column>
              </Columns>
            </Hero.Body>

            <Hero.Footer>
              <Tabs
                className="public-tabs"
                boxed
                centered
                style={{
                  overflow: "visible",
                  overflowX: "visible",
                  display: "inline-block",
                }}
              >
                <Tabs.Tab
                  active={!location.pathname.includes("/search")}
                  style={{ overflow: "hidden" }}
                >
                  <Link to={`/${externalOrgId}/${handbookId}`}>Håndbok</Link>
                </Tabs.Tab>
                <Tabs.Tab
                  active={location.pathname.includes("/search")}
                  style={{ overflow: "hidden" }}
                >
                  <Link to={`/${externalOrgId}/${handbookId}/search`}>
                    Søkeresultat
                  </Link>
                </Tabs.Tab>
                <Tabs.Tab className="has-dropdown">
                  <DropDownLinkItems linkCollections={linkCollections} />
                </Tabs.Tab>
              </Tabs>
            </Hero.Footer>
          </>
        ) : (
          <>
            <Hero.Header>
              <Nav shadow>
                <Container>
                  <Nav.Brand>
                    <Nav.Item brand>{t("brand")}</Nav.Item>
                  </Nav.Brand>
                </Container>
              </Nav>
            </Hero.Header>
            <Hero.Body>
              <Columns vCentered>
                {logoUrl && (
                  <Column narrow paddingLess>
                    <ImageContainer dimension="48x48">
                      <img src={logoUrl} alt="Organisasjonslogo" />
                    </ImageContainer>
                  </Column>
                )}
              </Columns>
            </Hero.Body>
          </>
        )}
      </Container>
    </GrowingHero>
  );
};
