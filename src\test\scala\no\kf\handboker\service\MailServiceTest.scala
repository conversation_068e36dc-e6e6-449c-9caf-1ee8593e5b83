package no.kf.handboker.service

import _root_.no.kf.handboker.model.local.{ChangeNotification, Handbook}
import no.kf.config.Settings
import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.config._
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.mockito.Mockito.when
import org.scalatest.Matchers._
import org.scalatest.junit.JUnitRunner
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, FunSuite}

@RunWith(classOf[JUnitRunner])
class MailServiceTest extends FunSuite with DefaultTestDI with MockitoSugar with BeforeAndAfterEach {

  override val componentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val settings: Settings = {
      val settingsMock = mock[Settings]
      when(settingsMock.settingFor(SMTPSendMailToRecipients)).thenReturn("false") // Set to true and update email address for a "live" test
      when(settingsMock.optionalSettingFor(SMTPHost)).thenReturn(Some("smtp.gmail.com"))
      when(settingsMock.optionalSettingFor(SMTPPort)).thenReturn(Some("587"))
      when(settingsMock.settingFor(SMTPUser)).thenReturn("<EMAIL>")
      when(settingsMock.settingFor(SMTPPwd)).thenReturn("C786Y##!")
      when(settingsMock.settingFor(SMTPSentEmailFrom)).thenReturn("<EMAIL>")
      when(settingsMock.optionalSettingFor(SMTPOverrideFile)).thenReturn(None)
      settingsMock
    }
    override lazy val mailService = new MailService
  }

  val mailService = componentRegistry.mailService

  test("That we can send mail") {
    val emailAddress = "<EMAIL>"
    val subject = "Test for sending av e-post fra Håndbøker"
    val body = "Dette er en automatisk genert e-post fra MailServiceTest"
    val status = mailService.send(subject, body, emailAddress)
    status shouldBe true
  }

  test("That we don't send emails if there are no notifications") {
    when(componentRegistry.subscriptionService.retrieveChangesForEmails).thenReturn(Map.empty[String, List[ChangeNotification]])
    assert(mailService.sendChangeNotificationMessages().isEmpty)
  }

//  test("That we can generate notifications mail") {
//    when(componentRegistry.subscriptionService.retrieveChangesForEmails).thenReturn(generateChanges("<EMAIL>"))
//    val res = mailService.sendChangeNotificationMessages()
//    assert(res.forall(_ == true))
//  }


  def generateChanges(email: String = "<EMAIL>"): Map[String, List[ChangeNotification]] = {
    val handbook1 = Handbook(Some("1"), "Håndbok 1", None, "9900")
    val handbook2 = Handbook(Some("2"), "Håndbok 2", None, "9900")
    val changes = List(
      /*ChangeNotification(Some("1"), "Slettet bok", handbook1, DateTime.now),
      ChangeNotification(Some("2"), "Endret tittel", handbook1, DateTime.now),
      ChangeNotification(Some("3"), "Nytt kapittel", handbook1, DateTime.now),

      ChangeNotification(Some("4"), "Fyttet kapittel", handbook2, DateTime.now),
      ChangeNotification(Some("5"), "Nytt avsnitt", handbook2, DateTime.now),
      ChangeNotification(Some("6"), "Endret nivå for kapittel", handbook2, DateTime.now)*/
    )

    Map(email -> changes)
  }
}
