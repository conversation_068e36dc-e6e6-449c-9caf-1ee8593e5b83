package no.kf.handboker.model.local

import org.joda.time.DateTime

trait HandbookProperties {
  val id: Option[String]
  val title: String
  val importedHandbookId: Option[String]
  val externalOrgId: String
  val localChange: Boolean = false
  val isPublic: Boolean = false
  val pendingChange: Boolean = false
  val pendingDeletion: Boolean = false
  val pendingChangeUpdatedDate: Option[DateTime] = None
  val updatedDate: Option[DateTime] = None
  val createdDate: Option[DateTime] = None
  val updatedBy: Option[String] = None
  val createdBy: Option[String] = None
  // TODO Denne burde flyttes til CentralHandbook, men det forutsetter at frontend begynner å bruke den. Slik det er nå, er alt Handbook i lokal modus
  // Alternativ navne denne om til centralHandbookIsPublished e.l.
  val isPublished: Boolean = false
}

case class Handbook(
                     id: Option[String],
                     title: String,
                     importedHandbookId: Option[String],
                     externalOrgId: String,
                     override val localChange: Boolean = false,
                     override val isPublic: Boolean = false,
                     override val pendingChange: Boolean = false,
                     override val pendingDeletion: Boolean = false,
                     override val pendingChangeUpdatedDate: Option[DateTime] = None,
                     override val updatedDate: Option[DateTime] = None,
                     override val createdDate: Option[DateTime] = None,
                     override val updatedBy: Option[String] = None,
                     override val createdBy: Option[String] = None,
                     override val isPublished: Boolean = false,
                     versionOf: Option[String] = None) extends HandbookProperties
