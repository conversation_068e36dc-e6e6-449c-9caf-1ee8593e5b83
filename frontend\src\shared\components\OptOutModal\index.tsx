import React, { useState, useRef, useEffect } from "react";
import { Modal, Button, Icon } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import Cookies from "js-cookie";
import "./OptOutModal.css";

interface OptOutModalProps {
  isOpen: boolean;
  toggleHide: () => void;
}

export const OptOutModal: React.FC<OptOutModalProps> = ({
  isOpen,
  toggleHide,
}) => {
  const t = usePrefixedTranslation("common.components.OptOutModal");
  const content = useRef<HTMLDivElement>(null);

  const [isMatomoEnabled, setIsMatomoEnabled] = useState(false);
  const [active, setActive] = useState(false);
  const [height, setHeight] = useState("0px");

  const matomoBase = (window as any).__MATOMOBASE__;

  useEffect(() => {
    if ((window as any).__MATOMOBASE__) {
      delete (window as any).__MATOMOBASE__;
    }
  }, []);

  const handleToggleChange = (isEnabled: boolean) => {
    setIsMatomoEnabled(isEnabled);
    localStorage.setItem("matomoEnabled", JSON.stringify(isEnabled));

    if (!isEnabled) {
      Cookies.remove("MATOMO_SESSID", { path: "" });

      const allCookies = Cookies.get();
      Object.keys(allCookies).forEach((cookieName) => {
        if (cookieName.startsWith("_pk_")) {
          Cookies.remove(cookieName, { path: "" });
        }
      });
    }
  };

  const toggleAccordion = () => {
    setActive(!active);
    setHeight(
      active ? "0px" : `${content.current ? content.current.scrollHeight : 0}px`
    );
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    if (value === "all") {
      handleToggleChange(true);
    } else if (value === "necessary") {
      handleToggleChange(false);
    }
  };

  useEffect(() => {
    const savedMatomoSetting = localStorage.getItem("matomoEnabled");
    setIsMatomoEnabled(
      savedMatomoSetting ? JSON.parse(savedMatomoSetting) : false
    );
  }, []);

  return (
    <Modal isOpen={isOpen} onClose={toggleHide}>
      <Modal.Header onClose={toggleHide}>
        <Modal.Title>{t("title")}</Modal.Title>
      </Modal.Header>
      <Modal.Body className="comments">
        <p className="cookie-desc">{t("description1")}</p>
        <p className="cookie-desc">{t("description2")}</p>
        <p className="cookie-desc">{t("description3")}</p>

        <a
          href="https://filer.kf-infoserie.no/veiledninger/Informasjonskapsler_KF_H%C3%A5ndb%C3%B8ker.html"
          target="_blank"
          rel="noopener noreferrer"
          className="cookie-link-desc"
        >
          {t("readMore")}
        </a>

        <div className="cookie-type-section-container">
          <label>
            <input
              type="radio"
              name="cookie-type"
              value="all"
              checked={isMatomoEnabled}
              onChange={handleRadioChange}
              disabled={!matomoBase}
            />
            <span>{t("acceptAll")}</span>
          </label>
          <label>
            <input
              type="radio"
              name="cookie-type"
              value="necessary"
              checked={!isMatomoEnabled}
              onChange={handleRadioChange}
            />
            <span>{t("acceptNecessary")}</span>
          </label>
        </div>

        <div className="toggle-cookies-container">
          <button
            type="button"
            className="toggle-cookies-button"
            onClick={toggleAccordion}
          >
            {t("showCookies")}
          </button>
          <Icon
            icon="AngleDown"
            size="small"
            onClick={toggleAccordion}
            className={active ? "rotate" : "rotate-0"}
            style={{ cursor: "pointer" }}
          />
        </div>

        <div
          className="cookies-wrapper"
          ref={content}
          style={{ maxHeight: height }}
        >
          <h4 className="cookie-type-title">{t("necessary")}</h4>

          <div className="cookie-item">
            <div className="toggle-button toggle-button--aava">
              <input id="JSESSIONID" type="checkbox" disabled checked />
              <label
                htmlFor="JSESSIONID"
                data-on-text="På"
                data-off-text="Av"
              />
              <div className="toggle-button__icon" />
            </div>
            <div className="cookie-details">
              <h4 className="cookie-title">
                <span className="cookie-name">JSESSIONID</span>
              </h4>
              <p className="cookie-provider">{t("recipient")}: KF</p>
              <p>{t("cookies.jsessionid.description")}</p>
            </div>
          </div>

          <div className="cookie-item">
            <div className="toggle-button toggle-button--aava">
              <input id="XSRF-TOKEN" type="checkbox" disabled checked />
              <label
                htmlFor="XSRF-TOKEN"
                data-on-text="På"
                data-off-text="Av"
              />
              <div className="toggle-button__icon" />
            </div>
            <div className="cookie-details">
              <h4 className="cookie-title">
                <span className="cookie-name">XSRF-TOKEN</span>
              </h4>
              <p className="cookie-provider">{t("recipient")}: KF</p>
              <p>{t("cookies.xsrfToken.description")}</p>
            </div>
          </div>

          <div className="cookie-item">
            <div className="toggle-button toggle-button--aava">
              <input id="TGC" type="checkbox" disabled checked />
              <label htmlFor="TGC" data-on-text="På" data-off-text="Av" />
              <div className="toggle-button__icon" />
            </div>
            <div className="cookie-details">
              <h4 className="cookie-title">
                <span className="cookie-name">TGC</span>
              </h4>
              <p className="cookie-provider">{t("recipient")}: KF</p>
              <p>{t("cookies.tgc.description")}</p>
            </div>
          </div>

          <h4 className="cookie-type-title">{t("optional")}</h4>

          {matomoBase ? (
            <div className="cookie-item">
              <div className="toggle-button toggle-button--aava">
                <input
                  id="Matomo"
                  type="checkbox"
                  checked={isMatomoEnabled}
                  onChange={() => handleToggleChange(!isMatomoEnabled)}
                />
                <label htmlFor="Matomo" data-on-text="På" data-off-text="Av" />
                <div className="toggle-button__icon" />
              </div>
              <div className="cookie-details">
                <h4 className="cookie-title">
                  <span className="cookie-name">KF Analytics</span>
                </h4>
                <p className="cookie-provider">{t("recipient")}: Matomo</p>
                <div className="cookie-sub-list">
                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">_pk_id</span>
                    <span className="cookie-sub-details">
                      {t("cookies.matomo.pkId")}
                    </span>
                  </div>
                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">_pk_ref</span>
                    <span className="cookie-sub-details">
                      {t("cookies.matomo.pkRef")}
                    </span>
                  </div>
                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">_pk_ses</span>
                    <span className="cookie-sub-details">
                      {t("cookies.matomo.pkSes")}
                    </span>
                  </div>
                  <div className="cookie-sub-item">
                    <span className="cookie-sub-name">MATOMO_SESSID</span>
                    <span className="cookie-sub-details">
                      {t("cookies.matomo.sessId")}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ) : null}

          {matomoBase && isMatomoEnabled ? (
            <iframe
              title="Tracking Opt-Out"
              className="optout-iframe"
              src={`https://${matomoBase}/index.php?module=CoreAdminHome&action=optOut&language=nb`}
            />
          ) : null}

          {!matomoBase && (
            <div className="no-matomo-message">{t("noMatomoInfo")}</div>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button control onClick={toggleHide}>
          {t("saveButton")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
