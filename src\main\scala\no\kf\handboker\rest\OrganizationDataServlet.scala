package no.kf.handboker.rest

import java.io.{BufferedWriter, OutputStreamWriter}
import java.nio.charset.StandardCharsets.UTF_8

import no.kf.db.IDGenerator
import no.kf.handboker.config.{LDAPSearchBase, SiteUrl}
import no.kf.handboker.model.LDAPUser
import no.kf.handboker.model.local.LocalEditor
import no.kf.handboker.rest.support.SessionSupport
import no.kf.handboker.service.{ ExternalOrganizationService, LocalHandbookService, OrganizationDataService}
import no.kf.rest.ScalatraExceptions
import no.kf.rest.support.JsonSupport
import org.joda.time.DateTime
import org.scalatra.{Ok, ScalatraServlet}

import scala.collection.mutable.ListBuffer

class OrganizationDataServlet extends ScalatraServlet with JsonSupport with SessionSupport {

  lazy val organizationDataService: OrganizationDataService = componentRegistry.organizationDataService
  lazy val externalOrgService: ExternalOrganizationService = componentRegistry.externalOrganizationService
  lazy val localHandbookService: LocalHandbookService = componentRegistry.localHandbookService
  private val deleteTokenSessionKey = "no.kf.handboker.rest.OrganizationDataServlet.token"
  private val migrateTokenSessionKey = "no.kf.handboker.rest.OrganizationDataServlet.migrateToken"
  lazy val url = s"${componentRegistry.settings.settingFor(SiteUrl)}/organization-data/delete"
  lazy val urlMigrate = s"${componentRegistry.settings.settingFor(SiteUrl)}/organization-data/migrate-editors"
  lazy val urlConvertLinks = s"${componentRegistry.settings.settingFor(SiteUrl)}/organization-data/convert-links"
  lazy val imgAddSize = s"${componentRegistry.settings.settingFor(SiteUrl)}/organization-data/size-images"

  get("/export/:externalOrgId/?") {
    val externalOrgId = extractRequiredParam("externalOrgId")
    if (!currentUser.globalAdmin) {
      ScalatraExceptions.unauthorizedException(s"Bare KF-Admin kan hente ut data for organisasjonen med id $externalOrgId")
    }

    contentType = "text/tab-separated-values"
    response.setHeader("Content-Disposition", s"attachment; filename=handboker_$externalOrgId.tsv")
    val outputBuffer = new BufferedWriter(new OutputStreamWriter(response.getOutputStream, UTF_8))
    organizationDataService.writeDataForExternalOrg(externalOrgId, outputBuffer)
  }

  get("/delete/:externalOrgId/?") {
    val externalOrgId = extractRequiredParam("externalOrgId")
    if (!currentUser.globalAdmin) {
      ScalatraExceptions.unauthorizedException(s"Bare KF-Admin kan slette all data for organisasjonen med id $externalOrgId")
    }

    val token = IDGenerator.generateUniqueId
    session.put(deleteTokenSessionKey, (token, externalOrgId))

    contentType = "text/html"

    <html>
      <head>
        <meta charset="utf-8"/>
      </head>
      <body>
        <form name="confirmdeleteform" action={url} method="POST">
          <input type="hidden" name="token" value={token}/>
          <input type="submit" value={s"Slett all data for kommune med id $externalOrgId"}/>
        </form>
      </body>
    </html>
  }

  post("/delete/?") {
    val providedToken = multiParams.getOrElse("token", Seq.empty).headOption.getOrElse(ScalatraExceptions.forbiddenException("Manglende token i request, bruk riktig endepunkt for sletting."))
    val (sessionToken, externalOrgId) = session.remove(deleteTokenSessionKey).asInstanceOf[Option[(String, String)]].getOrElse(ScalatraExceptions.notFound(Some("Ingen token i sesjonen, kan ikke starte sletting.")))

    if (providedToken != sessionToken) {
      ScalatraExceptions.unauthorizedException(s"Ukjent slettetoken, kan ikke starte sletting.")
    }

    organizationDataService.deleteDataForExternalOrg(externalOrgId)

    contentType = "text/html"
    Ok("Data slettet OK")
  }

  // Grants local editor access to all handbooks for every org admin users are members of
  get("/migrate-editors/?") {
    if (!currentUser.globalAdmin) {
      ScalatraExceptions.unauthorizedException(s"Bare KF-Admin kan migrere redaktører for alle organisasjoner.")
    }

    val token = IDGenerator.generateUniqueId
    session.put(migrateTokenSessionKey, (token, currentUser.email))

    contentType = "text/html"

    <html>
      <head>
        <meta charset="utf-8"/>
      </head>
      <body>
        <form name="confirmmigrateform" action={urlMigrate} method="POST">
          <input type="hidden" name="token" value={token}/>
          <input type="submit" value={s"Gi samtlige administratorer redaktørtilgang til samtlige håndbøker i sin(e) organisasjon(er)."}/>
        </form>
      </body>
    </html>
  }

  post("/migrate-editors/?") {
    val providedToken = multiParams.getOrElse("token", Seq.empty).headOption.getOrElse(ScalatraExceptions.forbiddenException("Manglende token i request, bruk riktig endepunkt for migrering."))
    val (sessionToken, actingUser) = session.remove(migrateTokenSessionKey).asInstanceOf[Option[(String, String)]].getOrElse(ScalatraExceptions.notFound(Some("Ingen token i sesjonen, kan ikke starte migrering.")))

    if (providedToken != sessionToken) {
      ScalatraExceptions.unauthorizedException(s"Ukjent token, kan ikke starte migrering.")
    }

    val externalOrgs = externalOrgService.fetchOrganizationsFromBrukerAdm() //localHandbookService.retrieveAllExternalOrgIds()
    val admins = getAdministrators.getOrElse(ScalatraExceptions.notFound(Some("Ingen administratorer funnet i AD.")))
    val orgAdminMap = externalOrgs.map {
      brukerAdmOrg => (brukerAdmOrg.id, admins.filter(admin => admin.organizations.contains(brukerAdmOrg.id)))
    }.toMap

    val editors = ListBuffer[LocalEditor]()
    val handbookIds = scala.collection.mutable.Set[String]()
    val currentLocalEditors = localHandbookService.retrieveAllLocalEditors()

    externalOrgs.foreach(org => {
      val handbooksForOrg = localHandbookService.retrieveHandbooksForExternalOrganization(org.id)
      orgAdminMap(org.id).foreach(adminUser => {
        handbooksForOrg.foreach(handbook => {
          if (!currentLocalEditors.exists(editor => editor.handbookId == handbook.id.get && editor.rightsHolder.trim.equalsIgnoreCase(adminUser.email.trim))
            && !editors.exists(editor => editor.handbookId == handbook.id.get && editor.rightsHolder.trim.equalsIgnoreCase(adminUser.email.trim))) {
            val newEditor = LocalEditor(id = None, handbookId = handbook.id.get, rightsHolder = adminUser.email, addedBy = "KF", addedDate = DateTime.now)
            localHandbookService.insertLocalEditor(newEditor)
            editors += newEditor
          }
          handbookIds.add(handbook.id.get)
        })
      })
    })

    contentType = "text/html"
    val okString = s"Lokale redaktørtilganger opprettet OK (admin: $actingUser): ${editors.size} tilganger i ${handbookIds.size} håndbøker for ${externalOrgs.size} organisasjoner"
    log.info(okString)
    Ok(okString)
  }

  private def getAdministrators: Option[List[LDAPUser]] = {
    val base = componentRegistry.settings.settingFor(LDAPSearchBase)
    val adminGroupString = s"CN=Håndbøker-Administrator,OU=Handboker,OU=KFApplications,$base"
    ldapService.findBrukerMemberOf(adminGroupString)
  }

  get("/convert-links/?") {
    if (!currentUser.globalAdmin) {
      ScalatraExceptions.unauthorizedException(s"Bare KF-Admin kan konvertere linker for håndbøker")
    }

    val token = IDGenerator.generateUniqueId
    session.put(deleteTokenSessionKey, (token, currentUser.email))

    contentType = "text/html"

    <html>
      <head>
        <meta charset="utf-8"/>
      </head>
      <body>
        <form name="confirmconvertlinksform" action={urlConvertLinks} method="POST">
          <input type="hidden" name="token" value={token}/>
          <input type="submit" value={s"Konverter linker i lokale og sentrale håndbøker til å åpnes i egen tab"}/>
        </form>
      </body>
    </html>
  }

  post("/convert-links/?") {
    val providedToken = multiParams.getOrElse("token", Seq.empty).headOption.getOrElse(ScalatraExceptions.forbiddenException("Manglende token i request, bruk riktig endepunkt for konvertering av lenker."))
    val (sessionToken, actingUser) = session.remove(deleteTokenSessionKey).asInstanceOf[Option[(String, String)]].getOrElse(ScalatraExceptions.notFound(Some("Ingen token i sesjonen, kan ikke starte konverteringen av lenker.")))

    if (providedToken != sessionToken) {
      ScalatraExceptions.unauthorizedException(s"Ukjent token, kan ikke starte konverteringen av lenker.")
    }

    val statsMessage = organizationDataService.replaceTargetForLinksInHandbooks(actingUser)

    contentType = "text/html"
    val okString = s"Linker er nå blitt konvertert til å åpnes i ny tab: ${statsMessage}"
    log.info(okString)
    Ok(okString)
  }

  
  get("/size-images/?") {
    if (!currentUser.globalAdmin) {
      ScalatraExceptions.unauthorizedException(s"Bare KF-Admin kan sette størrelser på bilder for håndbøker")
    }

    val token = IDGenerator.generateUniqueId
    session.put(deleteTokenSessionKey, (token, currentUser.email))

    contentType = "text/html"

    <html>
      <head>
        <meta charset="utf-8"/>
      </head>
      <body>
        <form name="confirmsizeimagesform" action={imgAddSize} method="POST">
          <input type="hidden" name="token" value={token}/>
          <input type="submit" value={s"Gi bilder i databasen bredde og høyde om de mangler"}/>
        </form>
      </body>
    </html>
  }

  post("/size-images/?") {
    val providedToken = multiParams.getOrElse("token", Seq.empty).headOption.getOrElse(ScalatraExceptions.forbiddenException("Manglende token i request, bruk riktig endepunkt for innsetting av bilde størrelser."))
    val (sessionToken, actingUser) = session.remove(deleteTokenSessionKey).asInstanceOf[Option[(String, String)]].getOrElse(ScalatraExceptions.notFound(Some("Ingen token i sesjonen, kan ikke starte innsetting av størrelser.")))

    if (providedToken != sessionToken) {
      ScalatraExceptions.unauthorizedException(s"Ukjent token, kan ikke starte innsetting av størrelser.")
    }
    
    val statsMessage = organizationDataService.addSizeToImages(actingUser)
    
    contentType = "text/html"
    val okString = s"Bilder har nå fått bredde og høyde tag: ${statsMessage}."
    log.info(okString)
    Ok(okString)
  }
}
