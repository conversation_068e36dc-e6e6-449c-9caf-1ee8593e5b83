package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.handboker.repository.CentralAccessRepositoryComponent

trait CentralAccessServiceComponent extends TransactionManager {
  this: CentralAccessRepositoryComponent =>

  val centralAccessService: CentralAccessService

  class CentralAccessServiceImpl extends CentralAccessService {

    override def hasAccess(importedHandbookId: String, externalOrgId: String): Boolean = {
      val accesses = retrieveOrgAccesses(externalOrgId)
      accesses.contains(importedHandbookId)
    }

    override def retrieveOrgAccesses(externalOrgId: String): List[String] = inTransaction {
      centralAccessRepository.retrieveAccesses(externalOrgId)
    }

    override def updateOrgAccesses(externalOrgId: String, handbookIds: List[String]): List[String] = inTransaction {
      centralAccessRepository.persistAccesses(externalOrgId, handbookIds)
      centralAccessRepository.retrieveAccesses(externalOrgId)
    }
  }
}

trait CentralAccessService {
  def hasAccess(importedHandbookId: String, externalOrgId: String): Boolean
  def retrieveOrgAccesses(externalOrgId: String): List[String]
  def updateOrgAccesses(externalOrgId: String, handbooksIds: List[String]): List[String]
}
