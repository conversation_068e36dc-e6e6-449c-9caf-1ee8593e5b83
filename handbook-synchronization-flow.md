## Synchronization Workflow

```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  CENTRAL TO LOCAL HANDBOOK SYNCHRONIZATION - DETAILED FLOW                                  │
│                                                                                             │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐               │
│  │ Publication │     │ Version     │     │ Identify    │     │ Apply       │               │
│  │ Triggered   │────►│ Creation    │────►│ Local       │────►│ Changes     │               │
│  │             │     │             │     │ Handbooks   │     │             │               │
│  └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘               │
│                                                                      │                      │
│                                                                      │                      │
│                                                                      ▼                      │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐               │
│  │ Send        │     │ Update      │     │ Create      │     │ Process     │               │
│  │ Notifi-     │◄────│ Search      │◄────│ Change      │◄────│ Each        │               │
│  │ cations     │     │ Index       │     │ Records     │     │ Handbook    │               │
│  └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘               │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### 1. Publication Triggered
- Admin publishes a central handbook
- System retrieves publication candidates from `publicationRepository`
- Each candidate represents a handbook ready for publication

### 2. Version Creation
- System creates a new version of the central handbook
- All chapters and sections are copied with new IDs
- Original IDs are mapped to new IDs for reference
- Version chain is maintained via `versionOf` references

### 3. Identify Local Handbooks
- System finds all local handbooks based on the central handbook ID
- Each organization that has imported the handbook is identified
- Local handbooks may have different modifications

### 4. Process Each Handbook
For each local handbook:
- Retrieve current state (chapters, sections)
- Identify which items are locally modified
- Determine which central items are new, modified, or deleted

### 5. Apply Changes
For each item (chapter/section):
- **New items**: Add to local handbook with reference to central
- **Modified items with no local changes**: Update with central content
- **Modified items with local changes**: Flag as pending change (requires manual merge)
- **Deleted items**: Mark as deleted if no local changes

### 6. Create Change Records
- System creates `CentralChangeNotification` records
- These track which items have pending changes
- Used to notify users about content that needs review

### 7. Update Search Index
- For each affected organization, update search index
- Ensures search results reflect the latest content
- Uses `searchService.doReindex(externalOrgId)`

### 8. Send Notifications
- Email notifications sent to subscribers
- Alerts users about new or changed content
- Handled by `EmailNotificationsBatchJob`

## Code Implementation

The core synchronization logic is implemented in `HandbookSynchronizationService`:

```scala
override def synchronizeHandbooks(): Unit = {
  // Get handbooks ready for publication
  val candidates = inTransaction {
    publicationRepository.getPublicationCandidates
  }
  
  // Track organizations that need reindexing
  val extOrgsToBeReindexed = candidates.flatMap(publication => {
    log.info(s"Publiserer håndbok ${publication.handbookId}")

    // Get the central handbook
    val centralHandbook = inTransaction {
      centralHandbookRepository.retrieveCentralHandbook(publication.handbookId).get
    }

    // Create a new version
    val version = inTransaction {
      persistCentralHandbookVersion(centralHandbook, publication.createdBy)
    }
    log.info("Opprettet håndbok-versjon " + version._1.id.get)

    // Find all local handbooks based on this central handbook
    val localHandbooks = inTransaction {
      handbookRepository.retrieveHandbooksBasedOnCentralHandbookId(publication.handbookId)
    }
    
    // Process each local handbook
    localHandbooks.map(handbook => {
      // Synchronize the local handbook with the new central version
      synchronizeLocalHandbook(handbook.id.get, version._1.id.get)
      
      // Return the organization ID for reindexing
      handbook.externalOrgId
    })
  })
  
  // Reindex affected organizations
  extOrgsToBeReindexed.foreach(orgId => {
    searchService.doReindex(orgId)
  })
}
```