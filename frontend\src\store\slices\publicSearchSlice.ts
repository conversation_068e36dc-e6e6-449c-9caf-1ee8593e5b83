import { createSlice, type PayloadAction } from "@reduxjs/toolkit";

interface PublicSearchState {
  query: string;
  debouncedQuery: string;
  isActive: boolean;
}

const initialState: PublicSearchState = {
  query: "",
  debouncedQuery: "",
  isActive: false,
};

export const publicSearchSlice = createSlice({
  name: "publicSearch",
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.query = action.payload;
      state.isActive = action.payload.trim().length > 0;
    },
    setDebouncedQuery: (state, action: PayloadAction<string>) => {
      state.debouncedQuery = action.payload;
    },
    clearSearch: (state) => {
      state.query = "";
      state.debouncedQuery = "";
      state.isActive = false;
    },
  },
});

export const { setSearchQuery, setDebouncedQuery, clearSearch } =
  publicSearchSlice.actions;
export default publicSearchSlice.reducer;
