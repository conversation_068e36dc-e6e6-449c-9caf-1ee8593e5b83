package no.kf.handboker.batch

import no.kf.handboker.ProductionRegistry
import org.quartz.{DisallowConcurrentExecution, Job, JobExecutionContext, JobExecutionException}
import no.kf.util.Logging

@DisallowConcurrentExecution
class SentralHandbookBatchJob extends Job with Logging {

  lazy val handbookSynchronizationService = ProductionRegistry.componentRegistry.handbookSynchronizationService

  override def execute(context: JobExecutionContext): Unit = {
    val jobName = "Central Handbook publication batch job"

    try {
      log.info("Starting " + jobName)

      handbookSynchronizationService.synchronizeHandbooks()

      log.info("Finished running " + jobName)
    } catch {
      case e: Exception =>
        log.error("Error occurred during " + jobName, e)
        throw new JobExecutionException("Error occurred during " + jobName, e, false)
    }
  }

}
