import React from "react";
import { Pagination } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { SearchResult } from "@/types";

export interface SearchPaginationProps {
  result: SearchResult;
  onPageClick: (page: number) => void;
}

const MAX_BUTTONS = 5;

export const SearchPagination: React.FC<SearchPaginationProps> = ({
  result,
  onPageClick,
}) => {
  const t = usePrefixedTranslation("common.components.SearchPagination");
  const { pageSize, totalHits, page: activePage } = result;

  if (!(pageSize < totalHits)) {
    return null;
  }

  const items = Math.ceil(totalHits / pageSize);
  let startPage: number;
  let endPage: number;
  let hasHiddenPagesAfter: boolean;
  const pageButtons: React.ReactNode[] = [];

  if (MAX_BUTTONS) {
    const hiddenPagesBefore = activePage - Math.floor(MAX_BUTTONS / 2);
    startPage = hiddenPagesBefore > 1 ? hiddenPagesBefore : 1;
    hasHiddenPagesAfter = startPage + MAX_BUTTONS <= items;

    if (!hasHiddenPagesAfter) {
      endPage = items;
      startPage = items - MAX_BUTTONS + 1;
      if (startPage < 1) {
        startPage = 1;
      }
    } else {
      endPage = startPage + MAX_BUTTONS - 1;
    }
  } else {
    startPage = 1;
    endPage = items;
  }

  for (let pageNumber = startPage; pageNumber <= endPage; pageNumber += 1) {
    pageButtons.push(
      <Pagination.Item
        key={pageNumber}
        active={pageNumber === activePage}
        aria-label={t("pageLabel", { pageNumber })}
        onClick={() => onPageClick(pageNumber)}
      >
        {pageNumber}
      </Pagination.Item>
    );
  }

  return <Pagination centered>{pageButtons}</Pagination>;
};