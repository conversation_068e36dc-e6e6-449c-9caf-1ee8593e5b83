import { useEffect, useState } from "react";
import { <PERSON>, useParams, useNavigate } from "react-router-dom";
import { Button, Column, Columns, Group, Icon, Title } from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Spinner } from "@/shared/components/Spinner";

import {
  useGetLocalSectionQuery,
  useGetLocalSectionVersionsQuery,
  useDeleteLocalSectionMutation,
  useGetAttachmentCountQuery,
} from "@/store/services/handbook/localHandbookApi";
import { NoSelectionScreen } from "../../handbook/NoSelectionScreen";
import { LocalDeleteButton as DeleteButton } from "../../local/LocalDeleteButton";
import { PendingChangeWarning } from "../../PendingChangeWarning";
import { LocalMetadata as Metadata } from "../../local/LocalMetadata";
import { Wysiwyg } from "../../shared/Wysiwyg";
import { OldVersionView } from "./OldVersionView";
import { AttachmentModal } from "../../shared/AttachmentModal";

export const LocalSectionScreen = () => {
  const { handbookId, sectionId } = useParams() as {
    handbookId: string;
    sectionId: string;
  };
  const t = usePrefixedTranslation("editor.containers.SectionSelection");
  const navigate = useNavigate();

  const [showAttachments, setShowAttachments] = useState(false);

  const {
    data: section,
    error: sectionError,
    isLoading: isLoadingSection,
  } = useGetLocalSectionQuery(sectionId, {
    skip: !sectionId,
  });

  const { data: sectionVersions = [], error: versionsError } =
    useGetLocalSectionVersionsQuery(sectionId, {
      skip: !sectionId,
    });

  const {
    data: attachmentCount = { count: 0 },
    refetch: refetchAttachmentCount,
    error: attachmentCountError,
    isLoading: isLoadingAttachmentCount,
  } = useGetAttachmentCountQuery(
    { type: "section", id: sectionId },
    { skip: !sectionId }
  );

  const [deleteLocalSection] = useDeleteLocalSectionMutation();

  const handleDeleteSection = async () => {
    if (!section?.id) return;

    try {
      await deleteLocalSection(section.id).unwrap();
      toast.success(t("editor.success.sectionDeleted"));
      navigate(`/editor/${handbookId}/chapter/${section.parentId}`);
    } catch (error) {
      console.error("Error deleting section:", error);
      toast.error(t("editor.error.sectionDeleteFailed"));
    }
  };

  const handleAttachmentCountChange = () => {
    refetchAttachmentCount();
  };

  useEffect(() => {
    if (sectionError) {
      console.error("Error loading section:", sectionError);
      toast.error(t("editor.error.sectionLoadFailed"));
    }
    if (versionsError) {
      console.error("Error loading section versions:", versionsError);
    }
    if (attachmentCountError) {
      console.error("Error loading attachment count:", attachmentCountError);
    }
  }, [sectionError, versionsError, attachmentCountError, t]);

  useEffect(() => {
    if (!section && !isLoadingSection && sectionId) {
      toast.error(t("editor.error.sectionNotFound", { id: sectionId }));
    }
  }, [section, isLoadingSection, sectionId]);

  if (!sectionId) {
    return <NoSelectionScreen />;
  }

  if (isLoadingSection) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (!section) {
    return <NoSelectionScreen />;
  }

  return (
    <>
      {showAttachments && (
        <AttachmentModal
          isOpen={showAttachments}
          sectionId={sectionId}
          sectionType="SECTION"
          onClose={() => setShowAttachments(false)}
          onAttachmentCountChange={handleAttachmentCountChange}
        />
      )}

      <Columns>
        <Column>
          <Title>
            <Icon
              icon="RegFileLines"
              size="medium"
              style={{ marginRight: "1rem" }}
            />
            <span>{section.title}</span>
            {section.pendingTitleChange && (
              <Icon
                icon="exclamation-triangle"
                size="small"
                style={{ marginLeft: "0.5rem", color: "orange" }}
                title={t("editor.status.titlePendingChanges")}
              />
            )}
          </Title>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <Group>
            <Button
              control
              as={Link}
              to={`/editor/${section.handbookId}/section/${section.id}/edit/`}
              size="small"
              icon="pencil"
            >
              {t("editButton")}
            </Button>

            <Button
              control
              onClick={() => setShowAttachments(true)}
              size="small"
              className="add-attachment-btn"
              icon="Paperclip"
            >
              {t("addAttachment")}
              {isLoadingAttachmentCount ? (
                <span style={{ marginLeft: "4px", fontSize: "12px" }}>
                  (...)
                </span>
              ) : (
                <span className="attachment-count">
                  {attachmentCount.count}
                </span>
              )}
            </Button>
          </Group>
        </Column>

        <Column narrow>
          <Group>
            <PendingChangeWarning
              element={section}
              mergeLink={`/merge/section/${section.id}/`}
            />

            <Button
              control
              as={Link}
              to={`/editor/${section.handbookId}/section/${section.id}/move/`}
              size="small"
            >
              {t("moveButton")}
            </Button>

            {!section.pendingDeletion && (
              <DeleteButton
                toDelete={{
                  id: section.id!,
                  title: section.title,
                  type: "LOCAL_SECTION",
                }}
                onDelete={handleDeleteSection}
              />
            )}
          </Group>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <hr />
          <Metadata element={section} />
          <hr />
        </Column>
      </Columns>

      <div style={{ marginBottom: "2rem" }}>
        <label
          htmlFor="section-text"
          style={{
            fontWeight: "bold",
            fontSize: "large",
            display: "block",
            marginBottom: "0.5rem",
          }}
        >
          {t("sectionText")}
          {section.pendingTextChange && (
            <Icon
              icon="exclamation-triangle"
              size="small"
              style={{ marginLeft: "0.5rem", color: "orange" }}
              title={t("editor.status.textPendingChanges")}
            />
          )}
        </label>
        <Wysiwyg id="section-text" value={section.text || ""} disabled={true} />
      </div>

      <hr />

      {sectionVersions.length > 0 && (
        <OldVersionView versions={sectionVersions} />
      )}
    </>
  );
};
