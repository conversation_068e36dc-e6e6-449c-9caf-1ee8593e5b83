import React, { useState, useEffect, Fragment } from "react";
import { Modal, Field, Button, Select, Label, FormattedDate } from "kf-bui";
import { toast } from "@/shared/components/Toast";
import type { CentralHandbook, Handbook } from "@/types";
import {
  useGetCentralHandbookVersionsQuery,
  useGetCentralSectionsQuery,
  useGetCentralChaptersQuery,
} from "@/store/services/handbook/centralHandbookApi";

interface EksporteringsModalProps {
  centralHandbook?: CentralHandbook;
  localHandbook?: Handbook;
  onHide: () => void;
  isOpen: boolean;
}

export const EksporteringsModal: React.FC<EksporteringsModalProps> = ({
  centralHandbook,
  localHandbook,
  isOpen,
  onHide,
}) => {
  const handbook = centralHandbook || localHandbook;
  const isCentral = !!centralHandbook;

  const [selectedVersion, setSelectedVersion] = useState<
    CentralHandbook | Handbook | undefined
  >(undefined);

  const {
    data: publishedVersions = [],
    isLoading: isLoadingVersions,
    error: versionsError,
  } = useGetCentralHandbookVersionsQuery(centralHandbook?.id || "", {
    skip: !isOpen || !centralHandbook?.id || !isCentral,
  });

  const { data: allChapters = [], error: chaptersError } =
    useGetCentralChaptersQuery();
  const { data: allSections = [], error: sectionsError } =
    useGetCentralSectionsQuery();

  useEffect(() => {
    if (versionsError) {
      console.error("Error loading handbook versions:", versionsError);
    }

    if (chaptersError) {
      console.error("Error loading chapters:", chaptersError);
    }

    if (sectionsError) {
      console.error("Error loading sections:", sectionsError);
    }
  }, [versionsError, chaptersError, sectionsError]);

  const handbookDescendants = isCentral
    ? [
        ...allChapters.filter(
          (chapter) => chapter.centralHandbookId === centralHandbook!.id
        ),
        ...allSections.filter(
          (section) => section.centralHandbookId === centralHandbook!.id
        ),
      ]
    : [];

  useEffect(() => {
    if (!isCentral && localHandbook && !selectedVersion) {
      setSelectedVersion(localHandbook);
    }
  }, [selectedVersion, isCentral, localHandbook]);

  const [downloadingPdf, setDownloadingPdf] = useState(false);
  const [downloadingWord, setDownloadingWord] = useState(false);

  const makeUrl = (fileType: string, id: string): string => {
    const BASE_URL = isCentral
      ? "/handboker/handbooks/download/central"
      : "/handboker/handbooks/download/local";
    return `${BASE_URL}/${fileType}/${id}`;
  };

  const handleDownload = async (fileType: string, versionId: string) => {
    const setLoading =
      fileType === "pdf" ? setDownloadingPdf : setDownloadingWord;

    if (!versionId) {
      toast.error("Versjon-ID mangler. Kan ikke eksportere.");
      return;
    }

    setLoading(true);
    try {
      const url = makeUrl(fileType, versionId);

      const response = await fetch(url, {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      const objectUrl = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = objectUrl;
      link.download = `${handbook?.title || "handbook"}.${fileType === "pdf" ? "pdf" : "docx"}`;
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(objectUrl);
    } catch (error) {
      console.error("Download failed:", error);
      toast.error(`Feil ved nedlasting av ${fileType.toUpperCase()}-fil.`);
    } finally {
      setLoading(false);
    }
  };

  const versionRender = (version: CentralHandbook | Handbook) => {
    if (isCentral) {
      const centralVersion = version as CentralHandbook;
      const versionOf = (
        centralVersion as CentralHandbook & { versionOf?: boolean }
      ).versionOf;

      return versionOf ? (
        <Fragment>
          <FormattedDate
            value={centralVersion.createdDate || new Date()}
            format="yyyy.MM.dd-HH:mm"
          />
          <span style={{ marginLeft: "5px" }}>
            publisert av {centralVersion.createdBy}
          </span>
        </Fragment>
      ) : (
        <span>Upublisert versjon</span>
      );
    } else {
      return <span>Gjeldende versjon</span>;
    }
  };

  const sortedVersions = isCentral
    ? [...publishedVersions].sort(
        (v1, v2) =>
          new Date(v2.createdDate || "").getTime() -
          new Date(v1.createdDate || "").getTime()
      )
    : [];

  const latestVersion = sortedVersions.length ? sortedVersions[0] : undefined;

  const unpublishedChanges =
    isCentral && latestVersion && centralHandbook
      ? new Date(centralHandbook.updatedDate || "").getTime() >
          new Date(latestVersion.createdDate || "").getTime() ||
        handbookDescendants.some(
          (descendant) =>
            new Date(descendant.updatedDate || "").getTime() >
            new Date(latestVersion.createdDate || "").getTime()
        )
      : true;

  const versionOptions = isCentral
    ? unpublishedChanges
      ? [centralHandbook].concat(publishedVersions)
      : publishedVersions
    : localHandbook
      ? [localHandbook]
      : [];

  const handleVersionChange = (version: CentralHandbook | Handbook) => {
    setSelectedVersion(version);
  };

  return (
    <Modal isOpen={isOpen} onClose={onHide} className="central-export-modal">
      <Modal.Header>
        <Modal.Title>Eksport av {handbook?.title}</Modal.Title>
      </Modal.Header>

      <Modal.Body>
        <div style={{ height: "10em" }}>
          {isCentral && isLoadingVersions ? (
            <div>Laster versjoner...</div>
          ) : (
            <>
              {isCentral && (
                <Field>
                  <Label htmlFor="handbookVersion">
                    Velg hvilken versjon av håndboken du vil eksportere
                  </Label>
                  <Select
                    id="handbookVersion"
                    name="handbookVersion"
                    aria-label="Velg versjon av håndboken du vil eksportere"
                    options={versionOptions}
                    value={selectedVersion}
                    onChange={handleVersionChange}
                    valueKey="id"
                    labelKey="createdDate"
                    optionRenderer={versionRender}
                    valueRenderer={versionRender}
                  />
                </Field>
              )}
              {!isCentral && (
                <p style={{ marginBottom: "1rem" }}>
                  Eksporterer gjeldende versjon av den lokale håndboka.
                </p>
              )}
            </>
          )}

          {selectedVersion && (
            <Fragment>
              <p style={{ marginBottom: "5px" }}>
                Vil du eksportere dokumentet?
              </p>
              <Button
                size="small"
                onClick={() => handleDownload("pdf", selectedVersion.id || "")}
                disabled={downloadingPdf}
                icon="RegFilePdf"
              >
                {downloadingPdf ? "Laster ned..." : "Eksporter som PDF"}
              </Button>
              {isCentral && (
                <Button
                  style={{ marginLeft: "5px" }}
                  size="small"
                  onClick={() =>
                    handleDownload("word", selectedVersion.id || "")
                  }
                  disabled={downloadingWord}
                  icon="RegFileWord"
                >
                  {downloadingWord ? "Laster ned..." : "Eksporter som Word"}
                </Button>
              )}
            </Fragment>
          )}
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={onHide}>Lukk</Button>
      </Modal.Footer>
    </Modal>
  );
};
