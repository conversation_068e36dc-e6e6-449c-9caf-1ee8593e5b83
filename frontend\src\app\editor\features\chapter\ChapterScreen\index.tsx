import { useState, useCallback, useEffect } from "react";
import { <PERSON>, useParams, useNavigate } from "react-router-dom";
import { Button, Column, Columns, Group, Icon, Menu, Title } from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Spinner } from "@/shared/components/Spinner";

import {
  useGetLocalChapterQuery,
  useDeleteLocalChapterMutation,
  useSortLocalItemsMutation,
  useGetAttachmentCountQuery,
} from "@/store/services/handbook/localHandbookApi";
import {
  useLocalChapters,
  useLocalSections,
} from "@/store/services/handbook/hooks";
import { NoSelectionScreen } from "../../handbook/NoSelectionScreen";
import { LocalSortChildrenScreen as SortChildrenScreen } from "../../local/LocalSortChildrenScreen";
import { LocalDeleteButton as DeleteButton } from "../../local/LocalDeleteButton";
import { PendingChangeWarning } from "../../PendingChangeWarning";
import { LocalMetadata as Metadata } from "../../local/LocalMetadata";
import { AttachmentModal } from "../../shared/AttachmentModal";

export const ChapterScreen = () => {
  const { handbookId, chapterId } = useParams() as {
    handbookId: string;
    chapterId: string;
  };
  const t = usePrefixedTranslation("editor.containers.ChapterSelection");
  const navigate = useNavigate();

  const [isSorting, setIsSorting] = useState(false);
  const [showAttachments, setShowAttachments] = useState(false);

  const { data: chapter, isLoading: isLoadingChapter } =
    useGetLocalChapterQuery(chapterId, {
      skip: !chapterId,
    });

  const { data: allChapters = [], isLoading: isLoadingChapters } =
    useLocalChapters();

  const { data: allSections = [], isLoading: isLoadingSections } =
    useLocalSections();

  const [sortLocalItems, { isLoading: isSortingLoading }] =
    useSortLocalItemsMutation();

  const {
    data: attachmentCount = { count: 0 },
    refetch: refetchAttachmentCount,
    isLoading: isLoadingAttachmentCount,
  } = useGetAttachmentCountQuery(
    { type: "chapter", id: chapterId },
    { skip: !chapterId }
  );

  const [deleteLocalChapter] = useDeleteLocalChapterMutation();

  const childChapters = allChapters.filter(
    (ch) => ch.parentId === chapterId && ch.handbookId === handbookId
  );
  const childSections = allSections.filter(
    (section) =>
      section.parentId === chapterId && section.handbookId === handbookId
  );

  const chaptersAndSections = [
    ...childChapters.map((ch) => ({ ...ch, type: "LOCAL_CHAPTER" as const })),
    ...childSections.map((sec) => ({ ...sec, type: "LOCAL_SECTION" as const })),
  ].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const toggleSort = useCallback(() => {
    setIsSorting((prev) => !prev);
  }, []);

  useEffect(() => {
    setIsSorting(false);
  }, [chapterId]);

  const handleDeleteChapter = useCallback(async () => {
    if (!chapter?.id) return;

    try {
      await deleteLocalChapter(chapter.id).unwrap();
      toast.success(t("editor.success.chapterDeleted"));
      navigate(`/editor/${handbookId}/`);
    } catch (error) {
      console.error("Error deleting chapter:", error);
      toast.error(t("editor.error.chapterDeleteFailed"));
    }
  }, [deleteLocalChapter, chapter?.id, navigate, handbookId]);

  const handleAttachmentCountChange = useCallback(() => {
    refetchAttachmentCount();
  }, [refetchAttachmentCount]);

  const renderChildren = useCallback(() => {
    if (isSorting) {
      return (
        <SortChildrenScreen
          items={chaptersAndSections}
          onCancel={() => setIsSorting(false)}
          sortFunction={async (itemIds) => {
            try {
              await sortLocalItems(itemIds).unwrap();
              toast.success("Lagret sortering.");
              setIsSorting(false);
            } catch (error) {
              console.error("Error sorting items:", error);
              toast.error(t("editor.error.sortFailed"));
            }
          }}
          isSaving={isSortingLoading}
        />
      );
    }

    if (!chaptersAndSections.length) {
      return <div style={{ marginTop: "1rem" }}>{t("noChildren")}</div>;
    }

    return (
      <Menu>
        <Menu.List>
          {chaptersAndSections.map((item) => (
            <Menu.Item
              key={item.id}
              as={Link}
              to={`/editor/${item.handbookId}/${
                item.type === "LOCAL_CHAPTER" ? "chapter" : "section"
              }/${item.id}/`}
            >
              <Icon
                icon={
                  item.type === "LOCAL_CHAPTER" ? "RegBookmark" : "RegFileLines"
                }
                size="small"
                style={{ marginRight: "4px" }}
              />
              {item.title}
            </Menu.Item>
          ))}
        </Menu.List>
      </Menu>
    );
  }, [
    chaptersAndSections,
    isSorting,
    isSortingLoading,
    setIsSorting,
    sortLocalItems,
    t,
  ]);

  if (!chapterId) {
    return <NoSelectionScreen />;
  }

  if (isLoadingChapter || isLoadingChapters || isLoadingSections) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (!chapter) {
    if (!isLoadingChapter) {
      toast.error(t("editor.error.chapterNotFound", { id: chapterId }));
    }
    return <NoSelectionScreen />;
  }

  return (
    <>
      <Columns>
        <Column>
          <Title>
            <Icon
              icon="RegBookmark"
              size="medium"
              style={{ marginRight: "1rem" }}
            />
            <span>{chapter.title}</span>
          </Title>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <Group>
            <Button
              control
              as={Link}
              to={`/editor/${chapter.handbookId}/chapter/${chapter.id}/edit/`}
              size="small"
              icon="pencil"
            >
              {t("editButton")}
            </Button>

            <Button
              control
              as={Link}
              to={`/editor/${chapter.handbookId}/chapter/add-new?parent=${chapter.id}`}
              size="small"
              icon="plus"
            >
              {t("newChapter")}
            </Button>

            <Button
              control
              as={Link}
              to={`/editor/${chapter.handbookId}/section/add-new/?parent=${chapter.id}`}
              size="small"
              icon="plus"
            >
              {t("newSection")}
            </Button>

            <Button
              control
              onClick={() => setShowAttachments(true)}
              size="small"
              className="add-attachment-btn"
              icon="Paperclip"
            >
              {t("addAttachment")}
              {isLoadingAttachmentCount ? (
                <span style={{ marginLeft: "4px", fontSize: "12px" }}>
                  (...)
                </span>
              ) : (
                <span className="attachment-count">
                  {attachmentCount.count}
                </span>
              )}
            </Button>
          </Group>
        </Column>

        <Column narrow>
          <Group>
            <PendingChangeWarning
              element={chapter}
              mergeLink={`/merge/chapter/${chapter.id}/`}
            />

            <Button
              control
              as={Link}
              to={`/editor/${chapter.handbookId}/chapter/${chapter.id}/move/`}
              size="small"
            >
              {t("moveButton")}
            </Button>

            <Button
              control
              active={isSorting}
              disabled={chaptersAndSections.length <= 1}
              onClick={toggleSort}
              size="small"
            >
              {t("sortButton")}
            </Button>

            {!chapter.pendingDeletion && (
              <DeleteButton
                toDelete={{
                  id: chapter.id!,
                  title: chapter.title,
                  type: "LOCAL_CHAPTER",
                }}
                onDelete={handleDeleteChapter}
              />
            )}
          </Group>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <hr />
          <Metadata element={chapter} />
          <hr style={{ marginBottom: "0" }} />
        </Column>
      </Columns>

      {showAttachments && (
        <AttachmentModal
          isOpen={showAttachments}
          sectionId={chapterId}
          sectionType="CHAPTER"
          onClose={() => setShowAttachments(false)}
          onAttachmentCountChange={handleAttachmentCountChange}
        />
      )}

      {renderChildren()}
    </>
  );
};
