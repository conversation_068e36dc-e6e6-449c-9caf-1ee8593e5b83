-- holds data about the current version of the system - fields cannot be renamed due to common code
CREATE TABLE handboker_meta_data (id SMALLINT, version INTEGER NOT NULL, PRIMARY KEY (id))

--local documents
CREATE TABLE handbook(id VARCHAR(37), title VARCHAR(255) NOT NULL, importedhandbook_id VARCHAR(100), external_org_id VARCHAR(200) NOT NULL, manualmerge SMALLINT NOT NULL, pendingchanges SMALLINT NOT NULL, pending_changes_updated_date BIGINT NOT NULL, updated_date BIGINT NOT NULL, created_date BIGINT NOT NULL, updated_by <PERSON><PERSON><PERSON><PERSON>(100), created_by VA<PERSON>HA<PERSON>(100), deleted SMALLINT NOT NULL, PRIMARY KEY(id))

CREATE TABLE handbookchapter(id VARCHAR(37), title VARCHAR(255) NOT NULL, importedhandbookchapter_id VARCHAR(100), importedhandbook_id VARCHAR(100), handbook_id VARCHAR(37) NOT NULL, parent_chapter_id VARCHAR(37), orderindex SMALLINT, manualmerge SMALLINT NOT NULL, pendingchanges SMALLINT NOT NULL, pending_changes_updated_date BIGINT NOT NULL, updated_date BIGINT NOT NULL, created_date BIGINT NOT NULL, updated_by VARCHAR(100), created_by VARCHAR(100), deleted SMALLINT NOT NULL, PRIMARY KEY(id))
ALTER TABLE handbookchapter ADD CONSTRAINT fk_handbookchapter_handbook FOREIGN KEY (handbook_id) REFERENCES handbook(id)
ALTER TABLE handbookchapter ADD CONSTRAINT fk_handbookchapter_parentchapter FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)

CREATE TABLE subscription(id VARCHAR(37), emailaddress VARCHAR(100) NOT NULL, handbook_id VARCHAR(37) NOT NULL, PRIMARY KEY(id))
CREATE TABLE changenotification(id VARCHAR(37), changedescription VARCHAR(8000), handbook_id VARCHAR(37), updated BIGINT NOT NULL)

ALTER TABLE subscription ADD CONSTRAINT fk_subscription_handbook FOREIGN KEY (handbook_id) REFERENCES handbook(id)
ALTER TABLE changenotification ADD CONSTRAINT fk_changenotification_handbook FOREIGN KEY (handbook_id) REFERENCES handbook(id)
CREATE UNIQUE INDEX ux_subscription_emailaddress_handbook_id ON subscription(emailaddress, handbook_id)

CREATE TABLE centralcontentaccess(id VARCHAR(37), external_org_id VARCHAR(200) NOT NULL, importedhandbook_id VARCHAR(100) NOT NULL, PRIMARY KEY(id))
CREATE UNIQUE INDEX ux_centralcontentaccess_external_org_id_importedhandbook_id ON centralcontentaccess(external_org_id, importedhandbook_id)

CREATE TABLE centralhandbookupdated(infoserie_id VARCHAR(100), updated_date BIGINT NOT NULL, PRIMARY KEY(infoserie_id))
CREATE TABLE centralchapterupdated(infoserie_id VARCHAR(100), updated_date BIGINT NOT NULL, PRIMARY KEY(infoserie_id))
CREATE TABLE centralsectionupdated(infoserie_id VARCHAR(100), updated_date BIGINT NOT NULL, PRIMARY KEY(infoserie_id))
