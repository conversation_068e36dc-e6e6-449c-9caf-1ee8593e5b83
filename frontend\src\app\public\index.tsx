import React, { useState } from "react";
import { Routes, Route } from "react-router-dom";
import { ToastContainer } from "@/shared/components/Toast";

import { usePrefixedTranslation } from "@/libs/i18n";
import { useFetchPublicConfigQuery } from "@/store/services/handbook/publicHandbookApi";
import { NoAccessPage } from "@/shared/components/NoAccessPage";
import { NotFoundPage } from "@/shared/components/NotFoundPage";
import { InvalidLinkPage } from "@/shared/components/InvalidLinkPage";
import { OptOutModal } from "@/shared/components/OptOutModal";
import { Spinner } from "@/shared/components/Spinner";
import { RouteErrorBoundaryWrapper } from "@/shared/components/ErrorBoundary";

import { WelcomePage } from "./pages/Welcome";
import { HandbookPage } from "./pages/Handbook";
import { ReadSectionPage } from "./pages/ReadSection";
import { PublicLayout } from "./layouts/PublicLayout";
import { Footer } from "./components/Footer";

import "./styles/public.css";

export const PublicApp: React.FC = () => {
  const t = usePrefixedTranslation("public.containers.App");
  const [showOptOutModal, setShowOptOutModal] = useState(false);

  const { isLoading: isConfigLoading } = useFetchPublicConfigQuery();

  const handleOptOutModalShow = () => {
    setShowOptOutModal(true);
  };

  const handleOptOutModalHide = () => {
    setShowOptOutModal(false);
    try {
      localStorage.setItem("kf-handboker-visited", "true");
    } catch {
      // Ignore localStorage errors in private/incognito mode
    }
  };

  if (typeof document !== "undefined") {
    document.title = t("title");
  }

  if (isConfigLoading) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div
        style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
      >
        <div style={{ flex: "1 0 auto" }}>
          <Routes>
            <Route 
              path="/readinglink/:linkId" 
              element={
                <RouteErrorBoundaryWrapper routeName="ReadSection">
                  <ReadSectionPage />
                </RouteErrorBoundaryWrapper>
              } 
            />
            <Route path="/410" element={<InvalidLinkPage />} />
            <Route path="/" element={<PublicLayout />}>
              <Route 
                index 
                element={
                  <RouteErrorBoundaryWrapper routeName="Welcome">
                    <WelcomePage />
                  </RouteErrorBoundaryWrapper>
                } 
              />
              <Route 
                path=":externalOrgId/:handbookId/*" 
                element={
                  <RouteErrorBoundaryWrapper routeName="Handbook">
                    <HandbookPage />
                  </RouteErrorBoundaryWrapper>
                } 
              />
              <Route path="forbidden" element={<NoAccessPage />} />
              <Route path="invalid-link" element={<InvalidLinkPage />} />
            </Route>
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </div>

        <Footer
          application={t("application")}
          showOptOutModal={handleOptOutModalShow}
        />
      </div>

      {showOptOutModal && (
        <OptOutModal
          isOpen={showOptOutModal}
          toggleHide={handleOptOutModalHide}
        />
      )}

      <ToastContainer />
    </div>
  );
};
