# Search UX Backend: How It Works (with diagrams)

## Table of contents
- Overview
- Core concepts (plain English)
- How our handbook app uses Elasticsearch
  - Indexing pipeline
  - Mapping and analyzers
  - Search query composition
  - Highlighting for the snippet
  
  - Highlighting for “N more matches” (counting)
  - Response assembly
  - What the UI does
  - Performance guardrails
- Mermaid diagrams (copy‑paste ready)
- Glossary

---

## Overview

- Index → analyze → search → highlight
- Indexing = store docs and build a word→document lookup (“inverted index”)
- Searching = look up tokens from the user’s query and rank results
- Highlighting = return short quotes (fragments) with matched words wrapped in `<em>…</em>`
- Our UX needs:
  - Sentence‑accurate snippet
  - Highlights in titles and body
  - “N more matches”
  - “Last Edited …” and sorting

---

## Core concepts (plain English)

- Index: named collection of documents (we use one per externalOrgId)
- Document: a handbook, chapter, or section (JSON with fields)
- Field: title, body, last_modified, etc.
- Mapping: schema defining field types and analyzers
- Analyzer: transforms text into searchable tokens
- Inverted index: fast lookup from token → {documents, positions}
- Highlighting:
  - Fragment: the quote we show
  - `number_of_fragments`: how many quotes to return
  - `fragment_size`: max length of a quote
  - `boundary_scanner`: how to cut (e.g., sentences)
  - `no_match_size`: fallback quote when body didn’t match
  - `pre_tags` / `post_tags`: `<em>` and `</em>` around matches

---

## How our handbook app uses Elasticsearch

### Indexing pipeline (HTML → searchable fields)
- Strip HTML to plain text
- Compute `last_modified`
- Store fields:
  - Titles: `handbook_title`, `chapter_title`, `section_title`
  - Body (sections): `text` (snippet) and `text_full` (counting)
  - Relationships and ids
  - `last_modified` (date)

### Mapping and analyzers
- Text fields use:
  - Index analyzer: Edge N‑gram + lowercase (good partial matches)
  - Search analyzer: Standard + Norwegian stop words + lowercase
- `last_modified` as date (sortable)

### Search query composition
- `must`: `externalOrgId` (and optional `handbookId`)
- `should`: boosted title fields + body (`text`)
- Optional `sortBy=lastModified`, `order=asc|desc`
- Pagination: page size from config

### Highlighting for the snippet (what users see)
- On `section_text` (body)
- `number_of_fragments = 1` (one quote)
- `boundary_scanner = sentence` (clean sentence)
- `fragment_size ≈ 800` (enough for 3‑line clamp)
- `no_match_size ≈ 600` (top of section when body didn’t match)
- `pre_tags` / `post_tags` = `<em>` / `</em>`

### Highlighting for counting “N more matches”
- On `section_text_full` (duplicate body field)
- `number_of_fragments ≈ 100` (many small quotes)
- `fragment_size ≈ 150` (short pieces)
- `pre_tags` / `post_tags` = `<em>` / `</em>`
- Backend counts total `<em>` occurrences across these fragments → `matchCount`

### Response assembly
- SectionHit includes:
  - title highlight, `textHighlight` (snippet), `matchCount`, `lastModified`, `handbookId`
- Chapter/Handbook hits include title highlight and `lastModified`
- We exclude `text` and `text_full` from `_source` in search responses to keep payloads small

### What the UI does
- Renders title and snippet (with `<em>` highlights)
- Clamps snippet to 3 lines (CSS)
- Counts visible `<em>` in snippet
- Shows “N more matches” = `matchCount − visibleInSnippet`
- Displays “Last Edited on …”
- Sorting control calls `sortBy=lastModified&order=asc|desc`

### Performance guardrails
- Page size: 10–20
- Snippet: 1 fragment, sentence boundary, size ~800, `no_match_size` ~600
- Counting: `fragment_size` ~100–150, fragments cap ~100–200
- Exclude `text`/`text_full` from `_source` in results
- Only sort by `lastModified` when requested

---

## Mermaid diagrams (copy‑paste ready)

### 1) System overview
```mermaid
flowchart LR
  subgraph Data_Sources[Content Sources]
    H[Handbook]
    C[Chapter]
    S[Section]
  end

  subgraph Backend[Backend Services]
    IDX[Indexing Service]
    SRV[Search API]
  end

  subgraph ES[Elasticsearch]
    ESIDX[(Index per externalOrgId)]
  end

  subgraph UI[Frontend]
    UQ[User Query]
    UR[Results List]
  end

  H --> IDX
  C --> IDX
  S --> IDX
  IDX -->|index documents| ESIDX

  UQ --> SRV
  SRV -->|build query + highlighting| ESIDX
  ESIDX -->|hits + highlights| SRV
  SRV --> UR
```

### 2) Indexing pipeline
```mermaid
flowchart TD
  subgraph Indexing_Pipeline
    A[Handbook/Chapter/Section JSON]
    B[Strip HTML (sections) → plain text]
    C[Compute last_modified]
    D[Build doc:\n- titles (text)\n- text (snippet source)\n- text_full (counting source)\n- ids, last_modified]
    E[PUT/UPSERT → ES Index (per org)]
  end
  A --> B --> C --> D --> E
```

### 3) Mapping and analyzers
```mermaid
flowchart LR
  M[Mapping] -->|text fields| T1[handbook_title]
  M --> T2[chapter_title]
  M --> T3[section_title]
  M --> T4[text]
  M --> T5[text_full]
  M --> D[last_modified (date)]
  subgraph Analyzers
    IA[Index Analyzer:\n- Edge N-gram\n- Lowercase]
    SA[Search Analyzer:\n- Standard\n- Norwegian stop words\n- Lowercase]
  end
  T1 --- IA & SA
  T2 --- IA & SA
  T3 --- IA & SA
  T4 --- IA & SA
  T5 --- IA & SA
```

### 4) Search query
```mermaid
flowchart TD
  Q[Incoming query (q, page, handbookId?, sort?)] --> F[Build Bool Query]
  F -->|must| M1[match externalOrgId]
  F -->|must (optional)| M2[match handbook_id]
  F -->|should| S1[handbook_title ^2.0]
  F -->|should| S2[chapter_title ^1.5]
  F -->|should| S3[section_title ^1.25]
  F -->|should| S4[text (body)]
  F --> SORT{Sort?}
  SORT -->|lastModified| L[Sort by last_modified asc/desc]
  SORT -->|else| R[Default relevance]
```

### 5) Highlighting for the snippet
```mermaid
flowchart LR
  ESQ[ES Query] --> HL[Highlighter (text)]
  HL --> CFG[Config:\n- number_of_fragments = 1\n- boundary_scanner = sentence\n- fragment_size ≈ 800\n- no_match_size ≈ 600\n- pre_tags/post_tags = <em>...</em>]
  CFG --> SNIPPET[Snippet fragment]
```

### 6) Highlighting for counting “N more matches”
```mermaid
flowchart LR
  ESQ2[ES Query] --> HLC[Highlighter (text_full)]
  HLC --> CFG2[Config:\n- number_of_fragments ≈ 100..200\n- fragment_size ≈ 100..150\n- pre_tags/post_tags = <em>...</em>]
  CFG2 --> FRAGS[Fragments with <em> tags]
  FRAGS --> CNT[Backend counts total <em> to compute matchCount]
```

### 7) Response assembly
```mermaid
flowchart TD
  HITS[ES Hits + Highlights] --> PARSE[Parse per hit]
  PARSE --> TITLEH[Extract title highlights]
  PARSE --> BODYH[Pick first text snippet]
  PARSE --> COUNT[Count <em> across text_full fragments]
  PARSE --> META[Read last_modified]
  TITLEH --> RES[SectionHit / ChapterHit / HandbookHit]
  BODYH --> RES
  COUNT --> RES
  META --> RES
```

### 8) UI wiring
```mermaid
flowchart TD
  API[Search API Response] --> UI1[Render title (with <em>)]
  API --> UI2[Render textHighlight (with <em>), clamp to 3 lines]
  UI2 --> VIS[Count <em> in visible snippet]
  API --> MC[Read matchCount]
  VIS --> DIFF[Compute N = matchCount - visible]
  MC --> DIFF
  DIFF --> UI3[Show: "N more matches. Open section to view all results."]
  API --> DATE[Render "Last Edited on: dd.mm.yyyy"]
```

### 9) Performance guardrails
```mermaid
flowchart LR
  G1[Page size 10..20]
  G2[Snippet: 1 fragment,\nsentence boundary,\nsize ~800,\nno_match ~600]
  G3[Counting: size ~100..150,\nfragments cap ~100..200]
  G4[Exclude text/text_full from _source in results]
  G5[Sort by lastModified only when requested]
```

---

## Glossary

- Index: collection of documents
- Document: one handbook/chapter/section
- Field: a named property (title, body, last_modified)
- Mapping: field types + analyzers
- Analyzer: tokenizer + filters (lowercase, stop words, n‑grams)
- Inverted index: token → documents/positions
- Highlighting: return fragments with `<em>…</em>` around matches
- Fragment: a returned quote
- `boundary_scanner`: sentence/word boundaries for fragments
- `no_match_size`: return top N chars when body didn’t match
- Doc values: column store for sorting/aggregations
- Boost: make some fields weigh more in ranking

