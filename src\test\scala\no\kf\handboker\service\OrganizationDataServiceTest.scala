package no.kf.handboker.service

import no.kf.handboker.DefaultTestDI
import no.kf.handboker.model.central.CentralSection
import no.kf.handboker.model.local.{Handbook, Section}
import no.kf.handboker.repository.DbTestHandler
import org.junit.runner.RunWith
import org.mockito.Matchers.{any, anyString, matches, eq => eqs}
import org.mockito.Mockito._
import org.scalatest.junit.JUnitRunner
import org.scalatest.mockito.MockitoSugar
import org.scalatest.{BeforeAndAfterEach, FunSuite}

import scala.xml._
import scala.xml.transform._

@RunWith(classOf[JUnitRunner])
class OrganizationDataServiceTest extends FunSuite with DefaultTestDI with MockitoSugar with BeforeAndAfterEach with DbTestHandler {

  //Todo: Assess whether handbook_local_editor needs to be exported
  private val tablesNotToExport = List(
    "central_handbooks.section",
    "central_handbooks.chapter",
    "central_handbooks.handbook",
    "central_handbooks.instance",
    "central_handbooks.handbook_structure",
    "central_handbooks.publication",
    "reading_link",
    "handbook_local_editor",
    "file_link"
  )
  private val tablesToExport = connectionManager.getUserDefinedTableNames(excludedTableNames = tablesNotToExport)
  private val tablesNotToDeleteFrom = connectionManager.metaDataTable :: tablesNotToExport
  private val tablesToDeleteFrom = connectionManager.getUserDefinedTableNames(excludedTableNames = tablesNotToDeleteFrom)
  private val repo = componentRegistry.exportAndDeleteRepository
  private val service: OrganizationDataService = componentRegistry.organizationDataService
  private val localHandbookService = componentRegistry.localHandbookService
  private val centralHandbookService = componentRegistry.centralHandbookService

  test("That we try to retrieve TSV of all the user created tables in the DB, with columns belonging to those tables") {
    when(repo.retrieveTSVOfTable(anyString())).thenReturn(Stream("something"))
    when(repo.retrieveTSVOfTableWhereFieldIn(anyString(), anyString(), any[List[String]])).thenReturn(Stream("something"))
    when(repo.retrieveColumnDataFromTableWhereFieldIn(anyString(), anyString(), anyString(), any[List[String]])).thenReturn(Nil)

    service.retrieveDataForExternalOrg("9900")

    tablesToExport.foreach(tableName => {
      val columnNames = connectionManager.getColumnNamesForTable(tableName)
      val columnPattern = columnNames.mkString("^", "$|^", "$")
      verify(repo, times(1)).retrieveTSVOfTableWhereFieldIn(eqs(tableName), matches(columnPattern), any[List[String]])
    })
    tablesNotToExport.foreach(tableName =>
      verify(repo, times(0)).retrieveTSVOfTableWhereFieldIn(eqs(tableName), anyString(), any[List[String]]())
    )
    assert(tablesToExport.length !== 0)
  }

  test("That we return all the TSVs and uses correct column names") {
    when(repo.retrieveColumnDataFromTableWhereFieldIn(anyString(), anyString(), anyString(), any[List[String]]())).thenReturn(Nil)
    tablesToExport.foreach(tableName => when(repo.retrieveTSVOfTableWhereFieldIn(eqs(tableName), anyString(), any[List[String]]())).thenReturn(Stream(tableName)))

    val tsv = service.retrieveDataForExternalOrg("9900")

    tablesToExport.foreach(tableName => assert(tsv.contains(tableName)))
    assert(tsv.length === tablesToExport.length)
  }

  test("That we delete organization data from all relevant tables") {
    when(repo.retrieveColumnDataFromTableWhereFieldIn(anyString(), anyString(), anyString(), any[List[String]]())).thenReturn(Nil)

    service.deleteDataForExternalOrg("9900")

    tablesToDeleteFrom.foreach(tableName => {
      val columnNames = connectionManager.getColumnNamesForTable(tableName)
      val columnPattern = columnNames.mkString("^", "$|^", "$")
      verify(repo, times(1)).deleteFromTableWhereFieldIn(eqs(tableName), matches(columnPattern), any[List[String]]())
    })

    tablesNotToDeleteFrom.foreach(tableName =>
      verify(repo, times(0)).deleteFromTableWhereFieldIn(eqs(tableName), anyString(), any[List[String]]())
    )

    assert(tablesToDeleteFrom.length !== 0)
  }

  test("That links are converted") {

    val htmlLocalSection1 = "<p>Section with one link for testing <abbr></abbr> <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" >https://www.vg.no/</a> links to vg, with no target or rel</p>"
    val htmlLocalSection2 = "<p><a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" target=\"_parent\" rel=\"noreffer\">test with target and rel</a> and <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" rel=\"noopener\">test only rel</a></p>"
    val hmtlCentralSection1 = htmlLocalSection2
    val hmtlCentralSection2 = "<p>test <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" target=\"_blank\" rel=\"noopener\">https://www.vg.no/</a></p>"
    val hmtlCentralSection3 = "<p>test <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\">https://www.vg.no/</a></p>"
    val extOrg = "9900"
    val localHandbookId1 = Some("localHandbook1")
    val localHandbookId2 = Some("localHandbook2")
    val centralSectionId1 = Some("centralSection1")
    val centralSectionId2 = Some("centralSection1")
    val centralSectionId3 = Some("centralSection1")


    val localHandbook1 = Handbook(localHandbookId1, "localHandbook1", None, extOrg)
    val localHandbook2 = Handbook(localHandbookId2, "localHandbook1", None, extOrg)
//    val centralHandbook = centralHandbook(localHandbookId2, "localHandbook1", None, extOrg)
    val centralSection1 = CentralSection(centralSectionId1, "centralSection1", "centralChapter1", "centralHandbook1", Some(hmtlCentralSection1))
    val centralSection2 = CentralSection(centralSectionId2, "centralSection2", "centralChapter2", "centralHandbook2", Some(hmtlCentralSection2))
    val centralSection3 = CentralSection(centralSectionId3, "centralSection3", "centralChapter3", "centralHandbook3", Some(hmtlCentralSection3))
    val localSection1 = Section(localHandbookId1, "localSection1", Some(htmlLocalSection1), None, None, localHandbook1.id.get, "localChapter1", None)
    val localSection2 = Section(localHandbookId2, "localSection2", Some(htmlLocalSection2), None, None, localHandbook2.id.get, "localChapter2", None)

    val centralSections = List(centralSection1, centralSection2, centralSection3)
    val listHandbooks = List(localHandbook1, localHandbook2)

    when(localHandbookService.retrieveAllExternalOrgIds()).thenReturn(List("9900"))
    when(localHandbookService.retrieveHandbooksForExternalOrganization("9900")).thenReturn(listHandbooks)
    when(localHandbookService.retrieveSectionsForHandbook(localHandbook1.id.get)).thenReturn(List(localSection1))
    when(localHandbookService.retrieveSectionsForHandbook(localHandbook2.id.get)).thenReturn(List(localSection2))
    when(centralHandbookService.retrieveAllCentralSections()).thenReturn(centralSections)

    val convertedLocalSection1 =  service.replaceLinksInSection(localSection1.text)
    val convertedLocalSection2 =  service.replaceLinksInSection(localSection2.text)

    val convertedLocalSection1ShouldBecome = "<p>Section with one link for testing <abbr></abbr> <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" >https://www.vg.no/</a> links to vg, with no target or rel</p>"
    val convertedLocalSection2ShouldBecome = "<p><a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" target=\"_blank\" rel=\"noopener\">test with target and rel</a> and <a target=\"_blank\" title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" rel=\"noopener\">test only rel</a></p>"

    val convertedCentralSection1ShouldBecome = convertedLocalSection2ShouldBecome
    val convertedCentralSection2ShouldBecome = "<p>test <a title=\"https://www.vg.no/\" href=\"https://www.vg.no/\" target=\"_blank\" rel=\"noopener\">https://www.vg.no/</a></p>"
    val convertedCentralSection3ShouldBecome = "<p>test <a rel=\"noopener\" target=\"_blank\" title=\"https://www.vg.no/\" href=\"https://www.vg.no/\">https://www.vg.no/</a></p>"

    val convertedCentralSection1 =  service.replaceLinksInSection(centralSection1.html)
    val convertedCentralSection2 =  service.replaceLinksInSection(centralSection2.html)
    val convertedCentralSection3 =  service.replaceLinksInSection(centralSection3.html)

    assert(convertedLocalSection1.get == convertedLocalSection1ShouldBecome)
    assert(convertedLocalSection2.get == convertedLocalSection2ShouldBecome)
    assert(convertedCentralSection1.get == convertedCentralSection1ShouldBecome)
    assert(convertedCentralSection2.get == convertedCentralSection2ShouldBecome)
    assert(convertedCentralSection3.get == convertedCentralSection3ShouldBecome)

    service.replaceTargetForLinksInHandbooks("birger-id")

    verify(centralHandbookService, times(2)).persistCentralSection(any(classOf[CentralSection]), anyString())
  }
}
