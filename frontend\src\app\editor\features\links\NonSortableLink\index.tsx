import { <PERSON>, Field } from "kf-bui";
import type { <PERSON> } from "@/types";
import { LinkComponent } from "../Link";

interface NonSortableLinkProps {
  link: Link;
  onSave: (link: Link) => void;
  onDelete: (linkId: string) => void;
}

export const NonSortableLink = ({
  link,
  onSave,
  onDelete,
}: NonSortableLinkProps) => {
  return (
    <Card style={{ marginBottom: "0.5rem" }}>
      <Card.Content as={Field} style={{ padding: "1rem", display: "flex" }}>
        <LinkComponent
          link={link}
          onSave={onSave}
          onDelete={() => onDelete(link.id!)}
          showActions={true}
        />
      </Card.Content>
    </Card>
  );
};
