package no.kf.handboker.repository

import no.kf.db.IDGenerator
import no.kf.db.RichSQL._
import no.kf.handboker.model.User
import no.kf.handboker.model.local.ChangeNotification
import no.kf.handboker.model.local.Handbook
import no.kf.handboker.model.central.CentralHandbook
import no.kf.util.Logging
import org.joda.time.DateTime

trait SubscriptionRepositoryComponent {
  this: DbConnectionManagerComponent
    with CentralHandbookRepositoryComponent
    with HandbookRepositoryComponent =>

  val subscriptionRepository: SubscriptionRepository

  object SubscriptionTableDef {
    val subscriptionTableName = "subscription"

    val fieldId = "id"
    val fieldEmailAddress = "emailaddress"
    val fieldHandbookId = "handbook_id"
  }

  object ChangeNotificationTableDef {
    val changeNoteTableName = "changenotification"

    val fieldId = "id"
    val fieldChangeDescription = "changedescription"
    val fieldHandbookId = "handbook_id"
    val fieldDate = "updated"
    val fieldCentralHandbookId = "central_handbook_id"
    val fieldChangePublished = "central_change_published"
  }


  class SubscriptionRepositoryImpl extends SubscriptionRepository with Logging {

    import ChangeNotificationTableDef._

    override def persistChangeNotification(changeDescription: String, handbook: Handbook, centralHandbook: CentralHandbook): ChangeNotification = {
      log.info(s"########Going to start persist change notification in local handbook title : ${handbook.title} AND  local handbook ID : ${handbook.id.get} #########")

      def retrieveChangeNotificationForHandbookWithDescription(changeDescription: String, handbook: Handbook, centralHandbook: CentralHandbook): Option[ChangeNotification] = {
        val select = s"select top 1 * from $changeNoteTableName where $fieldChangePublished=1 and $fieldChangeDescription = ? and $fieldHandbookId = ? and $fieldCentralHandbookId = ?"
        connectionManager.doWithConnection {
          _.ps(select) << changeDescription << handbook.id.get << centralHandbook.id.get <<# populateChangeNotification
        }
      }

      def insertChangeNotification(changeDescription: String, handbook: Handbook, centralHandbook: CentralHandbook): ChangeNotification = {
        val sql = s"insert into $changeNoteTableName ($fieldId, $fieldChangeDescription, $fieldHandbookId, $fieldDate, $fieldCentralHandbookId, $fieldChangePublished) values (?, ?, ?, ?, ?, ?)"
        val newId = IDGenerator.generateUniqueId
        val now = new DateTime
        connectionManager.doWithConnection {
          _.ps(sql) <<
            newId <<
            changeDescription <<
            handbook.id.get <<
            now <<
            centralHandbook.id.get <<
            false <<!
        }
        log.info(s"########Inserted change notification in local handbook title : ${handbook.title}  AND  local handbook ID : ${handbook.id.get} #########")
        ChangeNotification(Some(newId), changeDescription, handbook, now, centralHandbook, false)
      }

      def updateChangeNotification(changeNotification: ChangeNotification): ChangeNotification = {
        val sql = s"update $changeNoteTableName set $fieldDate = ? where $fieldId = ?"
        val now = new DateTime
        connectionManager.doWithConnection {
          _.ps(sql) <<
            now <<
            changeNotification.id.get <<!
        }
        changeNotification.copy(changedDate = now)
      }

      retrieveChangeNotificationForHandbookWithDescription(changeDescription, handbook, centralHandbook).getOrElse(insertChangeNotification(changeDescription, handbook, centralHandbook))
    }

    override def retrieveAndDeleteAllChangeNotifications: List[ChangeNotification] = {
      val changeNotifications = retrieveAllChangeNotifications
      deleteAllChangeNotifications
      changeNotifications
    }

    def retrieveAllChangeNotifications: List[ChangeNotification] = {
      val select = s"select * from $changeNoteTableName where $fieldChangePublished = 1"
      connectionManager.doWithConnection {
        _.ps(select) <<! populateChangeNotification toList
      }
    }

    def deleteAllChangeNotifications = {
      val sql = s"delete from $changeNoteTableName where $fieldChangePublished = 1"
      connectionManager.doWithConnection {
        _.ps(sql) <<!
      }
    }


    override def persistSubscription(emailAddress: EmailAddress, handbook: Handbook) = {
      import SubscriptionTableDef._
      val sql = s"insert into $subscriptionTableName ($fieldId, $fieldEmailAddress, $fieldHandbookId) values (?, ?, ?)"

      val newId = IDGenerator.generateUniqueId
      connectionManager.doWithConnection {
        _.ps(sql) <<
          newId <<
          emailAddress <<
          handbook.id.get <<!
      }
    }

    override def deleteSubscription(emailAddress: EmailAddress, handbook: Handbook) = {
      import SubscriptionTableDef._
      val sql = s"delete from $subscriptionTableName where $fieldEmailAddress = ? and $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) <<
          emailAddress <<
          handbook.id.get <<!
      }
    }

    override def deleteSubscription(emailAddress: EmailAddress) = {
      import SubscriptionTableDef._
      val sql = s"delete from $subscriptionTableName where $fieldEmailAddress = ?"
      connectionManager.doWithConnection {
        _.ps(sql) <<
          emailAddress <<!
      }
    }


    override def retrieveSubscriptionsForHandbook(handbookId: String): List[EmailAddress] = {
      import SubscriptionTableDef._
      val sql = s"select $fieldEmailAddress from $subscriptionTableName where $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) <<
          handbookId <<! populateString toList
      }
    }

    override def retrieveSubscriptions(): List[EmailAddress] = {
      import SubscriptionTableDef._
      val sql = s"select distinct $fieldEmailAddress from $subscriptionTableName"
      connectionManager.doWithConnection {
        _.ps(sql) <<! populateString toList
      }
    }

    override def retrieveSubscriptionsForUser(emailAddress: EmailAddress): List[String] = {
      import SubscriptionTableDef._
      val sql = s"select $fieldHandbookId from $subscriptionTableName where $fieldEmailAddress = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << emailAddress <<! populateString toList
      }
    }

    override def unsubscribeAllForHandbook(handbookId: String) = {
      import SubscriptionTableDef._
      val sql = s"DELETE FROM $subscriptionTableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << handbookId <<!
      }
    }

    override def updateChangeNotificationAsCentralPublished(centralHandbookId: String) = {
      val sql = s"update $changeNoteTableName set $fieldChangePublished = 1, $fieldDate = ? where $fieldCentralHandbookId = ?"
      val now = new DateTime
      connectionManager.doWithConnection {
        _.ps(sql) <<
          now <<
          centralHandbookId <<!
      }
    }

    private def populateString(rs: RichResultSet): String = rs

    private def populateChangeNotification(rs: RichResultSet): ChangeNotification = ChangeNotification(rs, rs, handbookRepository.retrieveHandbook(rs).get, rs, centralHandbookRepository.retrieveCentralHandbook(rs).get, rs)
  }
}

trait SubscriptionRepository {
  type EmailAddress = String

  def persistChangeNotification(changeDescription: String, handbook: Handbook, centralHandbook: CentralHandbook): ChangeNotification

  def retrieveAndDeleteAllChangeNotifications: List[ChangeNotification]

  def persistSubscription(emailAddress: EmailAddress, handbook: Handbook)

  def deleteSubscription(emailAddress: EmailAddress, handbook: Handbook)

  def deleteSubscription(emailAddress: EmailAddress)

  def retrieveSubscriptionsForHandbook(handbookId: String): List[EmailAddress]

  def retrieveSubscriptionsForUser(emailAddress: EmailAddress): List[String]

  def unsubscribeAllForHandbook(handbookId: String)

  def updateChangeNotificationAsCentralPublished(centralHandbookId: String)

  def retrieveSubscriptions(): List[EmailAddress]
}
