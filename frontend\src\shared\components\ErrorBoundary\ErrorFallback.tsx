import React from "react";
import styled from "styled-components";
import { Section, Container, Title, Subtitle, Button } from "kf-bui";
import type { ErrorFallbackProps } from "@/shared/types/errorBoundary";
import type { BaseErrorFallbackProps } from "@/shared/types/errorBoundary";

const ErrorContainer = styled.div`
  padding: 2rem;
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`;

const ErrorContent = styled.div`
  max-width: 600px;
  width: 100%;
`;

const ErrorDetails = styled.details`
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  text-align: left;

  summary {
    font-weight: 600;
    cursor: pointer;
    margin-bottom: 0.5rem;
    user-select: none;
  }

  pre {
    font-size: 0.875rem;
    white-space: pre-wrap;
    word-break: break-word;
    margin: 0.5rem 0 0 0;
    color: #6c757d;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1.5rem;
  flex-wrap: wrap;
`;

export const ErrorFallback: React.FC<
  ErrorFallbackProps & BaseErrorFallbackProps
> = ({
  error,
  errorInfo,
  resetError,
  retry,
  title = "Noe gikk galt",
  message = "Det oppstod en uventet feil. Prøv å laste siden på nytt.",
  showRetry = true,
  showReset = true,
  showDetails = process.env.NODE_ENV === "development",
  actions = [],
}) => {
  const defaultActions = [
    ...(showReset
      ? [
          {
            label: "Tilbakestill",
            onClick: resetError,
            color: "white" as const,
          },
        ]
      : []),
    ...(showRetry
      ? [
          {
            label: "Last inn på nytt",
            onClick: retry || (() => window.location.reload()),
            color: "primary" as const,
          },
        ]
      : []),
    ...actions,
  ];

  return (
    <ErrorContainer>
      <ErrorContent>
        <Title level={2}>{title}</Title>
        <Subtitle>{message}</Subtitle>

        {defaultActions.length > 0 && (
          <ActionButtons>
            {defaultActions.map((action, index) => (
              <Button
                key={index}
                color={action.color || "white"}
                onClick={action.onClick}
              >
                {action.label}
              </Button>
            ))}
          </ActionButtons>
        )}

        {showDetails && (
          <ErrorDetails>
            <summary>Tekniske detaljer (kun for utvikling)</summary>
            <div>
              <strong>Feilmelding:</strong>
              <pre>{error.message}</pre>
            </div>
            {error.stack && (
              <div>
                <strong>Stack trace:</strong>
                <pre>{error.stack}</pre>
              </div>
            )}
            {errorInfo.componentStack && (
              <div>
                <strong>Component stack:</strong>
                <pre>{errorInfo.componentStack}</pre>
              </div>
            )}
          </ErrorDetails>
        )}
      </ErrorContent>
    </ErrorContainer>
  );
};

export const AppErrorFallback: React.FC<ErrorFallbackProps> = (props) => (
  <Section>
    <Container>
      <ErrorFallback
        {...props}
        title="Applikasjonen har krasjet"
        message="Det oppstod en kritisk feil som førte til at applikasjonen krasjet. Siden lastes inn på nytt automatisk."
        showReset={false}
        showRetry={true}
      />
    </Container>
  </Section>
);

export const RouteErrorFallback: React.FC<ErrorFallbackProps> = (props) => (
  <ErrorFallback
    {...props}
    title="Siden kunne ikke lastes"
    message="Det oppstod en feil under lasting av denne siden. Prøv å gå tilbake og prøv igjen."
    actions={[
      {
        label: "Gå tilbake",
        onClick: () => window.history.back(),
        color: "white",
      },
    ]}
  />
);

export const FeatureErrorFallback: React.FC<
  ErrorFallbackProps & { featureName?: string }
> = ({ featureName = "denne funksjonen", ...props }) => (
  <ErrorFallback
    {...props}
    title={`Problem med ${featureName}`}
    message={`Det oppstod en feil i ${featureName}. Prøv å laste siden på nytt.`}
    showDetails={false}
  />
);

export const FormErrorFallback: React.FC<ErrorFallbackProps> = (props) => (
  <ErrorFallback
    {...props}
    title="Skjemafeil"
    message="Det oppstod en feil i skjemaet. Prøv å fylle ut på nytt."
    showRetry={false}
    showDetails={false}
  />
);
