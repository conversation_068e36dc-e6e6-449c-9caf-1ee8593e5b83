import { useState, useEffect, useCallback, useRef } from "react";

interface UseIdleTimeoutOptions {
  timeout?: number; // Idle timeout in milliseconds (default: 30 minutes)
  warningTime?: number; // Warning time before logout in milliseconds (default: 5 minutes)
  onIdle?: () => void;
  onActive?: () => void;
  onTimeout?: () => void;
  events?: string[]; // Events to listen for user activity
}

interface UseIdleTimeoutReturn {
  isIdle: boolean;
  remainingTime: number;
  reset: () => void;
  pause: () => void;
  resume: () => void;
}

const DEFAULT_EVENTS = [
  "mousedown",
  "mousemove",
  "keypress",
  "scroll",
  "touchstart",
  "click",
];

export function useIdleTimeout({
  timeout = 30 * 60 * 1000, // 30 minutes
  warningTime = 5 * 60 * 1000, // 5 minutes
  onIdle,
  onActive,
  onTimeout,
  events = DEFAULT_EVENTS,
}: UseIdleTimeoutOptions = {}): UseIdleTimeoutReturn {
  const [isIdle, setIsIdle] = useState(false);
  const [remainingTime, setRemainingTime] = useState(warningTime / 1000);
  const [isPaused, setIsPaused] = useState(false);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const lastActiveRef = useRef<number>(Date.now());

  const reset = useCallback(() => {
    lastActiveRef.current = Date.now();
    
    // Clear existing timeouts
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
    if (countdownRef.current) clearInterval(countdownRef.current);

    // If was idle, call onActive
    if (isIdle && onActive) {
      onActive();
    }

    setIsIdle(false);
    setRemainingTime(warningTime / 1000);

    if (!isPaused) {
      // Set warning timeout (idle - warningTime)
      warningTimeoutRef.current = setTimeout(() => {
        setIsIdle(true);
        setRemainingTime(warningTime / 1000);
        
        if (onIdle) {
          onIdle();
        }

        // Start countdown
        let remaining = warningTime / 1000;
        countdownRef.current = setInterval(() => {
          remaining -= 1;
          setRemainingTime(remaining);
          
          if (remaining <= 0) {
            if (countdownRef.current) clearInterval(countdownRef.current);
            if (onTimeout) {
              onTimeout();
            }
          }
        }, 1000);
      }, timeout - warningTime);
    }
  }, [timeout, warningTime, isIdle, isPaused, onIdle, onActive, onTimeout]);

  const pause = useCallback(() => {
    setIsPaused(true);
    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
    if (countdownRef.current) clearInterval(countdownRef.current);
  }, []);

  const resume = useCallback(() => {
    setIsPaused(false);
    reset();
  }, [reset]);

  // Activity handler
  const handleActivity = useCallback(() => {
    if (!isPaused && isIdle) {
      // User became active while idle, reset everything
      reset();
    } else if (!isPaused && !isIdle) {
      // User is active, just update last active time and reset timers
      reset();
    }
  }, [isPaused, isIdle, reset]);

  // Set up event listeners
  useEffect(() => {
    if (typeof window === "undefined") return;

    events.forEach((event) => {
      document.addEventListener(event, handleActivity, true);
    });

    // Initialize timeout
    reset();

    return () => {
      events.forEach((event) => {
        document.removeEventListener(event, handleActivity, true);
      });
      
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
      if (countdownRef.current) clearInterval(countdownRef.current);
    };
  }, [events, handleActivity, reset]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
      if (countdownRef.current) clearInterval(countdownRef.current);
    };
  }, []);

  return {
    isIdle,
    remainingTime: Math.max(0, Math.floor(remainingTime)),
    reset,
    pause,
    resume,
  };
}