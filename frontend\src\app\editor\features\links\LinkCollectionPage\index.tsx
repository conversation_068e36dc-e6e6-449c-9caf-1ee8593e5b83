import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Title,
  Icon,
  Card,
  Field,
  Label,
  Input,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { Spinner } from "@/shared/components/Spinner";

import {
  useGetLinkCollectionsQuery,
  useSaveLinkCollectionMutation,
  useDeleteLinkCollectionMutation,
} from "@/store/services/handbook/localHandbookApi";

export const LinkCollectionPage: React.FC = () => {
  const { handbookId } = useParams<{ handbookId: string }>();

  const [newCollection, setNewCollection] = useState({
    title: "",
  });
  const [isCreating, setIsCreating] = useState(false);

  const {
    data: linkCollections = [],
    error,
    isLoading,
  } = useGetLinkCollectionsQuery(handbookId!, {
    skip: !handbookId,
  });

  const [saveLinkCollection] = useSaveLinkCollectionMutation();
  const [deleteLinkCollection] = useDeleteLinkCollectionMutation();

  useEffect(() => {
    if (error) {
      console.error("Error loading link collections:", error);
      toast.error("Feil ved lasting av lenkesamlinger");
    }
  }, [error]);

  const handleCreateCollection = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newCollection.title.trim() || !handbookId) return;

    try {
      await saveLinkCollection({
        title: newCollection.title.trim(),
        handbookId,
        links: [],
        sortOrder: 0,
      }).unwrap();

      setNewCollection({ title: "" });
      setIsCreating(false);
      toast.success("Lenkesamling opprettet");
    } catch (error) {
      console.error("Error creating link collection:", error);
      toast.error("Feil ved opprettelse av lenkesamling");
    }
  };

  const handleDeleteCollection = async (collectionId: string) => {
    if (
      !window.confirm("Er du sikker på at du vil slette denne lenkesamlingen?")
    ) {
      return;
    }

    try {
      await deleteLinkCollection(collectionId).unwrap();
      toast.success("Lenkesamling slettet");
    } catch (error) {
      console.error("Error deleting link collection:", error);
      toast.error("Feil ved sletting av lenkesamling");
    }
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  return (
    <>
      <Columns>
        <Column>
          <Title>
            <Icon icon="link" size="small" style={{ marginRight: "1rem" }} />
            Lenkesamlinger
          </Title>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <Button
            as={Link}
            to={`/central-editor/${handbookId}`}
            size="small"
            style={{ marginBottom: "1rem" }}
          >
            <Icon icon="arrow-left" size="small" />
            Tilbake til håndbok
          </Button>

          <Button
            control
            color="primary"
            size="small"
            onClick={() => setIsCreating(true)}
            style={{ marginBottom: "1rem", marginLeft: "0.5rem" }}
            icon="plus"
          >
            Ny lenkesamling
          </Button>
        </Column>
      </Columns>

      {isCreating && (
        <Card style={{ marginBottom: "1rem" }}>
          <Card.Header title="Opprett ny lenkesamling" />
          <Card.Content>
            <form onSubmit={handleCreateCollection}>
              <Field>
                <Label htmlFor="title">Tittel</Label>
                <Input
                  id="title"
                  value={newCollection.title}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    setNewCollection({
                      ...newCollection,
                      title: e.target.value,
                    })
                  }
                  placeholder="Tittel på lenkesamling"
                  required
                />
              </Field>
              <Button type="submit" color="primary" size="small">
                Opprett
              </Button>
              <Button
                type="button"
                size="small"
                onClick={() => {
                  setIsCreating(false);
                  setNewCollection({ title: "" });
                }}
                style={{ marginLeft: "0.5rem" }}
              >
                Avbryt
              </Button>
            </form>
          </Card.Content>
        </Card>
      )}

      {linkCollections.length === 0 ? (
        <div style={{ textAlign: "center", padding: "2rem" }}>
          <Icon icon="link" size="large" style={{ opacity: 0.3 }} />
          <p>Ingen lenkesamlinger funnet</p>
        </div>
      ) : (
        <div>
          {linkCollections.map((collection) => (
            <Card key={collection.id} style={{ marginBottom: "1rem" }}>
              <Card.Header title={collection.title} />
              <Card.Content>
                {collection.title && <p>{collection.title}</p>}
                <p>
                  <strong>Antall lenker:</strong>{" "}
                  {collection.links?.length || 0}
                </p>
              </Card.Content>
              <Card.Footer>
                <Card.Footer.Item
                  as="a"
                  role="button"
                  onClick={() => {
                    toast.info("Redigering av lenker kommer snart");
                  }}
                >
                  <Icon icon="edit" />
                  Rediger lenker
                </Card.Footer.Item>
                <Card.Footer.Item
                  as="a"
                  role="button"
                  onClick={() => handleDeleteCollection(collection.id!)}
                >
                  <Icon icon="trash" />
                  Slett
                </Card.Footer.Item>
              </Card.Footer>
            </Card>
          ))}
        </div>
      )}
    </>
  );
};
