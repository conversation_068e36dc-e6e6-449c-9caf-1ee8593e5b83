package no.kf.handboker.model.local

import org.joda.time.DateTime

case class Section(
                    id: Option[String],
                    title: String,
                    text: Option[String], //Optional for frontend
                    importedHandbookSectionId: Option[String],
                    importedHandbookId: Option[String],
                    handbookId: String,
                    parentId: String,
                    sortOrder: Option[Int],
                    localTitleChange: Boolean = false,
                    pendingTitleChange: Boolean = false,
                    localTextChange: Boolean = false,
                    pendingTextChange: Boolean = false,
                    pendingDeletion: Boolean = false,
                    pendingChangeUpdatedDate: Option[DateTime] = None,
                    updatedDate: Option[DateTime] = None,
                    textUpdatedDate: Option[DateTime] = None,
                    createdDate: Option[DateTime] = None,
                    updatedBy: Option[String] = None,
                    textUpdatedBy: Option[String] = None,
                    createdBy: Option[String] = None,
                    isDeleted: Option[Boolean] = None,
                    deletedDate: Option[DateTime] = None,
                    versionOf: Option[String] = None) {

  def isBasedOnCentralContent: Boolean = importedHandbookId.isDefined && importedHandbookSectionId.isDefined
}
