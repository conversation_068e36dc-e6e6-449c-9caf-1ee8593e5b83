import { toast } from "@/shared/components/Toast";

export interface ErrorContext {
  userId?: string;
  organizationId?: string;
  userAgent?: string;
  url?: string;
  timestamp?: Date;
  sessionId?: string;
  componentStack?: string;
  userActions?: UserAction[];
  component?: string;
  boundaryLevel?: string;
}

export interface UserAction {
  type: string;
  timestamp: Date;
  target?: string;
  data?: any;
}

export interface ErrorReport {
  error: Error;
  errorInfo?: React.ErrorInfo;
  context: ErrorContext;
  level: "low" | "medium" | "high" | "critical";
  category: "ui" | "api" | "auth" | "data" | "network" | "unknown";
}

class ErrorReportingService {
  private userActions: UserAction[] = [];
  private maxUserActions = 50;
  private isEnabled = true;

  constructor() {
    this.setupGlobalErrorHandling();
    this.setupUserActionTracking();
  }

  private setupGlobalErrorHandling() {
    window.addEventListener("unhandledrejection", (event) => {
      this.reportError(
        new Error(`Unhandled Promise Rejection: ${event.reason}`),
        undefined,
        {
          level: "high",
          category: "unknown",
        }
      );
    });

    window.addEventListener("error", (event) => {
      this.reportError(
        new Error(
          `Global Error: ${event.message} at ${event.filename}:${event.lineno}:${event.colno}`
        ),
        undefined,
        {
          level: "high",
          category: "ui",
        }
      );
    });
  }

  private setupUserActionTracking() {
    ["click", "keydown", "submit"].forEach((eventType) => {
      document.addEventListener(
        eventType,
        (event) => {
          this.addUserAction({
            type: eventType,
            timestamp: new Date(),
            target: (event.target as Element)?.tagName || "unknown",
            data: {
              key: (event as KeyboardEvent).key,
              button: (event as MouseEvent).button,
            },
          });
        },
        { passive: true }
      );
    });
  }

  private addUserAction(action: UserAction) {
    this.userActions.push(action);
    if (this.userActions.length > this.maxUserActions) {
      this.userActions.shift();
    }
  }

  private getErrorContext(): ErrorContext {
    return {
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date(),
      userActions: [...this.userActions],
    };
  }

  private getErrorCategory(error: Error): ErrorReport["category"] {
    const message = error.message.toLowerCase();

    if (message.includes("network") || message.includes("fetch")) {
      return "network";
    }
    if (
      message.includes("auth") ||
      message.includes("unauthorized") ||
      message.includes("403") ||
      message.includes("401")
    ) {
      return "auth";
    }
    if (message.includes("api") || message.includes("http")) {
      return "api";
    }
    if (
      message.includes("data") ||
      message.includes("parse") ||
      message.includes("json")
    ) {
      return "data";
    }

    return "ui";
  }

  private getErrorLevel(
    error: Error,
    category: ErrorReport["category"]
  ): ErrorReport["level"] {
    if (category === "auth") return "critical";
    if (category === "network" && error.message.includes("500")) return "high";
    if (category === "data") return "medium";

    return "low";
  }

  public reportError(
    error: Error,
    errorInfo?: React.ErrorInfo,
    options?: {
      level?: ErrorReport["level"];
      category?: ErrorReport["category"];
      context?: Partial<ErrorContext>;
      showToast?: boolean;
      customMessage?: string;
    }
  ): void {
    if (!this.isEnabled) return;

    const category = options?.category || this.getErrorCategory(error);
    const level = options?.level || this.getErrorLevel(error, category);

    const errorReport: ErrorReport = {
      error,
      errorInfo,
      context: {
        ...this.getErrorContext(),
        ...options?.context,
      },
      level,
      category,
    };

    if (process.env.NODE_ENV === "development") {
      console.group(`🚨 Error Report [${level.toUpperCase()}] - ${category}`);
      console.error("Error:", error);
      if (errorInfo) {
        console.error("Component Stack:", errorInfo.componentStack);
      }
      console.log("Context:", errorReport.context);
      console.groupEnd();
    }

    if (options?.showToast !== false) {
      const message =
        options?.customMessage ||
        this.getUserFriendlyMessage(error, category, level);

      switch (level) {
        case "critical":
          toast.error(message, { autoClose: false });
          break;
        case "high":
          toast.error(message, { autoClose: 10000 });
          break;
        case "medium":
          toast.warning(message);
          break;
        default:
          toast.info(message);
      }
    }
  }

  private getUserFriendlyMessage(
    _error: Error,
    category: ErrorReport["category"],
    level: ErrorReport["level"]
  ): string {
    switch (category) {
      case "auth":
        return "Din økt har utløpt. Vennligst logg inn på nytt.";
      case "network":
        return level === "high"
          ? "Serverfeil oppstod. Prøv igjen senere."
          : "Nettverksfeil. Sjekk internetttilkoblingen din.";
      case "api":
        return "Det oppstod en feil med tjenesten. Prøv igjen.";
      case "data":
        return "Datafeil oppstod. Kontakt support hvis problemet vedvarer.";
      default:
        return level === "critical"
          ? "En kritisk feil oppstod. Siden lastes inn på nytt."
          : "En uventet feil oppstod.";
    }
  }

  public setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  public setContext(context: Partial<ErrorContext>) {
    Object.assign(this.getErrorContext(), context);
  }

  public clearUserActions() {
    this.userActions = [];
  }
}

export const errorReporter = new ErrorReportingService();

export const reportError = (
  error: Error,
  options?: Parameters<typeof errorReporter.reportError>[2]
) => {
  errorReporter.reportError(error, undefined, options);
};

export const reportComponentError = (
  error: Error,
  errorInfo: React.ErrorInfo,
  options?: Parameters<typeof errorReporter.reportError>[2]
) => {
  errorReporter.reportError(error, errorInfo, options);
};
