export {
  SUPPORTED_LOCALES,
  DEFAULT_LOCALE,
  LOCALE_NAMES,
  detectLocale,
  saveLocale,
  getMessagesForLocale,
  translationMessages,
  appLocales,
  formatTranslationMessages,
} from './config';

export type { SupportedLocale, TranslationMessages } from './config';

export {
  I18nProvider,
  useI18nContext,
  useLocale,
  useSetLocale,
  useAvailableLocales,
  useUpdateSessionLocale,
} from './I18nProvider';

export {
  useTranslation,
  usePrefixedTranslation,
  useDateFormat,
  useNumberFormat,
  useRelativeTimeFormat,
  withTranslation,
} from './hooks';

export type { TranslationFunction } from './hooks';

import { withTranslation } from './hooks';
export default withTranslation;
