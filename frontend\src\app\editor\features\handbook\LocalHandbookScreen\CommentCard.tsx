import { useState, Fragment } from "react";
import { Card, Icon, FormattedDate } from "kf-bui";
import { FormattedMessage } from "react-intl";
import type { Comment } from "@/types";

interface CommentCardProps {
  comment: Comment;
  deleteFunction: (id: string) => Promise<void>;
  saveFunction: (text: string) => Promise<void>;
}

export const CommentCard = ({
  comment,
  deleteFunction,
  saveFunction,
}: CommentCardProps) => {
  const [editComment, setEditComment] = useState<boolean>(false);
  const [newComment, setNewComment] = useState<string>(comment.text);

  const handleSave = async () => {
    try {
      await saveFunction(newComment);
      setEditComment(false);
    } catch (error) {
      console.error("Error saving comment:", error);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteFunction(comment.id!);
    } catch (error) {
      console.error("Error deleting comment:", error);
    }
  };

  return (
    <Card className="comment">
      <Card.Header>
        <div className="comment-edited-by">{comment.editedBy}</div>
        <FormattedDate
          id="editedDate"
          value={new Date(comment.editedDate)}
          format="dd.MM.yyyy - HH:mm"
          style={{ float: "right", marginRight: "5px" }}
        />
      </Card.Header>
      <Card.Content style={{ padding: "0" }}>
        {editComment ? (
          <textarea
            id={`kommentar-${comment.id}`}
            aria-label="Endre kommentaren"
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            style={{
              display: "block",
              width: "100%",
              height: "5em",
              padding: "5px",
            }}
          />
        ) : (
          <div
            style={{ padding: "5px", minHeight: "4em", whiteSpace: "pre-wrap" }}
          >
            {comment.text}
          </div>
        )}
      </Card.Content>

      <Card.Footer>
        {editComment ? (
          <Fragment>
            <Card.Footer.Item
              as="a"
              role="button"
              onClick={() => setEditComment((v) => !v)}
            >
              <Icon icon="edit" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.cancelButton" />
              </span>
            </Card.Footer.Item>
            <Card.Footer.Item
              as="a"
              role="button"
              size="small"
              onClick={handleSave}
            >
              <Icon icon="save" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.saveButton" />
              </span>
            </Card.Footer.Item>
          </Fragment>
        ) : (
          <Fragment>
            <Card.Footer.Item
              as="a"
              role="button"
              size="small"
              onClick={() => setEditComment((v) => !v)}
            >
              <Icon icon="edit" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentCard.editButton" />
              </span>
            </Card.Footer.Item>
            <Card.Footer.Item as="a" role="button" onClick={handleDelete}>
              <Icon icon="times" />
              <span>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentCard.deleteButton" />
              </span>
            </Card.Footer.Item>
          </Fragment>
        )}
      </Card.Footer>
    </Card>
  );
};
