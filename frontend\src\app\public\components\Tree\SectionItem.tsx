import React from "react";
import { Link } from "react-router-dom";
import { Icon } from "kf-bui";
import type { Section } from "@/types";

interface SectionItemProps {
  section: Section;
  externalOrgId: string;
  handbookId: string;
  activeSections: string[];
  onSectionNavigate: (sectionId: string) => void;
  depth?: number;
}

export const SectionItem: React.FC<SectionItemProps> = ({
  section,
  externalOrgId,
  handbookId,
  activeSections,
  onSectionNavigate,
  depth = 0,
}) => {
  // Only highlight the primary (first) active section to prevent multiple selections
  const isActive = activeSections.length > 0 && activeSections[0] === section.id;

  const createSectionLink = () => {
    return `/${externalOrgId}/${handbookId}/chapter/${section.parentId}?id=${section.id}`;
  };

  const handleSectionClick = (e: React.MouseEvent) => {
    e.preventDefault();
    
    // Use the coordination system - it handles navigation, scrolling, and state management
    onSectionNavigate(section.id!);
  };

  return (
    <div
      className={`tree-item section-item ${isActive ? "active" : ""}`}
      id={section.id}
    >
      <div
        className="tree-item-content"
        style={{
          "--tree-depth-padding": `${depth * 1.5}rem`,
        } as React.CSSProperties}
      >
        <Link
          to={createSectionLink()}
          className="tree-item-link"
          onClick={handleSectionClick}
          style={{
            color: isActive ? "#ffffff" : "inherit",
            fontWeight: isActive ? "bold" : "normal",
          }}
        >
          <Icon
            icon="RegFileLines"
            size="small"
            className="tree-item-icon"
          />
          {section.title}
        </Link>
      </div>
    </div>
  );
};
