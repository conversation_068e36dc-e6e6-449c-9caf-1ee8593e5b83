import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Tag, Icon, Button } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { PendingElement } from "@/types";
import { DeleteModal } from "../shared/DeleteModal";

interface DeleteText {
  buttonText?: string;
  title?: string;
  text?: string;
}

export interface PendingChangeWarningProps {
  element: PendingElement;
  mergeLink: string;
  onDelete?: (keepLocal?: boolean) => void;
  deleteText?: DeleteText;
}

export const PendingChangeWarning: React.FC<PendingChangeWarningProps> = ({
  element,
  mergeLink,
  onDelete,
  deleteText = {},
}) => {
  const t = usePrefixedTranslation("editor.components.Metadata");
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);

  if (element.pendingChange) {
    return (
      <Link to={mergeLink} title="Håndter endring" className="pending-warning">
        <Tag color="warning">
          <Icon icon="Exclamation" size="small" /> <span>{t("mergeLink")}</span>
        </Tag>
      </Link>
    );
  }

  if (
    ("pendingTextChange" in element && element.pendingTextChange) ||
    ("pendingTitleChange" in element && element.pendingTitleChange)
  ) {
    return (
      <Link to={mergeLink} title="Håndter endring" className="pending-warning">
        <Tag color="warning">
          <Icon icon="Exclamation" size="small" /> <span>{t("mergeLink")}</span>
        </Tag>
      </Link>
    );
  }

  if (
    element.pendingDeletion &&
    deleteText &&
    onDelete &&
    deleteText.buttonText &&
    deleteText.title &&
    deleteText.text
  ) {
    const { buttonText, title, text } = deleteText;
    return (
      <>
        <Button
          onClick={() => setShowDeleteModal(true)}
          icon="trash"
          outlined
          color="warning"
          size="small"
        >
          {buttonText}
        </Button>

        {showDeleteModal && (
          <DeleteModal
            isOpen={showDeleteModal}
            title={title}
            text={
              <div>
                <p>{text}</p>
              </div>
            }
            keepButton
            onDelete={onDelete}
            onHide={() => setShowDeleteModal(false)}
          />
        )}
      </>
    );
  }

  return null;
};
