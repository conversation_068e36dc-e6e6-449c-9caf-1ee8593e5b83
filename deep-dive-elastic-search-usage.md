# Deep Dive: Elasticsearch Usage in Handbooks Project

## Overview

The Handbooks project uses Elasticsearch as its primary search engine to provide fast, full-text search capabilities across handbook content. This document provides a comprehensive analysis of how Elasticsearch is integrated and utilized throughout the system.

## 1. Elasticsearch Architecture in the Project

```
ELASTICSEARCH INTEGRATION ARCHITECTURE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ APPLICATION LAYER                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                    Handbooks Application                           │   │
│ │                                                                     │   │
│ │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐   │   │
│ │  │   REST API      │  │   Web UI        │  │   Admin Panel   │   │   │
│ │  │   Controllers   │  │   Search Forms  │  │   Index Mgmt    │   │   │
│ │  └─────────────────┘  └─────────────────┘  └─────────────────┘   │   │
│ │           │                     │                     │           │   │
│ │           └─────────────────────┼─────────────────────┘           │   │
│ │                                 │                                 │   │
│ └─────────────────────────────────┼─────────────────────────────────┘   │
│                                   │                                     │
│ SEARCH SERVICE LAYER              │                                     │
│ ┌─────────────────────────────────┼─────────────────────────────────┐   │
│ │                                 ▼                                 │   │
│ │  ┌─────────────────────────────────────────────────────────────┐ │   │
│ │  │              SearchService                                  │ │   │
│ │  │  • Query building and execution                             │ │   │
│ │  │  • Result processing and highlighting                       │ │   │
│ │  │  • Pagination and sorting                                   │ │   │
│ │  │  • Error handling and fallbacks                             │ │   │
│ │  └─────────────────────────────────────────────────────────────┘ │   │
│ │                                 │                                 │   │
│ │  ┌─────────────────────────────────────────────────────────────┐ │   │
│ │  │           SearchIndexService                                │ │   │
│ │  │  • Index creation and mapping                               │ │   │
│ │  │  • Document indexing (CRUD operations)                      │ │   │
│ │  │  • Index management and cleanup                             │ │   │
│ │  └─────────────────────────────────────────────────────────────┘ │   │
│ │                                 │                                 │   │
│ │  ┌─────────────────────────────────────────────────────────────┐ │   │
│ │  │        SearchIndexBuilderService                            │ │   │
│ │  │  • Bulk indexing operations                                 │ │   │
│ │  │  • Full reindexing processes                                │ │   │
│ │  │  • Scheduled index updates                                  │ │   │
│ │  └─────────────────────────────────────────────────────────────┘ │   │
│ └─────────────────────────────────────────────────────────────────┘   │
│                                   │                                     │
│ ELASTICSEARCH CLIENT LAYER        │                                     │
│ ┌─────────────────────────────────┼─────────────────────────────────┐   │
│ │                                 ▼                                 │   │
│ │  ┌─────────────────────────────────────────────────────────────┐ │   │
│ │  │         ElasticClientManagerService                         │ │   │
│ │  │  • Connection management                                    │ │   │
│ │  │  • Client configuration                                     │ │   │
│ │  │  • Health monitoring                                        │ │   │
│ │  └─────────────────────────────────────────────────────────────┘ │   │
│ │                                 │                                 │   │
│ │                    ┌─────────────┴─────────────┐                   │   │
│ │                    │     Elastic4s Client      │                   │   │
│ │                    │   (Scala ES Client)       │                   │   │
│ │                    └─────────────┬─────────────┘                   │   │
│ └─────────────────────────────────┼─────────────────────────────────┘   │
│                                   │                                     │
│ ELASTICSEARCH CLUSTER             │                                     │
│ ┌─────────────────────────────────┼─────────────────────────────────┐   │
│ │                                 ▼                                 │   │
│ │  ┌─────────────────────────────────────────────────────────────┐ │   │
│ │  │              Elasticsearch Node                             │ │   │
│ │  │                                                             │ │   │
│ │  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │   │
│ │  │  │   Index:    │  │   Index:    │  │   Index:    │        │ │   │
│ │  │  │   org-001   │  │   org-002   │  │   org-003   │   ...  │ │   │
│ │  │  │             │  │             │  │             │        │ │   │
│ │  │  │ Documents:  │  │ Documents:  │  │ Documents:  │        │ │   │
│ │  │  │ • Handbooks │  │ • Handbooks │  │ • Handbooks │        │ │   │
│ │  │  │ • Chapters  │  │ • Chapters  │  │ • Chapters  │        │ │   │
│ │  │  │ • Sections  │  │ • Sections  │  │ • Sections  │        │ │   │
│ │  │  └─────────────┘  └─────────────┘  └─────────────┘        │ │   │
│ │  └─────────────────────────────────────────────────────────────┘ │   │
│ └─────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 2. Index Structure and Document Mapping

### 2.1 Index Organization

The project uses a **multi-tenant index strategy** where each organization gets its own dedicated index:

```
INDEX NAMING STRATEGY
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ Organization-Based Index Separation:                                        │
│                                                                             │
│ ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│ │   Index Name:   │    │   Index Name:   │    │   Index Name:   │         │
│ │   "kf-bergen"   │    │   "kf-oslo"     │    │   "kf-stavanger"│         │
│ │                 │    │                 │    │                 │         │
│ │ Contains docs   │    │ Contains docs   │    │ Contains docs   │         │
│ │ for Bergen KF   │    │ for Oslo KF     │    │ for Stavanger   │         │
│ │ organization    │    │ organization    │    │ KF organization │         │
│ └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│                                                                             │
│ Benefits:                                                                   │
│ • Data isolation between organizations                                      │
│ • Independent scaling and optimization                                      │
│ • Simplified access control                                                │
│ • Easier backup and recovery per organization                              │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 Document Types and Mapping

Each index contains three types of documents with a unified mapping structure:

```
DOCUMENT MAPPING STRUCTURE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ Field Mapping Configuration:                                                │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ {                                                                   │   │
│ │   "mappings": {                                                     │   │
│ │     "properties": {                                                 │   │
│ │       "id": {                                                       │   │
│ │         "type": "keyword",           // Exact match, not analyzed   │   │
│ │         "index": true                                               │   │
│ │       },                                                            │   │
│ │       "doc_type": {                                                 │   │
│ │         "type": "keyword",           // "handbook", "chapter", "section" │   │
│ │         "index": true                                               │   │
│ │       },                                                            │   │
│ │       "handbook_id": {                                              │   │
│ │         "type": "keyword",           // For filtering by handbook   │   │
│ │         "index": true                                               │   │
│ │       },                                                            │   │
│ │       "externalOrgId": {                                            │   │
│ │         "type": "keyword",           // Organization identifier     │   │
│ │         "index": true                                               │   │
│ │       },                                                            │   │
│ │       "handbook_title": {                                           │   │
│ │         "type": "text",                                             │   │
│ │         "analyzer": "custom_analyzer",      // For indexing         │   │
│ │         "search_analyzer": "search_analyzer" // For searching       │   │
│ │       },                                                            │   │
│ │       "chapter_title": {                                            │   │
│ │         "type": "text",                                             │   │
│ │         "analyzer": "custom_analyzer",                              │   │
│ │         "search_analyzer": "search_analyzer"                        │   │
│ │       },                                                            │   │
│ │       "section_title": {                                            │   │
│ │         "type": "text",                                             │   │
│ │         "analyzer": "custom_analyzer",                              │   │
│ │         "search_analyzer": "search_analyzer"                        │   │
│ │       },                                                            │   │
│ │       "section_text": {                                             │   │
│ │         "type": "text",                                             │   │
│ │         "analyzer": "custom_analyzer",                              │   │
│ │         "search_analyzer": "search_analyzer"                        │   │
│ │       }                                                             │   │
│ │     }                                                               │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ Document Type Examples:                                                     │
│                                                                             │
│ HANDBOOK DOCUMENT:                                                          │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ {                                                                   │   │
│ │   "id": "handbook-123",                                             │   │
│ │   "doc_type": "handbook",                                           │   │
│ │   "handbook_title": "Employee Safety Guidelines",                   │   │
│ │   "externalOrgId": "kf-bergen",                                     │   │
│ │   "chapter_title": null,                                            │   │
│ │   "section_title": null,                                            │   │
│ │   "section_text": null                                              │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ CHAPTER DOCUMENT:                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ {                                                                   │   │
│ │   "id": "chapter-456",                                              │   │
│ │   "doc_type": "chapter",                                            │   │
│ │   "handbook_id": "handbook-123",                                    │   │
│ │   "chapter_title": "Fire Safety Procedures",                       │   │
│ │   "externalOrgId": "kf-bergen",                                     │   │
│ │   "handbook_title": null,                                           │   │
│ │   "section_title": null,                                            │   │
│ │   "section_text": null                                              │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ SECTION DOCUMENT:                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ {                                                                   │   │
│ │   "id": "section-789",                                              │   │
│ │   "doc_type": "section",                                            │   │
│ │   "handbook_id": "handbook-123",                                    │   │
│ │   "section_title": "Emergency Evacuation Routes",                  │   │
│ │   "section_text": "In case of fire emergency, employees must...",  │   │
│ │   "externalOrgId": "kf-bergen",                                     │   │
│ │   "handbook_title": null,                                           │   │
│ │   "chapter_title": null                                             │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 3. Search Query Architecture

### 3.1 Query Building Process

The search functionality uses a sophisticated Boolean query structure with weighted scoring:

```
SEARCH QUERY CONSTRUCTION FLOW
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ User Input: "fire safety procedures"                                        │
│ Organization: "kf-bergen"                                                   │
│ Handbook Filter: Optional("handbook-123")                                   │
│                                                                             │
│                                    │                                        │
│                                    ▼                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │              buildBoolSearchQuery()                                 │   │
│ │                                                                     │   │
│ │ Step 1: Build MUST clauses (Required filters)                      │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ mustHaveQuery = [                                           │   │   │
│ │ │   matchQuery("externalOrgId", "kf-bergen"),                 │   │   │
│ │ │   matchQuery("handbook_id", "handbook-123")  // if provided │   │   │
│ │ │ ]                                                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ Step 2: Build SHOULD clauses (Scoring/Relevance)                   │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ shouldHaveQuery = should(                                   │   │   │
│ │ │   matchQuery("handbook_title", "fire safety procedures")    │   │   │
│ │ │     .boost(2.0),        // Highest priority                │   │   │
│ │ │   matchQuery("chapter_title", "fire safety procedures")     │   │   │
│ │ │     .boost(1.5),        // High priority                   │   │   │
│ │ │   matchQuery("section_title", "fire safety procedures")     │   │   │
│ │ │     .boost(1.25),       // Medium priority                 │   │   │
│ │ │   matchQuery("section_text", "fire safety procedures")      │   │   │
│ │ │     .boost(1.0)         // Base priority                   │   │   │
│ │ │ )                                                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ Step 3: Combine into final Boolean query                           │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ finalQuery = must(                                          │   │   │
│ │ │   mustHaveQuery ++ Seq(shouldHaveQuery)                    │   │   │
│ │ │ )                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ // This creates: MUST(filters) AND MUST(SHOULD(scoring))   │   │   │
│ │ │ // Ensures all results match filters AND have relevance    │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│                                    ▼                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                Generated Elasticsearch Query                        │   │
│ │                                                                     │   │
│ │ {                                                                   │   │
│ │   "query": {                                                        │   │
│ │     "bool": {                                                       │   │
│ │       "must": [                                                     │   │
│ │         {                                                           │   │
│ │           "match": {                                                │   │
│ │             "externalOrgId": "kf-bergen"                            │   │
│ │           }                                                         │   │
│ │         },                                                          │   │
│ │         {                                                           │   │
│ │           "match": {                                                │   │
│ │             "handbook_id": "handbook-123"                           │   │
│ │           }                                                         │   │
│ │         },                                                          │   │
│ │         {                                                           │   │
│ │           "bool": {                                                 │   │
│ │             "should": [                                             │   │
│ │               {                                                     │   │
│ │                 "match": {                                          │   │
│ │                   "handbook_title": {                              │   │
│ │                     "query": "fire safety procedures",             │   │
│ │                     "boost": 2.0                                    │   │
│ │                   }                                                 │   │
│ │                 }                                                   │   │
│ │               },                                                    │   │
│ │               {                                                     │   │
│ │                 "match": {                                          │   │
│ │                   "chapter_title": {                               │   │
│ │                     "query": "fire safety procedures",             │   │
│ │                     "boost": 1.5                                    │   │
│ │                   }                                                 │   │
│ │                 }                                                   │   │
│ │               },                                                    │   │
│ │               {                                                     │   │
│ │                 "match": {                                          │   │
│ │                   "section_title": {                               │   │
│ │                     "query": "fire safety procedures",             │   │
│ │                     "boost": 1.25                                   │   │
│ │                   }                                                 │   │
│ │                 }                                                   │   │
│ │               },                                                    │   │
│ │               {                                                     │   │
│ │                 "match": {                                          │   │
│ │                   "section_text": {                                 │   │
│ │                     "query": "fire safety procedures",             │   │
│ │                     "boost": 1.0                                    │   │
│ │                   }                                                 │   │
│ │                 }                                                   │   │
│ │               }                                                     │   │
│ │             ]                                                       │   │
│ │           }                                                         │   │
│ │         }                                                           │   │
│ │       ]                                                             │   │
│ │     }                                                               │   │
│ │   },                                                                │   │
│ │   "highlight": {                                                    │   │
│ │     "fields": {                                                     │   │
│ │       "section_text": {                                             │   │
│ │         "number_of_fragments": 1,                                   │   │
│ │         "fragment_size": 200,                                       │   │
│ │         "no_match_size": 200                                        │   │
│ │       }                                                             │   │
│ │     }                                                               │   │
│ │   },                                                                │   │
│ │   "_source": {                                                      │   │
│ │     "excludes": ["section_text"]  // Don't return full text        │   │
│ │   },                                                                │   │
│ │   "from": 0,                                                        │   │
│ │   "size": 20                                                        │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 3.2 Search Result Processing

```
SEARCH RESULT PROCESSING PIPELINE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ Raw Elasticsearch Response                                                  │
│                                    │                                        │
│                                    ▼                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                    executeSearch()                                  │   │
│ │                                                                     │   │
│ │ Step 1: Execute Query with Pagination                               │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ val (startAt, numResults) = getStartAtAndNumResults(page)   │   │   │
│ │ │ // page=2, pageSize=20 → startAt=20, numResults=20          │   │   │
│ │ │                                                             │   │   │
│ │ │ elasticClient.execute {                                     │   │   │
│ │ │   searchQuery                                               │   │   │
│ │ │     .start(startAt)      // Offset for pagination          │   │   │
│ │ │     .limit(numResults)   // Page size                      │   │   │
│ │ │ }.await                                                     │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ Step 2: Process Response and Handle Errors                         │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Try(elasticClient.execute { ... }) match {                 │   │   │
│ │ │   case Success(response) =>                                 │   │   │
│ │ │     // Convert to SearchResult                             │   │   │
│ │ │     SearchResult(                                           │   │   │
│ │ │       totalHits = response.result.totalHits,               │   │   │
│ │ │       took = response.result.took,                         │   │   │
│ │ │       page = page.getOrElse(1),                            │   │   │
│ │ │       pageSize = numResults,                               │   │   │
│ │ │       hits = response.result.to[SearchHit]                 │   │   │
│ │ │     )                                                       │   │   │
│ │ │                                                             │   │   │
│ │ │   case Failure(exception) =>                               │   │   │
│ │ │     errorHandler(exception.getCause)                       │   │   │
│ │ │ }                                                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│                                    ▼                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                    SearchResult Structure                           │   │
│ │                                                                     │   │
│ │ case class SearchResult(                                            │   │
│ │   totalHits: Long,           // Total matching documents            │   │
│ │   took: Long,                // Query execution time (ms)           │   │
│ │   page: Int,                 // Current page number                 │   │
│ │   pageSize: Int,             // Results per page                    │   │
│ │   hits: Seq[SearchHit]       // Actual search results               │   │
│ │ )                                                                   │   │
│ │                                                                     │   │
│ │ case class SearchHit(                                               │   │
│ │   id: String,                // Document ID                         │   │
│ │   docType: String,           // "handbook", "chapter", "section"    │   │
│ │   handbookId: Option[String], // Parent handbook ID                 │   │
│ │   title: String,             // Document title                      │   │
│ │   highlight: Option[String], // Highlighted text snippet            │   │
│ │   score: Float               // Relevance score                     │   │
│ │ )                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                    │                                        │
│                                    ▼                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                    Response to Client                               │   │
│ │                                                                     │   │
│ │ JSON Response Example:                                              │   │
│ │ {                                                                   │   │
│ │   "totalHits": 156,                                                 │   │
│ │   "took": 23,                                                       │   │
│ │   "page": 2,                                                        │   │
│ │   "pageSize": 20,                                                   │   │
│ │   "hits": [                                                         │   │
│ │     {                                                               │   │
│ │       "id": "section-789",                                          │   │
│ │       "docType": "section",                                         │   │
│ │       "handbookId": "handbook-123",                                 │   │
│ │       "title": "Emergency Evacuation Routes",                      │   │
│ │       "highlight": "In case of <em>fire</em> emergency...",        │   │
│ │       "score": 2.45                                                 │   │
│ │     },                                                              │   │
│ │     // ... more results                                             │   │
│ │   ]                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 4. Indexing Process and Data Flow

### 4.1 Document Indexing Workflow

```
DOCUMENT INDEXING LIFECYCLE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ DATA SOURCE CHANGES                                                         │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                                                                     │   │
│ │ ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │   │
│ │ │   New Handbook  │  │  Updated Chapter │  │  New Section    │     │   │
│ │ │   Created       │  │  Modified       │  │  Added          │     │   │
│ │ └─────────────────┘  └─────────────────┘  └─────────────────┘     │   │
│ │          │                    │                    │               │   │
│ │          └────────────────────┼────────────────────┘               │   │
│ │                               │                                    │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                 │                                          │
│                                 ▼                                          │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                    SearchIndexService                               │   │
│ │                                                                     │   │
│ │ Individual Document
</augment_code_snippet>