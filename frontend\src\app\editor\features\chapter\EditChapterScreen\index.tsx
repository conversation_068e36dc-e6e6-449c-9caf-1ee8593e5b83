import { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Field,
  Input,
  Label,
  Title,
  Subtitle,
  Icon,
  Help,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Spinner } from "@/shared/components/Spinner";

import {
  useGetLocalChapterQuery,
  useSaveLocalChapterMutation,
} from "@/store/services/handbook/localHandbookApi";
import type { Chapter } from "@/types";
import type { PublishedCentralChapter } from "@/store/services/handbook/centralHandbookApi";

import { CentralTreeModal } from "../../../shared/CentralTreeModal";

export const EditChapterScreen = () => {
  const { handbookId, chapterId } = useParams() as {
    handbookId: string;
    chapterId?: string;
  };
  const navigate = useNavigate();
  const location = useLocation();
  const t = usePrefixedTranslation("editor.containers.EditChapter");

  const searchParams = new URLSearchParams(location.search);
  const parentId = searchParams.get("parent");

  const isEditing = !!chapterId;
  const [title, setTitle] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [selectedCentralChapter, setSelectedCentralChapter] =
    useState<PublishedCentralChapter | null>(null);
  const [showCentralChapterModal, setShowCentralChapterModal] = useState(false);

  const {
    data: chapter,
    error: chapterError,
    isLoading: isLoadingChapter,
  } = useGetLocalChapterQuery(chapterId!, {
    skip: !isEditing,
  });

  const [saveLocalChapter] = useSaveLocalChapterMutation();

  useEffect(() => {
    if (chapter) {
      setTitle(chapter.title || "");
    }
  }, [chapter]);

  const handleCentralChapterSelection = (
    centralChapter: PublishedCentralChapter
  ) => {
    setSelectedCentralChapter(centralChapter);
    setTitle(centralChapter.title);
    setShowCentralChapterModal(false);
  };

  const handleRemoveCentralSelection = () => {
    setSelectedCentralChapter(null);
  };

  useEffect(() => {
    if (chapterError && isEditing) {
      console.error("Error loading chapter:", chapterError);
      toast.error(t("editor.error.chapterLoadFailed"));
    }
  }, [chapterError, isEditing]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!title.trim()) {
      return;
    }

    if (!handbookId) {
      toast.error("Kunne ikke lagre kapitlet");
      return;
    }

    setIsSaving(true);

    try {
      let chapterData: Partial<Chapter>;

      if (isEditing && chapter) {
        chapterData = {
          ...chapter,
          title: title.trim(),
          localChange: Boolean(chapter.importedHandbookChapterId),
        };
      } else {
        chapterData = {
          title: title.trim(),
          handbookId,
          parentId: parentId || undefined,
          importedHandbookChapterId: selectedCentralChapter?.id || undefined,
          importedHandbookId: selectedCentralChapter?.handbookId || undefined,
        };
      }

      const result = await saveLocalChapter({ chapter: chapterData }).unwrap();
      toast.success(isEditing ? "Kapittel oppdatert" : "Kapittel opprettet");

      const chapterIdToNavigate = isEditing ? chapter!.id : result.id;
      navigate(`/editor/${handbookId}/chapter/${chapterIdToNavigate}/`);
    } catch (error: unknown) {
      console.error("Error saving chapter:", error);
      const apiError = error as { data?: unknown; status?: unknown };

      if (apiError?.data && typeof apiError.data === "string") {
        toast.error(apiError.data);
      } else if (apiError?.status === 500) {
        toast.error(
          "Serverfeil ved lagring av kapittel. Sjekk console for detaljer."
        );
      } else {
        toast.error("Kunne ikke lagre kapitlet");
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (isEditing && chapter) {
      navigate(`/editor/${handbookId}/chapter/${chapter.id}/`);
    } else if (parentId) {
      navigate(`/editor/${handbookId}/chapter/${parentId}/`);
    } else {
      navigate(`/editor/${handbookId}/`);
    }
  };

  if (isEditing && isLoadingChapter) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (isEditing && !chapter && !isLoadingChapter) {
    toast.error(t("editor.error.chapterLoadFailed"));
    navigate(`/editor/${handbookId}`);
    return null;
  }

  return (
    <form onSubmit={handleSubmit}>
      <Columns>
        <Column>
          <Title>
            <Icon
              icon="RegBookmark"
              size="medium"
              style={{ marginRight: "1rem" }}
            />
            <span>{isEditing ? t("editTitle") : t("createTitle")}</span>
          </Title>
          {isEditing && chapter && <Subtitle>{chapter.title}</Subtitle>}
        </Column>
      </Columns>

      <hr />

      {!isEditing && (
        <Field>
          <Label>{t("centralTitle")}</Label>
          {selectedCentralChapter ? (
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "0.5rem",
                padding: "0.5rem",
                backgroundColor: "#f0f8ff",
                border: "1px solid #d0e7ff",
                borderRadius: "4px",
              }}
            >
              <Icon
                icon="RegBookmark"
                size="small"
                style={{ marginRight: "4px" }}
              />
              <span style={{ flex: 1 }}>{selectedCentralChapter.title}</span>
              <Button
                size="small"
                color="danger"
                onClick={handleRemoveCentralSelection}
                title={t("removeCentralSelection")}
              >
                <Icon icon="times" size="small" />
              </Button>
            </div>
          ) : (
            <Button
              onClick={() => setShowCentralChapterModal(true)}
              icon="plus"
            >
              <span>{t("centralButton")}</span>
            </Button>
          )}
        </Field>
      )}

      <Field style={{ marginTop: !isEditing ? "1rem" : "0" }}>
        <Label htmlFor="chapter-title">{t("titleLabel")} *</Label>
        <Input
          id="chapter-title"
          type="text"
          value={title}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setTitle(e.target.value)
          }
          placeholder={t("titleLabel")}
          required
          autoFocus
        />
        {selectedCentralChapter && title && (
          <Help>
            {selectedCentralChapter.title === title ? (
              <span>{t("autoSync")}</span>
            ) : (
              <span style={{ color: "#ff6600" }}>
                <Icon icon="warning" size="small" /> {t("manualSync")}
              </span>
            )}
          </Help>
        )}
      </Field>

      <Columns responsive="mobile">
        <Column>
          <Button type="button" onClick={handleCancel} disabled={isSaving}>
            {t("cancelButton")}
          </Button>
        </Column>
        <Column narrow>
          <Button
            type="submit"
            color="primary"
            disabled={!title.trim()}
            loading={isSaving}
          >
            {t("saveButton")}
          </Button>
        </Column>
      </Columns>

      <CentralTreeModal
        isOpen={showCentralChapterModal}
        onClose={() => setShowCentralChapterModal(false)}
        onChapterSelect={handleCentralChapterSelection}
        title={t("centralTitle")}
      />
    </form>
  );
};
