# CAS Authentication & LDAP Authorization Flow (Continued)
**Part 2: Session Management, Permission Handling & Advanced Scenarios**

## Phase 8 Continued: Permission Matrix Creation

```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PERMISSION MATRIX CREATION                                                                 │
│                                                                                             │
│  Organization 9900 Permissions:                                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ User Role: Admin (localAdmin=true, globalAdmin=true)                                  │   │
│  │                                                                                       │   │
│  │ Handbook Permissions:                                                                 │   │
│  │ ├── safety-manual                                                                     │   │
│  │ │   ├── READ: ✅ (all users with org access)                                         │   │
│  │ │   ├── WRITE: ✅ (admin users only)                                                 │   │
│  │ │   ├── DELETE: ✅ (admin users only)                                                │   │
│  │ │   ├── SORT: ✅ (admin users only)                                                  │   │
│  │ │   ├── PUBLISH: ✅ (admin users only)                                               │   │
│  │ │   └── MANAGE_EDITORS: ✅ (admin users only)                                        │   │
│  │ │                                                                                   │   │
│  │ ├── hr-policies                                                                       │   │
│  │ │   ├── READ: ✅                                                                      │   │
│  │ │   ├── WRITE: ✅                                                                     │   │
│  │ │   └── DELETE: ✅                                                                    │   │
│  │ │                                                                                   │   │
│  │ └── emergency-procedures                                                              │   │
│  │     ├── READ: ✅                                                                      │   │
│  │     ├── WRITE: ✅                                                                     │   │
│  │     └── DELETE: ✅                                                                    │   │
│  │                                                                                       │   │
│  │ System Permissions:                                                                   │   │
│  │ ├── CREATE_HANDBOOK: ✅                                                               │   │
│  │ ├── IMPORT_CENTRAL_HANDBOOK: ✅                                                       │   │
│  │ ├── MANAGE_ORGANIZATION_SETTINGS: ✅                                                  │   │
│  │ ├── VIEW_AUDIT_LOGS: ✅                                                               │   │
│  │ └── MANAGE_USER_ACCESS: ✅                                                            │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Phase 9: Application Response and UI Rendering

```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  PHASE 9: SUCCESSFUL AUTHENTICATION RESPONSE                                               │
│                                                                                             │
│  Filter Chain Completion:                                                                  │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 1. CAS Validation Filter: ✅ User authenticated and authorized                         │   │
│  │ 2. Request proceeds to application controller                                         │   │
│  │ 3. HandbookController.showHandbook() method called                                    │   │
│  │ 4. User session and permissions available in request                                  │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Application Controller Processing:                                                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ @RequestMapping("/9900/safety-manual")                                                │   │
│  │ def showHandbook(orgId: String, handbookId: String, request: HttpServletRequest) = {  │   │
│  │                                                                                       │   │
│  │   // Extract user from session                                                       │   │
│  │   val ldapUser = request.getSession.getAttribute("LDAP_USER").asInstanceOf[LDAPUser] │   │
│  │                                                                                       │   │
│  │   // Verify organization access                                                      │   │
│  │   if (!ldapUser.organizations.contains(orgId)) {                                     │   │
│  │     return Redirect("/forbidden")                                                    │   │
│  │   }                                                                                  │   │
│  │                                                                                       │   │
│  │   // Load handbook data                                                              │   │
│  │   val handbook = handbookService.getHandbook(orgId, handbookId)                     │   │
│  │   val chapters = chapterService.getChapters(handbook.id)                            │   │
│  │   val userPermissions = permissionService.getUserPermissions(ldapUser, handbook)    │   │
│  │                                                                                       │   │
│  │   // Render view with user context                                                   │   │
│  │   Ok(views.html.handbook(handbook, chapters, ldapUser, userPermissions))            │   │
│  │ }                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  HTTP Response to Browser:                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ HTTP/1.1 200 OK                                                                       │   │
│  │ Content-Type: text/html; charset=UTF-8                                                │   │
│  │ Set-Cookie: JSESSIONID=ABC123DEF456; Path=/; HttpOnly; Secure                        │   │
│  │ Cache-Control: no-cache, no-store, must-revalidate                                    │   │
│  │                                                                                       │   │
│  │ <html>                                                                                │   │
│  │   <head><title>KF Håndbøker - Safety Manual</title></head>                           │   │
│  │   <body>                                                                              │   │
│  │     <nav>Welcome, John Doe (<EMAIL>) | Admin</nav>                            │   │
│  │     <div class="handbook-content">                                                    │   │
│  │       <!-- Handbook content with admin controls visible -->                          │   │
│  │       <button class="sort-btn">🔄 Sort</button>                                      │   │
│  │       <button class="edit-btn">✏️ Edit</button>                                      │   │
│  │       <button class="delete-btn">🗑️ Delete</button>                                 │   │
│  │     </div>                                                                            │   │
│  │   </body>                                                                             │   │
│  │ </html>                                                                               │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Session Management and Lifecycle

### Session Timeout Handling
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  SESSION TIMEOUT MANAGEMENT                                                                 │
│                                                                                             │
│  Session Configuration:                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ web.xml Configuration:                                                                │   │
│  │ <session-config>                                                                      │   │
│  │   <session-timeout>30</session-timeout> <!-- 30 minutes -->                          │   │
│  │   <cookie-config>                                                                     │   │
│  │     <http-only>true</http-only>                                                       │   │
│  │     <secure>true</secure>                                                             │   │
│  │   </cookie-config>                                                                    │   │
│  │ </session-config>                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Timeout Detection Flow:                                                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ User Request After 30+ Minutes:                                                       │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. Browser sends request with expired JSESSIONID                               │ │   │
│  │ │ 2. Application server creates new session                                      │ │   │
│  │ │ 3. CAS Authentication Filter: No valid CAS user in session                    │ │   │
│  │ │ 4. Check if user has valid CAS TGT (Ticket Granting Ticket)                   │ │   │
│  │ │ 5. If TGT valid: Silent re-authentication                                      │ │   │
│  │ │ 6. If TGT expired: Redirect to CAS login                                       │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Silent Re-authentication (SSO):                                                          │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ If user has valid CASTGC cookie:                                                      │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. Redirect to CAS with service URL                                             │ │   │
│  │ │ 2. CAS validates TGT from CASTGC cookie                                        │ │   │
│  │ │ 3. CAS issues new Service Ticket automatically                                 │ │   │
│  │ │ 4. User redirected back with new ticket                                        │ │   │
│  │ │ 5. Application validates ticket and recreates session                          │ │   │
│  │ │ 6. User continues without seeing login page                                    │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Single Sign-Out (SLO) Process
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  SINGLE SIGN-OUT PROCESS                                                                    │
│                                                                                             │
│  User-Initiated Logout:                                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ User clicks "Logout" in application:                                                  │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. Application invalidates local session                                        │ │   │
│  │ │ 2. Redirect to CAS logout URL:                                                  │ │   │
│  │ │    https://cas.kf.no/cas/logout?service=https://handbook.kf.no                  │ │   │
│  │ │ 3. CAS invalidates TGT and all associated Service Tickets                      │ │   │
│  │ │ 4. CAS sends logout notifications to all registered services                   │ │   │
│  │ │ 5. User redirected to application homepage or login page                       │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  CAS-Initiated Logout (Back-channel):                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ When user logs out from another CAS-enabled application:                              │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. CAS sends POST request to application logout URL                            │ │   │
│  │ │ 2. CAS Single Sign Out Filter processes the request                            │ │   │
│  │ │ 3. Filter extracts session ID from logout request                              │ │   │
│  │ │ 4. Application invalidates the specified session                               │ │   │
│  │ │ 5. User's next request will require re-authentication                          │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Logout Request Format:                                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ POST /handbook/logout HTTP/1.1                                                        │   │
│  │ Content-Type: application/x-www-form-urlencoded                                       │   │
│  │                                                                                       │   │
│  │ logoutRequest=<samlp:LogoutRequest>                                                   │   │
│  │   <samlp:SessionIndex>ST-123456789-abcdef</samlp:SessionIndex>                       │   │
│  │ </samlp:LogoutRequest>                                                                │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Error Scenarios and Handling

### LDAP Connection Failure
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  LDAP CONNECTION FAILURE SCENARIO                                                           │
│                                                                                             │
│  Problem: LDAP server unavailable during authentication                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Error Flow:                                                                           │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. CAS ticket validation succeeds                                               │ │   │
│  │ │ 2. Application attempts LDAP lookup for user attributes                        │ │   │
│  │ │ 3. LDAP connection timeout or connection refused                               │ │   │
│  │ │ 4. LDAPException thrown                                                        │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Fallback Mechanism:                                                                       │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Configuration Check:                                                                  │   │
│  │ MockBrukerAdm=false (production mode)                                                 │   │
│  │                                                                                       │   │
│  │ Fallback Strategy:                                                                    │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. Log LDAP connection error                                                    │ │   │
│  │ │ 2. Create minimal user object from CAS attributes                              │ │   │
│  │ │ 3. Set limited permissions (read-only access)                                  │ │   │
│  │ │ 4. Display warning message to user                                             │ │   │
│  │ │ 5. Allow basic functionality but restrict admin operations                     │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  │                                                                                       │   │
│  │ Minimal User Object:                                                                  │   │
│  │ LDAPUser(                                                                             │   │
│  │   email = "<EMAIL>",                                                           │   │
│  │   fullName = Some("John Doe"),  // from CAS attributes                               │   │
│  │   organizations = Seq(),        // empty - no LDAP data                              │   │
│  │   language = Some("no"),                                                              │   │
│  │   localUser = true,                                                                   │   │
│  │   localAdmin = false,           // restricted due to LDAP failure                    │   │
│  │   globalAdmin = false                                                                 │   │
│  │ )                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  User Experience:                                                                          │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ ⚠️  System Notice                                                                     │   │
│  │                                                                                       │   │
│  │ Authentication successful, but some features may be limited due to                   │   │
│  │ a temporary system issue. Please contact support if this persists.                   │   │
│  │                                                                                       │   │
│  │ Available functions:                                                                  │   │
│  │ • View handbooks (read-only)                                                          │   │
│  │ • Search content                                                                      │   │
│  │ • Access public pages                                                                 │   │
│  │                                                                                       │   │
│  │ Restricted functions:                                                                 │   │
│  │ • Edit content                                                                        │   │
│  │ • Sort chapters/sections                                                              │   │
│  │ • Delete content                                                                      │   │
│  │ • Administrative functions                                                            │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Invalid Organization Access
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  INVALID ORGANIZATION ACCESS SCENARIO                                                       │
│                                                                                             │
│  Problem: User tries to access organization they don't have permission for                 │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ User Request: https://handbook.kf.no/9999/confidential-handbook                       │   │
│  │                                                                                       │   │
│  │ User's Organizations: ["9900", "9901", "9902"]                                        │   │
│  │ Requested Organization: "9999"                                                        │   │
│  │                                                                                       │   │
│  │ Authorization Check Result: ❌ ACCESS DENIED                                           │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Application Response:                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ HTTP/1.1 403 Forbidden                                                                │   │
│  │ Content-Type: text/html; charset=UTF-8                                                │   │
│  │                                                                                       │   │
│  │ <html>                                                                                │   │
│  │   <head><title>Access Denied - KF Håndbøker</title></head>                           │   │
│  │   <body>                                                                              │   │
│  │     <div class="error-page">                                                          │   │
│  │       <h1>🚫 Access Denied</h1>                                                       │   │
│  │       <p>You don't have permission to access this organization's content.</p>        │   │
│  │       <p>If you believe this is an error, please contact your administrator.</p>     │   │
│  │       <a href="/select-organization">Return to Organization Selection</a>            │   │
│  │     </div>                                                                            │   │
│  │   </body>                                                                             │   │
│  │ </html>                                                                               │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Audit Log Entry:                                                                          │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ {                                                                                     │   │
│  │   "timestamp": "2025-01-27T10:30:00Z",                                               │   │
│  │   "event": "UNAUTHORIZED_ACCESS_ATTEMPT",                                            │   │
│  │   "user": "<EMAIL>",                                                          │   │
│  │   "requestedOrg": "9999",                                                            │   │
│  │   "userOrgs": ["9900", "9901", "9902"],                                              │   │
│  │   "requestedResource": "/9999/confidential-handbook",                                │   │
│  │   "sourceIP": "*************",                                                       │   │
│  │   "userAgent": "Mozilla/5.0...",                                                     │   │
│  │   "sessionId": "ABC123DEF456"                                                        │   │
│  │ }                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Development and Testing Configurations

### Mock Authentication for Development
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  DEVELOPMENT MODE CONFIGURATION                                                             │
│                                                                                             │
│  Development Properties (src/test/resources/handboker.properties):                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ # Bypass CAS authentication for development                                           │   │
│  │ CasConfig=internal-test                                                               │   │
│  │ MockBrukerAdm=true                                                                    │   │
│  │ UseLDAP=false                                                                         │   │
│  │                                                                                       │   │
│  │ # Mock user configuration                                                             │   │
│  │ MockUser.email=<EMAIL>                                                        │   │
│  │ MockUser.fullName=Developer User                                                      │   │
│  │ MockUser.organizations=9900,9901,9902                                                 │   │
│  │ MockUser.globalAdmin=true                                                             │   │
│  │ MockUser.localAdmin=true                                                              │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Mock Authentication Flow:                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 1. User accesses application                                                          │   │
│  │ 2. CAS filter detects internal-test configuration                                     │   │
│  │ 3. Mock authentication filter creates fake user session                              │   │
│  │ 4. No redirect to CAS server                                                          │   │
│  │ 5. No LDAP lookup required                                                            │   │
│  │ 6. User immediately has full access with admin privileges                             │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Mock User Object:                                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ LDAPUser(                                                                             │   │
│  │   email = "<EMAIL>",                                                          │   │
│  │   fullName = Some("Developer User"),                                                  │   │
│  │   organizations = Seq("9900", "9901", "9902"),                                       │   │
│  │   language = Some("no"),                                                              │   │
│  │   localUser = true,                                                                   │   │
│  │   localAdmin = true,                                                                  │   │
│  │   globalAdmin = true                                                                  │   │
│  │ )                                                                                     │   │
│  │                                                                                       │   │
│  │ Benefits for Development:                                                             │   │
│  │ • No external dependencies (CAS/LDAP servers)                                        │   │
│  │ • Faster development cycle                                                            │   │
│  │ • Full access to test all features                                                    │   │
│  │ • Consistent test user across development team                                        │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Integration Testing Configuration
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  INTEGRATION TESTING SETUP                                                                  │
│                                                                                             │
│  Integration Test Properties (src/test/resources/integrationtest/handboker.properties):   │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ # Use demo CAS server for integration testing                                         │   │
│  │ CasConfig=internal-demo                                                               │   │
│  │ MockBrukerAdm=false                                                                   │   │
│  │ UseLDAP=false                                                                         │   │
│  │                                                                                       │   │
│  │ # Demo CAS server configuration                                                       │   │
│  │ CasServerUrl=https://kfdemo.knowit.no/cas                                             │   │
│  │ SiteUrl=https://kfdemo.knowit.no/handboker                                            │   │
│  │                                                                                       │   │
│  │ # Use BrukerAdm service instead of LDAP                                               │   │
│  │ BrukerAdmBaseUrl=https://kfdemo.knowit.no/brukeradm                                   │   │
│  │ ApplicationId=handboker                                                               │   │
│  │ AccessKey=123                                                                         │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Integration Test Flow:                                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 1. Test uses real CAS server (demo environment)                                       │   │
│  │ 2. CAS authentication with test user credentials                                      │   │
│  │ 3. User lookup via BrukerAdm REST API instead of LDAP                                 │   │
│  │ 4. Full authentication flow tested end-to-end                                         │   │
│  │ 5. Permissions and authorization tested with real data                                │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  BrukerAdm API Integration:                                                                │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ API Request:                                                                          │   │
│  │ GET https://kfdemo.knowit.no/brukeradm/api/users/<EMAIL>                       │   │
│  │ Authorization: Bearer <access-key>                                                    │   │
│  │ Application-Id: handboker                                                             │   │
│  │                                
</augment_code_snippet>