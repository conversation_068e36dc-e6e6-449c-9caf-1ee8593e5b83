import React, { useState } from "react";
import { <PERSON>ton, Field, Input, Label, Group, Help } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { Link } from "@/types";

interface LinkFormProps {
  sortOrder: number;
  onSave: (link: Link) => void;
  onCancel?: () => void;
  link?: Link;
}

export const LinkForm = ({
  sortOrder,
  onSave,
  onCancel,
  link,
}: LinkFormProps) => {
  const [title, setTitle] = useState(link?.title || "");
  const [url, setUrl] = useState(link?.url || "");
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState<{ title?: string; url?: string }>({});

  const t = usePrefixedTranslation(
    "editor.containers.LinkPage.components.LinkForm"
  );
  const isEditing = !!link;

  const validateForm = () => {
    const newErrors: { title?: string; url?: string } = {};

    if (!title || !title.trim()) {
      newErrors.title = "Lenken må ha en tittel";
    }

    if (!url || !url.trim()) {
      newErrors.url = "Lenken må ha en url";
    } else {
      try {
        new URL(url.trim());
      } catch {
        newErrors.url = "Ugyldig URL-format";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    const linkData: Link = {
      ...link,
      title: title?.trim() || "",
      url: url?.trim() || "",
      sortOrder: link?.sortOrder ?? sortOrder,
    };

    try {
      await onSave(linkData);
      if (!isEditing) {
        setTitle("");
        setUrl("");
        setErrors({});
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setTitle(link?.title || "");
    setUrl(link?.url || "");
    setErrors({});
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="link-collection-form">
      <Field>
        <Label htmlFor="link-title">{t("titleInputPlaceholder")}</Label>
        <Input
          id="link-title"
          type="text"
          value={title}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setTitle(e.target.value)
          }
          placeholder={t("titleInputPlaceholder")}
          required
          fullWidth
          color={errors.title ? "danger" : undefined}
        />
        {errors.title && <Help color="danger">{errors.title}</Help>}
      </Field>

      <Field>
        <Label htmlFor="link-url">URL</Label>
        <Input
          id="link-url"
          type="url"
          value={url}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setUrl(e.target.value)
          }
          placeholder="http://"
          required
          fullWidth
          color={errors.url ? "danger" : undefined}
        />
        {errors.url && <Help color="danger">{errors.url}</Help>}
      </Field>

      <Group>
        <Button
          type="submit"
          color="primary"
          size="small"
          disabled={!title || !title.trim() || !url || !url.trim() || isSaving}
          loading={isSaving}
          icon="check"
        >
          {isEditing ? "Oppdater" : t("addButtonText")}
        </Button>

        {onCancel && (
          <Button
            type="button"
            onClick={handleCancel}
            disabled={isSaving}
            icon="times"
            size="small"
          >
            {t("cancelButtonText")}
          </Button>
        )}
      </Group>
    </form>
  );
};
