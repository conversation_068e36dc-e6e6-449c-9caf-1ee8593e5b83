import React from "react";
import { <PERSON>, Container, Footer as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "kf-bui";
import type { SessionInfo } from "@/types";

export interface FooterProps {
  session: SessionInfo;
  showOptOutModal: () => void;
}

export const Footer: React.FC<FooterProps> = ({ session, showOptOutModal }) => (
  <RulmaFooter>
    <Container>
      <Level>
        <Level.Item textCentered flexible style={{ flexDirection: "column" }}>
          <a href="http://www.kf.no/" target="_blank" rel="noopener noreferrer">
            KF
          </a>
          <a
            href="https://filer.kf-infoserie.no/veiledninger/Personvern_KF_Håndbøker.html"
            target="_blank"
            rel="noopener noreferrer"
          >
            Personvernerklæring
          </a>
          <a
            href="https://filer.kf-infoserie.no/veiledninger/Informasjonskapsler_KF_Håndbøker.html"
            target="_blank"
            rel="noopener noreferrer"
          >
            Informasjonskapsler
          </a>
          <button
            type="button"
            className="cookie-settings-btn"
            onClick={showOptOutModal}
          >
            Innstillinger for informasjonskapsler
          </button>
        </Level.Item>
        <Level.Item textCentered>
          KF Håndbøker
          <br />
          {session.appVersion}
        </Level.Item>
        <Level.Item textCentered>
          Innlogget{" "}
          {(() => {
            if (session.user?.globalAdmin) return "som KF-Administrator";
            if (session.user?.localAdmin) return "som lokal administrator";
            if (session.user?.localUser) return "som lokal redaktør";
            return "uten rettigheter";
          })()}
          <br />
          {session.user?.email}
        </Level.Item>
      </Level>
    </Container>
  </RulmaFooter>
);
