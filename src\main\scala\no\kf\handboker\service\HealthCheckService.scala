package no.kf.handboker.service

import no.kf.db.{IDGenerator, TransactionManager}
import no.kf.healthcheck.ApplicationHealthChecker
import no.kf.healthcheck.ApplicationHealthChecker._
import no.kf.handboker.config.AppSettingComponent
import no.kf.handboker.repository.CentralAccessRepositoryComponent

trait HealthCheckServiceComponent extends TransactionManager {
  this: AppSettingComponent with LDAPServiceComponent with CentralAccessRepositoryComponent =>

  val healthCheckService: HealthCheckService

  class HealthCheckServiceImpl extends ApplicationHealthChecker with HealthCheckService {
    override def performApplicationSpecificCheck: String = {
      log.warn("Performing health check.")
      if (databaseHealthCheck && ldapHealthCheck) APPLICATION_UP else APPLICATION_DOWN
    }

    override def databaseHealthCheck: Boolean = {
      val nonValidId = IDGenerator.generateUniqueId

      try {
        inTransaction {
          val dbResult = centralAccessRepository.retrieveAccesses(nonValidId)
          if (dbResult.nonEmpty) {
            log.debug(s"Did not receive empty result from database when retrieving central handbook access $nonValidId. Got access: $dbResult.")
          }
        }
        true
      } catch {
        case npe: NullPointerException =>
          log.warn("NullPointerException during health check of Database connection. Should only happen in test!")
          false

        case t: Exception =>
          log.error("Trouble during health check of Database connection.", t)
          false
      }
    }

    override def ldapHealthCheck: Boolean = {
      try {
        ldapService.testConnection()
        true
      } catch {
        case npe: NullPointerException => {
          log.warn("NullPointerException during health check of LDAP connection. Should only happen in test!")
          false
        }
        case t: Exception => {
          log.error("Trouble during health check of LDAP connection.", t)
          false
        }
      }
    }
  }
}

trait HealthCheckService {
  def performApplicationSpecificCheck: String
  def databaseHealthCheck: Boolean
  def ldapHealthCheck: Boolean
}
