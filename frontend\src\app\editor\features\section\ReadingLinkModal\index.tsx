import React, { useState, useEffect } from "react";
import {
  Modal,
  Button,
  Label,
  Radio,
  Control,
  Icon,
  FormattedDate,
  Field,
} from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { toast } from "@/shared/components/Toast";
import {
  useGetReadingLinkBySectionIdQuery,
  useCreateReadingLinkMutation,
  useDeleteReadingLinkMutation,
} from "@/store/services/handbook/centralHandbookApi";
import type { CentralSection } from "@/types";

interface ReadingLinkModalProps {
  centralSection: CentralSection;
  isOpen: boolean;
  onClose: () => void;
}

export const ReadingLinkModal: React.FC<ReadingLinkModalProps> = ({
  centralSection,
  isOpen,
  onClose,
}) => {
  const t = usePrefixedTranslation("common.components.ReadingLinkModal");
  const [numberOfMonths, setNumberOfMonths] = useState<string>("");

  const {
    data: readingLink,
    error,
    isLoading: isLoadingLink,
  } = useGetReadingLinkBySectionIdQuery(centralSection.id!, {
    skip: !isOpen || !centralSection.id,
  });

  const [createReadingLink, { isLoading: isCreating }] =
    useCreateReadingLinkMutation();

  const [deleteReadingLinkMutation, { isLoading: isDeleting }] =
    useDeleteReadingLinkMutation();

  const currentReadingLink = readingLink;

  const handleMonthUpdate = (value: string | number): void => {
    setNumberOfMonths(String(value));
  };

  const isLinkValid = (validTo: string | Date): boolean => {
    try {
      return new Date(validTo).getTime() > new Date().getTime();
    } catch {
      return false;
    }
  };

  const monthsAhead = (months: string): Date => {
    const now = new Date();
    const noMonths = parseInt(months, 10) + now.getMonth();
    now.setMonth(noMonths);
    now.setHours(23);
    now.setMinutes(59);
    now.setSeconds(59);
    now.setMilliseconds(999);
    return now;
  };

  const persistUserdefinedReadingLink = async (
    months: string
  ): Promise<void> => {
    try {
      const newReadingLink = {
        centralSectionId: centralSection.id!,
        validTo: monthsAhead(months),
      };

      await createReadingLink(newReadingLink).unwrap();
      toast.success(t("linkGenerated"));
    } catch (error) {
      console.error("Failed to create reading link:", error);
      toast.error(t("errorCreate"));
    }
  };

  const handleDeleteLink = async (): Promise<void> => {
    if (currentReadingLink?.id) {
      try {
        await deleteReadingLinkMutation({
          id: currentReadingLink.id!,
          centralSectionId: centralSection.id!,
        }).unwrap();
        toast.success(t("linkDeleted"));
      } catch (error) {
        console.error("Failed to delete reading link:", error);
        toast.error(t("errorDelete"));
      }
    }
  };

  const copyUrl = async (): Promise<void> => {
    if (currentReadingLink?.link) {
      try {
        await navigator.clipboard.writeText(currentReadingLink.link);
        toast.success(t("copyLink"));
      } catch {
        // Fallback for older browsers
        const copyFrom = document.createElement("textarea");
        copyFrom.textContent = currentReadingLink.link;
        document.body.appendChild(copyFrom);
        copyFrom.select();
        document.execCommand("copy");
        copyFrom.blur();
        document.body.removeChild(copyFrom);
        toast.success(t("copyLink"));
      }
    } else {
      toast.error(t("noLinkToCopy"));
    }
  };

  const listOfMonths = [1, 12, 24, 60];

  const pluralWeek = (months: number): string => {
    if (months < 12) {
      return `${months} ${t("month")}${months > 1 ? "er" : ""}`;
    }
    const years = months / 12;
    return `${years} ${t("year")}${years > 1 ? "" : ""}`;
  };

  useEffect(() => {
    if (error) {
      toast.error(t("errorRetrieve"));
    }
  }, [error, t]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} autoFocus={false}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>{t("title")}</Modal.Title>
      </Modal.Header>

      <Modal.Body>
        {isLoadingLink ? (
          <div style={{ padding: "2rem", textAlign: "center" }}>
            <Label>{t("loading")}</Label>
          </div>
        ) : (
          <>
            <Field>
              <Label htmlFor="linkForm">{t("warning")}</Label>
            </Field>

            <Field>
              <Label htmlFor="months">
                {t("selectDuration")}
              </Label>
              <Control className="external-link-radio-month">
                {listOfMonths.map((months) => (
                  <Radio
                    key={`radio${months}`}
                    id={`radio${months}`}
                    name="months"
                    value={months.toString()}
                    checked={numberOfMonths === months.toString()}
                    onChange={handleMonthUpdate}
                  >
                    {`${pluralWeek(months)}`}
                  </Radio>
                ))}
              </Control>
            </Field>

            {currentReadingLink && (
              <Field>
                <Label htmlFor="link">{t("linkLabel")}</Label>
                {currentReadingLink.link ? (
                  <>
                    <a
                      id="link"
                      style={{ display: "block", marginBottom: "0.5rem" }}
                      href={currentReadingLink.link}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {currentReadingLink.link}
                    </a>
                    <Button size="small" onClick={copyUrl}>
                      <Icon icon="copy" size="small" />
                      <span>{t("copyLink")}</span>
                    </Button>
                  </>
                ) : (
                  <p style={{ fontStyle: "italic", color: "#666" }}>
                    {t("linkGeneratedAutomatically")}
                  </p>
                )}

                {isLinkValid(currentReadingLink.validTo) ? (
                  <div style={{ marginTop: "1rem" }}>
                    <Label htmlFor="validTo">{t("validUntil")}</Label>
                    <FormattedDate
                      id="validTo"
                      value={currentReadingLink.validTo}
                      format="dd.MM.yyyy"
                    />
                  </div>
                ) : (
                  <Label htmlFor="validTo" style={{ color: "#d73a49" }}>
                    {t("linkExpired")}
                  </Label>
                )}
              </Field>
            )}
          </>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button
          disabled={!numberOfMonths || isCreating || isLoadingLink}
          loading={isCreating}
          onClick={() => persistUserdefinedReadingLink(numberOfMonths)}
        >
          {currentReadingLink ? t("generateNew") : t("generateLink")}
        </Button>
        <Button
          onClick={handleDeleteLink}
          color="danger"
          disabled={!currentReadingLink || isDeleting || isLoadingLink}
          loading={isDeleting}
        >
          {t("deleteLink")}
        </Button>
        <Button onClick={onClose} disabled={isCreating || isDeleting}>
          {t("cancel")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
