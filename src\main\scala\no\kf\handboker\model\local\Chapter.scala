package no.kf.handboker.model.local

import org.joda.time.DateTime

trait ChapterProperties {
  val id: Option[String]
  val title: String
  val importedHandbookChapterId: Option[String]
  val importedHandbookId: Option[String]
  val handbookId: String
  val parentId: Option[String]
  val sortOrder: Option[Int]
  val localChange: Boolean = false
  val pendingChange: Boolean = false
  val pendingDeletion: Boolean = false
  val pendingChangeUpdatedDate: Option[DateTime] = None
  val updatedDate: Option[DateTime] = None
  val centralChapterUpdatedDateBeforePublish: Option[DateTime] = None
  val localChapterUpdatedDate: Option[DateTime] = None
  val createdDate: Option[DateTime] = None
  val updatedBy: Option[String] = None
  val createdBy: Option[String] = None
  val isDeleted: Option[Boolean] = None
  val deletedDate: Option[DateTime] = None
}

case class Chapter(
                    id: Option[String],
                    title: String,
                    importedHandbookChapterId: Option[String],
                    importedHandbookId: Option[String],
                    handbookId: String,
                    parentId: Option[String],
                    sortOrder: Option[Int],
                    override val localChange: Boolean = false,
                    override val pendingChange: Boolean = false,
                    override val pendingDeletion: Boolean = false,
                    override val pendingChangeUpdatedDate: Option[DateTime] = None,
                    override val updatedDate: Option[DateTime] = None,
                    override val centralChapterUpdatedDateBeforePublish: Option[DateTime] = None,
                    override val localChapterUpdatedDate: Option[DateTime] = None,
                    override val createdDate: Option[DateTime] = None,
                    override val updatedBy: Option[String] = None,
                    override val createdBy: Option[String] = None,
                    override val isDeleted: Option[Boolean] = None,
                    override val deletedDate: Option[DateTime] = None,
                    versionOf: Option[String] = None) extends ChapterProperties {

  def isBasedOnCentralContent = importedHandbookId.isDefined && importedHandbookChapterId.isDefined
}
