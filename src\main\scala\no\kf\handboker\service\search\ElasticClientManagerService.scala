package no.kf.handboker.service.search

import com.sksamuel.elastic4s.http.JavaClient
import com.sksamuel.elastic4s.{ElasticClient, ElasticNodeEndpoint, ElasticProperties}
import no.kf.handboker.config.{AppSettingComponent, ElasticSearchClusterName, ElasticSearchHost, ElasticSearchPort}
import org.elasticsearch.common.settings.Settings

trait ElasticClientManagerServiceComponent {
  this: AppSettingComponent =>

  val elasticClient: ElasticClient
  
  object ElasticClientManagerServiceImpl {
    

    def apply() = {

      val uri = ElasticNodeEndpoint(
        "http",
        settings.settingFor(ElasticSearchHost),
        settings.settingFor(ElasticSearchPort).toInt,
        None
      )
      
      ElasticClient(JavaClient(ElasticProperties(Seq(uri))))
    }
  }
}
