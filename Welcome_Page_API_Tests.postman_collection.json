{"info": {"name": "Welcome Page API - Comprehensive Test Suite", "description": "Complete test scenarios for Welcome Page API based on service tests and documentation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "handbook_base_url", "value": "http://localhost:8080", "description": "Base URL for the handbook application"}, {"key": "handbook_id", "value": "test-handbook-1", "description": "Test handbook ID"}, {"key": "draft_version_id", "value": "", "description": "Draft version ID - will be populated from responses"}, {"key": "customization_id", "value": "", "description": "Customization ID - will be populated from responses"}, {"key": "published_version_id", "value": "", "description": "Published version ID - will be populated from responses"}], "item": [{"name": "1. Setup & Initial State", "item": [{"name": "Get Default Values", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/defaults", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "defaults"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has default values', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('primaryColor');", "    pm.expect(jsonData).to.have.property('secondaryColor');", "    pm.expect(jsonData).to.have.property('welcomeHeader');", "    pm.expect(jsonData).to.have.property('welcomeText');", "});"]}}]}, {"name": "Check Initial Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/status/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "status", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Initially no draft or published', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.hasDraft).to.be.false;", "    pm.expect(jsonData.hasPublished).to.be.false;", "});"]}}]}, {"name": "Get Published (Should be 404)", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/published/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "published", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404 - no published version initially', function () {", "    pm.response.to.have.status(404);", "});"]}}]}, {"name": "Get Draft (Should be 404)", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404 - no draft initially', function () {", "    pm.response.to.have.status(404);", "});"]}}]}]}, {"name": "2. Draft Creation & Basic Operations", "item": [{"name": "Create First Draft with <PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Draft created with default values', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versionId');", "    pm.expect(jsonData).to.have.property('customization');", "    pm.expect(jsonData.customization).to.have.property('primaryColor');", "    pm.expect(jsonData.customization).to.have.property('secondaryColor');", "    ", "    // Store IDs for later use", "    pm.collectionVariables.set('draft_version_id', jsonData.versionId);", "    pm.collectionVariables.set('customization_id', jsonData.customization.id);", "});"]}}]}, {"name": "Get Draft After Creation", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Draft shows its own audit fields', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization).to.have.property('colorUpdatedAt');", "    pm.expect(jsonData.customization).to.have.property('welcomeHeaderUpdatedAt');", "    pm.expect(jsonData.customization).to.have.property('welcomeTextUpdatedAt');", "});", "", "pm.test('Draft has welcomeImageResponse when image exists', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.customization.welcomeImageUrl) {", "        pm.expect(jsonData.customization.welcomeImageResponse).to.exist;", "        pm.expect(jsonData.customization.welcomeImageResponse).to.include('data:image/');", "    }", "});"]}}]}]}, {"name": "3. Draft Updates & Customization", "item": [{"name": "Update Draft - Colors Only", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#FF5733\",\n    \"secondaryColor\": \"#33FF57\",\n    \"welcomeHeader\": \"Updated Welcome Header\",\n    \"welcomeText\": \"This is an updated welcome message for testing.\",\n    \"imageTitle\": \"Updated Image Title\",\n    \"altTitle\": \"Updated Alt Text\",\n    \"imageCredits\": \"Updated Photo Credits\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Colors updated correctly', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.primaryColor).to.equal('#FF5733');", "    pm.expect(jsonData.customization.secondaryColor).to.equal('#33FF57');", "});", "", "pm.test('Audit fields updated for changed sections', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.colorUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.colorUpdatedAt).to.exist;", "    pm.expect(jsonData.customization.welcomeHeaderUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.welcomeTextUpdatedBy).to.exist;", "});"]}}]}, {"name": "Test Audit Fields Ignore Client Values", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#000000\",\n    \"secondaryColor\": \"#FFFFFF\",\n    \"colorUpdatedBy\": \"<EMAIL>\",\n    \"colorUpdatedAt\": 999999999,\n    \"welcomeHeader\": \"Test Audit Fields\",\n    \"welcomeImageUrl\": \"/evil/change.jpg\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Client audit values ignored, server computed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.colorUpdatedBy).to.not.equal('<EMAIL>');", "    pm.expect(jsonData.customization.colorUpdatedAt).to.not.equal(999999999);", "});", "", "pm.test('Image URL not changed via JSON', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.welcomeImageUrl).to.not.equal('/evil/change.jpg');", "});"]}}]}, {"name": "Update Draft - Add Link Collections", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#000000\",\n    \"secondaryColor\": \"#FFFFFF\",\n    \"welcomeHeader\": \"Test Link Collections\",\n    \"welcomeText\": \"Testing link collections functionality.\"\n  },\n  \"linkCollections\": [\n    {\n      \"id\": \"\",\n      \"title\": \"External Resources\",\n      \"sortOrder\": 1,\n      \"links\": [\n        {\n          \"id\": \"\",\n          \"collectionId\": \"\",\n          \"title\": \"Company Website\",\n          \"url\": \"https://example.com\",\n          \"description\": \"Our main company website\",\n          \"sortOrder\": 1\n        },\n        {\n          \"id\": \"\",\n          \"collectionId\": \"\",\n          \"title\": \"Support Portal\",\n          \"url\": \"https://support.example.com\",\n          \"description\": \"Get help and support\",\n          \"sortOrder\": 2\n        }\n      ]\n    }\n  ],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Link collections created', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.linkCollections).to.have.lengthOf(1);", "    pm.expect(jsonData.linkCollections[0].title).to.equal('External Resources');", "    pm.expect(jsonData.linkCollections[0].links).to.have.lengthOf(2);", "});", "", "pm.test('Link collection has audit fields', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.linkCollections[0].lastUpdatedBy).to.exist;", "    pm.expect(jsonData.linkCollections[0].lastUpdatedAt).to.exist;", "});"]}}]}]}, {"name": "4. Image Upload Tests", "item": [{"name": "Upload Image to Draft", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Select a test image file (JPG, PNG, GIF, WebP, max 5MB)"}]}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}/image", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}", "image"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Image URL returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('imageUrl');", "    pm.expect(jsonData.imageUrl).to.include('/handboker/images/');", "});"]}}]}, {"name": "Remove Image from Draft", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}/image", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}", "image"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Image removed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.equal('Image removed successfully');", "});"]}}]}, {"name": "Update Draft with Multipart (Image + Data)", "request": {"method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Select a test image file"}, {"key": "data", "value": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#0066CC\",\n    \"secondaryColor\": \"#FFFFFF\",\n    \"welcomeHeader\": \"Multipart Update Header\",\n    \"welcomeText\": \"Updated via multipart request with new image.\",\n    \"imageTitle\": \"New Multipart Image\",\n    \"altTitle\": \"Alt text for multipart image\",\n    \"imageCredits\": \"Photo by multipart test\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": []\n}", "type": "text"}]}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Multipart update successful', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.welcomeHeader).to.equal('Multipart Update Header');", "    pm.expect(jsonData.customization.welcomeImageUrl).to.exist;", "    pm.expect(jsonData.customization.imageUpdatedBy).to.exist;", "});"]}}]}]}, {"name": "5. Shortcut Collections Tests", "item": [{"name": "Update Draft - Add Shortcut Collections", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#000000\",\n    \"secondaryColor\": \"#FFFFFF\",\n    \"welcomeHeader\": \"Test Shortcut Collections\",\n    \"welcomeText\": \"Testing shortcut collections functionality.\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": [\n    {\n      \"id\": \"\",\n      \"title\": \"Quick Access\",\n      \"disabled\": false,\n      \"sortOrder\": 1,\n      \"shortcuts\": [\n        {\n          \"id\": \"\",\n          \"collectionId\": \"\",\n          \"title\": \"Getting Started Guide\",\n          \"description\": \"Quick start guide for new users\",\n          \"sortOrder\": 1,\n          \"link\": null,\n          \"handbookId\": \"{{handbook_id}}\",\n          \"sectionId\": \"section-123\",\n          \"chapterId\": null\n        },\n        {\n          \"id\": \"\",\n          \"collectionId\": \"\",\n          \"title\": \"External Link Shortcut\",\n          \"description\": \"Link to external resource\",\n          \"sortOrder\": 2,\n          \"link\": \"https://external.example.com\",\n          \"handbookId\": null,\n          \"sectionId\": null,\n          \"chapterId\": null\n        }\n      ]\n    }\n  ]\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Shortcut collections created', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.shortcutCollections).to.have.lengthOf(1);", "    pm.expect(jsonData.shortcutCollections[0].title).to.equal('Quick Access');", "    pm.expect(jsonData.shortcutCollections[0].shortcuts).to.have.lengthOf(2);", "});", "", "pm.test('Shortcut collection has audit fields', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.shortcutCollections[0].lastUpdatedBy).to.exist;", "    pm.expect(jsonData.shortcutCollections[0].lastUpdatedAt).to.exist;", "});"]}}]}, {"name": "Test Collection Sorting - <PERSON>er", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#000000\",\n    \"secondaryColor\": \"#FFFFFF\"\n  },\n  \"linkCollections\": [\n    {\n      \"id\": \"\",\n      \"title\": \"Resources\",\n      \"sortOrder\": 1,\n      \"links\": [\n        {\n          \"id\": \"\",\n          \"collectionId\": \"\",\n          \"title\": \"Link B\",\n          \"url\": \"https://b.example.com\",\n          \"sortOrder\": 1\n        },\n        {\n          \"id\": \"\",\n          \"collectionId\": \"\",\n          \"title\": \"Link A\",\n          \"url\": \"https://a.example.com\",\n          \"sortOrder\": 2\n        }\n      ]\n    }\n  ],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Link reordering updates collection audit', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.linkCollections[0].lastUpdatedBy).to.exist;", "    pm.expect(jsonData.linkCollections[0].lastUpdatedAt).to.exist;", "    pm.expect(jsonData.linkCollections[0].links[0].title).to.equal('Link B');", "    pm.expect(jsonData.linkCollections[0].links[1].title).to.equal('Link A');", "});"]}}]}]}, {"name": "6. Publishing & Version Management", "item": [{"name": "Publish Draft", "request": {"method": "POST", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/publish/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "publish", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Draft published successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versionId');", "    pm.expect(jsonData).to.have.property('customization');", "    pm.collectionVariables.set('published_version_id', jsonData.versionId);", "});"]}}]}, {"name": "Get Published After Publishing", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/published/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "published", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Published version available', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versionId');", "    pm.expect(jsonData).to.have.property('customization');", "});"]}}]}, {"name": "Check Status After Publishing", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/status/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "status", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Status shows published exists, no draft', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.hasPublished).to.be.true;", "    pm.expect(jsonData.hasDraft).to.be.false;", "});"]}}]}, {"name": "Create New Draft from Published", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('New draft created from published', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versionId');", "    pm.expect(jsonData.versionId).to.not.equal(pm.collectionVariables.get('published_version_id'));", "    pm.collectionVariables.set('draft_version_id', jsonData.versionId);", "    pm.collectionVariables.set('customization_id', jsonData.customization.id);", "});", "", "pm.test('Draft preserves published collection audit', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.linkCollections.length > 0) {", "        pm.expect(jsonData.linkCollections[0].lastUpdatedBy).to.exist;", "        pm.expect(jsonData.linkCollections[0].lastUpdatedAt).to.exist;", "    }", "    if (jsonData.shortcutCollections.length > 0) {", "        pm.expect(jsonData.shortcutCollections[0].lastUpdatedBy).to.exist;", "        pm.expect(jsonData.shortcutCollections[0].lastUpdatedAt).to.exist;", "    }", "});"]}}]}]}, {"name": "7. Draft Discard & Cleanup", "item": [{"name": "Discard Draft", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Draft discarded successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.include('discarded successfully');", "});"]}}]}, {"name": "Verify Draft Discarded", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404 - draft no longer exists', function () {", "    pm.response.to.have.status(404);", "});"]}}]}, {"name": "Verify Published Still Exists", "request": {"method": "GET", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/published/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "published", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Published version unaffected by draft discard', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('versionId');", "    pm.expect(jsonData).to.have.property('customization');", "});"]}}]}]}, {"name": "8. <PERSON><PERSON><PERSON>", "item": [{"name": "Upload Invalid Image - Too Large", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Select a file larger than 5MB to test size validation"}]}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}/image", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}", "image"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400 for oversized file', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message mentions file size', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.error).to.include('exceeds maximum allowed size');", "});"]}}]}, {"name": "Upload Invalid Image - Wrong Format", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": [], "description": "Select a non-image file (e.g., .txt, .pdf) to test format validation"}]}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}/image", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}", "image"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400 for invalid format', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message mentions file format', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.error).to.include('must be an image');", "});"]}}]}, {"name": "Update Non-existent Draft", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"fake-id\",\n  \"customization\": {\n    \"id\": \"fake-customization-id\",\n    \"versionId\": \"fake-id\",\n    \"primaryColor\": \"#000000\",\n    \"secondaryColor\": \"#FFFFFF\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/nonexistent-handbook", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "nonexistent-handbook"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404 for non-existent draft', function () {", "    pm.response.to.have.status(404);", "});"]}}]}, {"name": "Publish Non-existent Draft", "request": {"method": "POST", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/publish/nonexistent-handbook", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "publish", "nonexistent-handbook"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404 for non-existent draft', function () {", "    pm.response.to.have.status(404);", "});"]}}]}, {"name": "Discard Non-existent Draft", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/nonexistent-handbook", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "nonexistent-handbook"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404 for non-existent draft', function () {", "    pm.response.to.have.status(404);", "});"]}}]}]}, {"name": "9. Advanced Scenarios from Service Tests", "item": [{"name": "Test Multiple Draft Edits Show Latest Metadata", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('New draft created', function () {", "    const jsonData = pm.response.json();", "    pm.collectionVariables.set('draft_version_id', jsonData.versionId);", "    pm.collectionVariables.set('customization_id', jsonData.customization.id);", "});"]}}]}, {"name": "Multiple Edits - Color Change", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#999999\",\n    \"secondaryColor\": \"#888888\",\n    \"welcomeHeader\": \"Multiple Edits Test\",\n    \"welcomeText\": \"Testing multiple draft edits\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Draft shows latest edit metadata', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.colorUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.colorUpdatedAt).to.exist;", "    pm.expect(jsonData.customization.welcomeHeaderUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.welcomeTextUpdatedBy).to.exist;", "});"]}}]}, {"name": "Test Concurrent Changes - Different Sections", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#111111\",\n    \"secondaryColor\": \"#222222\",\n    \"welcomeHeader\": \"Concurrent Changes Header\",\n    \"welcomeText\": \"Concurrent changes text\",\n    \"imageTitle\": \"Concurrent Image Title\",\n    \"altTitle\": \"Concurrent Alt Title\",\n    \"imageCredits\": \"Concurrent Credits\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Draft shows audit for each changed section', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.colorUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.colorUpdatedAt).to.exist;", "    pm.expect(jsonData.customization.welcomeHeaderUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.welcomeHeaderUpdatedAt).to.exist;", "    pm.expect(jsonData.customization.welcomeTextUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.welcomeTextUpdatedAt).to.exist;", "    pm.expect(jsonData.customization.imageUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.imageUpdatedAt).to.exist;", "});"]}}]}, {"name": "Test Image Metadata Only Change", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"versionId\": \"{{draft_version_id}}\",\n  \"customization\": {\n    \"id\": \"{{customization_id}}\",\n    \"versionId\": \"{{draft_version_id}}\",\n    \"primaryColor\": \"#111111\",\n    \"secondaryColor\": \"#222222\",\n    \"welcomeHeader\": \"Concurrent Changes Header\",\n    \"welcomeText\": \"Concurrent changes text\",\n    \"imageTitle\": \"New Title Only\",\n    \"altTitle\": \"New Alt Only\",\n    \"imageCredits\": \"New Credits Only\"\n  },\n  \"linkCollections\": [],\n  \"shortcutCollections\": []\n}"}, "url": {"raw": "{{handbook_base_url}}/api/welcome-page/draft/{{handbook_id}}", "host": ["{{handbook_base_url}}"], "path": ["api", "welcome-page", "draft", "{{handbook_id}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Image metadata change updates image audit', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.customization.imageTitle).to.equal('New Title Only');", "    pm.expect(jsonData.customization.imageUpdatedBy).to.exist;", "    pm.expect(jsonData.customization.imageUpdatedAt).to.exist;", "});"]}}]}]}]}