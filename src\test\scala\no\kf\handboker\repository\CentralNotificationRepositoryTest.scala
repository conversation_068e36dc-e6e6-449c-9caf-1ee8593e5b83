package no.kf.handboker.repository

import no.kf.handboker.model.local.{CentralChangeNotification, Chapter, Handbook, Section}
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.scalatest.{BeforeAndAfterEach, FunSuite}

@RunWith(classOf[JUnitRunner])
class CentralNotificationRepositoryTest extends FunSuite with DbTestHandler with BeforeAndAfterEach {

  val repository = componentRegistry.centralNotificationRepository
  val handbookRepo = componentRegistry.handbookRepository


  transactedTest("that we can update a handbook notification") {
    val now = DateTime.now
    val handbook = handbookRepo.persistHandbook(Handbook(None, "KF Avvik-Håndbok", None, "9900"))
    val notification: CentralChangeNotification = CentralChangeNotification(None, "A change description", handbook.id.get, now, None, None, Some("html"))
    val stored = repository.persistCentralChangeNotification(notification)
    val stored2 = repository.persistCentralChangeNotification(stored.copy(changeDescription = "A new description"))
    assert(stored.id == stored2.id)
    val notifications = repository.retrieveNotifications(handbook.id.get)
    assert(notifications.size == 1)
    assert(notifications.head.changeDescription === "A new description")
    assert(notifications.head.id.isDefined)
    assert(notifications.head.id == stored.id)
  }

  transactedTest("that we can update a chapter notification") {
    val now = DateTime.now
    val handbook = handbookRepo.persistHandbook(Handbook(None, "KF Avvik-Håndbok", None, "9900"))
    val chapter = handbookRepo.persistChapter(Chapter(None, "Chapter title", None, None, handbook.id.get, None, Some(0)))
    val notification: CentralChangeNotification = CentralChangeNotification(None, "A chapter change description", handbook.id.get, now, chapter.id, None, Some("html"), concernsTitle = true)
    val stored = repository.persistCentralChangeNotification(notification)
    val notifications = repository.retrieveNotifications(handbook.id.get)
    assert(notifications.size == 1)
    assert(notifications.head.changeDescription === "A chapter change description")
    assert(notifications.head.id.isDefined)
    assert(notifications.head.id == stored.id)
    assert(notifications.head.chapterId.isDefined)
    assert(notifications.head.chapterId == chapter.id)
    assert(notifications.head.concernsTitle)
  }

  transactedTest("that we can update a section notification") {
    val now = DateTime.now
    val handbook = handbookRepo.persistHandbook(Handbook(None, "KF Avvik-Håndbok", None, "9900"))
    val chapter = handbookRepo.persistChapter(Chapter(None, "Chapter title", None, None, handbook.id.get, None, Some(0)))
    val section = handbookRepo.persistSection(Section(None, "Section title", Some("Section text"), None, None, handbook.id.get, chapter.id.get, Some(0)))
    val notification: CentralChangeNotification = CentralChangeNotification(None, "A section change description", handbook.id.get, now, None, section.id, Some("html"))
    val stored = repository.persistCentralChangeNotification(notification)
    val notifications = repository.retrieveNotifications(handbook.id.get)
    assert(notifications.size == 1)
    assert(notifications.head.changeDescription === "A section change description")
    assert(notifications.head.id.isDefined)
    assert(notifications.head.id == stored.id)
    assert(notifications.head.sectionId == section.id)
  }

}
