import React from "react";
import { ChapterComponent } from "./ChapterComponent";
import { SectionComponent } from "./SectionComponent";
import type { Chapter, Section } from "@/types";

interface ChildrenListProps {
  children: Array<(Chapter | Section) & { type: "CHAPTER" | "SECTION" }>;
  chapters: Chapter[];
  sections: Section[];
  onSectionEnter?: (section: Section) => void;
  onSectionLeave?: (section: Section) => void;
  waypointsEnabled?: boolean;
}

export const ChildrenList: React.FC<ChildrenListProps> = ({
  children,
  chapters,
  sections,
  onSectionEnter,
  onSectionLeave,
  waypointsEnabled = true
}) => {
  return (
    <>
      {children.map(child =>
        child.type === "CHAPTER" ? (
          <ChapterComponent
            key={child.id}
            chapter={child as Chapter}
            chapters={chapters}
            sections={sections}
            onSectionEnter={onSectionEnter}
            onSectionLeave={onSectionLeave}
            waypointsEnabled={waypointsEnabled}
          />
        ) : (
          <SectionComponent
            key={child.id}
            section={child as Section}
            onSectionEnter={onSectionEnter}
            onSectionLeave={onSectionLeave}
            waypointsEnabled={waypointsEnabled}
          />
        )
      )}
    </>
  );
};