# Deep Dive: Elasticsearch Usage in Handbooks Project (Continued - Part 3)

## 8. Monitoring and Maintenance (Continued)

### 8.2 Maintenance Procedures (Continued)

```
ELASTICSEARCH MAINTENANCE PROCEDURES (CONTINUED)
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ BACKUP AND RECOVERY (CONTINUED)                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Recovery Process:                                                   │   │
│ │                                                                     │   │
│ │ # List available snapshots                                          │   │
│ │ GET /_snapshot/handbook_backups/_all                                │   │
│ │                                                                     │   │
│ │ # Restore specific organization index                               │   │
│ │ POST /_snapshot/handbook_backups/snapshot_20231201/_restore         │   │
│ │ {                                                                   │   │
│ │   "indices": "kf-bergen",                                           │   │
│ │   "ignore_unavailable": true,                                       │   │
│ │   "include_global_state": false,                                    │   │
│ │   "rename_pattern": "(.+)",                                         │   │
│ │   "rename_replacement": "restored_$1"                               │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ # Verify restored data                                              │   │
│ │ GET /restored_kf-bergen/_count                                      │   │
│ │                                                                     │   │
│ │ Automated Backup Script:                                            │   │
│ │ #!/bin/bash                                                         │   │
│ │ SNAPSHOT_NAME="snapshot_$(date +%Y%m%d_%H%M%S)"                     │   │
│ │ curl -X PUT "localhost:9200/_snapshot/handbook_backups/$SNAPSHOT_NAME" \│   │
│ │   -H 'Content-Type: application/json' -d '{                        │   │
│ │     "indices": "*",                                                 │   │
│ │     "ignore_unavailable": true,                                     │   │
│ │     "include_global_state": false                                   │   │
│ │   }'                                                                │   │
│ │                                                                     │   │
│ │ # Schedule via cron: 0 2 * * * /path/to/backup_script.sh           │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ TROUBLESHOOTING PROCEDURES                                                  │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Common Issues and Solutions:                                        │   │
│ │                                                                     │   │
│ │ 1. Index Corruption                                                 │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Symptoms:                                                   │   │   │
│ │ │ • Search queries return errors                              │   │   │
│ │ │ • Index health shows red status                             │   │   │
│ │ │ • Shard allocation failures                                 │   │   │
│ │ │                                                             │   │   │
│ │ │ Diagnosis:                                                  │   │   │
│ │ │ GET /_cluster/health?level=indices                          │   │   │
│ │ │ GET /_cat/shards?v&h=index,shard,prirep,state,unassigned.reason │   │
│ │ │                                                             │   │   │
│ │ │ Resolution:                                                 │   │   │
│ │ │ 1. Try to recover unassigned shards:                       │   │   │
│ │ │    POST /_cluster/reroute?retry_failed=true                │   │   │
│ │ │                                                             │   │   │
│ │ │ 2. If recovery fails, reindex from source:                 │   │   │
│ │ │    searchService.doReindex(affectedOrgId)                   │   │   │
│ │ │                                                             │   │   │
│ │ │ 3. As last resort, restore from backup:                    │   │   │
│ │ │    POST /_snapshot/handbook_backups/latest/_restore        │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ 2. Memory Issues                                                    │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Symptoms:                                                   │   │   │
│ │ │ • OutOfMemoryError in logs                                  │   │   │
│ │ │ • Slow query performance                                    │   │   │
│ │ │ • Node becomes unresponsive                                 │   │   │
│ │ │                                                             │   │   │
│ │ │ Diagnosis:                                                  │   │   │
│ │ │ GET /_nodes/stats/jvm                                       │   │   │
│ │ │ GET /_cat/nodes?v&h=heap.percent,ram.percent               │   │   │
│ │ │                                                             │   │   │
│ │ │ Resolution:                                                 │   │   │
│ │ │ 1. Increase JVM heap size in jvm.options:                  │   │   │
│ │ │    -Xms4g                                                   │   │   │
│ │ │    -Xmx4g                                                   │   │   │
│ │ │                                                             │   │   │
│ │ │ 2. Clear field data cache:                                 │   │   │
│ │ │    POST /_cache/clear?fielddata=true                       │   │   │
│ │ │                                                             │   │   │
│ │ │ 3. Optimize indices to reduce memory usage:                │   │   │
│ │ │    POST /*/_forcemerge?max_num_segments=1                  │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ 3. Connection Issues                                                │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Symptoms:                                                   │   │   │
│ │ │ • Application cannot connect to Elasticsearch              │   │   │
│ │ │ • Search requests timeout                                   │   │   │
│ │ │ • Connection refused errors                                 │   │   │
│ │ │                                                             │   │   │
│ │ │ Diagnosis:                                                  │   │   │
│ │ │ # Check if Elasticsearch is running                        │   │   │
│ │ │ curl -X GET "localhost:9200/"                               │   │   │
│ │ │                                                             │   │   │
│ │ │ # Check application configuration                           │   │   │
│ │ │ ElasticSearchHost=localhost                                 │   │   │
│ │ │ ElasticSearchPort=9200                                      │   │   │
│ │ │                                                             │   │   │
│ │ │ Resolution:                                                 │   │   │
│ │ │ 1. Restart Elasticsearch service:                          │   │   │
│ │ │    brew services restart elasticsearch@5.4                 │   │   │
│ │ │                                                             │   │   │
│ │ │ 2. Check network connectivity:                             │   │   │
│ │ │    telnet localhost 9200                                    │   │   │
│ │ │                                                             │   │   │
│ │ │ 3. Verify firewall settings                                │   │   │
│ │ │ 4. Enable MockElasticSearch for testing:                   │   │   │
│ │ │    MockElasticSearch=true                                   │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 9. Security and Access Control

### 9.1 Security Configuration

```
ELASTICSEARCH SECURITY IMPLEMENTATION
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ NETWORK SECURITY                                                            │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Network Access Control:                                             │   │
│ │                                                                     │   │
│ │ elasticsearch.yml Security Settings:                                │   │
│ │                                                                     │   │
│ │ # Bind to specific interface                                        │   │
│ │ network.host: 127.0.0.1  # Localhost only for development          │   │
│ │ # network.host: **********  # Specific IP for production           │   │
│ │                                                                     │   │
│ │ # Disable HTTP if not needed                                        │   │
│ │ http.enabled: true                                                  │   │
│ │ http.port: 9200                                                     │   │
│ │                                                                     │   │
│ │ # Transport layer security                                          │   │
│ │ transport.tcp.port: 9300                                            │   │
│ │                                                                     │   │
│ │ Production Recommendations:                                         │   │
│ │ • Use firewall to restrict access to Elasticsearch ports          │   │
│ │ • Consider VPN or private network for cluster communication        │   │
│ │ • Monitor access logs for suspicious activity                      │   │
│ │ • Use reverse proxy (nginx) for additional security layer          │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ APPLICATION-LEVEL SECURITY                                                  │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Organization-Based Access Control:                                  │   │
│ │                                                                     │   │
│ │ Index Isolation Strategy:                                           │   │
│ │ • Each organization has dedicated index                             │   │
│ │ • Index name = externalOrgId (e.g., "kf-bergen", "kf-oslo")        │   │
│ │ • No cross-organization data access possible                       │   │
│ │                                                                     │   │
│ │ Search Security Implementation:                                     │   │
│ │ def search(query: String, externalOrgId: String,                    │   │
│ │           page: Option[Int], handbookId: Option[String],            │   │
│ │           userContext: UserContext): SearchResult = {               │   │
│ │                                                                     │   │
│ │   // Verify user has access to organization                        │   │
│ │   if (!userContext.hasAccessToOrg(externalOrgId)) {                │   │
│ │     throw new UnauthorizedAccessException(                          │   │
│ │       s"User ${userContext.userId} cannot access $externalOrgId")  │   │
│ │   }                                                                 │   │
│ │                                                                     │   │
│ │   // Search only in user's authorized organization index           │   │
│ │   val searchRequest = search(externalOrgId)                         │   │
│ │     .query(buildSearchQuery(query, handbookId))                     │   │
│ │     .from(startAt)                                                  │   │
│ │     .size(pageSize)                                                 │   │
│ │                                                                     │   │
│ │   elasticClient.execute(searchRequest)                              │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Benefits:                                                           │   │
│ │ • Complete data isolation between organizations                     │   │
│ │ • No risk of cross-organization data leakage                       │   │
│ │ • Simplified access control logic                                   │   │
│ │ • Performance benefits from smaller index sizes                    │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ DATA PROTECTION                                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Sensitive Data Handling:                                            │   │
│ │                                                                     │   │
│ │ Document Sanitization:                                              │   │
│ │ def createHandbookDocument(handbook: Handbook): HandbookDocument = { │   │
│ │   HandbookDocument(                                                 │   │
│ │     id = handbook.id,                                               │   │
│ │     doc_type = "handbook",                                          │   │
│ │     handbook_title = sanitizeText(handbook.title),                  │   │
│ │     content = sanitizeContent(handbook.content),                    │   │
│ │     // Exclude sensitive fields like internal IDs, user data       │   │
│ │     external_org_id = handbook.externalOrgId,                       │   │
│ │     created_date = handbook.createdDate,                            │   │
│ │     modified_date = handbook.modifiedDate                           │   │
│ │   )                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ def sanitizeText(text: String): String = {                          │   │
│ │   text                                                              │   │
│ │     .replaceAll("<script[^>]*>.*?</script>", "")  // Remove scripts │   │
│ │     .replaceAll("<[^>]+>", "")                    // Strip HTML tags │   │
│ │     .trim                                                           │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Content Filtering:                                                  │   │
│ │ • Remove HTML/JavaScript for security                               │   │
│ │ • Strip personal identifiable information (PII)                    │   │
│ │ • Exclude internal system metadata                                  │   │
│ │ • Sanitize user-generated content                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 9.2 Audit and Compliance

```
AUDIT AND COMPLIANCE FRAMEWORK
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ SEARCH ACTIVITY LOGGING                                                     │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Search Audit Trail:                                                 │   │
│ │                                                                     │   │
│ │ class SearchAuditService {                                          │   │
│ │   def logSearchActivity(searchEvent: SearchEvent): Unit = {         │   │
│ │     val auditEntry = AuditEntry(                                    │   │
│ │       timestamp = Instant.now(),                                    │   │
│ │       userId = searchEvent.userContext.userId,                     │   │
│ │       organizationId = searchEvent.externalOrgId,                   │   │
│ │       action = "SEARCH",                                            │   │
│ │       query = sanitizeQuery(searchEvent.query),                    │   │
│ │       resultsCount = searchEvent.resultsCount,                      │   │
│ │       executionTime = searchEvent.executionTime,                    │   │
│ │       ipAddress = searchEvent.clientIp,                             │   │
│ │       userAgent = searchEvent.userAgent                             │   │
│ │     )                                                               │   │
│ │                                                                     │   │
│ │     auditLogger.info(Json.toJson(auditEntry).toString())            │   │
│ │   }                                                                 │   │
│ │                                                                     │   │
│ │   def logIndexingActivity(indexEvent: IndexEvent): Unit = {         │   │
│ │     val auditEntry = AuditEntry(                                    │   │
│ │       timestamp = Instant.now(),                                    │   │
│ │       userId = indexEvent.userContext.userId,                      │   │
│ │       organizationId = indexEvent.externalOrgId,                    │   │
│ │       action = "REINDEX",                                           │   │
│ │       documentsProcessed = indexEvent.documentCount,                │   │
│ │       executionTime = indexEvent.executionTime,                     │   │
│ │       success = indexEvent.success                                  │   │
│ │     )                                                               │   │
│ │                                                                     │   │
│ │     auditLogger.info(Json.toJson(auditEntry).toString())            │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Integration in SearchService:                                       │   │
│ │ override def search(query: String, externalOrgId: String,           │   │
│ │                    page: Option[Int], handbookId: Option[String],   │   │
│ │                    userContext: UserContext): SearchResult = {      │   │
│ │   val startTime = System.currentTimeMillis()                       │   │
│ │   try {                                                             │   │
│ │     val result = executeSearch(query, externalOrgId, page, handbookId) │   │
│ │                                                                     │   │
│ │     // Log successful search                                        │   │
│ │     auditService.logSearchActivity(SearchEvent(                     │   │
│ │       userContext = userContext,                                    │   │
│ │       externalOrgId = externalOrgId,                                │   │
│ │       query = query,                                                │   │
│ │       resultsCount = result.totalHits,                              │   │
│ │       executionTime = System.currentTimeMillis() - startTime,      │   │
│ │       clientIp = userContext.clientIp,                              │   │
│ │       userAgent = userContext.userAgent                             │   │
│ │     ))                                                              │   │
│ │                                                                     │   │
│ │     result                                                          │   │
│ │   } catch {                                                         │   │
│ │     case e: Exception =>                                            │   │
│ │       // Log failed search attempt                                  │   │
│ │       auditService.logSearchActivity(SearchEvent(                   │   │
│ │         userContext = userContext,                                  │   │
│ │         externalOrgId = externalOrgId,                              │   │
│ │         query = query,                                              │   │
│ │         error = Some(e.getMessage),                                 │   │
│ │         executionTime = System.currentTimeMillis() - startTime     │   │
│ │       ))                                                            │   │
│ │       throw e                                                       │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ COMPLIANCE REPORTING                                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Data Access Reports:                                                │   │
│ │                                                                     │   │
│ │ class ComplianceReportService {                                     │   │
│ │   def generateDataAccessReport(organizationId: String,              │   │
│ │                               startDate: LocalDate,                 │   │
│ │                               endDate: LocalDate): DataAccessReport = { │   │
│ │                                                                     │   │
│ │     val auditEntries = auditRepository.findByOrganizationAndDateRange( │   │
│ │       organizationId, startDate, endDate)                           │   │
│ │                                                                     │   │
│ │     DataAccessReport(                                               │   │
│ │       organizationId = organizationId,                              │   │
│ │       reportPeriod = DateRange(startDate, endDate),                 │   │
│ │       totalSearches = auditEntries.count(_.action == "SEARCH"),     │   │
│ │       uniqueUsers = auditEntries.map(_.userId).distinct.size,       │   │
│ │       topQueries = getTopQueries(auditEntries),                     │   │
│ │       accessPatterns = analyzeAccessPatterns(auditEntries),         │   │
│ │       dataModifications = auditEntries.count(_.action == "REINDEX") │   │
│ │     )                                                               │   │
│ │   }                                                                 │   │
│ │                                                                     │   │
│ │   def generateGDPRComplianceReport(userId: String): GDPRReport = {   │   │
│ │     val userAuditEntries = auditRepository.findByUserId(userId)     │   │
│ │                                                                     │   │
│ │     GDPRReport(                                                     │   │
│ │       userId = userId,                                              │   │
│ │       dataProcessingActivities = userAuditEntries.map { entry =>    │   │
│ │         DataProcessingActivity(                                     │   │
│ │           timestamp = entry.timestamp,                              │   │
│ │           activity = entry.action,                                  │   │
│ │           dataAccessed = entry.organizationId,                      │   │
│ │           legalBasis = "Legitimate Interest - Employee Access"      │   │
│ │         )                                                           │   │
│ │       },                                                            │   │
│ │       retentionPeriod = "7 years",                                  │   │
│ │       dataSubjectRights = List(                                     │   │
│ │         "Right to access", "Right to rectification",               │   │
│ │         "Right to erasure", "Right to data portability"            │   │
│ │       )                                                             │   │
│ │     )                                                               │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Automated Compliance Checks:                                        │   │
│ │ • Daily audit log integrity verification                            │   │
│ │ • Weekly access pattern anomaly detection                           │   │
│ │ • Monthly data retention policy compliance                          │   │
│ │ • Quarterly security assessment reports                             │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 10. Future Enhancements and Roadmap

### 10.1 Planned Improvements

```
ELASTICSEARCH ENHANCEMENT ROADMAP
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ SHORT-TERM IMPROVEMENTS (3-6 MONTHS)                                       │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ 1. Search Experience Enhancements                                   │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Auto-complete and Suggestions:                              │   │   │
│ │ │                                                             │   │   │
│ │ │ Implementation Plan:                                        │   │   │
│ │ │ • Add completion suggester to index mapping                │   │   │
│ │ │ • Index handbook titles and section headings for suggestions│   │   │
│ │ │ • Implement frontend autocomplete component                │   │   │
│ │ │                                                             │   │   │
│ │ │ Mapping Enhancement:                                        │   │   │
│ │ │ {                                                           │   │   │
│ │ │   "mappings": {                                             │   │   │
│ │ │     "properties": {                                         │   │   │
│ │ │       "title_suggest": {                                    │   │   │
│ │ │         "type": "completion",                               │   │   │
│ │ │         "analyzer": "simple",                               │   │   │
│ │ │         "preserve_separators": true,                        │   │   │
│ │ │         "preserve_position_increments": true,               │   │   │
│ │ │         "max_input_length": 50                              │   │   │
│ │ │       }                                                     │   │   │
│ │ │     }                                                       │   │   │
│ │ │   }                                                         │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ API Enhancement:                                            │   │   │
│ │ │ def getSuggestions(prefix: String, externalOrgId: String,   │   │   │
│ │ │                   limit: Int = 10): List[String] = {        │   │   │
│ │ │   val suggestionRequest = search(externalOrgId)             │   │   │
│ │ │     .suggestions(                                           │   │   │
│ │ │       completionSuggestion("title_completion")             │   │   │
│ │ │         .field("title_suggest")                             │   │   │
│ │ │         .prefix(prefix)                                     │   │   │
│ │ │         .size(limit)                                        │   │   │
│ │ │     )                                                       │   │   │
│ │ │                                                             │   │   │
│ │ │   elasticClient.execute(suggestionRequest)                  │   │   │
│ │ │     .map(_.result.suggestions("title_completion")           │   │   │
│ │ │       .flatMap(_.options.map(_.text)))                     │   │   │
│ │ │ }                                                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ 2. Advanced Search Features                                         │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Faceted Search Implementation:                              │   │   │
│ │ │                                                             │   │   │
│ │ │ def searchWithFacets(query: String, externalOrgId: String,  │   │   │
│ │ │                     filters: Map[String, List[String]],     │   │   │
│ │ │                     page: Option[Int]): FacetedSearchResult = { │   │
│ │ │                                                             │   │   │
│ │ │   val searchRequest = search(externalOrgId)                 │   │   │
│ │ │     .query(buildFilteredQuery(query, filters))             │   │   │
│ │ │     .aggregations(                                          │   │   │
│ │ │       termsAggregation("doc_types").field("doc_type"),     │   │   │
│ │ │       termsAggregation("handbooks").field("handbook_id"),  │   │   │
│ │ │       dateHistogramAggregation("created_dates")            │   │   │
│ │ │         .field("created_date")                              │   │   │
│ │ │         .calendarInterval(DateHistogramInterval.MONTH)     │   │   │
│ │ │     )                                                       │   │   │
│ │ │                                                             │   │   │
│ │ │   // Execute search and extract facets                     │   │   │
│ │ │   val response = elasticClient.execute(searchRequest)      │   │   │
│ │ │   extractFacetsFromResponse(response)                       │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ Benefits:                                                   │   │   │
│ │ │ • Filter by document type (handbook, chapter, section)     │   │   │
│ │ │ • Filter by specific handbook                               │   │   │
│ │ │ • Filter by creation date range                             │   │   │
│ │ │ • Improved search refinement capabilities                  │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ │                                                                     │   │
│ │ 3. Performance Optimizations                                        │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Search Result Caching:                                      │   │   │
│ │ │                                                             │   │   │
│ │ │ class CachedSearchService extends SearchService {           │   │   │
│ │ │   private val cache = CacheBuilder.newBuilder()            │   │   │
│ │ │     .maximumSize(1000