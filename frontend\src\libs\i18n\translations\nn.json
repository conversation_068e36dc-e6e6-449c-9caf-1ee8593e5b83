﻿{
  "common.components.NoAccessPage.header": "Ingen tilgang",
  "common.components.NoAccessPage.message": "Du har dessverre ikkje tilstrekkelege rettar til å opne denne sida.",
  "common.components.NotFoundPage.header": "Fant ikkje sida",
  "common.components.NotFoundPage.message": "Diverre er sida du leiter etter er blitt flytta, eller så eksisterer ho ikkje.",
  "common.components.InvalidLinkPage.header": "410",
  "common.components.InvalidLinkPage.title": "Utgått lenkje",
  "common.components.InvalidLinkPage.message": "Denne lenkja er utgått.",
  "common.components.OptOutModal.title": "Innstillingar for informasjonskapslar",
  "common.components.OptOutModal.description1": "Vi brukar informasjonskapslar til å samle inn informasjon om deg til ulike formål.",
  "common.components.OptOutModal.description2": "Nokre er nødvendige å innhente for at du kan gjenkjennast og logge inn som brukar. Andre er frivillige, og blir nytta til å sjå bruksstatistikk.",
  "common.components.OptOutModal.description3": "KF brukar Matomo, ei personvernvenleg statistikkløysing, til å analysere bruken av nettstaden. Statistikkrapportane Matomo gir KF, blir brukt til å analysere og forbetre brukarvennlegheita til nettsida, og til oppfølging av kundar.",
  "common.components.OptOutModal.readMore": "Les meir om informasjonskapslar her.",
  "common.components.OptOutModal.acceptAll": "Godta alle",
  "common.components.OptOutModal.acceptNecessary": "Godta berre nødvendige",
  "common.components.OptOutModal.showCookies": "Sjå kva informasjonskapslar vi nyttar",
  "common.components.OptOutModal.necessary": "Nødvendige",
  "common.components.OptOutModal.optional": "Frivillige",
  "common.components.OptOutModal.saveButton": "Lagre",
  "common.components.OptOutModal.recipient": "Mottakar",
  "common.components.OptOutModal.noMatomoInfo": "Klarte ikkje hente informasjon frå statistikkteneste.",
  "common.components.OptOutModal.cookies.jsessionid.description": "Blir nytta til økthandtering når du er innlogga",
  "common.components.OptOutModal.cookies.xsrfToken.description": "Denne informasjonskapselen blir brukt når du er innlogga, og sikrar at andre ikkje kan utgi seg for å vere deg.",
  "common.components.OptOutModal.cookies.tgc.description": "Innlogging",
  "common.components.IdleTimeoutModal.title": "Økta går ut snart",
  "common.components.IdleTimeoutModal.message": "Du har vore inaktiv ei stund. Økta di vil gå ut snart.",
  "common.components.IdleTimeoutModal.autoLogoutWarning": "Du blir automatisk logga ut når tida er ute.",
  "common.components.IdleTimeoutModal.continueSession": "Hald fram økta",
  "common.components.IdleTimeoutModal.logout": "Logg ut",
  "common.components.OptOutModal.cookies.matomo.pkId": "Blir brukt til å skilje brukarar frå kvarandre",
  "common.components.OptOutModal.cookies.matomo.pkRef": "Blir brukt til å sjå kva nettstad eller søkjemotor brukaren kom frå",
  "common.components.OptOutModal.cookies.matomo.pkSes": "Blir brukt til å skilje økter/besøk frå kvarandre når brukar er innlogga i løysinga.",
  "common.components.OptOutModal.cookies.matomo.sessId": "Tryggleik. Lagrar ingen brukardata som kan identifisere besøkjande.",
  "common.idleTimeout.sessionTimeoutTitle": "Sesjonen går ut snart",
  "common.idleTimeout.sessionTimeoutMessage": "Du har vore inaktiv ei stund. Sesjonen din vil gå ut snart. Klikk på knappen nedanfor for å halde fram å vere innlogga.",
  "common.idleTimeout.sessionExpiredTitle": "Sesjonen har gått ut",
  "common.idleTimeout.sessionExpiredMessage": "Sesjonen din har gått ut på grunn av inaktivitet. Du må logge inn på nytt for å halde fram.",
  "common.idleTimeout.sessionExpiredDetails": "Av tryggleiksomsyns blir du automatisk logga ut etter ein periode med inaktivitet.",
  "common.idleTimeout.sessionTimeoutRemaining": "Tid igjen",
  "common.idleTimeout.minutes": "minutt",
  "common.idleTimeout.seconds": "sekund",
  "common.idleTimeout.keepSessionActive": "Hald sesjonen aktiv",
  "common.idleTimeout.logOut": "Logg ut",
  "common.idleTimeout.close": "Lukk",
  "common.authError.authErrorTitle": "Autentiseringsfeil",
  "common.authError.authErrorMessage": "Det oppstod ein feil med innlogginga di. Dette kan kome av at sesjonen har gått ut eller at det er problem med tilkoplinga.",
  "common.authError.generalErrorTitle": "Noko gjekk gale",
  "common.authError.generalErrorMessage": "Det oppstod ein uventa feil. Prøv å laste sida på nytt eller kontakt support dersom problemet held fram.",
  "common.authError.errorDetails": "Tekniske detaljar",
  "common.authError.retry": "Prøv igjen",
  "common.authError.retrying": "Prøver igjen...",
  "common.authError.reloadPage": "Last sida på nytt",
  "common.authError.logout": "Logg ut",
  "common.authError.maxRetriesReached": "Maksimalt antal forsøk nådd. Last sida på nytt eller kontakt support.",
  "common.components.ReadingLinkModal.title": "Generer lesevisningslenkje",
  "common.components.ReadingLinkModal.warning": "Obs! Alle som har lenkja vil kunne opne ho. Lenkja vil vere gyldig til tidsrommet du vel er over.",
  "common.components.ReadingLinkModal.linkLabel": "Lesevisningslenkje:",
  "common.components.ReadingLinkModal.copyLink": "Kopier lenkje",
  "common.components.ReadingLinkModal.validUntil": "Varar fram til:",
  "common.components.ReadingLinkModal.linkExpired": "Lenkja er utgått",
  "common.components.ReadingLinkModal.generateNew": "Generer ny lenkje",
  "common.components.ReadingLinkModal.generateLink": "Generer lenkje",
  "common.components.ReadingLinkModal.deleteLink": "Slett lenkje",
  "common.components.ReadingLinkModal.cancel": "Avbryt",
  "common.components.ReadingLinkModal.month": "månad",
  "common.components.ReadingLinkModal.year": "år",
  "common.components.ReadingLinkModal.linkGenerated": "Leselenkje generert",
  "common.components.ReadingLinkModal.linkDeleted": "Leselenkje sletta",
  "common.components.ReadingLinkModal.errorGeneral": "Ein feil inntraff",
  "common.components.ReadingLinkModal.errorRetrieve": "Klarte ikkje hente leselenkje",
  "common.components.ReadingLinkModal.errorCreate": "Klarte ikkje opprette leselenkje",
  "common.components.ReadingLinkModal.errorDelete": "Klarte ikkje slette leselenkje",
  "common.components.ReadingLinkModal.loading": "Lastar...",
  "common.components.ReadingLinkModal.selectDuration": "Vel kor lenge lenka skal være gyldig",
  "common.components.ReadingLinkModal.linkGeneratedAutomatically": "Lenke blir generert automatisk ved opprettelse",
  "common.components.ReadingLinkModal.noLinkToCopy": "Ingen lenke å kopiere",
  "common.components.ReadSectionPage.loading": "Lastar innhald...",
  "common.components.ReadSectionPage.error": "Klarte ikkje laste innhald",
  "common.components.SearchResult.pageInfo": "Side {page} av {totalPages}. Viser {totalHits} resultat.",
  "common.components.SearchResult.noResults": "Ingen treff funne for «{query}»",
  "common.components.SearchResult.tips": "Tips",
  "common.components.SearchResult.tip1": "Forsøk å bruke meir generelle søketermar.",
  "common.components.SearchResult.tip2": "Viss du filtrerer på handbok, forsøk å fjerne eller justere filteret.",
  "common.components.SearchPagination.pageLabel": "Side {pageNumber}",
  "editor.components.Breadcrumb.rootLink": "Handbøker",
  "editor.components.DeleteModal.cancelButton": "Avbryt",
  "editor.components.DeleteModal.deleteButton": "Slett",
  "editor.components.Header.brand": "Handbøker",
  "editor.components.Header.editor": "Redaktør",
  "editor.components.Header.central": "Sentrale handbøker",
  "editor.components.Header.central-editor": "Sentral editor",
  "editor.components.Header.pending": "Sjå endringar frå sentrale handbøker som må du må handtere",
  "editor.components.Header.settings": "Innstillingar",
  "editor.components.MergeHandbookOrChapter.mergeChapter": "Det har vore ei endring i det sentrale kapittelet.",
  "editor.components.MergeHandbookOrChapter.mergeChapterParagraph": "Du må handtere dette ved å velje om du vil ta inn endringa eller behalde kapittelet slik det er.",
  "editor.components.MergeHandbookOrChapter.mergeManual": "Det har vore ei endring i den sentrale handboka.",
  "editor.components.MergeHandbookOrChapter.mergeManualParagraph": "Du må handtere dette ved å velje om du vil ta inn endringa eller behalde handboka slik ho er.",
  "editor.components.MergeHandbookOrChapter.headerCentral": "Ny sentral tittel",
  "editor.components.MergeHandbookOrChapter.useCentral": "Bruk sentral tittel",
  "editor.components.MergeHandbookOrChapter.headerExisting": "Noverande lokale tittel",
  "editor.components.MergeHandbookOrChapter.useLocal": "La noverande tittel stå",
  "editor.components.MergeHandbookOrChapter.saveButton": "Lagre",
  "editor.components.MergeHandbookOrChapter.fetchCentralElementFail": "Klarte ikkje hente sentralt innhald",
  "editor.components.MergeSection.lead.title": "Det har vore ei endring i det sentrale avsnittet.",
  "editor.components.MergeSection.lead.text": "Du må handtere dette ved å velje om du vil ta inn endringa eller behalde avsnittet slik det er.",
  "editor.components.MergeSection.central.header": "Sentralt avsnitt",
  "editor.components.MergeSection.central.text": "Sentral tittel",
  "editor.components.MergeSection.central.radio": "Bruk sentral tekst",
  "editor.components.MergeSection.local.header": "Lokalt avsnitt",
  "editor.components.MergeSection.local.text": "Lokal tittel",
  "editor.components.MergeSection.local.radio": "Bruk lokal tekst",
  "editor.components.MergeSection.saveButton": "Lagre",
  "editor.containers.HandbookPage.components.LocalEditorsModal.title": "Gi lese- og skrivetilgang",
  "editor.containers.HandbookPage.components.LocalEditorsModal.accessTitle": "Gi tilgang til",
  "editor.containers.HandbookPage.components.LocalEditorsModal.accessHelp": "Du kan velgje blant alle redaktørar i organisasjonen din. Du kan gi tilgang til ein eller fleire redaktørar. Den du vel vil kunne sjå og redigere i handboka, og kunne gi eller ta bort lese- og skriverettar til andre redaktørar i organisasjonen din.",
  "editor.containers.HandbookPage.components.LocalEditorsModal.accessListTitle": "Tilgangar",
  "editor.containers.HandbookPage.components.LocalEditorsModal.selfDeleteWarning": "Du har fjerna din eigen tilgang",
  "editor.containers.HandbookPage.components.LocalEditorsModal.noEditorsWarning": "OBS: Berre administratorar har tilgang til denne handboka!",
  "editor.components.MergeSection.fetchCentralElementFail": "Klarte ikkje hente sentralt innhald",
  "editor.components.Metadata.created": "Oppretta av",
  "editor.components.Metadata.updated": "Tittel oppdatert av",
  "editor.components.Metadata.textUpdated": "Tekst oppdatert av",
  "editor.components.Metadata.centralBased": "Sentralbasert",
  "editor.components.Metadata.mergeLink": "Uteståande sentral endring",
  "editor.components.Metadata.mergeLink.title": "Handter uteståande sentral endring",
  "editor.components.NoSelection.createButton": "Ny handbok",
  "editor.components.NoSelection.text": "Vel eit element i strukturen",
  "editor.components.SelectCentralHandbooks.availableBooks": "Tilgjengelege sentrale handbøker",
  "editor.components.SelectCentralHandbooks.orgBooks": "Sentrale handbøker i organisasjonen",
  "editor.components.SelectCentralHandbooks.resetButton": "Tilbakestill",
  "editor.components.SelectCentralHandbooks.saveButton": "Lagre",
  "editor.components.SelectExternalOrganizationPage.continueButton": "Hald fram",
  "editor.components.SelectExternalOrganizationPage.title": "Vel organisasjon",
  "editor.components.SettingsPage.title": "Innstillingar",
  "editor.components.SortChildren.cancelButton": "Avbryt",
  "editor.components.SortChildren.cancelButton.title": "Avbryt sortering",
  "editor.components.SortChildren.saveButton": "Lagre",
  "editor.components.SortChildren.saveButton.title": "Lagre sortering",
  "editor.containers.App.title": "KF Handbøker",
  "editor.containers.App.application": "KF Handbøker",
  "editor.containers.App.loggedIn": "Innlogga",
  "editor.containers.App.changedOrganization": "Byt organisasjon",
  "editor.containers.App.changedOrganizationFail1": "Ein feil inntraff",
  "editor.containers.App.changedOrganizationFail2": "Klarte ikkje velje organisasjon",
  "editor.containers.CentralHandbooksPage.title": "Tilgang til sentralt innhald",
  "editor.containers.CentralHandbooksPage.header": "Her kan du administere kva for sentrale handbøker ein organisasjon skal ha tilgang til",
  "editor.containers.CentralHandbooksPage.chooseOrganization": "Vel organisasjon",
  "editor.containers.CentralHandbooksPage.loading": "Lastar handbøker...",
  "editor.containers.CentralHandbooksPage.saveSuccess": "Lagra tilgang for organisasjon.",
  "editor.containers.CentralHandbooksPage.saveError": "Klarte ikkje lagre tilgang",
  "editor.containers.CentralHandbookEditorPage.title": "Editor for sentrale handbøker",
  "editor.containers.CentralHandbookEditorPage.header": "Her kan du leggje til, endre og slettet sentrale handbøker",
  "editor.containers.CentralHandbookEditorPage.createButton": "Ny handbok",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.titleLabel": "Tittel",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.titlePlaceholder": "Tittel",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.titleRequired": "Handboka må ha ein tittel",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.saveButton": "Lagre",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.cancelButton": "Avbryt",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.createTitle": "Opprett ny sentral handbok",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.editTitle": "Rediger sentral handbok",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.deleteButton": "Slett",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.createSuccess": "Handboka vart oppretta",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.updateSuccess": "Handboka vart oppdatert",
  "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.saveError": "Klarte ikkje lagre handboka",
  "editor.containers.ChapterSelection.newChapter": "Nytt kapittel",
  "editor.containers.ChapterSelection.newSection": "Nytt avsnitt",
  "editor.containers.ChapterSelection.addAttachment": "Vedlegg",
  "editor.containers.addAttachment": "Vedlegg",
  "editor.containers.SectionSelection.addAttachment": "Vedlegg",
  "editor.containers.attachmentModalTitle": "Last opp vedlegg",
  "editor.containers.dragAndDrop": "Dra og slipp filen din her",
  "editor.containers.or": "eller",
  "editor.containers.clickToAdd": "Last opp vedlegg fra filer",
  "editor.containers.accestedFileTypes": "Filtyper godkjent",
  "editor.containers.attachmentForSection": "vedlegg per avsnitt eller kapittel",
  "editor.containers.max": "Maks",
  "editor.containers.cancel": "Avbryt",
  "editor.containers.publish": "Publiser",
  "editor.containers.uploadedBy": "Lastet opp av",
  "editor.containers.readyToPublish": "Klar til å publiseres",
  "editor.containers.fileOversized": "Filen er større enn maksgrensen på 5 MB",
  "editor.containers.attachmentsAddedSuccessfully": "Vedlegg er lagt ved",
  "editor.containers.attachmentsRemovedSuccessfully": "Vedlegg er fjernet",
  "editor.containers.attachmentsUpdatedSuccessfully": "Vedlegg er oppdatert",
  "editor.containers.fileSizeExceeded": "Filen du har valgt er større enn 5 MB. Vennligst bruk en fil som er mindre enn 5 MB.",
  "editor.containers.uploadLimitReached": "Du har nådd maksgrensen for vedlegg. Du må fjerne vedlegg for å få plass til nye.",
  "editor.containers.ChapterSelection.editButton": "Rediger",
  "editor.containers.ChapterSelection.moveButton": "Flytt",
  "editor.containers.ChapterSelection.sortButton": "Sorter",
  "editor.containers.ChapterSelection.deleteButton": "Slett",
  "editor.containers.ChapterSelection.deleteTitle": "Vil du slette kapittel?",
  "editor.containers.ChapterSelection.deleteQuestion": "Er du sikker på at du vil slette kapitlet {title}?",
  "editor.containers.ChapterSelection.deleteWarning": "Merk at dette også vil slette eventuelle underliggjande kapittel og avsnitt.",
  "editor.containers.CentralTree.selectHandbookFail": "Klarte ikkje hente sentralt innhald",
  "editor.containers.CentralTree.errorLoading": "Feil ved lasting av handbøker",
  "editor.containers.CentralTree.loading": "Lastar...",
  "editor.containers.CreateOrUpdateHandbook.createTitle": "Opprett ny handbok",
  "editor.containers.CreateOrUpdateHandbook.editTitle": "Rediger handbok",
  "editor.containers.CreateOrUpdateHandbook.centralBased": "Vel dersom du vil basere ny handbok på ei sentral handbok.",
  "editor.containers.CreateOrUpdateHandbook.autoSync": "Endringar i den sentrale handboka blir automatisk synkronisert.",
  "editor.containers.CreateOrUpdateHandbook.manualSync": "Endringar frå sentral handbok må du halde ved like manuelt.",
  "editor.containers.CreateOrUpdateHandbook.publicLabel": "Offentleg tilgjengeleg",
  "editor.containers.CreateOrUpdateHandbook.saveButton": "Lagre",
  "editor.containers.DeleteButton.deleteLocalWarning": "Dersom du slettar denne handboka vil kundane framleis behalde dei lokale versjonane sine av denne handboka. Dei får per no ikkje varsel om at sentral handbok er sletta og at vidare vedlikehald av handboka må gjerast lokalt.",
  "editor.containers.DeleteButton.readLinkDeleteWarning": "Det er knytt ei lese-lenkje til eit eller fleire avsnitt i handboka. Alle lese-lenkjer vil også bli sletta, og dermed slutte å verke, når du slettar handboka.",
  "editor.containers.EditChapter.editTitle": "Rediger kapittel",
  "editor.containers.EditChapter.createTitle": "Opprett nytt kapittel",
  "editor.containers.EditChapter.createNew": "Opprett nytt kapittel",
  "editor.containers.EditChapter.titleLabel": "Tittel",
  "editor.containers.EditChapter.autoSync": "Endringar i det sentrale kapitlet blir automatisk synkronisert.",
  "editor.containers.EditChapter.manualSync": "Sentrale endringar må du handtere manuelt.",
  "editor.containers.EditChapter.cancelButton": "Avbryt",
  "editor.containers.EditChapter.removeCentralSelection": "Fjern val",
  "editor.containers.EditChapter.centralButton": "Vel sentralt kapittel",
  "editor.containers.EditChapter.centralTitle": "Sentralt kapittel",
  "editor.containers.EditChapter.saveButton": "Lagre",
  "editor.containers.EditSection.createTitle": "Opprett nytt avsnitt",
  "editor.containers.EditSection.editTitle": "Rediger avsnitt",
  "editor.containers.EditSection.titleLabel": "Tittel",
  "editor.containers.EditSection.centralRadio": "Bruk sentral tekst",
  "editor.containers.EditSection.localRadio": "Bruk sentral tekst med egne endringer",
  "editor.containers.EditSection.textLabel": "Tekst",
  "editor.containers.EditSection.manualSync": "Sentrale endringar må du handtere manuelt.",
  "editor.containers.EditSection.autoSync": "Endringar i det sentrale avsnittet blir automatisk synkronisert.",
  "editor.containers.EditSection.cancelButton": "Avbryt",
  "editor.containers.EditSection.removeCentralSelection": "Fjern val",
  "editor.containers.EditSection.centralButton": "Vel sentralt avsnitt",
  "editor.containers.EditSection.centralTitle": "Sentralt avsnitt",
  "editor.containers.EditSection.saveButton": "Lagre",
  "editor.containers.HandbookSelection.newChapter": "Nytt kapittel",
  "editor.containers.HandbookSelection.sortButton": "Sorter",
 "editor.containers.HandbookSelection.subscribeCheck": "Abboner på sentrale endringar",
  "editor.containers.HandbookSelection.publishedLink": "Sjå lesevisning av handbok",
  "editor.containers.HandbookSelection.deleteButton": "Slett",
  "editor.containers.HandbookSelection.editButton": "Rediger",
  "editor.containers.HandbookSelection.subscribeCheckMouseOver": "Få e-postvarsel når den sentrale handboka blir oppdatert",
  "editor.containers.HandbookSelection.deleteTitle": "Vil du slette handboka?",
  "editor.containers.HandbookSelection.deleteQuestion": "Er du sikker på at du vil slette handboka {title}?",
  "editor.containers.HandbookSelection.deleteWarning": "Merk at dette også vil slette alle underliggjande kapittel og avsnitt.",
  "editor.containers.HandbookPage.components.CommentModal.cancelButton": "Avbryt",
  "editor.containers.HandbookPage.components.CommentModal.closeButton": "Lukk",
  "editor.containers.HandbookPage.components.CommentModal.newCommentButton": "Ny kommentar",
  "editor.containers.HandbookPage.components.CommentModal.saveButton": "Lagre",
  "editor.containers.HandbookPage.components.CommentModal.title": "Interne redaksjonelle kommentarar",
  "editor.containers.HandbookPage.components.CommentCard.deleteButton": "Slett",
  "editor.containers.HandbookPage.components.CommentCard.editButton": "Endre",
  "editor.containers.HandbookPage.components.ExportModal.close": "Lukk",
  "editor.containers.HandbookPage.components.ExportModal.dateTitle": "Vel dato for versjonen du vil eksportere",
  "editor.containers.HandbookPage.components.ExportModal.exportPDF": "Eksporter som PDF",
  "editor.containers.HandbookPage.components.ExportModal.exportQuestion": "Vil du eksportere dokumentet?",
  "editor.containers.HandbookPage.components.ExportModal.title": "Eksport av ",
  "editor.containers.HandbookPage.components.HandbookScreen.exportButton": "Eksporter",
  "editor.containers.HandbookPage.components.HandbookScreen.internalComments": "Interne kommentarar",
  "editor.containers.HandbookPage.components.HandbookScreen.readLink": "SJÅ LESEVISNING AV HANDBOK",
  "editor.containers.HandbookPage.LocalEditorsModal.title": "Gi lese- og skrivetilgang",
  "editor.containers.LocalTree.treeHeader": "Handbøker",
  "editor.containers.LocalTree.errorLoading": "Feil ved lasting av handbøker",
  "editor.containers.LocalTree.loading": "Lastar...",
  "editor.containers.CentralTree.treeHeader": "Sentrale Handbøker",
  "editor.containers.MergePage.title": "Handter endring",
  "editor.containers.MergePage.loading": "Lastar endring",
  "editor.containers.MoveChapterOrSelection.moveTo": "Flytt inn under {title}?",
  "editor.containers.MoveChapterOrSelection.moveElement": "Vel elementet i strukturen der du vil flytte {title}...",
  "editor.containers.MoveChapterOrSelection.cancelButton": "Avbryt",
  "editor.containers.MoveChapterOrSelection.moveButton": "Flytt",
  "editor.containers.PendingPage.header": "Endringar i sentrale handbøker",
  "editor.containers.PendingPage.noChanges": "Ingen endringar i sentrale handbøker ",
  "editor.containers.PendingPage.title": "Sentrale endringar",
  "editor.containers.SectionPage.components.OldVersionView.pickVersion": "Vel kva versjon av avsnittet du vil sjå",
  "editor.containers.SectionPage.components.OldVersionView.title": "Tidlegare versjonar",
  "editor.containers.SectionSelection.editButton": "Rediger",
  "editor.containers.SectionSelection.moveButton": "Flytt",
  "editor.containers.SectionSelection.deleteTitle": "Vil du slette avsnittet?",
  "editor.containers.SectionSelection.deleteButton": "Slett",
  "editor.containers.SectionSelection.deleteQuestion": "Er du sikker på at du vil slette avsnittet {title}?",
  "editor.containers.SectionSelection.editor.error.sectionNotFound": "Avsnitt ikkje funne",
  "editor.containers.SectionSelection.editor.success.sectionDeleted": "Avsnitt sletta",
  "editor.containers.SectionSelection.editor.error.sectionDeleteFailed": "Kunne ikkje slette avsnitt",
  "editor.containers.SectionSelection.sectionText": "Avsnittstekst",
  "editor.containers.SelectOrganizationPage.changeOrganizationTitle": "Byt organisasjon",
  "editor.containers.SelectOrganizationPage.setOrganizationTitle": "Vel organisasjon",
  "editor.containers.SelectOrganizationPage.changeOrganizationButton": "Byt",
  "editor.containers.SelectOrganizationPage.setOrganizationButton": "Vel",
  "editor.containers.SelectOrganizationPage.selectOrganization": "Før du kan gå vidare må du velje ein organisasjon...",
  "editor.containers.WithBreadcrumb.toSearchButton": "Søk i handboka",
  "public.components.Header.brand": "Handbøker",
  "public.components.WelcomePage.heading": "Handbøker",
  "public.components.WelcomePage.message": "På grunn av tryggleiksårsaker kan handbøker berre aksesserast via direkte lenkjer.",
  "public.components.Footer.accessibility": "Tilgjengelegheitserklæring",
  "public.components.Footer.cookies": "Informasjonskapslar",
  "public.components.Footer.cookieSettings": "Innstillingar for informasjonskapslar",
  "public.containers.App.title": "KF Handbøker",
  "public.containers.App.application": "KF Handbøker",
  "public.containers.HandbookPage.title": "Handbok",
  "public.containers.HandbookPage.placeholder": "Dette er ei mellombels implementering av HandbookPage",
  "public.containers.ChapterPage.chapterNotFound": "Kapittel ikkje funne",
  "public.components.Attachments.loading": "Lastar inn vedlegg. Ver snill og vent...",
  "public.components.Attachments.errorLoading": "Feil ved lasting av vedlegg",
  "public.components.Attachments.noAttachments": "Ingen vedlegg funne",
  "public.containers.HandbookPage.loading": "Lastar handbok...",
  "public.containers.HandbookPage.error": "Feil ved lasting av handbok",
  "public.components.MobileTree.toggleTree": "Vis innhald",
  "public.components.MobileTree.toggleTreeLabel": "Opne eller lukk innhaldsfortegning",
  "public.components.MobileTree.closeTree": "Lukk innhaldsfortegning",
  "public.components.MobileTree.treeNavigationLabel": "Innhaldsfortegning navigasjon",
  "public.components.Tree.noHandbook": "Ingen handbok lasta",
  "public.components.Tree.treeTitle": "Navigasjonstruktur",
  "public.components.Tree.noChapters": "Ingen kapittel funne",
  "public.containers.SearchPage.title": "Søk - KF Handbøker",
  "public.containers.SearchPage.placeholderTitle": "Søkefunksjonalitet - Mellombels implementering",
  "public.containers.SearchPage.placeholderMessage": "Dette er ei forenkla implementering av søkefunksjonaliteten. Full implementering krev:",
  "public.containers.SearchPage.searchPlaceholder": "Søk i handboka...",
  "public.containers.SearchPage.search": "Søk",
  "public.containers.SearchPage.searching": "Søkjer...",
  "public.containers.SearchPage.searchError": "Ein feil oppstod under søking",
  "public.containers.SearchPage.searchResults": "Søkeresultat",
  "public.containers.SearchPage.noResults": "Ingen resultat funne",
  "public.containers.SearchPage.noResultsHint": "Prøv andre søkeord eller sjekk stavemåten",
  "public.containers.SearchPage.popularSearches": "Populære søk",
  "public.containers.SearchPage.searchTime": "Søketid",
  "public.components.SearchResultItem.chapter": "Kapittel",
  "public.components.SearchResultItem.section": "Seksjon",
  "public.components.SearchResultItem.content": "Innhald",
  "public.components.SearchResultItem.relevance": "Relevans",
  "public.components.SearchFilters.filtersTitle": "Filtrer resultat",
  "public.components.SearchFilters.totalResults": "Totalt antal resultat",
  "public.components.SearchFilters.contentType": "Innhaldstype",
  "public.components.SearchFilters.allTypes": "Alle typar",
  "public.components.SearchFilters.chaptersOnly": "Kun kapittel",
  "public.components.SearchFilters.sectionsOnly": "Kun seksjonar",
  "public.components.SearchFilters.sortBy": "Sorter etter",
  "public.components.SearchFilters.relevance": "Relevans",
  "public.components.SearchFilters.title": "Tittel",
  "public.components.SearchFilters.date": "Dato",
  "public.components.SearchFilters.sortOrder": "Rekkjefølgje",
  "public.components.SearchFilters.descending": "Synkande",
  "public.components.SearchFilters.ascending": "Stigande",
  "public.components.SearchFilters.resetFilters": "Tilbakestill filter",
  "public.components.SearchFilters.searchTips": "Søketips",
  "public.components.SearchFilters.tip1": "Bruk hermeteikn for eksakte frasar",
  "public.components.SearchFilters.tip2": "Prøv synonym eller relaterte ord",
  "public.components.SearchFilters.tip3": "Kortare søkeord gir breiare resultat",
  "editor.containers.LinkPage.components.LinkForm.srTitleLabel": "Lenketittel",
  "editor.containers.LinkPage.components.LinkForm.titleInputPlaceholder": "Tittel",
  "editor.containers.LinkPage.components.LinkForm.srUrlLabel": "Lenkeurl",
  "editor.containers.LinkPage.components.LinkForm.cancelButtonText": "Avbryt",
  "editor.containers.LinkPage.components.LinkForm.addButtonText": "Legg til",
  "editor.containers.LinkPage.components.Link.edit": "Rediger",
  "editor.containers.LinkPage.components.Link.delete": "Slett",
  "editor.containers.LinkPage.components.Link.deleteQuestion": "Er du sikker på at du vil slette lenka \"{title}\"?",
  "editor.containers.LinkPage.components.LinkCollection.changeName": "Endre namn",
  "editor.containers.LinkPage.components.LinkCollection.saveSorting": "Lagre sortering",
  "editor.containers.LinkPage.components.LinkCollection.startSorting": "Start sortering",
  "editor.containers.LinkPage.components.LinkCollection.resetSorting": "Resett sortering",
  "editor.containers.LinkPage.components.LinkCollection.deleteLinkCollection": "Slett lenkesamling",
  "editor.containers.LinkPage.components.LinkCollection.noLinksInCollection": "Det er ingen lenker i lenkesamling",
  "editor.containers.LinkPage.components.LinkCollectionForm.srTitleLabel": "Lenketittel",
  "editor.containers.LinkPage.components.LinkCollectionForm.srUrlLabel": "Lenkeurl",
  "editor.containers.LinkPage.components.LinkCollectionForm.titleInputPlaceholder": "Tittel",
  "editor.containers.LinkPage.components.LinkCollectionForm.cancelButtonText": "Avbryt",
  "editor.containers.LinkPage.components.LinkCollectionForm.addButtonText": "Legg til",
  "editor.containers.LinkPage.noLinkCollections": "Det finnes ingen lenkesamlingar. Opprett ei ny i skjema over.",
  "generic.close": "Lukk",
  "generic.save": "Lagre",
  "generic.cancel": "Avbryt",
  "generic.delete": "Slett",
  "editor.containers.EditSection.leaveEditorConfirmationCancel": "Avbryt",
  "editor.containers.EditSection.leaveEditorConfirmationLeave": "Fortsett uten å lagre",
  "editor.containers.EditSection.leaveEditorConfirmationModalTitle": "Ikke lagrede endringer",
  "editor.containers.EditSection.leaveEditorConfirmationMessage": "Du har ikke lagret endringene dine. Vil du fortsette, uten å lagre?",
  "editor.containers.EditSection.loading": "Lastar...",
  "editor.containers.EditSection.loadError": "Kunne ikkje laste avsnitt",
  "editor.containers.EditSection.titleRequired": "Tittel er påkrevd",
  "editor.containers.EditSection.parentRequired": "Foreldreelement er påkrevd",
  "editor.containers.EditSection.sectionUpdated": "Avsnitt oppdatert",
  "editor.containers.EditSection.sectionCreated": "Avsnitt opprettet",
  "editor.containers.EditSection.updateError": "Kunne ikkje oppdatere avsnitt",
  "editor.containers.EditSection.createError": "Kunne ikkje opprette avsnitt",
  "editor.containers.EditSection.sectionNotFound": "Avsnitt ikkje funnet",
  "editor.containers.EditSection.editing": "Redigerer",
  "editor.containers.EditSection.createNew": "Opprett nytt avsnitt",
  "editor.containers.EditSection.titlePlaceholder": "Skriv inn tittel...",
  "editor.containers.EditSection.updating": "Oppdaterer...",
  "editor.containers.EditSection.creating": "Opprettar...",
  "editor.containers.EditSection.updateButton": "Oppdater",
  "editor.containers.EditSection.createButton": "Opprett",
  "editor.containers.EditSection.parentInfo": "Dette avsnittet vil bli opprettet under det valde kapittelet",
  "editor.containers.EditSection.unsavedChanges": "Ikkje lagrede endringar",
  "editor.containers.EditSection.leaveConfirmation": "Du har ikkje lagrede endringar. Vil du fortsette utan å lagre?",
  "editor.containers.EditSection.leaveWithoutSaving": "Fortsett utan å lagre",
  "editor.containers.EditSection.createSuccess": "Avsnitt oppretta",
  "editor.containers.EditSection.updateSuccess": "Avsnitt oppdatert",
  "editor.containers.App.sessionInactiveTitle": "Hmm, no har du vore inaktiv lenge!",
  "editor.containers.App.sessionInactiveMessage": "Trykk på knappen under for å unngå å bli logga ut.",
  "editor.containers.App.sessionExpiredTitle": "No har du vore inaktiv litt for lenge",
  "editor.containers.App.sessionExpiredMessage": "Logg inn på nytt for å fortsette. Er det noko du ikkje har lagra? Lukk boksen og kopier arbeidet ditt før du loggar inn igjen.",
  "editor.containers.App.sessionTimeoutRemaining": "Gjenståande tid",
  "editor.containers.App.minutes": "min",
  "editor.containers.App.seconds": "sek",
  "editor.containers.App.close": "Lukk",
  "editor.containers.App.logOut": "Logg ut",
  "editor.containers.App.keepSessionActive": "Fortsett å jobba",
  "editor.containers.PendingPage.centralChange": "Sentral endring",
  "editor.components.MergeHandbookOrChapter.centralChange": "Sentral endring",
  "editor.containers.PendingPage.lastModified": "Sist endret",
  "editor.components.MergeHandbookOrChapter.lastModified": "Sist endret",
  "editor.components.MergeSection.lastModified": "Sist endret",
  "common.locale.select": "Vel språk",
  "examples.title": "Omsetjingseksempel",
  "examples.basic.title": "Grunnleggjande omsetjingar",
  "examples.prefixed.title": "Prefikserte omsetjingar",
  "examples.formatting.title": "Dato- og tidsformatering",
  "examples.numbers.title": "Talformatering",
  "examples.relative.title": "Relativ tid",
  "examples.legacy.title": "Legacy komponent eksempel",
  "examples.legacy.description": "Denne komponenten brukar den gamle withTranslation HOC for bakoverkompatibilitet.",
  "examples.legacy.section": "Legacy kompatibilitetseksempel",
  "common.loading": "Lastar...",
  "common.loadingWithEllipsis": "Lastar vedlegg. Ver snill og vent...",
  "editor.labels.loadingEditor": "Lastar editor...",
  "editor.merge.loadingMergeView": "Lastar sammenslåingsvisning...",
  "editor.error.saveHandbook": "Feil ved lagring av handbok",
  "editor.error.deleteHandbook": "Feil ved sletting av handbok",
  "editor.error.loadHandbook": "Feil ved lasting av handbok",
  "editor.error.saveChapter": "Feil ved lagring av kapittel",
  "editor.error.deleteChapter": "Feil ved sletting av kapittel",
  "editor.error.loadChapter": "Feil ved lasting av kapittel",
  "editor.error.saveSection": "Feil ved lagring av avsnitt",
  "editor.error.deleteSection": "Feil ved sletting av avsnitt",
  "editor.error.loadSection": "Feil ved lasting av avsnitt",
  "editor.error.saveComment": "Feil ved lagring av kommentar",
  "editor.error.deleteComment": "Feil ved sletting av kommentar",
  "editor.error.loadComments": "Feil ved lasting av kommentarar",
  "editor.error.saveEditor": "Feil ved lagring av redaktør",
  "editor.error.deleteEditor": "Feil ved sletting av redaktør",
  "editor.error.loadEditors": "Feil ved lasting av redaktørar",
  "editor.error.toggleSubscription": "Feil ved endring av abonnement",
  "editor.error.loadSubscriptions": "Feil ved lasting av abonnement",
  "editor.error.saveLinkCollection": "Feil ved lagring av lenkesamling",
  "editor.error.deleteLinkCollection": "Feil ved sletting av lenkesamling",
  "editor.error.loadLinkCollections": "Feil ved lasting av lenkesamlingar",
  "editor.error.uploadFile": "Feil ved opplasting av fil",
  "editor.error.deleteFile": "Feil ved sletting av fil",
  "editor.error.loadFile": "Feil ved lasting av fil",
  "editor.success.saveHandbook": "Handbok lagra",
  "editor.success.deleteHandbook": "Handbok sletta",
  "editor.success.saveChapter": "Kapittel lagra",
  "editor.success.deleteChapter": "Kapittel sletta",
  "editor.success.saveSection": "Avsnitt lagra",
  "editor.success.deleteSection": "Avsnitt sletta",
  "editor.success.saveComment": "Kommentar lagra",
  "editor.success.deleteComment": "Kommentar sletta",
  "editor.success.saveEditor": "Redaktør lagra",
  "editor.success.deleteEditor": "Redaktør sletta",
  "editor.success.toggleSubscription": "Abonnement oppdatert",
  "editor.success.saveLinkCollection": "Lenkesamling lagra",
  "editor.success.deleteLinkCollection": "Lenkesamling sletta",
  "editor.success.uploadFile": "Fil lasta opp",
  "editor.success.deleteFile": "Fil sletta",
  "editor.labels.handbookTitle": "Handboktittel",
  "editor.labels.chapterTitle": "Kapitteltittel",
  "editor.labels.sectionTitle": "Avsnittstitle",
  "editor.labels.commentText": "Kommentartekst",
  "editor.labels.editorEmail": "Redaktør e-post",
  "editor.labels.linkCollectionTitle": "Lenkesamlingstitle",
  "editor.labels.linkTitle": "Lenketittel",
  "editor.labels.linkUrl": "Lenke URL",
  "editor.labels.fileName": "Filnamn",
  "editor.labels.fileSize": "Filstorleik",
  "editor.merge.handbookMergeTitle": "Slå saman handbok",
  "editor.merge.chapterMergeTitle": "Slå saman kapittel",
  "editor.merge.sectionMergeTitle": "Slå saman avsnitt",
  "editor.merge.mergeInstructions": "Vel kva versjon du vil behalde",
  "editor.merge.keepLocal": "Behald lokal versjon",
  "editor.merge.keepCentral": "Behald sentral versjon",
  "editor.merge.mergeCompleted": "Sammenslåing fullført",
  "editor.common.save": "Lagre",
  "editor.common.cancel": "Avbrot",
  "editor.common.delete": "Slett",
  "editor.common.edit": "Rediger",
  "editor.common.close": "Lukk",
  "editor.common.confirm": "Stadfest",
  "editor.common.back": "Tilbake",
  "session.warning.title": "Du blir snart logga ut",
  "session.warning.expiresIn": "Økta di endar om {time}",
  "session.warning.inactivityMessage": "Sidan du har vore inaktiv ei stund blir du logga ut snart.",
  "session.warning.keepSessionMessage": "Trykk på knappen under for å ikkje bli logga ut.",
  "session.warning.keepActiveButton": "Bli verande logga inn",
  "session.expired.title": "Du er logga ut",
  "session.expired.message": "No har du vore inaktiv litt for lengje og er difor logga ut.",
  "session.expired.copyMessage": "Logg inn på nytt for å fortsetje.\nHar du endringar som ikkje er lagra? Lukk vindauget og kopier arbeide ditt før du loggar inn igjen.",
  "session.expired.closeButton": "Lukk"
}
