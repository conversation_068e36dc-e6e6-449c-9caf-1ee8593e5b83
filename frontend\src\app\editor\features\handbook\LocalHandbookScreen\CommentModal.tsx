import { useState, useEffect, Fragment } from "react";
import { Modal, Button, Content } from "kf-bui";
import { FormattedMessage } from "react-intl";
import { toast } from "@/shared/components/Toast";
import { useSession } from "@/store/services/session/hooks";
import type { Comment } from "@/types";

import {
  useGetLocalCommentsQuery,
  useCreateLocalCommentMutation,
  useUpdateLocalCommentMutation,
  useDeleteLocalCommentMutation,
} from "@/store/services/handbook/localHandbookApi";

import { CommentCard } from "./CommentCard";
import { Spinner } from "@/shared/components/Spinner";

interface CommentModalProps {
  handbookId: string;
  onHide: () => void;
  isOpen: boolean;
}

export const CommentModal = ({
  handbookId,
  onHide,
  isOpen,
}: CommentModalProps) => {
  const { session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState<boolean>(false);
  const [newText, setNewText] = useState<string>("");

  const {
    data: fetchedComments = [],
    isLoading: isLoadingComments,
    error: commentsError,
  } = useGetLocalCommentsQuery(handbookId, {
    skip: !isOpen,
  });

  const [createComment, { isLoading: isCreatingComment }] =
    useCreateLocalCommentMutation();
  const [updateComment] = useUpdateLocalCommentMutation();
  const [deleteComment] = useDeleteLocalCommentMutation();

  const resetNewComment = () => {
    setNewComment(false);
    setNewText("");
  };

  useEffect(() => {
    if (fetchedComments) {
      setComments(fetchedComments);
    }
  }, [fetchedComments]);

  useEffect(() => {
    if (commentsError) {
      console.error("Error loading comments:", commentsError);
      toast.error("Feil ved lasting av kommentarer");
    }
  }, [commentsError]);

  const handleCreateComment = async () => {
    if (!newText.trim()) {
      return;
    }

    if (!session?.user?.email) {
      return;
    }

    try {
      const newCommentData = await createComment({
        text: newText.trim(),
        editedBy: session.user.email,
        editedDate: new Date().toISOString(),
        handbookId,
      }).unwrap();

      setComments((old) => [...old, newCommentData]);
      resetNewComment();
    } catch (error) {
      console.error("Error creating comment:", error);
      toast.error("Feil ved opprettelse av kommentar");
    }
  };

  const handleSaveComment = async (
    text: string,
    comment: Comment
  ): Promise<void> => {
    if (!session?.user?.email) {
      return;
    }

    try {
      const updatedComment = await updateComment({
        ...comment,
        text,
        editedBy: session.user.email,
      }).unwrap();

      setComments((old) => [
        ...old.filter((f) => f.id !== comment.id),
        updatedComment,
      ]);
    } catch (error) {
      console.error("Error updating comment:", error);
      toast.error("Feil ved oppdatering av kommentar");
      throw error;
    }
  };

  const handleDeleteComment = async (id: string): Promise<void> => {
    try {
      await deleteComment({ id, handbookId }).unwrap();
      setComments((old) => old.filter((f) => f.id !== id));
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast.error("Feil ved sletting av kommentar");
      throw error;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onHide}>
      <Modal.Header onClose={onHide}>
        <Modal.Title>
          <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.title" />
        </Modal.Title>
      </Modal.Header>
      <Modal.Body
        className="comments"
        style={{ maxHeight: "60vh", overflowY: "auto" }}
      >
        {isLoadingComments && (
          <div className="loading-container">
            <Spinner />
          </div>
        )}

        <Content>
          {[...comments]
            .sort(
              (c1, c2) =>
                new Date(c2.editedDate).getTime() -
                new Date(c1.editedDate).getTime()
            )
            .map((c) => (
              <CommentCard
                key={c.id}
                comment={c}
                saveFunction={(text) => handleSaveComment(text, c)}
                deleteFunction={() => handleDeleteComment(c.id!)}
              />
            ))}
          {newComment && (
            <Fragment>
              <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.newCommentButton" />
              <textarea
                id="kommentar"
                value={newText}
                onChange={(e) => setNewText(e.target.value)}
                style={{
                  display: "block",
                  width: "100%",
                  marginBottom: "5px",
                  height: "4em",
                  padding: "5px",
                }}
              />
              <Button
                size="small"
                onClick={handleCreateComment}
                loading={isCreatingComment}
              >
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.saveButton" />
              </Button>
              <Button size="small" onClick={resetNewComment}>
                <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.cancelButton" />
              </Button>
            </Fragment>
          )}
          {!newComment && (
            <Button onClick={() => setNewComment(true)}>
              <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.newCommentButton" />
            </Button>
          )}
        </Content>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={onHide}>
          <FormattedMessage id="editor.containers.HandbookPage.components.CommentModal.closeButton" />
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
