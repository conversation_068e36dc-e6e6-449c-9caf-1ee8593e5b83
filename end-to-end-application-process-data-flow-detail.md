# End-to-End Application Process Data Flow - Detailed Analysis

## Overview
This document provides a crystal-clear, step-by-step explanation of how every API request flows through the Håndbøker application. We'll trace a real request from start to finish, showing exactly which classes execute, in what order, and how data flows between components.

## 1. Application Startup - What Happens When Server Starts

### 1.1 The Complete Startup Process

```
STEP 1: JVM STARTS THE APPLICATION
┌─────────────────────────────────────────────────────────────────┐
│ Command: java -jar handboker-app.jar                           │
│                                                                 │
│ ┌─────────────┐                                                 │
│ │   JVM       │  Loads main class and starts Jetty server      │
│ │   Process   │  ──────────────────────────────────────────►   │
│ │             │                                                 │
│ └─────────────┘                                                 │
└─────────────────────────────────────────────────────────────────┘

STEP 2: JETTY SERVER INITIALIZATION
┌─────────────────────────────────────────────────────────────────┐
│ Jetty Embedded Server starts on port 5100                     │
│                                                                 │
│ ┌─────────────┐    ┌─────────────┐    ┌─────────────┐          │
│ │   Jetty     │───►│  Web.xml    │───►│ Servlet     │          │
│ │   Server    │    │  Processing │    │ Container   │          │
│ │             │    │             │    │             │          │
│ └─────────────┘    └─────────────┘    └─────────────┘          │
└─────────────────────────────────────────────────────────────────┘

STEP 3: SCALATRA BOOTSTRAP EXECUTION
┌─────────────────────────────────────────────────────────────────┐
│ ScalatraBootstrap.init() method is called                      │
│                                                                 │
│ This is THE MOST IMPORTANT method - it sets up everything!     │
│                                                                 │
│ ┌─────────────┐                                                 │
│ │ Bootstrap   │  Creates all servlet instances                 │
│ │ .init()     │  Mounts them to URL patterns                   │
│ │             │  Initializes services                          │
│ └─────────────┘                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 Servlet Mounting - The URL Routing Setup

```
WHAT HAPPENS IN ScalatraBootstrap.init():

┌─────────────────────────────────────────────────────────────────┐
│ SERVLET MOUNTING PROCESS                                        │
│                                                                 │
│ Each line creates a servlet and assigns it to URL patterns:    │
│                                                                 │
│ context.mount(new MainServlet, "/*")                            │
│ ├── Creates: MainServlet instance                               │
│ ├── Pattern: "/*" (catches ALL requests)                       │
│ ├── Purpose: Serves React frontend, static files               │
│ └── Priority: LOWEST (last to be checked)                      │
│                                                                 │
│ context.mount(new SessionServlet, "/session/*")                 │
│ ├── Creates: SessionServlet instance                            │
│ ├── Pattern: "/session/*"                                      │
│ ├── Handles: /session/login, /session/logout, /session/info    │
│ └── Purpose: User login/logout, session management             │
│                                                                 │
│ context.mount(new LocalHandbookServlet, "/handbooks/local/*")   │
│ ├── Creates: LocalHandbookServlet instance                      │
│ ├── Pattern: "/handbooks/local/*"                              │
│ ├── Handles: CRUD operations on local handbooks                │
│ └── Auth: Requires valid user session                          │
│                                                                 │
│ context.mount(new HandbookApiServlet, "/api/*")                 │
│ ├── Creates: HandbookApiServlet instance                        │
│ ├── Pattern: "/api/*"                                          │
│ ├── Handles: RESTful API endpoints                             │
│ └── Auth: Requires valid API key                               │
│                                                                 │
│ context.mount(new SearchServlet, "/search/")                    │
│ ├── Creates: SearchServlet instance                             │
│ ├── Pattern: "/search/"                                        │
│ ├── Handles: ElasticSearch queries                             │
│ └── Purpose: Full-text search across handbooks                 │
└─────────────────────────────────────────────────────────────────┘
```

## 2. Request Processing - Step by Step

### 2.1 Example: API Request Flow

Let's trace this exact request: `GET /api/handbooks?org=9900`

```
STEP 1: CLIENT SENDS REQUEST
┌─────────────────────────────────────────────────────────────────┐
│ Browser/App makes HTTP request:                                 │
│                                                                 │
│ GET /api/handbooks?org=9900 HTTP/1.1                           │
│ Host: localhost:5100                                            │
│ X-API-Key: abc123def456                                         │
│ Accept: application/json                                        │
│                                                                 │
│ ┌─────────────┐                                                 │
│ │   Client    │ ──── HTTP Request ────►                        │
│ │ (Browser)   │                                                 │
│ └─────────────┘                                                 │
└─────────────────────────────────────────────────────────────────┘

STEP 2: JETTY RECEIVES REQUEST
┌─────────────────────────────────────────────────────────────────┐
│ Jetty server receives the HTTP request                         │
│                                                                 │
│ ┌─────────────┐    ┌─────────────┐                              │
│ │   Network   │───►│   Jetty     │                              │
│ │   Layer     │    │   Server    │                              │
│ │             │    │             │                              │
│ └─────────────┘    └─────────────┘                              │
│                                                                 │
│ Jetty parses:                                                   │
│ - HTTP method: GET                                              │
│ - URL path: /api/handbooks                                      │
│ - Query params: org=9900                                        │
│ - Headers: X-API-Key, Accept, etc.                             │
└─────────────────────────────────────────────────────────────────┘

STEP 3: URL PATTERN MATCHING
┌─────────────────────────────────────────────────────────────────┐
│ Jetty checks mounted servlets to find which one handles        │
│ the URL "/api/handbooks"                                        │
│                                                                 │
│ URL: /api/handbooks                                             │
│                                                                 │
│ ✗ "/helsesjekk/*"     → Does NOT match                         │
│ ✗ "/files/*"          → Does NOT match                         │
│ ✗ "/images/*"         → Does NOT match                         │
│ ✗ "/public/*"         → Does NOT match                         │
│ ✗ "/search/"          → Does NOT match                         │
│ ✓ "/api/*"            → MATCHES! ← This one wins               │
│                                                                 │
│ Result: HandbookApiServlet will handle this request            │
└─────────────────────────────────────────────────────────────────┘

STEP 4: SERVLET INSTANCE RETRIEVAL
┌─────────────────────────────────────────────────────────────────┐
│ Jetty gets the HandbookApiServlet instance                     │
│                                                                 │
│ ┌─────────────┐                                                 │
│ │   Servlet   │ Already created during startup                 │
│ │  Registry   │ Returns existing instance                      │
│ │             │                                                 │
│ └─────────────┘                                                 │
│                                                                 │
│ servlet = servletRegistry.get("/api/*")                         │
│ // Returns: HandbookApiServlet instance                        │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 Inside HandbookApiServlet - The Real Processing

```
STEP 5: SERVLET PROCESSING BEGINS
┌─────────────────────────────────────────────────────────────────┐
│ HandbookApiServlet.service() method is called                  │
│                                                                 │
│ ┌─────────────┐                                                 │
│ │ Servlet     │ service(request, response) {                   │
│ │ Container   │   // Determine HTTP method                     │
│ │             │   if (GET) → doGet()                           │
│ │             │   if (POST) → doPost()                         │
│ │             │   if (PUT) → doPut()                           │
│ │             │   if (DELETE) → doDelete()                     │
│ │             │ }                                               │
│ └─────────────┘                                                 │
│                                                                 │
│ Since method is GET → doGet() is called                        │
└─────────────────────────────────────────────────────────────────┘

STEP 6: BEFORE() METHOD EXECUTION
┌─────────────────────────────────────────────────────────────────┐
│ HandbookApiServlet.before() runs BEFORE the main logic         │
│                                                                 │
│ before() {                                                      │
│   // Set response content type                                 │
│   response.setContentType("application/json; charset=UTF-8")   │
│                                                                 │
│   // Enable CORS if needed                                     │
│   response.setHeader("Access-Control-Allow-Origin", "*")       │
│                                                                 │
│   // Log the request                                           │
│   logger.info(s"API request: ${request.getMethod} ${request.getRequestURI}")│
│ }                                                               │
└─────────────────────────────────────────────────────────────────┘

STEP 7: AUTHENTICATION CHECK
┌─────────────────────────────────────────────────────────────────┐
│ Method: externalOrgIdOrThrowIfMissingKeyOrNoAccess()            │
│                                                                 │
│ def externalOrgIdOrThrowIfMissingKeyOrNoAccess(): String = {    │
│                                                                 │
│   // 1. Extract API key from request                           │
│   val apiKey = request.getHeader("X-API-Key") orElse           │
│                request.getHeader("Authorization") orElse       │
│                params.get("apiKey")                             │
│                                                                 │
│   // 2. Check if API key exists                                │
│   if (apiKey.isEmpty) {                                        │
│     throw new UnauthorizedException("Missing API key")         │
│   }                                                             │
│                                                                 │
│   // 3. Look up organization for this API key                  │
│   val organizationsMap = componentRegistry.settings             │
│     .settingsFor(HandbookApiKey)                               │
│                                                                 │
│   // organizationsMap looks like:                              │
│   // Map("abc123def456" -> "9900", "xyz789" -> "1002")         │
│                                                                 │
│   val externalOrgId = organizationsMap.get(apiKey.get)         │
│                                                                 │
│   // 4. Validate the API key                                   │
│   if (externalOrgId.isEmpty) {                                 │
│     logger.warn(s"Invalid API key: ${apiKey.get}")             │
│     throw new UnauthorizedException("Invalid API key")         │
│   }                                                             │
│                                                                 │
│   externalOrgId.get  // Return "9900"                          │
│ }                                                               │
└─────────────────────────────────────────────────────────────────┘

STEP 8: ROUTE TO HANDLER METHOD
┌─────────────────────────────────────────────────────────────────┐
│ URL path "/handbooks" matches this route definition:            │
│                                                                 │
│ get("/handbooks") {                                             │
│   val externalOrgId = externalOrgIdOrThrowIfMissingKeyOrNoAccess()│
│   val handbookApiService = componentRegistry.handbookApiService │
│   val handbooks = handbookApiService                            │
│     .retrieveHandbooksWithMetadata(externalOrgId)               │
│   Map("handbooks" -> handbooks)                                 │
│ }                                                               │
│                                                                 │
│ This method will:                                               │
│ 1. Get organization ID (already done above)                    │
│ 2. Get the service from component registry                     │
│ 3. Call the service method                                     │
│ 4. Return a Map that gets converted to JSON                    │
└─────────────────────────────────────────────────────────────────┘
```

### 2.3 Service Layer Processing

```
STEP 9: COMPONENT REGISTRY LOOKUP
┌─────────────────────────────────────────────────────────────────┐
│ componentRegistry.handbookApiService                            │
│                                                                 │
│ ComponentRegistry is a singleton that holds all services       │
│                                                                 │
│ lazy val handbookApiService: HandbookApiService = {             │
│   if (_handbookApiService == null) {                            │
│     _handbookApiService = new HandbookApiServiceImpl()          │
│     // Inject dependencies                                     │
│     _handbookApiService.localHandbookService = this.localHandbookService│
│     _handbookApiService.centralHandbookService = this.centralHandbookService│
│     _handbookApiService.searchService = this.searchService     │
│   }                                                             │
│   _handbookApiService                                           │
│ }                                                               │
│                                                                 │
│ Returns: HandbookApiServiceImpl instance                        │
└─────────────────────────────────────────────────────────────────┘

STEP 10: SERVICE METHOD EXECUTION
┌─────────────────────────────────────────────────────────────────┐
│ HandbookApiService.retrieveHandbooksWithMetadata("9900")        │
│                                                                 │
│ def retrieveHandbooksWithMetadata(externalOrgId: String) = {    │
│                                                                 │
│   // 1. Get basic handbooks for this organization              │
│   val handbooks = localHandbookService                         │
│     .retrieveHandbooksForExternalOrganization(externalOrgId)   │
│                                                                 │
│   // 2. For each handbook, add metadata                        │
│   handbooks.map { handbook =>                                  │
│     val chapterCount = localHandbookService                    │
│       .countChaptersForHandbook(handbook.id.get)               │
│     val sectionCount = localHandbookService                    │
│       .countSectionsForHandbook(handbook.id.get)               │
│     val lastModified = handbook.updatedDate                    │
│                                                                 │
│     // Create enriched object                                  │
│     TreeStructureHandbook(                                     │
│       id = handbook.id,                                        │
│       title = handbook.title,                                  │
│       externalOrgId = handbook.externalOrgId,                  │
│       chapters = createTreeStructure(handbook.id.get),         │
│       chapterCount = chapterCount,                             │
│       sectionCount = sectionCount,                             │
│       lastModified = lastModified                              │
│     )                                                           │
│   }                                                             │
│ }                                                               │
└─────────────────────────────────────────────────────────────────┘

STEP 11: DATABASE ACCESS
┌─────────────────────────────────────────────────────────────────┐
│ LocalHandbookService.retrieveHandbooksForExternalOrganization() │
│                                                                 │
│ def retrieveHandbooksForExternalOrganization(externalOrgId: String) = {│
│                                                                 │
│   // This method uses the inTransaction wrapper                │
│   inTransaction {                                               │
│                                                                 │
│     // 1. Get database connection from pool                    │
│     val connection = connectionManager.getConnection()          │
│                                                                 │
│     // 2. Prepare SQL query                                    │
│     val sql = """                                               │
│       SELECT h.id, h.title, h.description,                     │
│              h.created_date, h.updated_date,                   │
│              h.imported_handbook_id, h.pending_change,         │
│              h.external_org_id, h.is_public                    │
│       FROM handbook h                                           │
│       WHERE h.external_org_id = ?                              │
│         AND h.deleted = false                                  │
│       ORDER BY h.title ASC                                     │
│     """                                                         │
│                                                                 │
│     // 3. Execute query                                        │
│     val preparedStatement = connection.prepareStatement(sql)    │
│     preparedStatement.setString(1, externalOrgId) // "9900"    │
│     val resultSet = preparedStatement.executeQuery()           │
│                                                                 │
│     // 4. Convert ResultSet to Handbook objects               │
│     val handbooks = mutable.ListBuffer[Handbook]()             │
│     while (resultSet.next()) {                                 │
│       handbooks += Handbook(                                   │
│         id = Some(resultSet.getString("id")),                  │
│         title = resultSet.getString("title"),                  │
│         description = Option(resultSet.getString("description")),│
│         createdDate = Option(resultSet.getTimestamp("created_date")),│
│         updatedDate = Option(resultSet.getTimestamp("updated_date")),│
│         importedHandbookId = Option(resultSet.getString("imported_handbook_id")),│
│         pendingChange = resultSet.getBoolean("pending_change"), │
│         externalOrgId = resultSet.getString("external_org_id"), │
│         isPublic = resultSet.getBoolean("is_public")           │
│       )                                                         │
│     }                                                           │
│                                                                 │
│     handbooks.toList                                           │
│   }                                                             │
│ }                                                               │
└─────────────────────────────────────────────────────────────────┘
```

### 2.4 Response Generation

```
STEP 12: RESPONSE OBJECT CREATION
┌─────────────────────────────────────────────────────────────────┐
│ Back in HandbookApiServlet, the handler method returns:        │
│                                                                 │
│ Map("handbooks" -> handbooks)                                   │
│                                                                 │
│ This creates a Scala Map like:                                 │
│ Map(                                                            │
│   "handbooks" -> List(                                         │
│     TreeStructureHandbook(                                     │
│       id = Some("123"),                                        │
│       title = "Safety Manual",                                 │
│       externalOrgId = "9900",                                  │
│       chapters = List(...),                                    │
│       chapterCount = 5,                                        │
│       sectionCount = 23,                                       │
│       lastModified = Some(DateTime.now)                        │
│     ),                                                          │
│     TreeStructureHandbook(...)                                 │
│   )                                                             │
│ )                                                               │
└─────────────────────────────────────────────────────────────────┘

STEP 13: JSON SERIALIZATION
┌─────────────────────────────────────────────────────────────────┐
│ JsonSupport trait automatically converts the Map to JSON       │
│                                                                 │
│ The JsonSupport trait uses Json4s library:                     │
│                                                                 │
│ implicit val jsonFormats: Formats = DefaultFormats ++ JodaTimeSerializers.all│
│                                                                 │
│ def toJson(obj: Any): String = {                                │
│   write(obj)(jsonFormats)                                       │
│ }                                                               │
│                                                                 │
│ Result JSON:                                                    │
│ {                                                               │
│   "handbooks": [                                               │
│     {                                                           │
│       "id": "123",                                             │
│       "title": "Safety Manual",                               │
│       "externalOrgId": "9900",                                 │
│       "chapters": [...],                                       │
│       "chapterCount": 5,                                       │
│       "sectionCount": 23,                                      │
│       "lastModified": "2024-01-15T10:30:00.000Z"              │
│     }                                                           │
│   ]                                                             │
│ }                                                               │
└─────────────────────────────────────────────────────────────────┘

STEP 14: HTTP RESPONSE CREATION
┌─────────────────────────────────────────────────────────────────┐
│ Scalatra framework creates the HTTP response:                  │
│                                                                 │
│ HTTP/1.1 200 OK                                                 │
│ Content-Type: application/json; charset=UTF-8                  │
│ Content-Length: 1234                                            │
│ Access-Control-Allow-Origin: *                                  │
│                                                                 │
│ {                                                               │
│   "handbooks": [...]                                           │
│ }                                                               │
└─────────────────────────────────────────────────────────────────┘

STEP 15: RESPONSE SENT TO CLIENT
┌─────────────────────────────────────────────────────────────────┐
│ Jetty sends the HTTP response back to the client               │
│                                                                 │
│ ┌─────────────┐                                                 │
│ │   Client    │ ◄──── HTTP Response ────                       │
│ │ (Browser)   │                                                 │
│ └─────────────┘                                                 │
│                                                                 │
│ Client receives:                                                │
│ - Status: 200 OK                                               │
│ - JSON data with handbooks                                     │
│ - Can now display the data in UI                               │
└─────────────────────────────────────────────────────────────────┘
```

## 3. Complete Flow Summary

### 3.1 The Entire Request Journey

```
┌─────────────────────────────────────────────────────────────────┐
│ COMPLETE REQUEST FLOW SUMMARY                                   │
│                                                                 │
│ 1. Client Request                                               │
│    └── GET /api/handbooks with API key                         │
│                                                                 │
│ 2. Jetty Server                                                 │
│    └── Receives HTTP request, parses headers/params            │
│                                                                 │
│ 3. URL Pattern Matching                                         │
│    └── "/api/*" matches → HandbookApiServlet                   │
│                                                                 │
│ 4. Servlet Processing                                           │
│    ├── before() method sets JSON content type                  │
│    ├── Authentication validates API key                        │
│    ├── Route handler method executes                           │
│    └── Returns Map object                                      │
│                                                                 │
│ 5. Service Layer                                                │
│    ├── ComponentRegistry provides service instance             │
│    ├── HandbookApiService processes business logic             │
│    └── Calls LocalHandbookService for data                     │
│                                                                 │
│ 6. Data Access                                                  │
│    ├── LocalHandbookService executes SQL query                 │
│    ├── Database returns ResultSet                              │
│    └── Converts to Handbook domain objects                     │
│                                                                 │
│ 7. Response Processing                                          │
│    ├── Service enriches data with metadata                     │
│    ├── Returns TreeStructureHandbook objects                   │
│    └── Servlet wraps in Map                                    │
│                                                                 │
│ 8. JSON Serialization                                           │
│    ├── JsonSupport trait converts Map to JSON                  │
│    ├── Json4s library handles serialization                    │
│    └── Creates JSON string                                     │
│                                                                 │
│ 9. HTTP Response                                                │
│    ├── Scalatra creates HTTP response                          │
│    ├── Sets appropriate headers                                │
│    └── Includes JSON body                                      │
│                                                                 │
│ 10. Client Receives                                             │
│     └── Browser/app gets JSON data and displays it             │
└─────────────────────────────────────────────────────────────────┘
```

### 3.2 Key Classes and Their Roles

```
┌─────────────────────────────────────────────────────────────────┐
│ MAIN CLASSES INVOLVED IN API REQUEST PROCESSING                │
│                                                                 │
│ ┌─────────────────────┐                                         │
│ │ ScalatraBootstrap   │ ← Application entry point              │
│ │                     │   Sets up all servlets                 │
│ └─────────────────────┘                                         │
│                                                                 │
│ ┌─────────────────────┐                                         │
│ │ HandbookApiServlet  │ ← Handles /api/* requests              │
│ │                     │   API key authentication               │
│ │                     │   Route definitions                    │
│ └─────────────────────┘                                         │
│                                                                 │
│ ┌─────────────────────┐                                         │
│ │ ComponentRegistry   │ ← Dependency injection container       │
│ │                     │   Provides service instances           │
│ └─────────────────────┘                                         │
│                                                                 │
│ ┌─────────────────────┐                                         │
│ │ HandbookApiService  │ ← Business logic layer                 │
│ │                     │   Processes API requests               │
│ └─────────────────────┘                                         │
│                                                                 │
│ ┌─────────────────────┐                                         │
│ │ LocalHandbookService│ ← Data access layer                    │
│ │                     │   Database operations                  │
│ └─────────────────────┘                                         │
│                                                                 │
│ ┌─────────────────────┐                                         │
│ │ JsonSupport         │ ← Response formatting                  │
│ │                     │   JSON serialization                   │
│ └─────────────────────┘                                         │
└─────────────────────────────────────────────────────────────────┘
```

This detailed flow shows exactly how your Scala/Scalatra application processes API requests from start to finish, with every class, method, and data transformation clearly explained.