import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "react-router-dom";
import { debounce } from "lodash";
import {
  Button,
  Container,
  Section,
  Columns,
  Column,
  Hero,
  Menu,
  ImageHero,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";

import { useSession } from "@/store/services/session/hooks";
import {
  useLazySearchEditorQuery,
  useResetSearchIndexesMutation,
  useResetManySearchIndexesMutation,
} from "@/store/services/search";
import { useGetLocalHandbooksQuery } from "@/store/services/handbook";
import type { SearchResult, SearchHit } from "@/types";

import { SearchField } from "../../features/shared/SearchField";
import { SearchResult as SearchResultComponent } from "../../features/shared/SearchResult";
import { SearchPagination } from "../../features/shared/SearchPagination";

// Generate React Router 7 link for the different search results
function createLink(hit: SearchHit): string {
  if (hit.isChapter) {
    return `/editor/${hit.handbookId}/chapter/${hit.id}`;
  }
  if (hit.isSection) {
    return `/editor/${hit.handbookId}/section/${hit.id}`;
  }
  return `/editor/${hit.id}/`;
}

export const SearchPage: React.FC = () => {
  const { session } = useSession();
  const [searchParams, setSearchParams] = useSearchParams();

  // Local state
  const [searchQuery, setSearchQuery] = useState(searchParams.get("q") || "");
  const [handbookId, setHandbookId] = useState<string | undefined>(
    searchParams.get("id") || undefined
  );
  const [currentPage, setCurrentPage] = useState(
    parseInt(searchParams.get("p") || "1", 10)
  );
  const [searchResult, setSearchResult] = useState<SearchResult | null>(null);
  const [masterResetComplete, setMasterResetComplete] = useState(false);

  // API hooks
  const [searchTrigger, { isFetching: isSearching }] =
    useLazySearchEditorQuery();
  const { data: handbooksData } = useGetLocalHandbooksQuery();
  const [resetIndexes, { isLoading: isResetting }] =
    useResetSearchIndexesMutation();
  const [resetManyIndexes, { isLoading: isMasterResetting }] =
    useResetManySearchIndexesMutation();

  const updateURL = useCallback(
    (query: string, page: number, handbookFilter?: string) => {
      const params = new URLSearchParams();
      if (query) params.set("q", query);
      if (page > 1) params.set("p", page.toString());
      if (handbookFilter) params.set("id", handbookFilter);

      setSearchParams(params);
    },
    [setSearchParams]
  );

  const doSearch = useCallback(async () => {
    const query = searchQuery.toLowerCase();
    if (!query) return;

    updateURL(query, currentPage, handbookId);

    try {
      const result = await searchTrigger({
        query,
        page: currentPage,
        handbookId,
      }).unwrap();

      setSearchResult(result);
    } catch (error) {
      toast.error("En feil inntraff under søking.");
      console.error("Search error:", error);
    }
  }, [searchQuery, currentPage, handbookId, searchTrigger, updateURL]);

  const debouncedSearch = useCallback(
    debounce(() => {
      if (searchQuery) {
        doSearch();
      }
    }, 300),
    [doSearch, searchQuery]
  );

  // Sync URL params with local state on mount
  useEffect(() => {
    if (searchQuery) {
      doSearch();
    }
  }, [searchQuery, doSearch]);

  // Trigger search when handbook filter or page changes
  useEffect(() => {
    if (searchQuery) {
      doSearch();
    }
  }, [handbookId, currentPage, searchQuery, doSearch]);

  // Trigger debounced search when query changes
  useEffect(() => {
    if (searchQuery) {
      debouncedSearch();
    }
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, debouncedSearch]);

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    setCurrentPage(1);
    debouncedSearch.cancel();
    doSearch();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setSearchResult(null);
    setCurrentPage(1);
  };

  const handleHandbookClick = useCallback(
    (event: React.MouseEvent, selectedHandbookId?: string) => {
      event.preventDefault();
      setHandbookId(selectedHandbookId);
      setCurrentPage(1);
    },
    []
  );

  const onPageClick = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const doReset = async () => {
    try {
      await resetIndexes().unwrap();
      toast.success("Reindekserte håndbøker");
    } catch (error) {
      toast.error("Kunne ikke reindeksere håndbøker");
      console.error("Reset error:", error);
    }
  };

  const doMasterReset = async () => {
    if (!session?.user?.organizations) return;

    setMasterResetComplete(false);
    console.log(
      "Started resetting indexes for organizations ",
      session.user.organizations
    );

    try {
      await resetManyIndexes(
        session.user.organizations
      ).unwrap();

      setMasterResetComplete(true);
    } catch (error) {
      toast.error("Kunne ikke reindeksere håndbøker for alle organisasjoner");
      console.error("Master reset error:", error);
    }
  };

  if (!session) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <h1>Loading session...</h1>
        <p>Please wait while we load your session information.</p>
      </div>
    );
  }

  const handbooks = handbooksData?.handbooks || [];
  const activeHandbookId = handbookId;

  document.title = "Søk - KF Håndbøker";

  return (
    <div>
      <ImageHero color="primary" imageUrl={session.bannerUrl}>
        <Hero.Body>
          <Container>
            <SearchField
              onChange={handleChange}
              onSubmit={handleSubmit}
              query={searchQuery}
              autoFocus
            />
          </Container>
        </Hero.Body>
      </ImageHero>
      <Section>
        <Container>
          <Columns>
            <Column size="1/4">
              <Menu>
                <Menu.Label>Håndbøker</Menu.Label>
                <Menu.List>
                  <Menu.Item
                    onClick={(event: React.MouseEvent) =>
                      handleHandbookClick(event)
                    }
                    active={!activeHandbookId}
                    href=""
                    role="button"
                  >
                    Alle
                  </Menu.Item>
                  {handbooks.map((handbook) => (
                    <Menu.Item
                      active={activeHandbookId === handbook.id}
                      key={handbook.id}
                      onClick={(event: React.MouseEvent) =>
                        handleHandbookClick(event, handbook.id)
                      }
                      role="button"
                      href=""
                    >
                      {handbook.title}
                    </Menu.Item>
                  ))}
                </Menu.List>
              </Menu>
              {session.isKfAdmin && (
                <div style={{ marginTop: "1rem" }}>
                  <Menu.Label style={{ marginBottom: "0.5rem" }}>
                    Administrator
                  </Menu.Label>
                  <Button
                    outlined
                    fullWidth
                    onClick={doReset}
                    loading={isResetting}
                    color="black"
                  >
                    Reindekser håndbøker
                  </Button>
                </div>
              )}
              {session.isKfAdmin && (
                <div style={{ marginTop: "1rem" }}>
                  <Button
                    outlined
                    onClick={doMasterReset}
                    loading={isMasterResetting}
                    color="black"
                  >
                    Reindekser håndbøker for all organisasjoner
                  </Button>

                  <div style={{ marginTop: "1rem" }}>
                    <i>
                      NB! Bare organisasjoner denne brukeren har tilgang til vil
                      bli reindeksert, og dette vil kunne ta lang tid
                    </i>
                    {masterResetComplete && (
                      <div className="masterResetComplete">
                        Reindekseringen er ferdig. Se i konsoll for status
                      </div>
                    )}
                  </div>
                </div>
              )}
            </Column>
            <Column>
              <Columns>
                <Column>
                  {isSearching && (
                    <div style={{ textAlign: "center", padding: "2rem" }}>
                      <p>Søker...</p>
                    </div>
                  )}
                  {searchResult && !isSearching && (
                    <SearchResultComponent
                      linkFunc={createLink}
                      query={searchQuery}
                      result={searchResult}
                    />
                  )}
                </Column>
              </Columns>
              <Columns>
                <Column>
                  {searchResult && !isSearching && (
                    <SearchPagination
                      onPageClick={onPageClick}
                      result={searchResult}
                    />
                  )}
                </Column>
              </Columns>
            </Column>
          </Columns>
        </Container>
      </Section>
    </div>
  );
};
