import React from "react";
import { Link } from "react-router-dom";
import { Card, Icon, Section, Container, Title } from "kf-bui";
import moment from "moment";

import { usePrefixedTranslation } from "@/libs/i18n";
import { usePendingItems } from "@/store/services/handbook/hooks";

export const PendingPage: React.FC = () => {
  const t = usePrefixedTranslation("editor.containers.PendingPage");
  const pendingHandbooks = usePendingItems();

  React.useEffect(() => {
    document.title = `${t("title")} - K<PERSON>`;
  }, [t]);

  return (
    <Section>
      <Container>
        <Title textCentered={pendingHandbooks.length === 0}>
          {pendingHandbooks.length > 0 ? (
            t("header")
          ) : (
            <span>
              {t("noChanges")} <Icon icon="smile-o" size="small" />
            </span>
          )}
        </Title>
        {pendingHandbooks.length > 0 && (
          <PendingContent pending={pendingHandbooks} t={t} />
        )}
      </Container>
    </Section>
  );
};

const PendingContent: React.FC<{
  pending: ReturnType<typeof usePendingItems>;
  t: ReturnType<typeof usePrefixedTranslation>;
}> = ({ pending, t }) => {
  return (
    <div>
      {pending.map((handbook) => (
        <Card key={handbook.id}>
          <Card.Content>
            <Icon icon="book" size="small" />{" "}
            {handbook.pendingChange ? (
              <Link to={`/merge/handbook/${handbook.id}/`}>
                {handbook.title}
              </Link>
            ) : (
              <span>{handbook.title}</span>
            )}
          </Card.Content>

          {handbook.chapters.length > 0 && (
            <Card.Content>
              {handbook.chapters.map((chapter) => (
                <div key={chapter.id}>
                  <Icon
                    icon="RegBookmark"
                    size="small"
                    style={{ marginRight: "4px" }}
                  />
                  <Link
                    to={
                      chapter.pendingChange
                        ? `/merge/chapter/${chapter.id}/`
                        : `/editor/${chapter.handbookId}/chapter/${chapter.id}/`
                    }
                  >
                    {chapter.title}
                  </Link>
                  <div className="pending-timestamp">
                    {`${t("centralChange")} ${moment(chapter.pendingChangeUpdatedDate).format("DD.MM.YYYY")}`}
                  </div>
                </div>
              ))}
            </Card.Content>
          )}

          {handbook.sections.length > 0 && (
            <Card.Content className="pending-sections">
              {handbook.sections.map((section) => (
                <div key={section.id} className="pending-item">
                  <Icon
                    icon="RegFileLines"
                    size="small"
                    style={{ marginRight: "4px" }}
                  />
                  <Link
                    to={
                      section.pendingTitleChange || section.pendingTextChange
                        ? `/merge/section/${section.id}/`
                        : `/editor/${section.handbookId}/section/${section.id}/`
                    }
                  >
                    {section.title}
                  </Link>
                  <div className="pending-timestamp">
                    {`${t("centralChange")} ${moment(section.pendingChangeUpdatedDate).format("DD.MM.YYYY")}`}
                  </div>
                </div>
              ))}
            </Card.Content>
          )}
        </Card>
      ))}
    </div>
  );
};
