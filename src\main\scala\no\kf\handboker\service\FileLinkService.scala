package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.handboker.model.local.{FileLink, FileLinksInput}
import no.kf.handboker.model.{CountResponse}
import no.kf.handboker.repository.FileLinkRepositoryComponent
import no.kf.util.Logging

trait FileLinkServiceComponent extends TransactionManager with Logging{
  this: FileLinkRepositoryComponent =>

  val fileLinkService: FileLinkService

  class FileLinkServiceImpl extends FileLinkService {
    override def retrieveFileLinks(ownerId: String, belongsTo: String): List[FileLink] = inTransaction {
      fileLinkRepository.retrieveFileLinks(ownerId, belongsTo)
    }

    override def retrieveFileLinkCount(ownerId: String, belongsTo: String): CountResponse = inTransaction {
      new CountResponse(fileLinkRepository.retrieveFileLinkCount(ownerId, belongsTo))
    }

    override def deleteFileLink(linkId: String): Unit = inTransaction {
      fileLinkRepository.deleteLink(linkId)
    }

    override def persistFileLinks(fileLinksInput: FileLinksInput, user: String): List[FileLink] = inTransaction {
      fileLinksInput.newlyAdded.foreach(n=>{
        val linkToSave = new FileLink(Option("Empty"), n.title, fileLinksInput.belongsTo, fileLinksInput.ownerId, n.url, n.sortOrder, n.size);
        fileLinkRepository.persistLink(linkToSave, user)
      })

      fileLinksInput.removed.foreach(r=>{
        fileLinkRepository.deleteLink(r)
      })
      retrieveFileLinks(fileLinksInput.ownerId, fileLinksInput.belongsTo)
    }
  }
}

trait FileLinkService {
  def retrieveFileLinks(ownerId: String, belongsTo: String): List[FileLink]
  def persistFileLinks(fileLinksInput: FileLinksInput, user: String): List[FileLink]
  def deleteFileLink(linkId: String): Unit
  def retrieveFileLinkCount(ownerId: String, belongsTo: String): CountResponse
}
