import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Title, Subtitle, Columns, Column, Button } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useAppSelector, useAppDispatch } from "@/store";
import { Spinner } from "@/shared/components/Spinner";
import { toast } from "@/shared/components/Toast";
import {
  selectSelectedLocalItem,
  clearLocalSelection,
} from "@/store/slices/localTreeSlice";
import {
  useGetLocalChapterQuery,
  useGetLocalSectionQuery,
  useSaveLocalChapterMutation,
  useSaveLocalSectionMutation,
} from "@/store/services/handbook/localHandbookApi";
import type { Chapter, Section } from "@/types";
import {
  localSameLocation,
  localSectionToHandbook,
  localSectionToSection,
  isValidLocalMoveTarget,
  wouldCreateCircularReference,
  isDescendantOfMovedItem,
} from "../../../utils/localMoveValidation";

export const MoveLocalChapterOrSection: React.FC = () => {
  const t = usePrefixedTranslation("editor.containers.MoveChapterOrSelection");
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { chapterId, sectionId } = useParams<{
    chapterId?: string;
    sectionId?: string;
  }>();

  const selectedLocalItem = useAppSelector(selectSelectedLocalItem);
  const [moveError, setMoveError] = useState<string | null>(null);

  const {
    data: chapter,
    isLoading: chapterLoading,
    error: chapterError,
  } = useGetLocalChapterQuery(chapterId!, { skip: !chapterId });

  const {
    data: section,
    isLoading: sectionLoading,
    error: sectionError,
  } = useGetLocalSectionQuery(sectionId!, { skip: !sectionId });

  const [saveLocalChapter, { isLoading: chapterUpdateLoading }] =
    useSaveLocalChapterMutation();
  const [saveLocalSection, { isLoading: sectionUpdateLoading }] =
    useSaveLocalSectionMutation();

  const movedItem = chapter || section;
  const isLoading = chapterLoading || sectionLoading;
  const hasError = chapterError || sectionError;
  const isUpdating = chapterUpdateLoading || sectionUpdateLoading;

  useEffect(() => {
    return () => {
      dispatch(clearLocalSelection());
    };
  }, [dispatch]);

  if (isLoading) {
    return (
      <div>
        <Title>Flytt</Title>
        <div className="loading-container">
          <Spinner />
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div>
        <Title>Flytt</Title>
        <Subtitle>Feil ved lasting av data</Subtitle>
        <Button onClick={() => navigate(-1)} size="medium">
          Gå tilbake
        </Button>
      </div>
    );
  }

  if (!movedItem) {
    return (
      <div>
        <Title>Flytt</Title>
        <Subtitle>Fant ikke elementet som skal flyttes</Subtitle>
        <Button onClick={() => navigate(-1)} size="medium">
          Gå tilbake
        </Button>
      </div>
    );
  }

  const validationResult = selectedLocalItem
    ? isValidLocalMoveTarget(movedItem, selectedLocalItem)
    : { isValid: false, reason: "Ingen destinasjon valgt" };

  const sameLoc =
    selectedLocalItem && localSameLocation(selectedLocalItem, movedItem);
  const sectionToHandbook =
    selectedLocalItem && localSectionToHandbook(selectedLocalItem, movedItem);
  const sectionToSection =
    selectedLocalItem && localSectionToSection(selectedLocalItem, movedItem);
  const circularRef =
    selectedLocalItem &&
    wouldCreateCircularReference(selectedLocalItem, movedItem);
  const descendant =
    selectedLocalItem && isDescendantOfMovedItem(selectedLocalItem, movedItem);

  const showMoveButton = selectedLocalItem && validationResult.isValid;

  const handleMove = async () => {
    if (!selectedLocalItem || !validationResult.isValid) return;

    setMoveError(null);

    try {
      const parentIsHandbook = selectedLocalItem.type === "HANDBOOK";
      const targetHandbookId =
        selectedLocalItem.type === "HANDBOOK"
          ? selectedLocalItem.id
          : selectedLocalItem.handbookId;
      const newParentId = parentIsHandbook ? undefined : selectedLocalItem.id;

      const oldParentId = movedItem.parentId;
      const oldHandbookId = movedItem.handbookId;
      const isMoved =
        oldParentId !== newParentId || oldHandbookId !== targetHandbookId;

      const updatedItem = {
        ...movedItem,
        handbookId: targetHandbookId,
        parentId: newParentId,
      };

      let result;

      if (updatedItem.type === "SECTION") {
        result = await saveLocalSection({
          section: updatedItem as Section,
          centralChange: false,
          centralTextChange: false,
        }).unwrap();
        toast.success("Avsnittet ble flyttet.");
      } else {
        result = await saveLocalChapter({
          chapter: updatedItem as Chapter,
          centralChange: false,
        }).unwrap();
        toast.success("Kapittelet ble flyttet.");
      }

      if (isMoved) {
        const itemType = movedItem.type === "SECTION" ? "section" : "chapter";
        const newPath = `/editor/${targetHandbookId}/${itemType}/${result.id}/`;
        navigate(newPath, { replace: true });
      } else {
        navigate(-1);
      }
    } catch (error: unknown) {
      console.error("Failed to move item:", error);

      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data
          ?.message ||
        (error as { message?: string })?.message ||
        "Det oppstod en feil ved flytting av elementet.";
      setMoveError(errorMessage);
      toast.error(`Feil ved flytting: ${errorMessage}`);
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <div>
      <Title>Flytt</Title>
      <Subtitle>
        {t("moveElement", {
          title: <em>{movedItem.title}</em>,
        })}
      </Subtitle>
      <hr />

      {selectedLocalItem ? (
        <Subtitle>Ny plassering valgt: {selectedLocalItem.title}</Subtitle>
      ) : null}

      {moveError && (
        <Subtitle style={{ color: "red" }}>Feil: {moveError}</Subtitle>
      )}

      {sectionToHandbook && (
        <Subtitle>
          Du kan ikke plassere en seksjon direkte under en håndbok
        </Subtitle>
      )}

      {sectionToSection && (
        <Subtitle>
          Du kan ikke plassere en seksjon under en annen seksjon
        </Subtitle>
      )}

      {sameLoc && (
        <Subtitle>
          {movedItem.title} er allerede i {selectedLocalItem?.title}
        </Subtitle>
      )}

      {(circularRef || descendant) && (
        <Subtitle>
          Du kan ikke flytte et kapittel til et av sine egne underkapitler
        </Subtitle>
      )}

      <Columns responsive="mobile">
        <Column>
          <Button onClick={handleCancel}>Avbryt</Button>
        </Column>

        <Column narrow>
          <Button
            color="primary"
            onClick={handleMove}
            loading={isUpdating}
            disabled={isUpdating || !showMoveButton}
          >
            Flytt
          </Button>
        </Column>
      </Columns>
    </div>
  );
};
