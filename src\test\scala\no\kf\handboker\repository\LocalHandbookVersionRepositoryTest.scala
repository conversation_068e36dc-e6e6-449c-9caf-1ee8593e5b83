package no.kf.handboker.repository

import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.scalatest.{BeforeAndAfterEach, FunSuite}

@RunWith(classOf[JUnitRunner])
class LocalHandbookVersionRepositoryTest extends FunSuite with DbTestHandler with BeforeAndAfterEach {

  val versionRepository: LocalHandbookVersionRepository = componentRegistry.localHandbookVersionRepository
  val handbookRepository: HandbookRepository = componentRegistry.handbookRepository

  transactedTest("that we can store a chapter version") {
    val handbookId = handbookRepository.persistHandbook(Handbook(None, "title", None, "9900")).id.get
    val chapter = handbookRepository.persistChapter(Chapter(None, "title", None, None, handbookId, None, None, updatedBy = Some("user"), createdBy = Some("user")))
    val version = versionRepository.insertLocalHandbookChapterVersion(chapter, DateTime.now)
    val versions = versionRepository.retrieveVersionsOfChapter(chapter.id.get)
    assert(versions.nonEmpty)
    assert(version.copy(pendingChangeUpdatedDate = None, versionOf = chapter.id) == versions.head)
    assert(version.updatedBy.contains("user"))
    assert(version.createdBy.contains("user"))
  }

  transactedTest("that we can store a section version") {
    val handbookId = handbookRepository.persistHandbook(Handbook(None, "title", None, "9900")).id.get
    val chapterId = handbookRepository.persistChapter(Chapter(None, "title", None, None, handbookId, None, None)).id.get
    val section = handbookRepository.persistSection(Section(None, "title", Some("text"), None, None, handbookId, chapterId, None, updatedBy = Some("user"), createdBy = Some("user"), textUpdatedBy = Some("user")))
    val version = versionRepository.insertLocalHandbookSectionVersion(section, DateTime.now)
    val versions = versionRepository.retrieveVersionsOfSection(section.id.get)
    assert(versions.nonEmpty)
    assert(version.copy(pendingChangeUpdatedDate = None, text = None, versionOf = section.id) == versions.head)
    assert(version.updatedBy.contains("user"))
    assert(version.createdBy.contains("user"))
    assert(version.textUpdatedBy.contains("user"))
  }

  transactedTest("that we can get chapter version for a specific date") {
    val handbookId = handbookRepository.persistHandbook(Handbook(None, "title", None, "9900")).id.get
    val chapter = handbookRepository.persistChapter(Chapter(None, "title4", None, None, handbookId, None, None, updatedBy = Some("user"), createdBy = Some("user")))
    val now = DateTime.now
    val chapterUpdated = chapter.updatedDate.get
    val version1 = versionRepository.insertLocalHandbookChapterVersion(chapter.copy(title = "title1", updatedDate = Some(chapterUpdated)), now)
    val version2 = versionRepository.insertLocalHandbookChapterVersion(chapter.copy(title = "title2", updatedDate = Some(chapterUpdated.plus(5))), now.plus(5))
    val version3 = versionRepository.insertLocalHandbookChapterVersion(chapter.copy(title = "title3", updatedDate = Some(chapterUpdated.plus(10))), now.plus(10))
    assert(versionRepository.retrieveVersionsOfChapter(chapter.id.get).size == 3)
    assert(versionRepository.retrieveChapterVersionsInHandbook(handbookId, chapterUpdated.minus(1)).isEmpty)
    assert(versionRepository.retrieveChapterVersionsInHandbook(handbookId, chapterUpdated).contains(version1.copy(pendingChangeUpdatedDate = None, versionOf = chapter.id)))
    assert(versionRepository.retrieveChapterVersionsInHandbook(handbookId, chapterUpdated.plus(1)).contains(version1.copy(pendingChangeUpdatedDate = None, versionOf = chapter.id)))
    assert(versionRepository.retrieveChapterVersionsInHandbook(handbookId, chapterUpdated.plus(6)).contains(version2.copy(pendingChangeUpdatedDate = None, versionOf = chapter.id)))
    assert(versionRepository.retrieveChapterVersionsInHandbook(handbookId, chapterUpdated.plus(11)).contains(version3.copy(pendingChangeUpdatedDate = None, versionOf = chapter.id)))
  }

  transactedTest("that we can get section version for a specific date") {
    val handbookId = handbookRepository.persistHandbook(Handbook(None, "title", None, "9900")).id.get
    val chapterId = handbookRepository.persistChapter(Chapter(None, "title", None, None, handbookId, None, None)).id.get
    val section = handbookRepository.persistSection(Section(None, "title4", Some("text4"), None, None, handbookId, chapterId, None, updatedBy = Some("user"), createdBy = Some("user"), textUpdatedBy = Some("user")))
    val now = DateTime.now
    val sectionUpdated = section.updatedDate.get
    val version1 = versionRepository.insertLocalHandbookSectionVersion(section.copy(title = "title1", updatedDate = Some(sectionUpdated)), now)
    val version2 = versionRepository.insertLocalHandbookSectionVersion(section.copy(title = "title2", updatedDate = Some(sectionUpdated.plus(5))), now.plus(5))
    val version3 = versionRepository.insertLocalHandbookSectionVersion(section.copy(title = "title3", updatedDate = Some(sectionUpdated.plus(10))), now.plus(10))
    assert(versionRepository.retrieveVersionsOfSection(section.id.get).size == 3)
    assert(versionRepository.retrieveSectionVersionsInHandbook(handbookId, sectionUpdated.minus(1)).isEmpty)
    assert(versionRepository.retrieveSectionVersionsInHandbook(handbookId, sectionUpdated).contains(version1.copy(pendingChangeUpdatedDate = None, text = None, versionOf = section.id)))
    assert(versionRepository.retrieveSectionVersionsInHandbook(handbookId, sectionUpdated.plus(1)).contains(version1.copy(pendingChangeUpdatedDate = None, text = None, versionOf = section.id)))
    assert(versionRepository.retrieveSectionVersionsInHandbook(handbookId, sectionUpdated.plus(6)).contains(version2.copy(pendingChangeUpdatedDate = None, text = None, versionOf = section.id)))
    assert(versionRepository.retrieveSectionVersionsInHandbook(handbookId, sectionUpdated.plus(11)).contains(version3.copy(pendingChangeUpdatedDate = None, text = None, versionOf = section.id)))
  }

  transactedTest("That we cat fetch section versions without text") {
    val handbookId = handbookRepository.persistHandbook(Handbook(None, "title", None, "9900")).id.get
    val chapterId = handbookRepository.persistChapter(Chapter(None, "title", None, None, handbookId, None, None)).id.get
    val section = handbookRepository.persistSection(Section(None, "title4", Some("text4"), None, None, handbookId, chapterId, None, updatedBy = Some("user"), createdBy = Some("user"), textUpdatedBy = Some("user")))
    val now = DateTime.now
    versionRepository.insertLocalHandbookSectionVersion(section.copy(title = "title1"), now)
    versionRepository.insertLocalHandbookSectionVersion(section.copy(title = "title2"), now.plus(5))
    versionRepository.insertLocalHandbookSectionVersion(section.copy(title = "title3"), now.plus(10))
    assert(versionRepository.retrieveVersionsOfSection(section.id.get).forall(c => c.text.isEmpty))
    assert(versionRepository.retrieveSectionVersionsInHandbook(handbookId, now.plus(11)).forall(c => c.text.isEmpty))
  }

  transactedTest("That we cat fetch a section version with version id") {
    val handbookId = handbookRepository.persistHandbook(Handbook(None, "title", None, "9900")).id.get
    val chapterId = handbookRepository.persistChapter(Chapter(None, "title", None, None, handbookId, None, None)).id.get
    val section = handbookRepository.persistSection(Section(None, "title4", Some("text4"), None, None, handbookId, chapterId, None, updatedBy = Some("user"), createdBy = Some("user"), textUpdatedBy = Some("user")))
    val now = DateTime.now
    val version1 = versionRepository.insertLocalHandbookSectionVersion(section.copy(title = "title1"), now)
    assert(versionRepository.retrieveSectionVersion(version1.id.get).isDefined)
    assert(versionRepository.retrieveSectionVersion(version1.id.get).get.id == version1.id)
    assert(versionRepository.retrieveSectionVersion(version1.id.get).get.title == version1.title)
  }

}
