// Base Types
export interface BaseEntity {
  id?: string;
  createdDate?: string;
  updatedDate?: string;
  createdBy?: string;
  updatedBy?: string;
}

// Session & User Management
export interface LDAPUser {
  email: string;
  fullName?: string;
  organizations: string[];
  language?: string;
  localUser: boolean;
  localAdmin: boolean;
  globalAdmin: boolean;
}

export interface Organization {
  id: string;
  name: string;
  language?: string;
  integrations: Integration[];
}

export interface Integration {
  // Define based on your integration structure
}

export interface SessionInfo {
  appVersion: string;
  user?: LDAPUser;
  bannerUrl?: string;
  isKfAdmin: boolean;
  organization?: Organization;
  userOrgsWithAccess: Organization[];
  logoUrlStart: string;
  basename: string;
  publicBasename: string;
  ssoLogOutUrl?: string;
}

// Content Management
export interface HandbookProperties extends BaseEntity {
  title: string;
  importedHandbookId?: string;
  externalOrgId: string;
  localChange: boolean;
  isPublic: boolean;
  pendingChange: boolean;
  pendingDeletion: boolean;
  pendingChangeUpdatedDate?: string;
  isPublished: boolean;
  versionOf?: string;
}

export interface Handbook extends HandbookProperties {
  type: "HANDBOOK";
}

export interface CentralHandbook extends BaseEntity {
  type: "HANDBOOK";
  title: string;
  versionOf?: string;
  isPublished: boolean;
  pendingChange: boolean;
}

export interface ChapterProperties extends BaseEntity {
  title: string;
  importedHandbookChapterId?: string;
  importedHandbookId?: string;
  handbookId: string;
  parentId?: string;
  sortOrder?: number;
  localChange: boolean;
  pendingChange: boolean;
  pendingDeletion: boolean;
  pendingChangeUpdatedDate?: string;
  centralChapterUpdatedDateBeforePublish?: string;
  localChapterUpdatedDate?: string;
  isDeleted?: boolean;
  deletedDate?: string;
  versionOf?: string;
}

export interface Chapter extends ChapterProperties {
  type: "CHAPTER";
}

export interface CentralChapter extends BaseEntity {
  type: "CHAPTER";
  title: string;
  parentId?: string;
  centralHandbookId: string;
  versionOf?: string;
  updatedDateBeforePublish?: string;
  sortOrder: number;
}

export interface Section extends BaseEntity {
  type: "SECTION";
  title: string;
  text?: string;
  importedHandbookSectionId?: string;
  importedHandbookId?: string;
  handbookId: string;
  parentId: string;
  sortOrder?: number;
  localTitleChange: boolean;
  pendingTitleChange: boolean;
  localTextChange: boolean;
  pendingTextChange: boolean;
  pendingDeletion: boolean;
  pendingChange?: boolean; // Added to match legacy usage (may be a bug in legacy but replicating exactly)
  pendingChangeUpdatedDate?: string;
  textUpdatedDate?: string;
  textUpdatedBy?: string;
  isDeleted?: boolean;
  deletedDate?: string;
  versionOf?: string;
  centralSectionId?: string; // ID of linked central section
}

export interface CentralSection extends BaseEntity {
  type: "SECTION";
  title: string;
  parentId: string;
  centralHandbookId: string;
  html?: string;
  versionOf?: string;
  registeredDate?: string;
  titleUpdatedDate?: string;
  htmlUpdatedDate?: string;
  titleUpdatedBy?: string;
  htmlUpdatedBy?: string;
  sortOrder: number;
}

// Tree Structure for API
export interface TreeStructureHandbook extends HandbookProperties {
  chapters: TreeStructureChapter[];
}

export interface TreeStructureChapter extends ChapterProperties {
  chapters: TreeStructureChapter[];
  sections: Section[];
}

// Comments & Editors
export interface Comment extends BaseEntity {
  text: string;
  editedBy: string;
  editedDate: string;
  handbookId: string;
}

export interface LocalEditor extends BaseEntity {
  handbookId: string;
  rightsHolder: string;
  addedBy: string;
  addedDate: string;
}

// Links
export interface Link extends BaseEntity {
  title: string;
  url: string;
  sortOrder: number;
}

export interface LinkCollection {
  id?: string;
  title: string;
  handbookId: string;
  sortOrder: number;
  links: Link[];
}

export interface Link {
  id?: string;
  title: string;
  url: string;
  sortOrder: number;
}

export interface ReadingLink extends BaseEntity {
  link?: string; // Optional to match backend Option[String]
  centralSectionId: string;
  validTo: Date | string;
}

// File Management
export interface FileLink extends BaseEntity {
  title: string;
  belongsTo: string;
  ownerId: string;
  url: string;
  sortOrder: number;
  size: number;
}

export interface FileLinkInput {
  title: string;
  url: string;
  sortOrder: number;
  size: number;
}

export interface FileLinksInput {
  belongsTo: string;
  ownerId: string;
  newlyAdded: FileLinkInput[];
  removed: string[];
}

export interface KFFile {
  id?: string;
  name: string;
  contentType?: string;
  byteArray: Uint8Array;
}

// File Attachment Management
export interface AttachmentFile extends BaseEntity {
  title: string;
  belongsTo: string;
  ownerId: string;
  url: string;
  sortOrder: number;
  size: number;
  progress?: number;
  status?: "uploading" | "uploaded" | "error" | "oversized";
  fileSize?: string;
  file?: File;
}

export interface AttachmentUploadResponse {
  location: string;
}

export interface AttachmentCountResponse {
  count: number;
}

export interface SaveAttachmentsRequest {
  belongsTo: string;
  ownerId: string;
  newlyAdded: FileLinkInput[];
  removed: string[];
}

export interface Image {
  id?: string;
  name: string;
  contentType?: string;
  byteArray: Uint8Array;
}

// Search
export interface SearchResult {
  totalHits: number;
  ms: number;
  page: number;
  pageSize: number;
  results: SearchHit[];
}

export interface SearchHit {
  id: string;
  title: string;
  highlight?: string;
  textHighlight?: string;
  handbookId?: string;
  isHandbook: boolean;
  isChapter: boolean;
  isSection: boolean;
}

export interface HandbookHit extends SearchHit {
  isHandbook: true;
}

export interface ChapterHit extends SearchHit {
  handbookId?: string;
  isChapter: true;
}

export interface SectionHit extends SearchHit {
  textHighlight?: string;
  handbookId?: string;
  isSection: true;
}

// Publications
export interface Publication extends BaseEntity {
  publicationDate: string;
  notifyByEmail: boolean;
  handbookId: string;
  published: boolean;
}

// Central Change Notifications
export interface CentralChangeNotification extends BaseEntity {
  changeDescription: string;
  handbookId: string;
  changedDate: string;
  chapterId?: string;
  sectionId?: string;
  changeHTML?: string;
  concernsTitle: boolean;
  deletion: boolean;
}

// API Response Types
export interface LocalHandbooksResponse {
  handbooks: Handbook[];
  chapters: Chapter[];
  sections: Section[];
}

export interface CentralHandbookContentResponse {
  chapters: CentralChapter[];
  sections: CentralSection[];
}

export interface PublicHandbookResponse {
  handbook: Handbook;
  chapters: Chapter[];
  sections: Section[];
  organization: Organization;
  linkCollections: LinkCollection[];
}

export interface HandbooksApiResponse {
  handbooks: TreeStructureHandbook[];
}

export interface EditedEntitiesResponse {
  handbooks: Handbook[];
  chapters: Chapter[];
  sections: Section[];
}

export interface PublicConfigResponse {
  basename: string;
  editorBasename: string;
  logoUrlStart: string;
  bannerUrlStart: string;
}

// Request Types
export interface SearchRequest {
  query: string;
  page?: number;
  handbookId?: string;
  externalOrgId?: string;
}

export interface SortRequest {
  ids: string[];
}

export interface AccessUpdateRequest {
  handbookIds: string[];
}

export interface PublicationRequest {
  publicationDate?: string;
}

export interface ReindexRequest {
  externalOrgs: string[];
}

// Session API Request/Response Types
export interface SessionGetRequest {}

export interface SessionGetResponse extends SessionInfo {}

export interface SessionSetOrgRequest {
  externalOrgId: string; // Path parameter
}

export interface SessionSetOrgResponse extends SessionInfo {}

export interface SessionEditorsRequest {
  externalOrgId: string; // Path parameter
}

export interface SessionEditorsResponse extends Array<LDAPUser> {}

// Legacy Session type alias for backward compatibility
export type Session = SessionInfo;

export type CentralTreeNode = CentralHandbook | CentralChapter | CentralSection;

export type CentralChildren = CentralChapter | CentralSection;

// Local tree node type for move operations and tree navigation
export type LocalTreeNode = Handbook | Chapter | Section;

// Tree node types for the tree components (includes WithChildren variants)
export type CentralTreeNodeWithChildren =
  | import("@/store/services/handbook/utils").CentralHandbookWithChildren
  | import("@/store/services/handbook/utils").CentralChapterWithChildren
  | CentralSection;

export type LocalTreeNodeWithChildren =
  | import("@/store/services/handbook/utils").LocalHandbookWithChildren
  | import("@/store/services/handbook/utils").LocalChapterWithChildren
  | Section;

export type PendingElement = Handbook | Chapter | Section;
