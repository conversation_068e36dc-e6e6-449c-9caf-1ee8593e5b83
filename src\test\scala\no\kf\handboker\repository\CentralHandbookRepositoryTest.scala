package no.kf.handboker.repository

import java.sql.SQLException

import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import no.kf.test.TimeTestHelp
import no.kf.util.Logging
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.FunSuite
import org.scalatest.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class CentralHandbookRepositoryTest extends FunSuite with DbTestHandler with TimeTestHelp with Logging {

  val repository = componentRegistry.centralHandbookRepository
  val currentUser = "<EMAIL>"

  def persistHandbook(title: Option[String] = Some("TestHandbook")): CentralHandbook = {
    repository.persistCentralHandbook(CentralHandbook(None, title.get), currentUser)
  }
  def persistChapter(title: Option[String] = Some("TestChapter"), parentId: Option[String] = None, handbookId: Option[String]): CentralChapter = {
    repository.persistCentralChapter(CentralChapter(None, title.get, parentId, handbookId.get), currentUser)
  }
  def persistSection(title: Option[String] = Some("TestChapter"), parentId: Option[String] = None, handbookId: Option[String]): CentralSection = {
    repository.persistCentralSection(CentralSection(None, "TestSection", parentId.get, handbookId.get, Some("html")), currentUser)
  }

  transactedTest("That we can insert a handbook"){
    val testBook = CentralHandbook(None, "TestHandbook")
    val result = repository.persistCentralHandbook(testBook, currentUser)
    assert(result.id.isDefined)
    assert(result.title === testBook.title)
  }

  transactedTest("That we can insert a chapter") {
    val testBook = persistHandbook()
    val testChapter = persistChapter(handbookId = testBook.id)
    val testChapter2 = persistChapter(handbookId = testBook.id)

    assert(testChapter.id.isDefined)
    assert(testChapter.centralHandbookId === testBook.id.get)
    assert(testChapter.title === testChapter.title)
    assert(testChapter.sortOrder < testChapter2.sortOrder)
  }

  transactedTest("That we can insert a central section and that sort order is correct for chapters and sections") {
    val testBook = persistHandbook()
    val testChapter = persistChapter(handbookId = testBook.id)
    val testSection = CentralSection(None,"TestSection", testChapter.id.get, testBook.id.get, Some("html"))

    val result = repository.persistCentralSection(testSection, currentUser)
    val result2 = repository.persistCentralSection(testSection, currentUser)
    val result3 = persistChapter(handbookId = testBook.id, parentId = testChapter.id)
    assert(result.sortOrder < result2.sortOrder && result2.sortOrder < result3.sortOrder)
    val stored = repository.retrieveCentralSection(result.id.get)
    assert(stored.isDefined)
    assert(stored.get.id.isDefined)
    assert(stored.get.title === testSection.title)
    assert(stored.get.parentId === testSection.parentId)
    assert(stored.get.centralHandbookId === result.centralHandbookId)
  }

  transactedTest("That we cannot persist a central section if its parent chapter does not exist") {
    val testBook = persistHandbook()
    val testSection2 = CentralSection(None,"TestSection", "nonExistentChapterId", testBook.id.get, Some("html"))

    assertThrows[SQLException](repository.persistCentralSection(testSection2, currentUser))
  }

  transactedTest("That we can retrieve a central handbook") {
    val testBook = persistHandbook()
    val result = repository.retrieveCentralHandbook(testBook.id.get)
    assert(result.isDefined)
    assert(result.get.title === testBook.title)
  }
  transactedTest("That we can retrieve a central chapter") {
    val testBook = persistHandbook()
    val testChapter = persistChapter(handbookId = testBook.id)

    val result = repository.retrieveCentralChapter(testChapter.id.get, testBook.id.get)
    assert(result.isDefined)
    assert(result.get.title === testChapter.title)
  }
  transactedTest("That we can retrieve a central section") {
    val testBook = persistHandbook()
    val testChapter = persistChapter(handbookId = testBook.id)
    val testSection = CentralSection(None,"TestSection", testChapter.id.get, testBook.id.get, Some("html"))
    val savedSection = repository.persistCentralSection(testSection, currentUser)

    val result = repository.retrieveCentralSection(savedSection.id.get)
    assert(result.isDefined)
  }

  transactedTest("That we can retrieve all central handbooks") {
    val testBook1 = persistHandbook()
    val testBook2 = persistHandbook()
    val testBook3 = persistHandbook()
    val testChapter1 = persistChapter(handbookId = testBook1.id)
    val testChapter2 = persistChapter(handbookId = testBook3.id)

    val result = repository.retrieveShallowHandbooks

    assert(result.contains(testBook1))
    assert(result.contains(testBook2))
    assert(result.contains(testBook3))
    assert(result.exists( book => book.id.get === testBook1.id.get && testChapter1.centralHandbookId === book.id.get))
    assert(result.exists( book => book.id.get === testBook3.id.get && testChapter2.centralHandbookId === book.id.get))
  }

  transactedTest("That we can retrieve all central chapters") {
    val testBook1 = persistHandbook()
    val testBook2 = persistHandbook()
    val testChapter1 = persistChapter(handbookId = testBook1.id)
    val testChapter2 = persistChapter(handbookId = testBook1.id, parentId = testChapter1.id)
    val testChapter3 = persistChapter(handbookId = testBook2.id)

    val result = repository.retrieveAllChapters()

    assert(result.contains(testChapter1))
    assert(result.contains(testChapter2))
    assert(result.contains(testChapter3))
  }

  transactedTest("That we can retrieve all central sections") {
    val testBook = persistHandbook()
    val testChapter1 = persistChapter(handbookId = testBook.id)
    val testChapter2 = persistChapter(handbookId = testBook.id)

    val testSection1 = persistSection(parentId = testChapter1.id, handbookId = testBook.id)
    val testSection2 = persistSection(parentId = testChapter1.id, handbookId = testBook.id)
    val testSection3 = persistSection(parentId = testChapter2.id, handbookId = testBook.id)

    val result = repository.retrieveAllSections()
    assert(result.contains(testSection1))
    assert(result.contains(testSection2))
    assert(result.contains(testSection3))
  }

  transactedTest("That we cannot retrieve a central handbook that does not exist") {
    val result = repository.retrieveCentralHandbook("fakeHandbookId")
    assert(result.isEmpty)
  }

  transactedTest("That we cannot retrieve a central chapter that does not exist") {
    val result = repository.retrieveCentralChapter("fakeId", "fakeHandbookId")
    assert(result.isEmpty)
  }

  transactedTest("That we cannot retrieve a central section that does not exist") {
    val result = repository.retrieveCentralSection("fakeId")
    assert(result.isEmpty)
  }

  transactedTest("That we can update a central handbook") {
    val savedBook = persistHandbook()
    val updatedBook = savedBook.copy(title = "NewTitle")
    val result = repository.persistCentralHandbook(updatedBook, currentUser)

    assert(result.id === savedBook.id)
    assert(result.title !== savedBook.title)
    assert(result.title === updatedBook.title)
  }
  transactedTest("That we can update a central chapter") {
    val testBook = persistHandbook()
    val testChapter = persistChapter(handbookId = testBook.id)
    val updatedChapter = testChapter.copy(title = "UpdatedTitle")

    val result = repository.persistCentralChapter(updatedChapter, currentUser)

    assert(result.id === testChapter.id)
    assert(result.title !== testChapter.title)
    assert(result.title === updatedChapter.title)
  }
  transactedTest("That we can update a central section") {
    val testBook = persistHandbook()
    val testChapter = persistChapter(handbookId = testBook.id)
    val savedSection = persistSection(parentId = testChapter.id, handbookId = testBook.id)
    val result = repository.persistCentralSection(savedSection.copy( html = Some("This is actually the section text")), currentUser)

    assert(result.id === savedSection.id)
    assert(result.title === savedSection.title)
    assert(!result.html.isEmpty)
  }

  transactedTest("That we can delete a central handbook"){
    val testBook = persistHandbook()
    assert(repository.retrieveCentralHandbook(testBook.id.get).isDefined)

    repository.deleteCentralHandbook(testBook.id.get)
    val shouldBeEmpty = repository.retrieveCentralHandbook(testBook.id.get)
    assert(shouldBeEmpty.isEmpty)
  }
  transactedTest("That we can delete a central chapter"){
    val testBook = persistHandbook()
    val savedChapter = persistChapter(handbookId = testBook.id)
    assert(repository.retrieveCentralChapter(savedChapter.id.get, testBook.id.get).isDefined)

    repository.deleteCentralChapter(savedChapter.id.get)
    val shouldBeEmpty = repository.retrieveCentralChapter(savedChapter.id.get, testBook.id.get)
    assert(shouldBeEmpty.isEmpty)
  }
  transactedTest("That we can delete a central section"){
    val testBook = persistHandbook()
    val savedChapter = persistChapter(handbookId = testBook.id)
    val savedSection = repository.persistCentralSection(CentralSection(None, "TestSection", savedChapter.id.get, testBook.id.get, Some("html"), None, Some(DateTime.now), None, None, None, None, Some("CreatedBy"), None, None, None, 0), currentUser)

    val res = repository.retrieveCentralSection(savedSection.id.get)
    assert(res.isDefined)

    repository.deleteCentralSection(savedSection.id.get)
    val shouldBeEmpty = repository.retrieveCentralSection(savedSection.id.get)
    assert(shouldBeEmpty.isEmpty)
  }

  transactedTest("That we can delete children of a handbook") {
    val testBook = persistHandbook()
    val savedChapter1 = persistChapter(handbookId = testBook.id)
    val savedChapter2 = persistChapter(handbookId = testBook.id)
    val savedSection = repository.persistCentralSection(CentralSection(None, "TestSection", savedChapter1.id.get, testBook.id.get, Some("html"), None, Some(DateTime.now), None, None, None, None, Some("CreatedBy"), None, None, None, 0), currentUser)

    repository.deleteChildrenForHandbook(testBook.id.get)
    val centralBook = repository.retrieveCentralHandbook(testBook.id.get)
    val centralChapter1 = repository.retrieveCentralChapter(savedChapter1.id.get, testBook.id.get)
    val centralChapter2 = repository.retrieveCentralChapter(savedChapter2.id.get, testBook.id.get)
    val centralSection = repository.retrieveCentralSection(savedSection.id.get)

    assert(centralBook.nonEmpty)
    assert(centralChapter1.isEmpty)
    assert(centralChapter2.isEmpty)
    assert(centralSection.isEmpty)
  }

  transactedTest("That we can delete central chapters with subchapters that contains sections") {
    val repository = componentRegistry.centralHandbookRepository
    val handbook = repository.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
    val ch2 = repository.persistCentralChapter(CentralChapter(None, "ch2", None, handbook.id.get, sortOrder = 2), "user")
    val ch21 = repository.persistCentralChapter(CentralChapter(None, "ch21", ch2.id, handbook.id.get, sortOrder = 1), "user")
    repository.persistCentralChapter(CentralChapter(None, "ch211", ch21.id, handbook.id.get, sortOrder = 1), "user")

    repository.persistCentralSection(CentralSection(None, "s21", ch2.id.get, handbook.id.get, Some("s21html"), sortOrder = 2), "user")
    repository.persistCentralSection(CentralSection(None, "s22", ch2.id.get, handbook.id.get, Some("s22html"), sortOrder = 3), "user")
    repository.persistCentralSection(CentralSection(None, "s23", ch2.id.get, handbook.id.get, Some("s23html"), sortOrder = 4), "user")
    repository.persistCentralSection(CentralSection(None, "s211", ch21.id.get, handbook.id.get, Some("s211html"), sortOrder = 1), "user")

    repository.deleteCentralChapter(ch2.id.get)
  }

  transactedTest("That deleting a chapter deletes all sub-chapters and sections") {
    val testBook = persistHandbook()
    val parentChapter = persistChapter(handbookId = testBook.id)
    val childChapter1 = persistChapter(handbookId = testBook.id, parentId = parentChapter.id)
    val childChapter2 = persistChapter(handbookId = testBook.id, parentId = parentChapter.id)
    val grandChildChapter = persistChapter(handbookId = testBook.id, parentId = childChapter2.id)
    val greatGrandChildChapter = persistChapter(handbookId = testBook.id, parentId = grandChildChapter.id)

    assert(repository.retrieveCentralChapter(parentChapter.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(childChapter1.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(childChapter2.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(grandChildChapter.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(greatGrandChildChapter.id.get, testBook.id.get).isDefined)

    repository.deleteCentralChapter(parentChapter.id.get)

    assert(repository.retrieveCentralChapter(parentChapter.id.get, testBook.id.get).isEmpty)
    assert(repository.retrieveCentralChapter(childChapter1.id.get, testBook.id.get).isEmpty)
    assert(repository.retrieveCentralChapter(childChapter2.id.get, testBook.id.get).isEmpty)
    assert(repository.retrieveCentralChapter(grandChildChapter.id.get, testBook.id.get).isEmpty)
    assert(repository.retrieveCentralChapter(greatGrandChildChapter.id.get, testBook.id.get).isEmpty)
  }

  transactedTest("That deleting a chapter does not delete chapters or sections that are not its children") {
    val testBook = persistHandbook()
    val parentChapter1 = persistChapter(handbookId = testBook.id)
    val parentChapter2 = persistChapter(handbookId = testBook.id)
    val childChapter1 = persistChapter(handbookId = testBook.id, parentId = parentChapter1.id)
    val childChapter2 = persistChapter(handbookId = testBook.id, parentId = parentChapter2.id)
    val grandChildChapter = persistChapter(handbookId = testBook.id, parentId = childChapter2.id)

    assert(repository.retrieveCentralChapter(parentChapter1.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(parentChapter2.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(childChapter1.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(childChapter2.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(grandChildChapter.id.get, testBook.id.get).isDefined)

    repository.deleteCentralChapter(parentChapter2.id.get)

    assert(repository.retrieveCentralChapter(parentChapter2.id.get, testBook.id.get).isEmpty)
    assert(repository.retrieveCentralChapter(parentChapter1.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(childChapter1.id.get, testBook.id.get).isDefined)
    assert(repository.retrieveCentralChapter(childChapter2.id.get, testBook.id.get).isEmpty)
    assert(repository.retrieveCentralChapter(grandChildChapter.id.get, testBook.id.get).isEmpty)
  }

  transactedTest("That we can retrieve all handbooks") {
    val title1 = "TestHandbook1"
    val title2 = "TestHandbook2"
    val title3 = "TestHandbook3"

    repository.persistCentralHandbook(CentralHandbook(None, title1, None, Some(DateTime.now), None, Some("CreatedBy"), None), currentUser)
    repository.persistCentralHandbook(CentralHandbook(None, title2, None, Some(DateTime.now), None, Some("CreatedBy"), None), currentUser)
    repository.persistCentralHandbook(CentralHandbook(None, title3, None, Some(DateTime.now), None, Some("CreatedBy"), None), currentUser)

    val books = repository.retrieveShallowHandbooks
    assert(books.exists(handbook => handbook.title === title1))
    assert(books.exists(handbook => handbook.title === title2))
    assert(books.exists(handbook => handbook.title === title3))
  }

  private def assertEqualSectionLists(alphaSections: List[CentralSection], betaSections: List[CentralSection], withDates: Boolean = false): Unit = {
    assert(alphaSections.length === betaSections.length)
    alphaSections.foreach(alphaSection => {
      val betaSection = betaSections.find(section => section.id == alphaSection.id && section.parentId == alphaSection.parentId).getOrElse(fail("First list contains a section not present in second list"))
      assert(alphaSection.title === betaSection.title)
      assert(alphaSection.parentId === betaSection.parentId)
      assert(alphaSection.html === betaSection.html)
      if (withDates) {
        assert(alphaSection.createdDate === betaSection.createdDate)
        assert(alphaSection.updatedDate === betaSection.updatedDate)
        assert(alphaSection.registeredDate === betaSection.registeredDate)
      }
    })
  }

  private def assertEqualChapterLists(alphaChapters: List[CentralChapter], betaChapters: List[CentralChapter], withDates: Boolean = false): Unit = {
    assert(alphaChapters.length === betaChapters.length)
    alphaChapters.foreach(alphaChapter => {
      val betaChapter = betaChapters.find(chapter => chapter.id == alphaChapter.id).getOrElse(fail("First list contains a chapter not present in second list"))
      assert(alphaChapter.title === betaChapter.title)
      assert(alphaChapter.parentId === betaChapter.parentId)
      if (withDates) {
        assert(alphaChapter.updatedDate === betaChapter.updatedDate)
      }
    })
  }
}
