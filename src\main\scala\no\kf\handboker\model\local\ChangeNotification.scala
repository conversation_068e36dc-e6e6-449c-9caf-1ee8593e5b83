package no.kf.handboker.model.local

import org.joda.time.DateTime
import no.kf.handboker.model.central.{CentralHandbook}

case class ChangeNotification(id: Option[String],
                              changeDescription: String,
                              handbook: Handbook,
                              changedDate: DateTime,
                              centralHandbook: CentralHandbook,
                              centralChangePublished: Boolean)
