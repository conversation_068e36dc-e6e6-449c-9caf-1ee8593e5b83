import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { toast } from "@/shared/components/Toast";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  addPendingHandbook,
  setCurrentlyViewedHandbook,
  removePendingHandbook,
  selectPendingHandbooks,
  selectCurrentlyViewedHandbookId,
} from "@/store/slices/pendingPublicationsSlice";
import {
  useCheckPendingPublicationsQuery,
  useGetPendingPublicationsQuery,
} from "./centralHandbookApi";

const extractHandbookIdFromPath = (pathname: string): string | null => {
  const match = pathname.match(/\/central-editor\/([^/]+)/);
  return match ? match[1] : null;
};

export const useOptimizedPendingPublications = () => {
  const dispatch = useAppDispatch();
  const location = useLocation();

  const pendingHandbooks = useAppSelector(selectPendingHandbooks);
  const currentlyViewedHandbookId = useAppSelector(
    selectCurrentlyViewedHandbookId
  );

  useEffect(() => {
    const handbookId = extractHandbookIdFromPath(location.pathname);
    dispatch(setCurrentlyViewedHandbook(handbookId));
  }, [location.pathname, dispatch]);

  const shouldCheckCurrentHandbook =
    currentlyViewedHandbookId &&
    !pendingHandbooks.some((h) => h.handbookId === currentlyViewedHandbookId);

  const { data: currentHandbookPendingStatus } =
    useGetPendingPublicationsQuery(currentlyViewedHandbookId!, {
      skip: !shouldCheckCurrentHandbook,
      refetchOnMountOrArgChange: true,
    });

  useEffect(() => {
    if (
      currentHandbookPendingStatus === true &&
      currentlyViewedHandbookId &&
      shouldCheckCurrentHandbook
    ) {
      dispatch(
        addPendingHandbook({
          handbookId: currentlyViewedHandbookId,
          handbookTitle: "Håndbok",
        })
      );
    }
  }, [
    currentHandbookPendingStatus,
    currentlyViewedHandbookId,
    shouldCheckCurrentHandbook,
    dispatch,
  ]);

  const shouldPoll = pendingHandbooks.length > 0;

  const { data: pendingStatusResults = [] } =
    useCheckPendingPublicationsQuery(pendingHandbooks, {
      pollingInterval: shouldPoll ? 20000 : 0,
      skipPollingIfUnfocused: true,
      refetchOnMountOrArgChange: false,
      skip: !shouldPoll,
    });

  useEffect(() => {
    if (pendingStatusResults.length > 0) {
      const completedHandbooks = pendingStatusResults.filter(
        (result) => !result.isPending
      );

      completedHandbooks.forEach((completed) => {
        dispatch(removePendingHandbook(completed.handbookId));

        toast.success(`Endringer publisert: ${completed.handbookTitle}`);
      });
    }
  }, [pendingStatusResults, dispatch]);


  return {
    pendingHandbooks,
    currentlyViewedHandbookId,
    isCurrentHandbookPending: pendingHandbooks.some(
      (h) => h.handbookId === currentlyViewedHandbookId
    ),
    pendingCount: pendingHandbooks.length,
  };
};

export const useIsHandbookPending = (
  handbookId: string | undefined
): boolean => {
  const pendingHandbooks = useAppSelector(selectPendingHandbooks);

  if (!handbookId) return false;

  return pendingHandbooks.some(
    (handbook) => handbook.handbookId === handbookId
  );
};

export const useAddToPendingTracking = () => {
  const dispatch = useAppDispatch();

  return (handbookId: string, handbookTitle: string) => {
    dispatch(
      addPendingHandbook({
        handbookId,
        handbookTitle,
      })
    );
  };
};
