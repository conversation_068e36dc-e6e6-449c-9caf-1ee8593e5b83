package no.kf.handboker.integrationtest

import java.io.File

import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.{FunSuite, PrivateMethodTester}
import org.scalatest.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class CentralHandbookVersionTest extends FunSuite with FullStack with PrivateMethodTester {

  override def handbookConfigDir = new File("src/test/resources/integrationtest")

  val persistCentralHandbookVersion: PrivateMethod[(CentralHandbook, List[CentralChapter], List[CentralSection])] = PrivateMethod[(CentralHandbook, List[CentralChapter], List[CentralSection])]('persistCentralHandbookVersion)
  lazy val synchronizationService = componentRegistry.handbookSynchronizationService
  lazy val centralHandbookRepository = componentRegistry.centralHandbookRepository

  test("That we can create a central handbook version with chapters and sections") {
    testInTransaction {
      val (handbook: CentralHandbook, ch1: CentralChapter, ch2: CentralChapter, ch3: CentralChapter, ch21: CentralChapter, ch211: CentralChapter, s21: CentralSection, s22: CentralSection, s211: CentralSection, s23: CentralSection) = createHandbook
      assert(!centralHandbookRepository.isPublished(handbook.id.get))

      val handbookVersion = synchronizationService invokePrivate persistCentralHandbookVersion(handbook, "testbruker")
      assert(handbookVersion._1.versionOf == handbook.id)
      assert(handbookVersion._2.size == 5)
      assert(handbookVersion._3.size == 4)
      assert(List(ch1, ch2, ch21, ch211).map(c => c.title).toSet.subsetOf(handbookVersion._2.map(ch => ch.title).toSet))
      assert(List(s21, s22, s211, s23).map(s => s.title).toSet.subsetOf(handbookVersion._3.map(sec => sec.title).toSet))
      assert(centralHandbookRepository.isPublished(handbook.id.get))

      // Check that right chapter parents where stored
      val chapterIdMap = Map(ch1.id -> ch1, ch2.id -> ch2, ch3.id -> ch3, ch21.id -> ch21, ch211.id -> ch211)
      val chapterParentTitleMap = List(ch1, ch2, ch3, ch21, ch211).map(c => c.title -> chapterIdMap(c.id).title).toMap
      val storedChapterIdMap = handbookVersion._2 map (c => c.id -> c) toMap
      val storedChapterParentMap = handbookVersion._2.map(c => (c.title -> storedChapterIdMap(c.id).title))
      assert(storedChapterParentMap.forall(sm => sm._2 == chapterParentTitleMap(sm._1)))

      assert(handbookVersion._2.forall(_.versionOf.isDefined))
      assert(handbookVersion._2.forall(ch =>
        List(ch1, ch2, ch3, ch21, ch211).exists(c => c.id == ch.versionOf)
      ))

      // Check that right section parents where stored
      val sectionIdMap = Map(s21.id -> s21, s22.id -> s22, s211.id -> s211, s23.id -> s23)
      val sectionParentTitleMap = List(s21, s22, s211, s23).map(c => (c.title -> sectionIdMap(c.id).title)).toMap
      val storedSectionIdMap = handbookVersion._3 map (c => c.id -> c) toMap
      val storedSectionParentMap = handbookVersion._3.map(c => c.title -> storedSectionIdMap(c.id).title)
      assert(storedSectionParentMap.forall(sm => sm._2 == sectionParentTitleMap(sm._1)))
      assert(handbookVersion._3.size == 4)
      assert(handbookVersion._3.forall(_.versionOf.isDefined))
      assert(handbookVersion._3.forall(ch =>
        List(s21, s22, s211, s23).exists(c => c.id == ch.versionOf)
      ))

      // Check chapter content and sort order
      val originalChapters = List(ch1, ch2, ch3, ch21, ch211)
      val originalSections = List(s21, s22, s211, s23)
      assert(handbookVersion._2.forall(chapterVersion => {
        val originalChapter = originalChapters.find(oc => oc.id == chapterVersion.versionOf)
        System.out.println(s"$chapterVersion")
        System.out.println(s"$originalChapter")
        originalChapter.isDefined && originalChapter.get.sortOrder == chapterVersion.sortOrder
      }))
      assert(handbookVersion._3.forall(sectionVersion => {
        val originalSection = originalSections.find(oc => oc.id == sectionVersion.versionOf)
        originalSection.isDefined && originalSection.get.sortOrder == sectionVersion.sortOrder
      }))

    }
  }

  test("That we can retrieve the latest published version") {
    testInTransaction {
      val centralHandbookService = componentRegistry.centralHandbookService
      val (handbook: CentralHandbook, ch1: CentralChapter, ch2: CentralChapter, ch3: CentralChapter, ch21: CentralChapter, ch211: CentralChapter, s21: CentralSection, s22: CentralSection, s211: CentralSection, s23: CentralSection) = createHandbook
      val changedHandbook = centralHandbookService.persistCentralHandbook(handbook.copy(title = "handbook new"), "testbruker")
      centralHandbookService.persistCentralChapter(ch1.copy(title = "ch1 v1"), currentUser = "user")
      val v1 = synchronizationService invokePrivate persistCentralHandbookVersion(changedHandbook, "testbruker")
      centralHandbookService.persistCentralChapter(ch1.copy(title = "ch1 v2"), currentUser = "user")
      val v2 = synchronizationService invokePrivate persistCentralHandbookVersion(changedHandbook, "testbruker")
      centralHandbookService.persistCentralChapter(ch1.copy(title = "ch1 v3"), currentUser = "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(changedHandbook, "testbruker")
      centralHandbookService.persistCentralChapter(ch1.copy(title = "ch1 v4"), currentUser = "user")
      synchronizationService invokePrivate persistCentralHandbookVersion(changedHandbook, "testbruker")

      // Check that we can retrieve latest version for handbook
      val latest = componentRegistry.centralHandbookService.retrieveLatestHandbookVersion(handbook.id.get)
      assert(latest.isDefined)
      assert(latest.get.title == "handbook new")
      // Check latest chapter and section version
      assert(centralHandbookService.retrieveLatestPublishedCentralChapter(ch1.id.get, ch1.centralHandbookId).isDefined)
      assert(centralHandbookService.retrieveLatestPublishedCentralSection(s21.id.get, s21.centralHandbookId).isDefined)
    }
  }

  private def createHandbook = {
    val repository = componentRegistry.centralHandbookRepository
    val handbook = repository.persistCentralHandbook(CentralHandbook(None, "handbook", None, None, Some(DateTime.now), Some("CreatedBy"), Some("UpdatedBy")), "user")
    val ch1 = repository.persistCentralChapter(CentralChapter(None, "ch1", None, handbook.id.get, sortOrder = 1), "user")
    val ch2 = repository.persistCentralChapter(CentralChapter(None, "ch2", None, handbook.id.get, sortOrder = 2), "user")
    val ch3 = repository.persistCentralChapter(CentralChapter(None, "ch3", None, handbook.id.get, sortOrder = 3), "user")
    val ch21 = repository.persistCentralChapter(CentralChapter(None, "ch21", ch2.id, handbook.id.get, sortOrder = 1), "user")
    val ch211 = repository.persistCentralChapter(CentralChapter(None, "ch211", ch21.id, handbook.id.get, sortOrder = 1), "user")

    val s21 = repository.persistCentralSection(CentralSection(None, "s21", ch2.id.get, handbook.id.get, Some("s21html"), sortOrder = 2), "user")
    val s22 = repository.persistCentralSection(CentralSection(None, "s22", ch2.id.get, handbook.id.get, Some("s22html"), sortOrder = 3), "user")
    val s23 = repository.persistCentralSection(CentralSection(None, "s23", ch2.id.get, handbook.id.get, Some("s23html"), sortOrder = 4), "user")
    val s211 = repository.persistCentralSection(CentralSection(None, "s211", ch21.id.get, handbook.id.get, Some("s211html"), sortOrder = 1), "user")
    (handbook, ch1, ch2, ch3, ch21, ch211, s21, s22, s211, s23)
  }


}
