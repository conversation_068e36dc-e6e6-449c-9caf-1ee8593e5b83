alter table handbook ADD deleted_date bigint
ALTER TABLE handbooksection ADD deleted_date bigint
ALTER TABLE handbookchapter ADD deleted_date bigint

create table handbooktitle_version(id VARCHAR(37), version_of VARCHAR(37), title VARCHAR(2000), external_org_id VARCHAR(200), created_date bigint, created_by varchar(100), updated_date bigint, updated_by varchar(100), version_date bigint, PRIMARY KEY(id))
ALTER TABLE handbooktitle_version ADD CONSTRAINT fk_handbooktitle_version_handbook FOREIGN KEY (version_of) REFERENCES handbook(id)
