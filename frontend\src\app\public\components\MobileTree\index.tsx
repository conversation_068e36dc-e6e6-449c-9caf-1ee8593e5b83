import React, { useEffect } from "react";
import { <PERSON><PERSON>, Column, Icon } from "kf-bui";
import Sticky from "react-stickynode";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Tree } from "../Tree";
import type { Handbook, Chapter, Section } from "@/types";

interface MobileTreeProps {
  toggleTree: () => void;
  showTree: boolean;
  handbook: Handbook;
  chapters: Chapter[];
  sections: Section[];
  externalOrgId: string;
  handbookId: string;
  activeSections: string[];
  expandedChapters: string[];
  onChapterExpand: (item: Chapter) => void;
  onChapterNavigate: (item: Chapter) => void;
  onSectionNavigate: (sectionId: string) => void;
}

export const MobileTree: React.FC<MobileTreeProps> = ({
  toggleTree,
  showTree,
  handbook,
  chapters,
  sections,
  externalOrgId,
  handbookId,
  activeSections,
  expandedChapters,
  onChapterExpand,
  onChapterNavigate,
  onSectionNavigate,
}) => {
  const t = usePrefixedTranslation("public.components.MobileTree");

  useEffect(() => {
    if (showTree) {
      document.body.style.overflow = "hidden";
      document.documentElement.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
    }

    return () => {
      document.body.style.overflow = "";
      document.documentElement.style.overflow = "";
    };
  }, [showTree]);

  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && showTree) {
        toggleTree();
      }
    };

    if (showTree) {
      document.addEventListener("keydown", handleEscapeKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [showTree, toggleTree]);

  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      toggleTree();
    }
  };

  return (
    <>
      <Column hiddenTablet hiddenDesktop>
        <Sticky
          top={0}
          enabled={true}
          activeClass="sticky-active"
          releasedClass="sticky-released"
          innerClass="sticky-inner"
          enableTransforms={true}
          bottomBoundary={0}
          innerZ={100}
        >
          <div className="mobile-tree-toggle">
            <Button
              onClick={toggleTree}
              color="white"
              size="medium"
              aria-label={t("toggleTreeLabel")}
              aria-expanded={showTree}
              icon="list"
            >
              {t("toggleTree")}
            </Button>
          </div>
        </Sticky>
      </Column>

      <div
        className={`mobile-tree-overlay ${showTree ? "visible" : ""}`}
        onClick={handleOverlayClick}
        role="dialog"
        aria-modal="true"
        aria-label={t("treeNavigationLabel")}
      >
        <div className={`mobile-tree-sidebar ${showTree ? "visible" : ""}`}>
          <div className="mobile-tree-header">
            <div className="mobile-tree-close">
              <h3 style={{ margin: 0, fontSize: "1.1rem", fontWeight: 600 }}>
                {handbook.title}
              </h3>
              <Button
                onClick={toggleTree}
                color="white"
                size="small"
                aria-label={t("closeTree")}
                style={{
                  minHeight: "44px",
                  minWidth: "44px",
                  borderRadius: "50%",
                  padding: 0,
                }}
              >
                <Icon icon="times" size="small" />
              </Button>
            </div>
          </div>

          <div className="mobile-tree-content">
            <Tree
              handbook={handbook}
              chapters={chapters}
              sections={sections}
              externalOrgId={externalOrgId}
              handbookId={handbookId}
              activeSections={activeSections}
              expandedChapters={expandedChapters}
              onChapterExpand={onChapterExpand}
              onChapterNavigate={(item) => {
                onChapterNavigate(item);
                toggleTree();
              }}
              onSectionNavigate={(sectionId) => {
                onSectionNavigate(sectionId);
                toggleTree();
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
};
