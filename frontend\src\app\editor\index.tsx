import { Suspense, useEffect, useState } from "react";
import { Routes, Route, Navigate } from "react-router-dom";

import { useSession } from "@/store/services/session/hooks";
import { usePendingCount } from "@/store/services/handbook/hooks";
import { useOptimizedPendingPublications } from "@/store/services/handbook/useOptimizedPendingPublications";
import { ProtectedRoute } from "@/components/auth";
import { NoAccessPage } from "@/shared/components/NoAccessPage";
import { NotFoundPage } from "@/shared/components/NotFoundPage";
import { OptOutModal } from "@/shared/components/OptOutModal";
import { useUpdateSessionLocale } from "@/libs/i18n";
import { RouteErrorBoundaryWrapper } from "@/shared/components/ErrorBoundary";

import { Navbar } from "./layouts/Navbar";
import { Footer } from "./layouts/Footer";
import {
  CentralHandbook,
  SelectOrganization,
  LocalHandbook,
  PendingPage,
  CentralHandbooksAccessPage,
  MergePage,
  SearchPage,
} from "./pages";
import { Spinner } from "@/shared/components/Spinner";

export const EditorApp = () => {
  const { session, isLoading, isError, error, refetch } = useSession();
  const updateSessionLocale = useUpdateSessionLocale();
  const [hasInitialized, setHasInitialized] = useState(false);
  const [showOptOutModal, setShowOptOutModal] = useState(false);

  const localPendingCount = usePendingCount();
  const { pendingCount: centralPendingCount } =
    useOptimizedPendingPublications();

  const totalPendingCount = localPendingCount + centralPendingCount;

  useEffect(() => {
    if (!hasInitialized) {
      refetch();
      setHasInitialized(true);
    }
  }, [refetch, hasInitialized]);

  useEffect(() => {
    if (hasInitialized) {
      updateSessionLocale(session || null);
    }
  }, [session, updateSessionLocale, hasInitialized]);

  useEffect(() => {
    if (typeof localStorage !== "undefined") {
      const hasVisited = localStorage.getItem("hasVisited");
      if (hasVisited) {
        setShowOptOutModal(false);
      } else {
        localStorage.setItem("hasVisited", "true");
        setShowOptOutModal(true);
      }
    }
  }, []);

  if (isLoading || !hasInitialized) {
    return (
      <div className="full-screen-center">
        <Spinner text="Laster inn økt..." />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="error-container">
        <h1>Authentication Error</h1>
        <p>There was a problem loading your session.</p>
        <p className="error-message">
          {error && typeof error === "object" && "message" in error
            ? (error as { message: string }).message
            : "Please check your network connection and try refreshing the page."}
        </p>
        <button className="retry-button" onClick={() => refetch()}>
          Retry
        </button>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="error-container">
        <h1>No Session Available</h1>
        <p>
          Unable to load session information. Please try refreshing the page.
        </p>
        <button className="retry-button" onClick={() => refetch()}>
          Refresh
        </button>
      </div>
    );
  }

  return (
    <div className="app-layout">
      <div className="main-layout">
        <div className="main-content">
          <Navbar
            session={session}
            pendingCount={totalPendingCount}
            hasCentralAccess={session.isKfAdmin}
          />
          {showOptOutModal && (
            <OptOutModal
              isOpen={showOptOutModal}
              toggleHide={() => setShowOptOutModal(false)}
            />
          )}

          <Suspense
            fallback={
              <div className="suspense-loading">
                <Spinner text="Laster..." />
              </div>
            }
          >
            <Routes>
              <Route
                index
                element={
                  session?.organization ? (
                    <Navigate to="editor" replace />
                  ) : (
                    <Navigate to="select" replace />
                  )
                }
              />

              <Route
                path="select"
                element={
                  <RouteErrorBoundaryWrapper routeName="SelectOrganization">
                    <ProtectedRoute requireAuth>
                      <SelectOrganization />
                    </ProtectedRoute>
                  </RouteErrorBoundaryWrapper>
                }
              />

              <Route
                path="search"
                element={
                  <RouteErrorBoundaryWrapper routeName="Search">
                    <ProtectedRoute requireAuth requireOrganization>
                      <SearchPage />
                    </ProtectedRoute>
                  </RouteErrorBoundaryWrapper>
                }
              />

              <Route
                path="pending"
                element={
                  <RouteErrorBoundaryWrapper routeName="Pending">
                    <ProtectedRoute requireAuth requireOrganization>
                      <PendingPage />
                    </ProtectedRoute>
                  </RouteErrorBoundaryWrapper>
                }
              />

              <Route
                path="merge/*"
                element={
                  <RouteErrorBoundaryWrapper routeName="Merge">
                    <ProtectedRoute requireAuth requireOrganization>
                      <MergePage />
                    </ProtectedRoute>
                  </RouteErrorBoundaryWrapper>
                }
              />

              <Route
                path="editor/*"
                element={
                  <RouteErrorBoundaryWrapper routeName="LocalHandbook">
                    <ProtectedRoute requireAuth requireOrganization>
                      <LocalHandbook />
                    </ProtectedRoute>
                  </RouteErrorBoundaryWrapper>
                }
              />

              <Route
                path="central"
                element={
                  <RouteErrorBoundaryWrapper routeName="CentralHandbooksAccess">
                    <ProtectedRoute requireAuth requireAdmin>
                      <CentralHandbooksAccessPage />
                    </ProtectedRoute>
                  </RouteErrorBoundaryWrapper>
                }
              />

              <Route
                path="central-editor/*"
                element={
                  <RouteErrorBoundaryWrapper routeName="CentralHandbook">
                    <ProtectedRoute requireAuth requireAdmin>
                      <CentralHandbook />
                    </ProtectedRoute>
                  </RouteErrorBoundaryWrapper>
                }
              />

              <Route path="forbidden" element={<NoAccessPage />} />
              <Route path="403" element={<NoAccessPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Suspense>
        </div>
        <Footer
          session={session!}
          showOptOutModal={() => setShowOptOutModal(true)}
        />
      </div>
    </div>
  );
};
