# CAS Authentication & LDAP Authorization Flow (Continued - Part 3)
**Advanced Scenarios, Security Considerations & Troubleshooting**

## BrukerAdm API Integration (Continued)

```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  BRUKERADM API INTEGRATION (CONTINUED)                                                      │
│                                                                                             │
│  API Response Format:                                                                      │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ {                                                                                     │   │
│  │   "email": "<EMAIL>",                                                         │   │
│  │   "fullName": "John Doe",                                                            │   │
│  │   "department": "Safety Department",                                                 │   │
│  │   "title": "Safety Manager",                                                         │   │
│  │   "organizations": [                                                                 │   │
│  │     {                                                                                │   │
│  │       "id": "9900",                                                                  │   │
│  │       "name": "KF Safety Division",                                                 │   │
│  │       "role": "admin"                                                               │   │
│  │     },                                                                               │   │
│  │     {                                                                                │   │
│  │       "id": "9901",                                                                  │   │
│  │       "name": "KF HR Division",                                                     │   │
│  │       "role": "user"                                                                │   │
│  │     }                                                                                │   │
│  │   ],                                                                                 │   │
│  │   "globalPermissions": {                                                             │   │
│  │     "globalAdmin": true,                                                             │   │
│  │     "systemAccess": true                                                             │   │
│  │   }                                                                                  │   │
│  │ }                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Error Handling:                                                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ HTTP 404 - User Not Found:                                                           │   │
│  │ • Create minimal user with basic access                                              │   │
│  │ • Log warning for admin review                                                       │   │
│  │ • Allow read-only access to public content                                           │   │
│  │                                                                                       │   │
│  │ HTTP 500 - Service Unavailable:                                                      │   │
│  │ • Fall back to LDAP if configured                                                    │   │
│  │ • Use cached user data if available                                                  │   │
│  │ • Provide degraded service with warning                                              │   │
│  │                                                                                       │   │
│  │ HTTP 401 - Authentication Failed:                                                    │   │
│  │ • Check API key configuration                                                        │   │
│  │ • Log security incident                                                              │   │
│  │ • Deny access and redirect to error page                                             │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Security Considerations and Best Practices

### Cross-Site Request Forgery (CSRF) Protection
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  CSRF PROTECTION IMPLEMENTATION                                                             │
│                                                                                             │
│  CSRF Token Generation:                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Session-based CSRF Token:                                                             │   │
│  │                                                                                       │   │
│  │ 1. Generate unique token per session                                                  │   │
│  │ 2. Store token in session: session.setAttribute("CSRF_TOKEN", token)                 │   │
│  │ 3. Include token in all forms and AJAX requests                                       │   │
│  │ 4. Validate token on all state-changing operations                                    │   │
│  │                                                                                       │   │
│  │ Token Format: Base64(HMAC-SHA256(sessionId + timestamp + secret))                    │   │
│  │ Token Lifetime: Same as session (30 minutes)                                          │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Form Integration:                                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ <form method="POST" action="/9900/safety-manual/edit">                                │   │
│  │   <input type="hidden" name="csrfToken" value="${csrfToken}" />                       │   │
│  │   <input type="text" name="title" value="Safety Manual" />                            │   │
│  │   <button type="submit">Save Changes</button>                                         │   │
│  │ </form>                                                                               │   │
│  │                                                                                       │   │
│  │ AJAX Integration:                                                                     │   │
│  │ $.ajaxSetup({                                                                         │   │
│  │   beforeSend: function(xhr) {                                                         │   │
│  │     xhr.setRequestHeader('X-CSRF-Token', '${csrfToken}');                            │   │
│  │   }                                                                                   │   │
│  │ });                                                                                   │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Server-side Validation:                                                                   │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ def validateCSRFToken(request: HttpServletRequest): Boolean = {                       │   │
│  │   val sessionToken = request.getSession.getAttribute("CSRF_TOKEN")                    │   │
│  │   val requestToken = Option(request.getParameter("csrfToken"))                        │   │
│  │     .orElse(Option(request.getHeader("X-CSRF-Token")))                                │   │
│  │                                                                                       │   │
│  │   (sessionToken, requestToken) match {                                                │   │
│  │     case (token: String, Some(reqToken)) if token == reqToken => true                │   │
│  │     case _ => false                                                                   │   │
│  │   }                                                                                   │   │
│  │ }                                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Session Security and Hijacking Prevention
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  SESSION SECURITY MEASURES                                                                  │
│                                                                                             │
│  Session Configuration:                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ web.xml Security Settings:                                                            │   │
│  │                                                                                       │   │
│  │ <session-config>                                                                      │   │
│  │   <session-timeout>30</session-timeout>                                               │   │
│  │   <cookie-config>                                                                     │   │
│  │     <http-only>true</http-only>        <!-- Prevent XSS access -->                   │   │
│  │     <secure>true</secure>              <!-- HTTPS only -->                           │   │
│  │     <same-site>Strict</same-site>      <!-- CSRF protection -->                      │   │
│  │   </cookie-config>                                                                    │   │
│  │   <tracking-mode>COOKIE</tracking-mode> <!-- No URL rewriting -->                    │   │
│  │ </session-config>                                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Session Validation:                                                                       │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Per-Request Session Checks:                                                           │   │
│  │                                                                                       │   │
│  │ 1. Session Timeout Validation:                                                        │   │
│  │    • Check last access time                                                           │   │
│  │    • Invalidate if > 30 minutes                                                       │   │
│  │                                                                                       │   │
│  │ 2. IP Address Validation:                                                             │   │
│  │    • Store original IP in session                                                     │   │
│  │    • Compare with current request IP                                                  │   │
│  │    • Log suspicious changes                                                           │   │
│  │                                                                                       │   │
│  │ 3. User Agent Validation:                                                             │   │
│  │    • Store original User-Agent                                                        │   │
│  │    • Detect major changes                                                             │   │
│  │    • Flag potential session hijacking                                                 │   │
│  │                                                                                       │   │
│  │ 4. Session Regeneration:                                                              │   │
│  │    • Regenerate session ID after login                                                │   │
│  │    • Regenerate on privilege escalation                                               │   │
│  │    • Prevent session fixation attacks                                                 │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Suspicious Activity Detection:                                                            │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Security Monitoring:                                                                  │   │
│  │                                                                                       │   │
│  │ • Multiple concurrent sessions from different IPs                                     │   │
│  │ • Rapid succession of requests (potential bot activity)                               │   │
│  │ • Access patterns inconsistent with user behavior                                     │   │
│  │ • Attempts to access unauthorized organizations                                        │   │
│  │ • Failed CSRF token validations                                                       │   │
│  │                                                                                       │   │
│  │ Response Actions:                                                                     │   │
│  │ • Log security events with full context                                               │   │
│  │ • Invalidate suspicious sessions                                                      │   │
│  │ • Require re-authentication                                                           │   │
│  │ • Alert security team for investigation                                               │   │
│  │ • Temporarily block IP if severe violations                                           │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Performance Optimization and Caching

### LDAP Connection Pooling
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  LDAP CONNECTION POOL MANAGEMENT                                                            │
│                                                                                             │
│  Connection Pool Configuration:                                                             │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ LDAP Pool Settings (handboker.properties):                                           │   │
│  │                                                                                       │   │
│  │ # Connection Pool Configuration                                                       │   │
│  │ LDAPPool.initialSize=5                                                                │   │
│  │ LDAPPool.maxActive=20                                                                 │   │
│  │ LDAPPool.maxIdle=10                                                                   │   │
│  │ LDAPPool.minIdle=2                                                                    │   │
│  │ LDAPPool.maxWait=5000                                                                 │   │
│  │                                                                                       │   │
│  │ # Connection Validation                                                               │   │
│  │ LDAPPool.testOnBorrow=true                                                            │   │
│  │ LDAPPool.testOnReturn=false                                                           │   │
│  │ LDAPPool.testWhileIdle=true                                                           │   │
│  │ LDAPPool.validationQuery=(&(objectClass=*))                                          │   │
│  │                                                                                       │   │
│  │ # Connection Lifecycle                                                                │   │
│  │ LDAPPool.timeBetweenEvictionRunsMillis=30000                                          │   │
│  │ LDAPPool.minEvictableIdleTimeMillis=60000                                             │   │
│  │ LDAPPool.numTestsPerEvictionRun=3                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Pool Lifecycle Management:                                                                │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Initialization (Application Startup):                                                │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. Create initial 5 LDAP connections                                            │ │   │
│  │ │ 2. Validate each connection with test query                                     │ │   │
│  │ │ 3. Mark pool as ready for use                                                   │ │   │
│  │ │ 4. Start background validation thread                                           │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  │                                                                                       │   │
│  │ Runtime Operations:                                                                   │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. Borrow connection from pool (max wait 5 seconds)                             │ │   │
│  │ │ 2. Validate connection if testOnBorrow=true                                     │ │   │
│  │ │ 3. Execute LDAP search operation                                                │ │   │
│  │ │ 4. Return connection to pool                                                    │ │   │
│  │ │ 5. Pool automatically creates new connections if needed                         │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  │                                                                                       │   │
│  │ Cleanup (Application Shutdown):                                                       │   │
│  │ ┌─────────────────────────────────────────────────────────────────────────────────┐ │   │
│  │ │ 1. Stop accepting new requests                                                  │ │   │
│  │ │ 2. Wait for active operations to complete                                       │ │   │
│  │ │ 3. Close all pooled connections gracefully                                      │ │   │
│  │ │ 4. Release pool resources                                                       │ │   │
│  │ └─────────────────────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### User Data Caching Strategy
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  USER DATA CACHING IMPLEMENTATION                                                           │
│                                                                                             │
│  Cache Configuration:                                                                       │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Ehcache Configuration (ehcache.xml):                                                 │   │
│  │                                                                                       │   │
│  │ <cache name="ldapUsers"                                                               │   │
│  │        maxElementsInMemory="1000"                                                     │   │
│  │        eternal="false"                                                                │   │
│  │        timeToIdleSeconds="1800"    <!-- 30 minutes -->                               │   │
│  │        timeToLiveSeconds="3600"    <!-- 1 hour -->                                   │   │
│  │        overflowToDisk="false"                                                         │   │
│  │        diskPersistent="false"                                                         │   │
│  │        memoryStoreEvictionPolicy="LRU" />                                             │   │
│  │                                                                                       │   │
│  │ <cache name="userPermissions"                                                         │   │
│  │        maxElementsInMemory="2000"                                                     │   │
│  │        eternal="false"                                                                │   │
│  │        timeToIdleSeconds="900"     <!-- 15 minutes -->                               │   │
│  │        timeToLiveSeconds="1800"    <!-- 30 minutes -->                               │   │
│  │        overflowToDisk="false" />                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Cache Usage Pattern:                                                                      │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ User Lookup Flow with Caching:                                                       │   │
│  │                                                                                       │   │
│  │ 1. Check cache for user data: cache.get(userEmail)                                   │   │
│  │ 2. If cache hit: Return cached LDAPUser object                                       │   │
│  │ 3. If cache miss: Perform LDAP lookup                                                │   │
│  │ 4. Store result in cache: cache.put(userEmail, ldapUser)                             │   │
│  │ 5. Return LDAPUser object to caller                                                  │   │
│  │                                                                                       │   │
│  │ Cache Key Strategy:                                                                   │   │
│  │ • Primary key: user email address                                                     │   │
│  │ • Secondary key: organization-specific permissions                                    │   │
│  │ • Composite key: email + organization for permission cache                           │   │
│  │                                                                                       │   │
│  │ Cache Invalidation Triggers:                                                          │   │
│  │ • User logout (remove user-specific entries)                                         │   │
│  │ • LDAP connection failure (clear all entries)                                        │   │
│  │ • Administrative user changes (targeted invalidation)                                │   │
│  │ • Scheduled refresh (every hour)                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Performance Benefits:                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Metrics (typical production environment):                                            │   │
│  │                                                                                       │   │
│  │ • LDAP lookup time: 200-500ms                                                        │   │
│  │ • Cache lookup time: 1-5ms                                                           │   │
│  │ • Cache hit ratio: 85-95%                                                            │   │
│  │ • Reduced LDAP server load: 90%                                                      │   │
│  │ • Improved page load times: 40-60%                                                   │   │
│  │                                                                                       │   │
│  │ Memory Usage:                                                                         │   │
│  │ • Average LDAPUser object: 2KB                                                       │   │
│  │ • 1000 cached users: ~2MB memory                                                     │   │
│  │ • Permission cache: ~4MB additional                                                  │   │
│  │ • Total cache overhead: <10MB                                                        │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

## Troubleshooting and Diagnostics

### Common Authentication Issues
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  AUTHENTICATION TROUBLESHOOTING GUIDE                                                       │
│                                                                                             │
│  Issue 1: Infinite Redirect Loop                                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Symptoms:                                                                             │   │
│  │ • User redirected to CAS login repeatedly                                             │   │
│  │ • Browser shows "too many redirects" error                                            │   │
│  │ • CAS login appears successful but returns to login                                   │   │
│  │                                                                                       │   │
│  │ Common Causes:                                                                        │   │
│  │ 1. Service URL mismatch between CAS and application                                   │   │
│  │ 2. SSL/TLS certificate issues                                                         │   │
│  │ 3. Incorrect CAS server URL configuration                                             │   │
│  │ 4. Session cookie domain/path misconfiguration                                        │   │
│  │                                                                                       │   │
│  │ Diagnostic Steps:                                                                     │   │
│  │ 1. Check application logs for CAS validation errors                                   │   │
│  │ 2. Verify service URL in handboker.properties matches actual URL                     │   │
│  │ 3. Test CAS server accessibility: curl https://cas.kf.no/cas/login                   │   │
│  │ 4. Validate SSL certificates: openssl s_client -connect cas.kf.no:443                │   │
│  │ 5. Check browser developer tools for cookie issues                                    │   │
│  │                                                                                       │   │
│  │ Resolution:                                                                           │   │
│  │ • Update SiteUrl property to match exact application URL                              │   │
│  │ • Ensure HTTPS is used consistently                                                   │   │
│  │ • Verify CAS server configuration allows the service URL                             │   │
│  │ • Check firewall/proxy settings                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Issue 2: LDAP Connection Timeouts                                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Symptoms:                                                                             │   │
│  │ • Slow login process (>10 seconds)                                                    │   │
│  │ • Intermittent authentication failures                                                │   │
│  │ • "User not found" errors for valid users                                             │   │
│  │ • Application logs show LDAP timeout exceptions                                       │   │
│  │                                                                                       │   │
│  │ Diagnostic Commands:                                                                  │   │
│  │ # Test LDAP connectivity                                                              │   │
│  │ ldapsearch -H ldap://************:389 -D "CN=DelegeringAppUser,..." -W              │   │
│  │            -b "dc=kinn,dc=id,dc=local" "(mail=<EMAIL>)"                           │   │
│  │                                                                                       │   │
│  │ # Check network connectivity                                                          │   │
│  │ telnet ************ 389                                                              │   │
│  │ ping ************                                                                    │   │
│  │                                                                                       │   │
│  │ # Monitor connection pool                                                             │   │
│  │ jconsole -> MBeans -> org.apache.commons.pool -> LDAP connections                    │   │
│  │                                                                                       │   │
│  │ Resolution Steps:                                                                     │   │
│  │ 1. Increase LDAP connection timeout in configuration                                  │   │
│  │ 2. Implement connection pooling with proper validation                                │   │
│  │ 3. Add retry logic for transient failures                                             │   │
│  │ 4. Configure fallback authentication mechanism                                        │   │
│  │ 5. Monitor LDAP server performance and capacity                                       │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Issue 3: Permission Denied After Successful Login                                         │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Symptoms:                                                                             │   │
│  │ • User can log in but sees "Access Denied" pages                                      │   │
│  │ • User appears in logs but has no organization access                                 │   │
│  │ • Permissions seem correct in LDAP but not in application                             │   │
│  │                                                                                       │   │
│  │ Diagnostic Queries:                                                                   │   │
│  │ # Check user's LDAP groups                                                            │   │
│  │ ldapsearch -H ldap://************:389 -D "..." -W                                   │   │
│  │            -b "dc=kinn,dc=id,dc=local"                                               │   │
│  │            "(mail=<EMAIL>)" memberOf                                              │   │
│  │                                                                                       │   │
│  │ # Verify group membership                                                             │   │
│  │ ldapsearch -H ldap://************:389 -D "..." -W                                   │   │
│  │            -b "CN=Org-9900-Users,OU=Groups,DC=kinn,DC=id,DC=local"                  │   │
│  │            "(objectClass=group)" member                                              │   │
│  │                                                                                       │   │
│  │ Resolution:                                                                           │   │
│  │ 1. Verify LDAP group naming conventions match application expectations                │   │
│  │ 2. Check group membership propagation in Active Directory                             │   │
│  │ 3. Clear user cache and retry authentication                                          │   │
│  │ 4. Review organization mapping configuration                                          │   │
│  │ 5. Validate LDAP search filter and base DN settings                                   │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
└─────────────────────────────────────────────────────────────────────────────────────────────┘
```

### Monitoring and Alerting
```
┌─────────────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                             │
│  AUTHENTICATION MONITORING DASHBOARD                                                        │
│                                                                                             │
│  Key Metrics to Monitor:                                                                    │
│  ┌─────────────────────────────────────────────────────────────────────────────────────┐   │
│  │ Authentication Metrics:                                                               │   │
│  │ ├── Successful logins per hour                                                        │   │
│  │ ├── Failed authentication attempts                                                    │   │
│  │ ├── Average authentication time                                                       │   │
│  │ ├── CAS ticket validation success rate                                                │   │
│  │ └── Session timeout occurrences                                                       │   │
│  │                                                                                       │   │
│  │ LDAP Performance Metrics:                                                             │   │
│  │ ├── LDAP query response time                                                          │   │
│  │ ├── LDAP connection pool utilization                                                  │   │
│  │ ├── LDAP connection failures                                                          │   │
│  │ ├── Cache hit ratio for user lookups                                                  │   │
│  │ └── LDAP server availability                                                          │   │
│  │                                                                                       │   │
│  │ Security Metrics:                                                                     │   │
│  │ ├── Unauthorized access attempts                                                      │   │
│  │ ├── Suspicious session activity                                                       │   │
│  │ ├── CSRF token validation failures                                                    │   │
│  │ ├── Multiple concurrent sessions per user                                             │   │
│  │ └── Failed permission checks                                                          │   │
│  └─────────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                             │
│  Alert Thresholds: