package no.kf.handboker.model

import no.kf.db.IDGenerator

case class KFFile (
                   id: String,
                   fileExtension: String,
                   createdName: String,
                   file: Array[Byte]
                 )

object KFFile {

  def apply(fileExtension: String, file: Array[Byte]): KFFile =  {
    val id = IDGenerator.generateUniqueId
    new KFFile(id, fileExtension, s"$id.$fileExtension", file)
  }
}
