import { useState, useEffect, useMemo } from "react";
import {
  Modal,
  Button,
  Icon,
  Select,
  Label,
  Field,
  Help,
  Checkbox,
  Tag,
  Columns,
  Column,
  FormattedDate,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { useSession } from "@/store/services/session/hooks";

import {
  useGetLocalEditorsQuery,
  useAddLocalEditorMutation,
  useDeleteLocalEditorMutation,
} from "@/store/services/handbook/localHandbookApi";
import { useGetSessionEditorsQuery } from "@/store/services/session/sessionApi";
import type { LocalEditor } from "@/types";
import { FormattedMessage } from "react-intl";
import { usePrefixedTranslation } from "@/libs/i18n";

interface LocalEditorsModalProps {
  handbookId: string;
  onHide: () => void;
  isOpen: boolean;
}

interface EmailName {
  email: string;
  displayName: string;
}

export const LocalEditorsModal = ({
  handbookId,
  onHide,
  isOpen,
}: LocalEditorsModalProps) => {
  const { session } = useSession();
  const t = usePrefixedTranslation(
    "editor.containers.HandbookPage.components.LocalEditorsModal"
  );
  const userEmail = session?.user?.email || "";

  const [userHasAccess, setUserHasAccess] = useState<boolean>(true);
  const [newEditors, setNewEditors] = useState<Set<LocalEditor>>(new Set());

  const {
    data: localEditors = [],
    error: editorsError,
    isLoading: isLoadingEditors,
  } = useGetLocalEditorsQuery(handbookId, {
    skip: !isOpen,
  });

  const externalOrgId = session?.organization?.id;
  const { data: orgEditors = [], isLoading: isLoadingOrgEditors } =
    useGetSessionEditorsQuery(externalOrgId || "", {
      skip: !isOpen || !externalOrgId,
    });

  const [addLocalEditor, { isLoading: isAddingEditor }] =
    useAddLocalEditorMutation();
  const [deleteLocalEditor, { isLoading: isDeletingEditor }] =
    useDeleteLocalEditorMutation();

  const isSaving = isAddingEditor || isDeletingEditor;

  useEffect(() => {
    if (editorsError) {
      console.error("Error loading local editors:", editorsError);
      toast.error("Feil ved lasting av lokale redaktører");
    }
  }, [editorsError]);

  useEffect(() => {
    setNewEditors(new Set(localEditors));
  }, [localEditors]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    const currentEditorsSet = new Set(
      localEditors.map((e) => e.rightsHolder.toLowerCase())
    );
    const newEditorsSet = new Set(
      [...newEditors].map((e) => e.rightsHolder.toLowerCase())
    );

    const toSave: LocalEditor[] = [...newEditors].filter(
      (newEditor) =>
        !currentEditorsSet.has(newEditor.rightsHolder.toLowerCase())
    );

    const toDelete: LocalEditor[] = localEditors.filter(
      (curEditor) => !newEditorsSet.has(curEditor.rightsHolder.toLowerCase())
    );

    try {
      for (const editor of toSave) {
        await addLocalEditor({
          handbookId: editor.handbookId,
          rightsHolder: editor.rightsHolder,
          addedBy: editor.addedBy,
        }).unwrap();
      }

      for (const editor of toDelete) {
        if (editor.id) {
          await deleteLocalEditor(editor.id).unwrap();
        }
      }

      if (toSave.length > 0 || toDelete.length > 0) {
        toast.success("Lokale redaktører oppdatert");
      }
      onHide();
    } catch (error) {
      console.error("Error updating local editors:", error);
      toast.error("Feil ved oppdatering av lokale redaktører");
    }
  };

  const onCancel = () => {
    setNewEditors(new Set(localEditors));
    setUserHasAccess(true);
    onHide();
  };

  const filteredEditorPool = useMemo((): EmailName[] => {
    if (!orgEditors || !Array.isArray(orgEditors)) {
      return [];
    }

    const newEditorsEmails = new Set(
      [...newEditors].map((e) => e.rightsHolder.toLowerCase())
    );

    const editors = orgEditors
      .filter(
        (orgEditor) => !newEditorsEmails.has(orgEditor.email.toLowerCase())
      )
      .sort((a, b) => {
        const nameA = a.fullName || a.email;
        const nameB = b.fullName || b.email;
        return nameA.localeCompare(nameB);
      });

    return editors.map((editor) => ({
      email: editor.email,
      displayName: editor.fullName
        ? `${editor.fullName} - ${editor.email}`
        : editor.email,
    }));
  }, [orgEditors, newEditors]);

  return (
    <Modal isOpen={isOpen} onClose={onCancel} autoFocus={false}>
      <form onSubmit={handleSubmit}>
        <Modal.Header onClose={onCancel}>
          <Modal.Title>
            <FormattedMessage id="editor.containers.HandbookPage.components.LocalEditorsModal.title" />
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Field>
            <Label htmlFor="grantAccess">{t("accessTitle")}</Label>
            {isLoadingOrgEditors ? (
              <div
                style={{ textAlign: "center", padding: "1rem", color: "#666" }}
              >
                Laster organisasjonsredaktører...
              </div>
            ) : (
              <>
                <Select
                  id="grantAccess"
                  aria-label={t("accessTitle")}
                  name="grantAccess"
                  options={filteredEditorPool}
                  placeholder="Velg redaktør..."
                  labelKey="displayName"
                  maxMenuHeight="8em"
                  disabled={isSaving}
                  onChange={(selectedEditor: EmailName) => {
                    if (selectedEditor) {
                      if (
                        selectedEditor.email.toLowerCase() ===
                        userEmail.toLowerCase()
                      ) {
                        setUserHasAccess(true);
                      }
                      const updated = new Set(newEditors);
                      updated.add({
                        handbookId,
                        rightsHolder: selectedEditor.email,
                        addedBy: userEmail,
                        addedDate: new Date().toISOString(),
                      } as LocalEditor);
                      setNewEditors(updated);
                    }
                  }}
                />
                <Help>{t("accessHelp")}</Help>
              </>
            )}
          </Field>

          <Label htmlFor="tilganger">
            {`${t("accessListTitle")} `}
            {!userHasAccess && (
              <Tag color="warning">
                <Icon icon="exclamation" size="small" />{" "}
                <FormattedMessage id="editor.containers.HandbookPage.components.LocalEditorsModal.selfDeleteWarning" />
              </Tag>
            )}
          </Label>

          {isLoadingEditors ? (
            <div
              style={{ textAlign: "center", padding: "1rem", color: "#666" }}
            >
              Laster lokale redaktører...
            </div>
          ) : newEditors.size > 0 ? (
            [...newEditors].map((editor) => (
              <Field key={editor.rightsHolder}>
                <Columns gapless>
                  <Column>
                    <Checkbox
                      checked
                      name="hasAccess"
                      disabled={isSaving}
                      onChange={() => {
                        if (
                          editor.rightsHolder.toLowerCase() ===
                          userEmail.toLowerCase()
                        ) {
                          setUserHasAccess(false);
                        }
                        const updated = new Set(newEditors);
                        updated.delete(editor);
                        setNewEditors(updated);
                      }}
                    >
                      {` ${editor.rightsHolder}`}
                    </Checkbox>
                  </Column>
                  <Column style={{ fontSize: "0.8em" }}>
                    {`${editor.addedBy} - `}
                    <FormattedDate
                      value={editor.addedDate}
                      format="yyyy.MM.dd-HH:mm"
                    />
                  </Column>
                </Columns>
              </Field>
            ))
          ) : (
            <FormattedMessage id="editor.containers.HandbookPage.components.LocalEditorsModal.noEditorsWarning" />
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={onCancel} disabled={isSaving}>
            <FormattedMessage id="generic.cancel" />
          </Button>
          <Button
            type="submit"
            color="success"
            disabled={isSaving}
            loading={isSaving}
          >
            <FormattedMessage id="generic.save" />
          </Button>
        </Modal.Footer>
      </form>
    </Modal>
  );
};
