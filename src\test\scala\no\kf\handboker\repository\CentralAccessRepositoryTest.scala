package no.kf.handboker.repository

import java.sql.BatchUpdateException

import org.junit.runner.RunWith
import org.scalatest.{BeforeAndAfterAll, FunSuite}
import org.scalatest.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class CentralAccessRepositoryTest extends FunSuite with DbTestHandler with BeforeAndAfterAll {

  val repository: CentralAccessRepository = componentRegistry.centralAccessRepository

  val defaultExternalOrgId = "9999"

  transactedTest("That we can store a list of accesses") {
    val centralHandbookIds = initCentralHandbookIds()
    repository.persistAccesses(defaultExternalOrgId, centralHandbookIds)
    val savedHandbookIds = repository.retrieveAccesses(defaultExternalOrgId)

    assert(centralHandbookIds === savedHandbookIds)
  }

  transactedTest("That we can remove a list of accesses") {
    val centralHandbookIds = initCentralHandbookIds()
    repository.persistAccesses(defaultExternalOrgId, centralHandbookIds)

    repository.persistAccesses(defaultExternalOrgId, List.empty)
    val emptyHandbookIds = repository.retrieveAccesses(defaultExternalOrgId)

    assert(centralHandbookIds !== emptyHandbookIds)
    assert(emptyHandbookIds.size === 0)
  }

  transactedTest("That we can update a list of accesses") {
    val centralHandbookIds = initCentralHandbookIds()

    repository.persistAccesses(defaultExternalOrgId, centralHandbookIds)

    val newCentralHandbookIds = List("centralHandbook6", "centralHandbook7")

    repository.persistAccesses(defaultExternalOrgId, newCentralHandbookIds)
    val updatedCentralHandbookIds = repository.retrieveAccesses(defaultExternalOrgId)

    assert(updatedCentralHandbookIds !== centralHandbookIds)
    assert(updatedCentralHandbookIds.size === 2)
  }

  transactedTest("That non unique central handbooks for an organization throws an exception") {
    intercept[BatchUpdateException] {
      repository.persistAccesses(defaultExternalOrgId, List("same", "same"))
    }
  }

  transactedTest("That we can save accesses for multiple external organizations") {
    val otherExternalOrgId = "9900"
    val centralHandbookIds = initCentralHandbookIds()
    val otherCentralHandbookIds = List("centralHandbook4", "centralHandbook1")

    repository.persistAccesses(defaultExternalOrgId, centralHandbookIds)
    repository.persistAccesses(otherExternalOrgId, otherCentralHandbookIds)

    val fromDefaultExternalOrgId = repository.retrieveAccesses(defaultExternalOrgId)
    val fromOtherExternalOrgId = repository.retrieveAccesses(otherExternalOrgId)

    assert(fromDefaultExternalOrgId !== fromOtherExternalOrgId)
    assert(fromDefaultExternalOrgId.size === 5)
    assert(fromOtherExternalOrgId.size === 2)
  }

  def initCentralHandbookIds(): List[String] = {
    List(
      "centralHandbook1",
      "centralHandbook2",
      "centralHandbook3",
      "centralHandbook4",
      "centralHandbook5"
    )
  }
}
