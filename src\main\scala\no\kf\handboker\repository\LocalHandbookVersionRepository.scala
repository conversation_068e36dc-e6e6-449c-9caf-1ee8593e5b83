package no.kf.handboker.repository

import no.kf.db.IDGenerator
import no.kf.db.RichSQL._
import no.kf.handboker.config.AppSettingComponent
import no.kf.handboker.model.local.{Handbook, Chapter, Section}
import no.kf.util.Logging
import org.joda.time.DateTime


trait LocalHandbookVersionRepositoryComponent {

  this: AppSettingComponent with DbConnectionManagerComponent =>
  val localHandbookVersionRepository: LocalHandbookVersionRepository

  object LocalHandbookTitleVersionTableDef {
    val tableName = "handbooktitle_version"
    val fieldId = "id"
    val fieldVersionOf = "version_of"
    val fieldTitle = "title"
    val fieldExternalOrgId = "external_org_id"
    val fieldCreatedDate = "created_date"
    val fieldCreatedBy = "created_by"
    val fieldUpdatedDate = "updated_date"
    val fieldUpdatedBy = "updated_by"
    val fieldVersionDate = "version_date"

    val tableColumns = List(
      fieldId,
      fieldVersionOf,
      fieldTitle,
      fieldExternalOrgId,
      fieldCreatedDate,
      fieldCreatedBy,
      fieldUpdatedDate,
      fieldUpdatedBy,
      fieldVersionDate
    )
    val updateTable: String = tableColumns.filterNot(_.matches(s"$fieldId")).mkString("", "=?,", "=?")
    val columnString: String = tableColumns.mkString(",")
    val selectString: String = tableColumns.map(column => tableName + "." + column).mkString(",")
  }

  object LocalHandbookChapterVersionTableDef {
    val tableName = "handbookchapter_version"
    val fieldId = "id"
    val fieldHandbookId = "handbook_id"
    val fieldVersionOf = "version_of"
    val fieldTitle = "title"
    val fieldParentChapterId = "parent_chapter_id"
    val fieldOrderIndex = "orderindex"
    val fieldCreatedDate = "created_date"
    val fieldCreatedBy = "created_by"
    val fieldUpdatedDate = "updated_date"
    val fieldUpdatedBy = "updated_by"
    val fieldVersionDate = "version_date"

    val tableColumns = List(
      fieldId,
      fieldHandbookId,
      fieldVersionOf,
      fieldParentChapterId,
      fieldOrderIndex,
      fieldTitle,
      fieldCreatedDate,
      fieldCreatedBy,
      fieldUpdatedDate,
      fieldUpdatedBy,
      fieldVersionDate
    )
    val updateTable: String = tableColumns.filterNot(_.matches(s"$fieldId")).mkString("", "=?,", "=?")
    val columnString: String = tableColumns.mkString(",")
    val selectString: String = tableColumns.map(column => tableName + "." + column).mkString(",")
  }

  object LocalHandbookSectionVersionTableDef {
    val tableName = "handbooksection_version"
    val fieldId = "id"
    val fieldHandbookId = "handbook_id"
    val fieldVersionOf = "version_of"
    val fieldTitle = "title"
    val fieldHtml = "html"
    val fieldParentChapterId = "parent_chapter_id"
    val fieldOrderIndex = "orderindex"
    val fieldCreatedDate = "created_date"
    val fieldCreatedBy = "created_by"
    val fieldUpdatedDate = "updated_date"
    val fieldUpdatedBy = "updated_by"
    val fieldTextUpdatedDate = "text_updated_date"
    val fieldTextUpdatedBy = "text_updated_by"
    val fieldVersionDate = "version_date"

    val tableColumns = List(
      fieldId,
      fieldHandbookId,
      fieldVersionOf,
      fieldParentChapterId,
      fieldOrderIndex,
      fieldTitle,
      fieldHtml,
      fieldCreatedDate,
      fieldCreatedBy,
      fieldUpdatedDate,
      fieldUpdatedBy,
      fieldTextUpdatedDate,
      fieldTextUpdatedBy,
      fieldVersionDate
    )
    val updateTable: String = tableColumns.filterNot(_.matches(s"$fieldId")).mkString("", "=?,", "=?")
    val columnString: String = tableColumns.mkString(",")
    val selectString: String = tableColumns.map(column => tableName + "." + column).mkString(",")
  }

  class LocalHandbookVersionRepositoryImpl extends LocalHandbookVersionRepository with Logging {

    override def insertLocalHandbookTitleVersion(handbook: Handbook, time: DateTime): Handbook = {
      val newId = IDGenerator.generateUniqueId
      val sql = s"INSERT INTO ${LocalHandbookTitleVersionTableDef.tableName}(${LocalHandbookTitleVersionTableDef.columnString}) " +
        s"values (${#?(LocalHandbookTitleVersionTableDef.tableColumns)})"
      connectionManager.doWithConnection {
        conn =>
          conn.ps(sql) <<
            newId <<
            handbook.id <<
            handbook.title <<
            handbook.externalOrgId <<
            handbook.createdDate <<
            handbook.createdBy <<
            handbook.updatedDate <<
            handbook.updatedBy <<
            time <<!
      }
      handbook.copy(id = Some(newId))
    }

    override def insertLocalHandbookChapterVersion(chapter: Chapter, time: DateTime): Chapter = {
      val newId = IDGenerator.generateUniqueId
      val sql = s"INSERT INTO ${LocalHandbookChapterVersionTableDef.tableName}(${LocalHandbookChapterVersionTableDef.columnString}) " +
        s"values (${#?(LocalHandbookChapterVersionTableDef.tableColumns)})"
      connectionManager.doWithConnection {
        conn =>
          conn.ps(sql) <<
            newId <<
            chapter.handbookId <<
            chapter.id <<
            chapter.parentId <<
            chapter.sortOrder <<
            chapter.title <<
            chapter.createdDate <<
            chapter.createdBy <<
            chapter.updatedDate <<
            chapter.updatedBy <<
            time <<!
      }
      chapter.copy(id = Some(newId))
    }

    override def deleteLocalHandbookChapterVersions(chapterId: String): Unit = {
      val sql = s"DELETE FROM ${LocalHandbookChapterVersionTableDef.tableName} WHERE ${LocalHandbookChapterVersionTableDef.fieldVersionOf} = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << chapterId <<!
      }
    }

    override def retrieveVersionsOfChapter(chapterId: String): List[Chapter] = {
      val sql = s"$localHandbookChapterVersionQuery WHERE ${LocalHandbookChapterVersionTableDef.fieldVersionOf} = ?"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookChapterVersionQuery(conn.ps(sql) << chapterId)
      }
    }

    override def retrieveChapterVersionsInHandbook(localHandbookId: String, time: DateTime): List[Chapter] = {
      val sql = s"$localHandbookChapterVersionQuery c1 WHERE c1.${LocalHandbookChapterVersionTableDef.fieldHandbookId} = ? AND  c1.${LocalHandbookChapterVersionTableDef.fieldVersionDate} = " +
        s"(SELECT MAX(c2.${LocalHandbookChapterVersionTableDef.fieldVersionDate}) FROM ${LocalHandbookChapterVersionTableDef.tableName} c2 " +
        s"WHERE c2.${LocalHandbookChapterVersionTableDef.fieldVersionOf} = c1.${LocalHandbookChapterVersionTableDef.fieldVersionOf} AND c2.${LocalHandbookChapterVersionTableDef.fieldUpdatedDate} <= ?)"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookChapterVersionQuery(conn.ps(sql) << localHandbookId << time)
      }
    }

    override def retrieveChapterVersion(versionId: String): Option[Chapter] = {
      val sql = s"$localHandbookChapterVersionQuery WHERE ${LocalHandbookChapterVersionTableDef.fieldId} = ?"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookChapterVersionQuery(conn.ps(sql) << versionId)
      }.headOption
    }

    override def insertLocalHandbookSectionVersion(section: Section, time: DateTime): Section = {
      val newId = IDGenerator.generateUniqueId
      val sql = s"INSERT INTO ${LocalHandbookSectionVersionTableDef.tableName}(${LocalHandbookSectionVersionTableDef.columnString}) " +
        s"values (${#?(LocalHandbookSectionVersionTableDef.tableColumns)})"
      connectionManager.doWithConnection {
        conn =>
          conn.ps(sql) <<
            newId <<
            section.handbookId <<
            section.id <<
            section.parentId <<
            section.sortOrder <<
            section.title <<
            section.text <<
            section.createdDate <<
            section.createdBy <<
            section.updatedDate <<
            section.updatedBy <<
            section.textUpdatedDate <<
            section.textUpdatedBy <<
            time <<!
      }
      section.copy(id = Some(newId))
    }

    override def deleteLocalHandbookSectionVersions(sectionId: String): Unit = {
      val sql = s"DELETE FROM ${LocalHandbookSectionVersionTableDef.tableName} WHERE ${LocalHandbookSectionVersionTableDef.fieldVersionOf} = ?"
      connectionManager.doWithConnection {
        _.ps(sql) << sectionId <<!
      }
    }

    override def retrieveVersionsOfSection(sectionId: String): List[Section] = {
      val sql = s"$localHandbookSectionVersionQuery WHERE ${LocalHandbookSectionVersionTableDef.fieldVersionOf} = ?"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookSectionVersionQuery(conn.ps(sql) << sectionId)
      }
    }

    override def retrieveSectionVersionsInHandbook(localHandbookId: String, time: DateTime, withText: Boolean = false): List[Section] = {
      val sql = s"$localHandbookSectionVersionQuery s1 WHERE s1.${LocalHandbookSectionVersionTableDef.fieldHandbookId} = ? AND  s1.${LocalHandbookSectionVersionTableDef.fieldVersionDate} = " +
        s"(SELECT MAX(s2.${LocalHandbookSectionVersionTableDef.fieldVersionDate}) FROM ${LocalHandbookSectionVersionTableDef.tableName} s2 " +
        s"WHERE s2.${LocalHandbookSectionVersionTableDef.fieldVersionOf} = s1.${LocalHandbookSectionVersionTableDef.fieldVersionOf} " +
        s"AND s2.${LocalHandbookSectionVersionTableDef.fieldUpdatedDate} <= ? AND s2.${LocalHandbookSectionVersionTableDef.fieldTextUpdatedDate} <= ? )"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookSectionVersionQuery(conn.ps(sql) << localHandbookId << time << time, withText)
      }
    }

    override def retrieveSectionVersion(versionId: String): Option[Section] = {
      val sql = s"$localHandbookSectionVersionQuery WHERE ${LocalHandbookSectionVersionTableDef.fieldId} = ?"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookSectionVersionQuery(conn.ps(sql) << versionId, withText = true)
      }.headOption
    }

    override def retrieveAllTitleVersions(localHandbookId: String): List[Handbook] = {
      val sql = s"$localHandbookTitleVersionQuery WHERE ${LocalHandbookTitleVersionTableDef.fieldVersionOf} = ?"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookTitleVersionQuery(conn.ps(sql) << localHandbookId)
      }
    }

    override def retrieveHandbookTitleVersion(localHandbookId: String, time: DateTime): List[Handbook] = {
      val sql = s"$localHandbookTitleVersionQuery t1 WHERE t1.${LocalHandbookTitleVersionTableDef.fieldVersionOf} = ? AND  t1.${LocalHandbookTitleVersionTableDef.fieldVersionDate} = " +
        s"(SELECT MAX(t2.${LocalHandbookTitleVersionTableDef.fieldVersionDate}) FROM ${LocalHandbookTitleVersionTableDef.tableName} t2 " +
        s"WHERE t2.${LocalHandbookTitleVersionTableDef.fieldUpdatedDate} <= ?)"
      connectionManager.doWithConnection { conn =>
        executeLocalHandbookTitleVersionQuery(conn.ps(sql) << localHandbookId << time)
      }
    }

    private def executeLocalHandbookTitleVersionQuery(localHandbookTitleVersionQueryFunc: => RichPreparedStatement): List[Handbook] = {
      localHandbookTitleVersionQueryFunc <<! populateLocalHandbookTitleVersion
    }

    private def executeLocalHandbookChapterVersionQuery(localHandbookChapterVersionQueryFunc: => RichPreparedStatement): List[Chapter] = {
      localHandbookChapterVersionQueryFunc <<! populateLocalHandbookChapterVersion
    }

    private def executeLocalHandbookSectionVersionQuery(localHandbookSectionVersionQueryFunc: => RichPreparedStatement, withText: Boolean = false): List[Section] = {
      if (withText)
      localHandbookSectionVersionQueryFunc <<! populateLocalHandbookSectionVersion
      else
        localHandbookSectionVersionQueryFunc <<! populateLocalHandbookSectionVersionWithoutText

    }

    private def populateLocalHandbookTitleVersion(rs: RichResultSet): Handbook = {
      val id: Option[String] = rs
      val versionOf: String = rs
      val title: String = rs
      val externalOrgId: String = rs
      val createdDate: DateTime = rs
      val createdBy: String = rs
      val updatedDate: DateTime = rs
      val updatedBy: String = rs

      Handbook(id, title, None, externalOrgId = externalOrgId, createdDate = Some(createdDate), createdBy = Some(createdBy),
        updatedDate = Some(updatedDate), updatedBy = Some(updatedBy), versionOf = Some(versionOf))
    }

    private def populateLocalHandbookSectionVersion(rs: RichResultSet): Section = {
      val id: Option[String] = rs
      val handbookId: String = rs
      val versionOf: String = rs
      val parentChapterId: String = rs
      val sortOrder: Int = rs
      val title: String = rs
      val html: Option[String] = rs
      val createdDate: DateTime = rs
      val createdBy: String = rs
      val updatedDate: DateTime = rs
      val updatedBy: String = rs
      val textUpdatedDate: DateTime = rs
      val textUpdatedBy: String = rs

      Section(id, title, html, None, None, handbookId, parentChapterId, Some(sortOrder), createdDate = Some(createdDate), createdBy = Some(createdBy),
        updatedDate = Some(updatedDate), updatedBy = Some(updatedBy), textUpdatedDate = Some(textUpdatedDate), textUpdatedBy = Some(textUpdatedBy), versionOf = Some(versionOf))
    }

    private def populateLocalHandbookSectionVersionWithoutText(rs: RichResultSet): Section = {
      val id: Option[String] = rs
      val handbookId: String = rs
      val versionOf: String = rs
      val parentChapterId: String = rs
      val sortOrder: Int = rs
      val title: String = rs
      val html: Option[String] = rs
      val createdDate: DateTime = rs
      val createdBy: String = rs
      val updatedDate: DateTime = rs
      val updatedBy: String = rs
      val textUpdatedDate: DateTime = rs
      val textUpdatedBy: String = rs

      Section(id, title, None, None, None, handbookId, parentChapterId, Some(sortOrder), createdDate = Some(createdDate),
        createdBy = Some(createdBy), updatedDate = Some(updatedDate), updatedBy = Some(updatedBy),
        textUpdatedDate = Some(textUpdatedDate), textUpdatedBy = Some(textUpdatedBy), versionOf = Some(versionOf))
    }

    private def populateLocalHandbookChapterVersion(rs: RichResultSet): Chapter = {
      val id: Option[String] = rs
      val handbookId: String = rs
      val versionOf: String = rs
      val parentChapterId: Option[String] = rs
      val sortOrder: Int = rs
      val title: String = rs
      val createdDate: DateTime = rs
      val createdBy: String = rs
      val updatedDate: DateTime = rs
      val updatedBy: String = rs

      Chapter(id, title, None, None, handbookId, parentChapterId, Some(sortOrder), createdDate = Some(createdDate), createdBy = Some(createdBy),
        updatedDate = Some(updatedDate), updatedBy = Some(updatedBy), versionOf = Some(versionOf))
    }

    private val localHandbookTitleVersionQuery = s"SELECT ${LocalHandbookTitleVersionTableDef.columnString} FROM ${LocalHandbookTitleVersionTableDef.tableName} "

    private val localHandbookChapterVersionQuery = s"SELECT ${LocalHandbookChapterVersionTableDef.columnString} FROM ${LocalHandbookChapterVersionTableDef.tableName} "

    private val localHandbookSectionVersionQuery = s"SELECT ${LocalHandbookSectionVersionTableDef.columnString} FROM ${LocalHandbookSectionVersionTableDef.tableName} "

  }

}

trait LocalHandbookVersionRepository {
  def insertLocalHandbookTitleVersion(localHandbookTitleVersion: Handbook, time: DateTime): Handbook

  def insertLocalHandbookChapterVersion(localHandbookChapterVersion: Chapter, time: DateTime): Chapter

  def deleteLocalHandbookChapterVersions(localHandbookChapterId: String): Unit

  def retrieveVersionsOfChapter(localHandbookChapterId: String): List[Chapter]

  def retrieveChapterVersionsInHandbook(localHandbookId: String, time: DateTime): List[Chapter]

  def retrieveChapterVersion(versionId: String): Option[Chapter]

  def insertLocalHandbookSectionVersion(localHandbookSectionVersion: Section, time: DateTime): Section

  def deleteLocalHandbookSectionVersions(localHandbookSectionId: String): Unit

  def retrieveVersionsOfSection(localHandbookSectionId: String): List[Section]

  def retrieveSectionVersionsInHandbook(localHandbookId: String, time: DateTime, withText: Boolean = false): List[Section]

  def retrieveSectionVersion(versionId: String): Option[Section]

  def retrieveHandbookTitleVersion(localHandbookId: String, time: DateTime): List[Handbook]

  def retrieveAllTitleVersions(localHandbookId: String): List[Handbook]
}
