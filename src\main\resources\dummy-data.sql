-- Make sure to delete in the correct order of the foreign keys
DELETE FROM handbook_local_editor
DELETE FROM handbooksection
DELETE FROM handbookchapter
DELETE FROM handbook

DELETE FROM central_handbooks.section
DELETE FROM central_handbooks.chapter
DELETE FROM central_handbooks.handbook

INSERT INTO handbook (id, title, external_org_id, local_change, pending_change, pending_change_updated_date, updated_date, created_date, deleted) VALUES ('fkld-gkld-rklf-koka', 'HMS', '9900', 0, 0, 1480506037000, 1480506037000, 1480506037000, 0)
INSERT INTO handbook (id, title, external_org_id, local_change, pending_change, pending_change_updated_date, updated_date, created_date, deleted) VALUES ('flfg-gqkl-fmvs-ajgd', 'HMT', '9900', 0, 0, 1480506037000, 1480506037000, 1480506037000, 0)

INSERT INTO handbookchapter (id, title, handbook_id, local_change, pending_change, pending_change_updated_date, updated_date, created_date, deleted) VALUES ('4u42-48jf-f84k-49sl', 'Avvik', 'fkld-gkld-rklf-koka', 0, 0, 1480506037000, 1480506037000, 1480506037000, 0)
INSERT INTO handbookchapter (id, title, handbook_id, local_change, pending_change, pending_change_updated_date, updated_date, created_date, deleted) VALUES ('ldks-3jfa-9rmf-2294', 'Ved brann', 'fkld-gkld-rklf-koka', 0, 0, 1480506037000, 1480506037000, 1480506037000, 0)

INSERT INTO handbookchapter (id, title, handbook_id, parent_chapter_id, local_change, pending_change, pending_change_updated_date, updated_date, created_date, deleted) VALUES ('ldks-3jfa-9rmf-2295', 'Ved brann-sub - deleted', 'fkld-gkld-rklf-koka', 'ldks-3jfa-9rmf-2294', 0, 0, 1480506037000, 1480506037000, 1480506037000, 1)

INSERT INTO handbookchapter (id, title, handbook_id, parent_chapter_id, local_change, pending_change, pending_change_updated_date, updated_date, created_date, deleted) VALUES ('ldks-3jfa-9rmf-2296', 'Ved brann-sub-sub - deleted', 'fkld-gkld-rklf-koka', 'ldks-3jfa-9rmf-2295', 0, 0, 1480506037000, 1480506037000, 1480506037000, 1)



INSERT INTO central_handbooks.handbook(central_id, title, updated_date, created_date, updated_by, created_by) VALUES ('fkld-gkld-rklf-kola', 'HMS', 1480506037000, 1480506037000, NULL, 'Samantha')
INSERT INTO central_handbooks.chapter(central_id, title, central_parent_id, central_handbook_id, updated_date, created_date, created_by, updated_by) VALUES ('4u42-48jf-f84k-48sl', 'Avvik', NULL ,'fkld-gkld-rklf-kola', 1480506037000, 1480506037000, 'Samantha', NULL)

INSERT INTO central_handbooks.chapter(central_id, title, central_parent_id, central_handbook_id, updated_date, created_date, created_by, updated_by) VALUES ('4u42-48jf-f84k-48s2', 'Helse', '4u42-48jf-f84k-48sl' ,'fkld-gkld-rklf-kola', 1480506037000, 1480506037000, 'Samantha', NULL)

INSERT INTO central_handbooks.section(id, central_id, title, central_handbook_id, central_parent_id, html, created_date, registered_date, updated_date, created_by, updated_by, sort_order) VALUES ('4u42-48jf-f84k-48ss', '4u42-48jf-f84k-48ss', 'Sub-avvik', 'fkld-gkld-rklf-kola','4u42-48jf-f84k-48sl','<p>Hello World</p>', 1480506037000, 1480506037000, 1480506037000, 'Samantha', '', 0)
