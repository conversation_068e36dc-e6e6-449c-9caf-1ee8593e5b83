alter table changenotification drop constraint fk_changenotification_handbook
CREATE TABLE central_changenotification(id varchar(37), changedescription nvarchar(max), handbook_id varchar(37)constraint fk_changenotification_handbook references handbook, updated bigint not null, chapter_id varchar(37), section_id varchar(37), change_html nvarchar(max), concerns_title smallint, deletion smallint)

sp_rename 'handbook.manualmerge', 'local_change', 'COLUMN';
sp_rename 'handbook.pendingchanges', 'pending_change', 'COLUMN';
sp_rename 'handbook.pending_changes_updated_date', 'pending_change_updated_date', 'COLUMN';

sp_rename 'handbookchapter.manualmerge', 'local_change', 'COLUMN';
sp_rename 'handbookchapter.pendingchanges', 'pending_change', 'COLUMN';
sp_rename 'handbookchapter.pending_changes_updated_date', 'pending_change_updated_date', 'COLUMN';

sp_rename 'handbooksection.manualmerge', 'local_title_change', 'COLUMN';
sp_rename 'handbooksection.pendingchanges', 'pending_title_change', 'COLUMN';
sp_rename 'handbooksection.pending_changes_updated_date', 'pending_change_updated_date', 'COLUMN';

ALTER TABLE handbooksection ADD local_text_change smallint
ALTER TABLE handbooksection ADD pending_text_change smallint
