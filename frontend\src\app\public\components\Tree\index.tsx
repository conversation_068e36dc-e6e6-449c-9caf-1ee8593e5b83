import React from "react";
import { Tree as UITree } from "kf-bui";
import { ChapterItem } from "./ChapterItem";
import type { Handbook, Chapter, Section } from "@/types";

interface TreeProps {
  handbook: Handbook;
  chapters: Chapter[];
  sections: Section[];
  externalOrgId: string;
  handbookId: string;
  activeSections: string[];
  expandedChapters: string[];
  onChapterExpand: (item: Chapter) => void; // Pure expansion for arrows
  onChapterNavigate: (item: Chapter) => void; // Navigation for title clicks
  onSectionNavigate: (sectionId: string) => void; // Section navigation
}

export const Tree: React.FC<TreeProps> = ({
  handbook,
  chapters,
  sections,
  externalOrgId,
  handbookId,
  activeSections,
  expandedChapters,
  onChapterExpand,
  onChapterNavigate,
  onSectionNavigate,
}) => {
  // Get root chapters (chapters without parent)
  const rootChapters = chapters
    .filter((chapter) => !chapter.parentId)
    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  if (!handbook) {
    return <div>No handbook loaded</div>;
  }

  const items = rootChapters.map((chapter) => (
    <ChapterItem
      key={chapter.id}
      chapter={chapter}
      chapters={chapters}
      sections={sections}
      externalOrgId={externalOrgId}
      handbookId={handbookId}
      activeSections={activeSections}
      expandedChapters={expandedChapters}
      onChapterExpand={onChapterExpand}
      onChapterNavigate={onChapterNavigate}
      onSectionNavigate={onSectionNavigate}
    />
  ));

  return <UITree>{items}</UITree>;
};
