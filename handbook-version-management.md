# Handbook Version Management Visualization

## Local and Central Handbook Version Management

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  CENTRAL HANDBOOK VERSION MANAGEMENT                                        │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  CENTRAL HANDBOOK VERSIONING TABLES                                   │  │
│  │                                                                       │  │
│  │  central_handbook                                                     │  │
│  │  ┌───────────┬─────────────┬────────────┬────────────┬───────────┐   │  │
│  │  │ id        │ title       │updated_date│created_date│ version_of│   │  │
│  │  ├───────────┼─────────────┼────────────┼────────────┼───────────┤   │  │
│  │  │ ch-123    │ HMS Handbook│ 1623456789 │ 1623456789 │ NULL      │   │  │
│  │  │ ch-456    │ HMS Handbook│ 1623556789 │ 1623556789 │ ch-123    │   │  │
│  │  │ ch-789    │ HMS Handbook│ 1623656789 │ 1623656789 │ ch-456    │   │  │
│  │  └───────────┴─────────────┴────────────┴────────────┴───────────┘   │  │
│  │                                                                       │  │
│  │  central_chapter                                                      │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────┬───────┐│  │
│  │  │ id        │ title       │parent_ │handbook_ │updated_date│version││  │
│  │  │           │             │id      │id        │            │_of    ││  │
│  │  ├───────────┼─────────────┼────────┼──────────┼────────────┼───────┤│  │
│  │  │ cc-101    │ Safety Rules│ NULL   │ ch-123   │ 1623456789 │ NULL  ││  │
│  │  │ cc-201    │ Safety Rules│ NULL   │ ch-456   │ 1623556789 │ cc-101││  │
│  │  │ cc-301    │ Safety Rules│ NULL   │ ch-789   │ 1623656789 │ cc-201││  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────┴───────┘│  │
│  │                                                                       │  │
│  │  central_section                                                      │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────┬───────┐│  │
│  │  │ id        │ title       │parent_ │handbook_ │updated_date│version││  │
│  │  │           │             │id      │id        │            │_of    ││  │
│  │  ├───────────┼─────────────┼────────┼──────────┼────────────┼───────┤│  │
│  │  │ cs-102    │ Equipment   │ cc-101 │ ch-123   │ 1623456789 │ NULL  ││  │
│  │  │ cs-202    │ Equipment   │ cc-201 │ ch-456   │ 1623556789 │ cs-102││  │
│  │  │ cs-302    │ Equipment   │ cc-301 │ ch-789   │ 1623656789 │ cs-202││  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────┴───────┘│  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  LOCAL HANDBOOK VERSION MANAGEMENT                                          │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  LOCAL HANDBOOK CURRENT TABLES                                        │  │
│  │                                                                       │  │
│  │  handbook                                                             │  │
│  │  ┌───────────┬─────────────┬────────────────┬───────────────┐        │  │
│  │  │ id        │ title       │importedhandbook│external_org_id│        │  │
│  │  │           │             │_id             │               │        │  │
│  │  ├───────────┼─────────────┼────────────────┼───────────────┤        │  │
│  │  │ lh-123    │ HMS Handbook│ ch-789         │ 9900          │        │  │
│  │  └───────────┴─────────────┴────────────────┴───────────────┘        │  │
│  │                                                                       │  │
│  │  chapter                                                              │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────────┐    │  │
│  │  │ id        │ title       │parent_ │handbook_ │importedhandbook│    │  │
│  │  │           │             │id      │id        │_chapter_id     │    │  │
│  │  ├───────────┼─────────────┼────────┼──────────┼────────────────┤    │  │
│  │  │ lc-101    │ Safety Rules│ NULL   │ lh-123   │ cc-301         │    │  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────────┘    │  │
│  │                                                                       │  │
│  │  section                                                              │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────────┐    │  │
│  │  │ id        │ title       │parent_ │handbook_ │importedhandbook│    │  │
│  │  │           │             │id      │id        │_section_id     │    │  │
│  │  ├───────────┼─────────────┼────────┼──────────┼────────────────┤    │  │
│  │  │ ls-102    │ Equipment   │ lc-101 │ lh-123   │ cs-302         │    │  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────────┘    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  LOCAL HANDBOOK VERSION TABLES                                        │  │
│  │                                                                       │  │
│  │  handbooktitle_version                                                │  │
│  │  ┌───────────┬─────────────┬────────────┬────────────┬───────────┐   │  │
│  │  │ id        │ title       │updated_date│created_date│ version_of│   │  │
│  │  ├───────────┼─────────────┼────────────┼────────────┼───────────┤   │  │
│  │  │ lhv-001   │ HMS Handbook│ 1623456789 │ 1623456789 │ lh-123    │   │  │
│  │  │ lhv-002   │ HMS Handbook│ 1623556789 │ 1623556789 │ lh-123    │   │  │
│  │  │ lhv-003   │ HMS Handbook│ 1623656789 │ 1623656789 │ lh-123    │   │  │
│  │  └───────────┴─────────────┴────────────┴────────────┴───────────┘   │  │
│  │                                                                       │  │
│  │  handbookchapter_version                                              │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────┬───────┐│  │
│  │  │ id        │ title       │parent_ │handbook_ │updated_date│version││  │
│  │  │           │             │id      │id        │            │_of    ││  │
│  │  ├───────────┼─────────────┼────────┼──────────┼────────────┼───────┤│  │
│  │  │ lcv-001   │ Safety Rules│ NULL   │ lh-123   │ 1623456789 │ lc-101││  │
│  │  │ lcv-002   │ Safety Rules│ NULL   │ lh-123   │ 1623556789 │ lc-101││  │
│  │  │ lcv-003   │ Safety Rules│ NULL   │ lh-123   │ 1623656789 │ lc-101││  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────┴───────┘│  │
│  │                                                                       │  │
│  │  handbooksection_version                                              │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────┬───────┐│  │
│  │  │ id        │ title       │parent_ │handbook_ │updated_date│version││  │
│  │  │           │             │id      │id        │            │_of    ││  │
│  │  ├───────────┼─────────────┼────────┼──────────┼────────────┼───────┤│  │
│  │  │ lsv-001   │ Equipment   │ lc-101 │ lh-123   │ 1623456789 │ ls-102││  │
│  │  │ lsv-002   │ Equipment   │ lc-101 │ lh-123   │ 1623556789 │ ls-102││  │
│  │  │ lsv-003   │ Equipment   │ lc-101 │ lh-123   │ 1623656789 │ ls-102││  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────┴───────┘│  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Version Creation Process Flow

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  LOCAL HANDBOOK VERSION CREATION PROCESS                                    │
│                                                                             │
│  1. BEFORE LOCAL EDIT                                                       │
│                                                                             │
│  section                                                                    │
│  ┌───────────┬─────────────┬────────┬──────────┬────────────────┬─────────┐│
│  │ id        │ title       │parent_ │handbook_ │importedhandbook│text     ││
│  │           │             │id      │id        │_section_id     │         ││
│  ├───────────┼─────────────┼────────┼──────────┼────────────────┼─────────┤│
│  │ ls-102    │ Equipment   │ lc-101 │ lh-123   │ cs-302         │ <p>Use  ││
│  │           │             │        │          │                │protect..││
│  └───────────┴─────────────┴────────┴──────────┴────────────────┴─────────┘│
│                                                                             │
│  handbooksection_version                                                    │
│  ┌───────────┬─────────────┬────────┬──────────┬────────────┬───────┬─────┐│
│  │ id        │ title       │parent_ │handbook_ │updated_date│version│text ││
│  │           │             │id      │id        │            │_of    │     ││
│  ├───────────┼─────────────┼────────┼──────────┼────────────┼───────┼─────┤│
│  │ (empty)   │             │        │          │            │       │     ││
│  └───────────┴─────────────┴────────┴──────────┴────────────┴───────┴─────┘│
│                                                                             │
│  2. LOCAL EDIT OCCURS                                                       │
│                                                                             │
│  // In LocalHandbookService.scala                                           │
│  override def persistSection(section: Section): Section = {                 │
│    val oldSection = handbookRepository.retrieveSection(section.id.get).get  │
│                                                                             │
│    // Check if content has changed                                          │
│    if (section.title != oldSection.title ||                                 │
│        section.text != oldSection.text ||                                   │
│        section.sortOrder != oldSection.sortOrder ||                         │
│        section.parentId != oldSection.parentId) {                           │
│                                                                             │
│      // Create version of previous state                                    │
│      localHandbookVersionRepository.insertLocalHandbookSectionVersion(      │
│        oldSection, DateTime.now)                                            │
│    }                                                                        │
│                                                                             │
│    // Save the new state                                                    │
│    handbookRepository.persistSection(section)                               │
│  }                                                                          │
│                                                                             │
│  3. AFTER LOCAL EDIT                                                        │
│                                                                             │
│  section                                                                    │
│  ┌───────────┬─────────────┬────────┬──────────┬────────────────┬─────────┐│
│  │ id        │ title       │parent_ │handbook_ │importedhandbook│text     ││
│  │           │             │id      │id        │_section_id     │         ││
│  ├───────────┼─────────────┼────────┼──────────┼────────────────┼─────────┤│
│  │ ls-102    │ Equipment   │ lc-101 │ lh-123   │ cs-302         │ <p>Use  ││
│  │           │             │        │          │                │protect..││
│  │           │             │        │          │                │Follow   ││
│  │           │             │        │          │                │local...</││
│  └───────────┴─────────────┴────────┴──────────┴────────────────┴─────────┘│
│                                                                             │
│  handbooksection_version                                                    │
│  ┌───────────┬─────────────┬────────┬──────────┬────────────┬───────┬─────┐│
│  │ id        │ title       │parent_ │handbook_ │updated_date│version│text ││
│  │           │             │id      │id        │            │_of    │     ││
│  ├───────────┼─────────────┼────────┼──────────┼────────────┼───────┼─────┤│
│  │ lsv-001   │ Equipment   │ lc-101 │ lh-123   │ 1623656789 │ ls-102│<p>Use││
│  │           │             │        │          │            │       │protec││
│  │           │             │        │          │            │       │t...</││
│  └───────────┴─────────────┴────────┴──────────┴────────────┴───────┴─────┘│
│                                                                             │
│  4. CENTRAL HANDBOOK SYNCHRONIZATION                                        │
│                                                                             │
│  // In HandbookSynchronizationService.scala                                 │
│  private def synchronizeLocalHandbook(localHandbookId: String,              │
│                                      centralhandbookId: String): Unit = {   │
│    // ... synchronization logic ...                                         │
│                                                                             │
│    // When central content changes and conflicts with local changes         │
│    if (sectionChanged) {                                                    │
│      // Create version of current state before applying central changes     │
│      localHandbookVersionRepository.insertLocalHandbookSectionVersion(      │
│        section, DateTime.now)                                               │
│                                                                             │
│      // Apply central changes or mark as pending                            │
│      // ...                                                                 │
│    }                                                                        │
│  }                                                                          │
│                                                                             │
│  5. AFTER CENTRAL SYNCHRONIZATION                                           │
│                                                                             │
│  handbooksection_version                                                    │
│  ┌───────────┬─────────────┬────────┬──────────┬────────────┬───────┬─────┐│
│  │ id        │ title       │parent_ │handbook_ │updated_date│version│text ││
│  │           │             │id      │id        │            │_of    │     ││
│  ├───────────┼─────────────┼────────┼──────────┼────────────┼───────┼─────┤│
│  │ lsv-001   │ Equipment   │ lc-101 │ lh-123   │ 1623656789 │ ls-102│<p>Use││
│  │           │             │        │          │            │       │protec││
│  │           │             │        │          │            │       │t...</││
│  │ lsv-002   │ Equipment   │ lc-101 │ lh-123   │ 1623756789 │ ls-102│<p>Use││
│  │           │             │        │          │            │       │protec││
│  │           │             │        │          │            │       │t...  ││
│  │           │             │        │          │            │       │Follow││
│  │           │             │        │          │            │       │local.││
│  │           │             │        │          │            │       │..</p>││
│  └───────────┴─────────────┴────────┴──────────┴────────────┴───────┴─────┘│
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Version Retrieval Process

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  VERSION RETRIEVAL PROCESS                                                  │
│                                                                             │
│  1. RETRIEVING VERSIONS OF A SECTION                                        │
│                                                                             │
│  // In LocalHandbookVersionService.scala                                    │
│  override def retrieveSectionVersions(sectionId: String): List[Section] = { │
│    localHandbookVersionRepository.retrieveVersionsOfSection(sectionId)      │
│  }                                                                          │
│                                                                             │
│  // In LocalHandbookVersionRepository.scala                                 │
│  override def retrieveVersionsOfSection(sectionId: String): List[Section] = {│
│    val sql = s"SELECT ${LocalHandbookSectionVersionTableDef.selectString} " +│
│      s"FROM ${LocalHandbookSectionVersionTableDef.tableName} " +            │
│      s"WHERE ${LocalHandbookSectionVersionTableDef.fieldVersionOf} = ? " +  │
│      s"ORDER BY ${LocalHandbookSectionVersionTableDef.fieldVersionDate} DESC"│
│                                                                             │
│    connectionManager.doWithConnection { conn =>                             │
│      conn.ps(sql) << sectionId <<! { rs =>                                  │
│        // Map result set to Section objects                                 │
│        // ...                                                               │
│      }                                                                      │
│    }                                                                        │
│  }                                                                          │
│                                                                             │
│  2. RETRIEVING A SPECIFIC VERSION                                           │
│                                                                             │
│  // In LocalHandbookVersionService.scala                                    │
│  override def retrieveSectionVersion(versionId: String): Option[Section] = {│
│    localHandbookVersionRepository.retrieveSectionVersion(versionId)         │
│  }                                                                          │
│                                                                             │
│  // In LocalHandbookVersionRepository.scala                                 │
│  override def retrieveSectionVersion(versionId: String): Option[Section] = {│
│    val sql = s"SELECT ${LocalHandbookSectionVersionTableDef.selectString} " +│
│      s"FROM ${LocalHandbookSectionVersionTableDef.tableName} " +            │
│      s"WHERE ${LocalHandbookSectionVersionTableDef.fieldId} = ?"            │
│                                                                             │
│    connectionManager.doWithConnection { conn =>                             │
│      conn.ps(sql) << versionId <<! { rs =>                                  │
│        // Map result set to Section object                                  │
│        // ...                                                               │
│      }                                                                      │
│    }.headOption                                                             │
│  }                                                                          │
│                                                                             │
│  3. RETRIEVING HANDBOOK AT A SPECIFIC POINT IN TIME                         │
│                                                                             │
│  // In LocalHandbookService.scala                                           │
│  override def retrieveChapterAndSectionVersions(                            │
│    handbookId: String,                                                      │
│    time: Option[DateTime]                                                   │
│  ): (List[Chapter], List[Section]) = {                                      │
│                                                                             │
│    if (time.isDefined) {                                                    │
│      // Get chapter versions as of the specified time                       │
│      val chapterVersions = localHandbookVersionRepository                   │
│        .retrieveChapterVersionsInHandbook(handbookId, time.get)             │
│                                                                             │
│      // Get section versions as of the specified time                       │
│      val sectionVersions = localHandbookVersionRepository                   │
│        .retrieveSectionVersionsInHandbook(handbookId, time.get, true)       │
│                                                                             │
│      // Get current chapters and sections                                   │
│      val rawUnversionedChapters = handbookRepository                        │
│        .retrieveDeletedAndNotDeletedChaptersForHandbook(handbookId)         │
│      val rawUnversionedSections = handbookRepository                        │
│        .retrieveDeletedAndNotDeletedSectionsForHandbook(handbookId)         │
│                                                                             │
│      // Filter to include only items that existed at the specified time     │
│      val unversionedChapters = rawUnversionedChapters.filter(c => {         │
│        if (chapterVersions.exists(cv => cv.versionOf == c.id))              │
│          c.updatedDate.get.compareTo(time.get) <= 0                         │
│        else                                                                 │
│          c.createdDate.get.compareTo(time.get) <= 0                         │
│      })                                                                     │
│                                                                             │
│      // Similar filtering for sections                                      │
│      // ...                                                                 │
│                                                                             │
│      // Combine and return the results                                      │
│      (unversionedChapters ++ chapterVersions, unversionedSections ++ sectionVersions)│
│    } else {                                                                 │
│      // Return current state if no time specified                           │
│      // ...                                                                 │
│    }                                                                        │
│  }                                                                          │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## UI Representation of Versions

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  VERSION HISTORY UI                                                         │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │ Version History: Equipment                                            │  │
│  │                                                                       │  │
│  │ ┌─────────────────────────────────────────────────────────────────┐  │  │
│  │ │ Version from 2021-06-14 15:39:49 <NAME_EMAIL>│  │  │
│  │ │                                                                 │  │  │
│  │ │ <p>Use protective gear at all times. Inspect before each use.   │  │  │
│  │ │ Follow local protocols.</p>                                     │  │  │
│  │ │                                                                 │  │  │
│  │ │ [Restore this version]                                          │  │  │
│  │ └─────────────────────────────────────────────────────────────────┘  │  │
│  │                                                                       │  │
│  │ ┌─────────────────────────────────────────────────────────────────┐  │  │
│  │ │ Version from 2021-06-13 12:26:29 <NAME_EMAIL>│  │  │
│  │ │                                                                 │  │  │
│  │ │ <p>Use protective gear at all times. Follow local protocols.</p>│  │  │
│  │ │                                                                 │  │  │
│  │ │ [Restore this version]                                          │  │  │
│  │ └─────────────────────────────────────────────────────────────────┘  │  │
│  │                                                                       │  │
│  │ ┌─────────────────────────────────────────────────────────────────┐  │  │
│  │ │ Version from 2021-06-12 09:13:09 <NAME_EMAIL>       │  │  │
│  │ │                                                                 │  │  │
│  │ │ <p>Use protective gear at all times.</p>                        │  │  │
│  │ │                                                                 │  │  │
│  │ │ [Restore this version]                                          │  │  │
│  │ └─────────────────────────────────────────────────────────────────┘  │  │
│  │                                                                       │  │
│  │ [Close]                                                               │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Version Management Implementation Details

The version management system is implemented through several key components:

1. **Version Tables**: Separate tables store versions of handbooks, chapters, and sections
2. **Version Creation**: Versions are created before content changes
3. **Version Retrieval**: API to retrieve version history or content at a specific point in time
4. **UI Components**: Interface for viewing and restoring previous versions

The system creates versions in these scenarios:
- When a user edits local content
- Before synchronizing with central content
- Before deleting content
- When resolving conflicts between local and central changes

This comprehensive versioning system allows organizations to:
- Track all changes to their handbooks
- Restore previous versions if needed
- View the state of a handbook at any point in time
- Understand how content has evolved over time