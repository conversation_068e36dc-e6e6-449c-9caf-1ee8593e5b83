// Clean HTML function to extract body content if full document
export const cleanHtml = (html: string): string => {
  if (!html) return "";

  const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
  if (bodyMatch) {
    return bodyMatch[1];
  }

  if (html.includes("<html")) {
    const cleaned = html
      .replace(/<html[^>]*>/gi, "")
      .replace(/<\/html>/gi, "")
      .replace(/<head[^>]*>[\s\S]*?<\/head>/gi, "")
      .replace(/<body[^>]*>/gi, "")
      .replace(/<\/body>/gi, "")
      .trim();
    return cleaned;
  }

  return html;
};
