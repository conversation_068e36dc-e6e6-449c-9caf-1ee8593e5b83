package no.kf.handboker.repository

import no.kf.db.IDGenerator
import no.kf.db.RichSQL._
import no.kf.handboker.model.local.CentralChangeNotification
import no.kf.util.Logging

trait CentralNotificationRepositoryComponent {
  this: DbConnectionManagerComponent
    with HandbookRepositoryComponent =>

  val centralNotificationRepository: CentralNotificationRepository

  object CentralChangeNotificationTableDef {
    val notificationTableName = "central_changenotification"

    val fieldId = "id"
    val fieldChangeDescription = "changedescription"
    val fieldHandbookId = "handbook_id"
    val fieldDate = "updated"
    val fieldChapterId = "chapter_id"
    val fieldSectionId = "section_id"
    val fieldChangeHTML = "change_html"
    val fieldConcernsTitle = "concerns_title"
    val fieldDeletion = "deletion"

    val notificationCols = List(
      fieldId,
      fieldChangeDescription,
      fieldHandbookId,
      fieldDate,
      fieldChapterId,
      fieldSectionId,
      fieldChangeHTML,
      fieldConcernsTitle,
      fieldDeletion)
    val colString = notificationCols.mkString(",")
    val updateString = notificationCols.filterNot(List(fieldId, fieldHandbookId, fieldChapterId, fieldSectionId).contains(_)).mkString("", "=?,", "=?")
  }


  class CentralNotificationRepositoryImpl extends CentralNotificationRepository with Logging {

    import CentralChangeNotificationTableDef._


    override def persistCentralChangeNotification(notification: CentralChangeNotification): CentralChangeNotification = {

      def insertNotification(notification: CentralChangeNotification): CentralChangeNotification = {
        val sql = s"INSERT INTO $notificationTableName($colString) VALUES(${#?(notificationCols)})"

        val newId = IDGenerator.generateUniqueId
        connectionManager.doWithConnection {
          _.ps(sql) <<
            newId <<
            notification.changeDescription <<
            notification.handbookId <<
            notification.changedDate <<
            notification.chapterId <<
            notification.sectionId <<
            notification.changeHTML <<
            notification.concernsTitle <<
            notification.deletion <<!
        }
        notification.copy(id = Some(newId))
      }

      def updateNotification(notification: CentralChangeNotification): CentralChangeNotification = {
        val sql = s"UPDATE $notificationTableName SET $updateString WHERE $fieldId = ?"

        connectionManager.doWithConnection {
          _.ps(sql) <<
            notification.changeDescription <<
            notification.changedDate <<
            notification.changeHTML <<
            notification.concernsTitle <<
            notification.deletion <<
            notification.id.get <<!
        }
        notification
      }

      def retrieve(notification: CentralChangeNotification): Option[CentralChangeNotification] = {
        val chapterIdCrit = if (notification.chapterId.isDefined) s"$fieldChapterId = '${notification.chapterId.get}'" else s"$fieldChapterId is null"
        val sectionIdCrit = if (notification.sectionId.isDefined) s"$fieldSectionId = '${notification.sectionId.get}'" else s"$fieldSectionId is null"
        val sql = s"select $colString from $notificationTableName where $fieldHandbookId = ? and $chapterIdCrit and $sectionIdCrit"
        connectionManager.doWithConnection {
          _.ps(sql) <<
            notification.handbookId <<! populateNotification toList
        }.headOption
      }

      val maybeNotification = retrieve(notification)
      if (maybeNotification.isDefined) {
        updateNotification(notification.copy(id = maybeNotification.get.id))
      } else {
        insertNotification(notification)
      }
    }

    override def retrieveNotifications(handbookId: String): List[CentralChangeNotification] = {
      val sql = s"SELECT $colString FROM $notificationTableName WHERE $fieldHandbookId = ?"
      connectionManager.doWithConnection {
        _.ps(sql) <<
          handbookId <<! populateNotification toList
      }

    }

    def populateNotification(rs: RichResultSet): CentralChangeNotification = {
      CentralChangeNotification(rs, rs, rs, rs, rs, rs, rs, rs, rs)
    }

  }

}

trait CentralNotificationRepository {
  type EmailAddress = String

  def persistCentralChangeNotification(notification: CentralChangeNotification): CentralChangeNotification

  def retrieveNotifications(handbook_id: String): List[CentralChangeNotification]

}
