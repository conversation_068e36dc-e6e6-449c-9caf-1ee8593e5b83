package no.kf.handboker.model.central

import org.joda.time.DateTime
/*
  sortOrder,
  created/updatedDate and
  created/updatedBy are set as default None/0
  because they are set in repository level.
 */
case class CentralChapter( id: Option[String],
                           title: String,
                           parentId: Option[String] = None,
                           centralHandbookId: String,
                           versionOf: Option[String] = None,
                           createdDate: Option[DateTime] = None,
                           updatedDate: Option[DateTime] = None,
                           updatedDateBeforePublish: Option[DateTime] = None,
                           createdBy: Option[String] = None,
                           updatedBy: Option[String] = None,
                           sortOrder: Int = 0
                         )
