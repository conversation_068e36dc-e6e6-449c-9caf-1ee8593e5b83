package no.kf.handboker.rest


import java.nio.charset.StandardCharsets.UTF_8
import no.kf.handboker.ProductionRegistry.componentRegistry
import no.kf.rest.ScalatraExceptions
import no.kf.rest.support.JsonSupport
import org.scalatra.ScalatraServlet

class ImageServlet extends ScalatraServlet with JsonSupport {

  lazy val imageService = componentRegistry.imageService

  //TODO: <PERSON>le requests to non-existant images, empty route, possibly limit access only to images in reading link
  get("/:imgId") {
    try {
      val imageId = extractRequiredParam("imgId")
      val image = imageService.retrieveImage(imageId)

      contentType = image.contentType.getOrElse("image/png")
      response.addHeader("Content-Disposition", generateContentDispositionValue(image.name))
      image.byteArray
    } catch {
      case e: Exception =>
        ScalatraExceptions.notFound(Some("Image not found"))
    }
  }

  def generateContentDispositionValue(fileName: String): String = {
    val urlEncodedFileName = new org.scalatra.util.RicherString.RicherStringImplicitClass(fileName).urlEncode(UTF_8)
    s"inline; filename=${"\""}$fileName${"\""}; filename*=UTF-8''$urlEncodedFileName"
  }

}
