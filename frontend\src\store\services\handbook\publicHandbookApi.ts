import { publicApi } from "@/store/api";
import type {
  PublicHandbookResponse,
  Section,
  FileLink,
} from "@/types";

const PUBLIC_BASE_URL = "/public";

export const publicHandbookApi = publicApi.injectEndpoints({
  overrideExisting: process.env.NODE_ENV === "development",
  endpoints: (builder) => ({
    // Fetch public base configuration
    fetchPublicConfig: builder.query<
      {
        basename: string;
        editorBasename: string;
        logoUrlStart: string;
        bannerUrlStart: string;
      },
      void
    >({
      query: () => ({
        url: `${PUBLIC_BASE_URL}/`,
        method: "GET",
      }),
      keepUnusedDataFor: 3600,
    }),

    // Fetch public handbook with chapters and sections
    fetchPublicHandbook: builder.query<
      PublicHandbookResponse,
      { externalOrgId: string; handbookId: string }
    >({
      query: ({ externalOrgId, handbookId }) => ({
        url: `${PUBLIC_BASE_URL}/${externalOrgId}/${handbookId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 900,
      providesTags: (_, __, { handbookId }) => [
        { type: "Handbook", id: handbookId },
      ],
    }),

    // Fetch individual section content (lazy loaded)
    fetchPublicSection: builder.query<Section, string>({
      query: (sectionId) => ({
        url: `${PUBLIC_BASE_URL}/section/${sectionId}`,
        method: "GET",
      }),
      providesTags: (_, __, sectionId) => [{ type: "Section", id: sectionId }],
    }),



    // Download attachment file (legacy endpoint has typo: attactment)
    downloadPublicAttachment: builder.mutation<Blob, string>({
      query: (fileId) => ({
        url: `${PUBLIC_BASE_URL}/attactment/${fileId}`,
        method: "GET",
        responseHandler: (response) => response.blob(),
      }),
    }),

    // Fetch reading link data
    getReadingLinkData: builder.query<{ title: string; text: string }, string>({
      query: (linkId) => ({
        url: `${PUBLIC_BASE_URL}/linkdata/${linkId}`,
        method: "GET",
      }),
      providesTags: (_, __, linkId) => [{ type: "ReadingLink", id: linkId }],
    }),

    // Get chapter attachment count
    getPublicChapterAttachmentCount: builder.query<{ count: number }, string>({
      query: (chapterId) => ({
        url: `${PUBLIC_BASE_URL}/chapter/${chapterId}/attachments/count`,
        method: "GET",
      }),
      providesTags: (_, __, chapterId) => [{ type: "Attachment", id: `chapter-${chapterId}` }],
    }),

    // Get section attachment count
    getPublicSectionAttachmentCount: builder.query<{ count: number }, string>({
      query: (sectionId) => ({
        url: `${PUBLIC_BASE_URL}/section/${sectionId}/attachments/count`,
        method: "GET",
      }),
      providesTags: (_, __, sectionId) => [{ type: "Attachment", id: `section-${sectionId}` }],
    }),

    // Get chapter attachments list
    getPublicChapterAttachments: builder.query<FileLink[], string>({
      query: (chapterId) => ({
        url: `${PUBLIC_BASE_URL}/chapter/${chapterId}/attachments`,
        method: "GET",
      }),
      providesTags: (_, __, chapterId) => [{ type: "Attachment", id: `chapter-${chapterId}` }],
    }),

    // Get section attachments list
    getPublicSectionAttachments: builder.query<FileLink[], string>({
      query: (sectionId) => ({
        url: `${PUBLIC_BASE_URL}/section/${sectionId}/attachments`,
        method: "GET",
      }),
      providesTags: (_, __, sectionId) => [{ type: "Attachment", id: `section-${sectionId}` }],
    }),
  }),
});

export const {
  useFetchPublicConfigQuery,
  useLazyFetchPublicConfigQuery,
  useFetchPublicHandbookQuery,
  useLazyFetchPublicHandbookQuery,
  useFetchPublicSectionQuery,
  useLazyFetchPublicSectionQuery,
  useDownloadPublicAttachmentMutation,
  useGetReadingLinkDataQuery,
  useLazyGetReadingLinkDataQuery,
  useGetPublicChapterAttachmentCountQuery,
  useGetPublicSectionAttachmentCountQuery,
  useGetPublicChapterAttachmentsQuery,
  useGetPublicSectionAttachmentsQuery,
} = publicHandbookApi;
