package no.kf.handboker.util.section

import better.files.File
import no.kf.handboker.ProductionRegistry
import no.kf.handboker.ProductionRegistry.componentRegistry.imageService
import no.kf.handboker.config.WebAppPath
import no.kf.handboker.model.local.Section
import no.kf.util.Logging

import java.awt.Color
import java.awt.image.BufferedImage
import java.io.ByteArrayOutputStream
import java.net.URL
import java.util.Base64
import javax.imageio.ImageIO
import scala.annotation.tailrec

object ImageUtil extends Logging {

  lazy val webAppPath = ProductionRegistry.componentRegistry.settings.settingFor(WebAppPath)
  
  def updateImageDimensions(section: Section): (Section, Int, Int) = {
    section.text match {
      case Some(text) =>
        if(section.text.get.contains("<img")){
          val imgRegex = s"""(<img [^>]* */>)""".r
          val imageTagsWithoutSize = imgRegex.findAllMatchIn(text).map(matchedId => matchedId.group(1)).toList.filter(tag => !tag.contains("width") || !tag.contains("height"))
          val externalImagesWithSize = imageTagsWithoutSize.map( imageTag => {
            try{
              val idAndExtRegex = s"""src="$webAppPath/images/([a-zA-Z0-9-]+).(\\w+)"""".r
              if(idAndExtRegex.findAllMatchIn(imageTag).isEmpty){
                //println("imageTag: " + imageTag)
                val srcRegex = "(?<=src=)(.*?)(?=\\s)".r
                val srcUrl = srcRegex.findFirstIn(imageTag).getOrElse("").replace("\"","")
                //println("srcUrl: " + srcUrl)
                
                try{
                  val image = ImageIO.read(new URL(srcUrl))
                  val height = image.getHeight
                  val width = image.getWidth

                  val (scaledWidth: Int, scaledHeight: Int) = scaleDimensions(List(), height, width)
                  val newImage = s"""<img src="$srcUrl" width="$scaledWidth" height="$scaledHeight" />"""

                  Some((imageTag, newImage))
                } catch {
                  case e: Exception => {
                    log.warn(s"Noe gikk galt med bilde ${srcUrl}")
                    log.warn(s"replaceImageData: ${e.getMessage}")
                    None
                  }
                }
              }else{
                val srcRegex = "(?<=src=)(.*?)(?=\\s)".r
                val srcUrl = srcRegex.findFirstIn(imageTag).getOrElse("").replace("\"","")
                
                try {
                  val idRegex = "[a-zA-Z0-9-.]+$".r
                  val imageId = idRegex.findFirstIn(srcUrl).getOrElse("")
                  val (imageFile: File, height: Int, width: Int) = imageAndDimensions(imageId)
                  val (scaledWidth: Int, scaledHeight: Int) = scaleDimensions(List(), height, width)

                  val newImage = s"""<img src="$srcUrl" width="$scaledWidth" height="$scaledHeight" />"""
                  Some((imageTag, newImage))
                } catch {
                  case e: Exception => {
                    log.warn(s"Noe gikk galt med bilde ${srcUrl}")
                    log.warn(s"replaceImageData: ${e.getMessage}")
                    None
                  }
                }
              }
            } catch {
              case e: Exception => {
                log.warn(s"replaceImageData: ${e.getMessage}")
                None
              }
            }
          })
          val imagesWasUpdatedInSection = if(externalImagesWithSize.count(_.isDefined) > 0) 1 else 0
          (section.copy(text = Some(addSizeToExtrernalImages(externalImagesWithSize.filter(_.isDefined).map(_.get), text))), externalImagesWithSize.count(_.isDefined), imagesWasUpdatedInSection)
        } else {
          (section, 0, 0)
        }
      case _ => (section, 0, 0)
    }
  }
  
  def replaceImageData(section: Section): Section = {
    section.text match {
      case Some(text) =>
        if (section.text.get.contains("<img")) {
          val imgRegex = s"""(<img [^>]* */>)""".r
          val imageTags = imgRegex.findAllMatchIn(text).map(matchedId => matchedId.group(1)).toList
          val scaledAndEncodedImages = imageTags.map {
            imageTag =>
              try{
                val (imgTagAttributes: List[String], src: String) = htmlAttributes(imageTag)
                val (imgId, encImg, imgHeight, imgWidth) =
                  if (src.contains("data:image")) {
                    val encodedImagePattern = """.*(data:image[^"]+).*""".r
                    val encodedImagePattern(encImg) = src
                    // TODO if height and width is not given, handle it by getting dimensions from encoded image string. For now, we're just throw an exception
                    val (w, h) = (imgTagAttributes.find(s => s.startsWith("width")), imgTagAttributes.find(s => s.startsWith("height")))
                    if (w.isEmpty || h.isEmpty) throw new RuntimeException("Image dimensions missing in section html")
                    val givenWidth: Int = ("[0-9]".r findAllIn imgTagAttributes.find(s => s.startsWith("width")).get mkString).toInt
                    val givenHeight: Int = ("[0-9]".r findAllIn imgTagAttributes.find(s => s.startsWith("height")).get mkString).toInt
                    (encImg, encImg, givenHeight, givenWidth)
                  } else {
                    val (ext: String, idWithExt: String) = imagefileNameAndExtension(src)
                    val (imageFile: File, height: Int, width: Int) = imageAndDimensions(idWithExt)
                    val encImg = try {
                      s"data:image/$ext.;base64," + Base64.getEncoder.encodeToString(imageFile.byteArray)
                    } catch {
                      case e: Exception =>
                        log.warn(e.getMessage)
                        "data:image/gif;base64,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"
                    }
                    (idWithExt, encImg, height, width)
                  }
                val (scaledWidth: Int, scaledHeight: Int) = scaleDimensions(imgTagAttributes, imgHeight, imgWidth)
                val otherAttrs = imgTagAttributes.filter(s => List("width", "height", "src").contains(s))
                Some((imgId, s"""<img src="$encImg" height="$scaledHeight" width="$scaledWidth" ${otherAttrs.mkString(" ")} />"""))
              } catch {
                case e:Exception =>
                  log.warn(s"replaceImageData: ${e.getMessage}")
                  None
              }

          }
          
          val encodeExternalImageUrls = imageTags.map( imageTag =>{
            try{
              val idAndExtRegex = s"""src="$webAppPath/images/([a-zA-Z0-9-]+).(\\w+)"""".r
              if(idAndExtRegex.findAllMatchIn(imageTag).isEmpty){
                val srcRegex = "(?<=src=)(.*?)(?=\\s)".r

                val srcUrl = srcRegex.findFirstIn(imageTag).getOrElse("").replace("\"","")
                val width = "(?<=width=)(.*?)(?=\\s)".r.findFirstIn(imageTag).getOrElse("").replace("\"","").toInt
                val height = "(?<=height=)(.*?)(?=\\s)".r.findFirstIn(imageTag).getOrElse("").replace("\"","").toInt
                val imageUrl = new URL(srcUrl)
                val connection = imageUrl.openConnection
                connection.setRequestProperty("User-Agent", s"Mozilla/5.0 (compatible; KF Handboker/2.x; +http://www.kf.no)")
                val img = ImageIO.read(connection.getInputStream)
                val typeconvertedBI = new BufferedImage(img.getWidth(null), img.getHeight(null), BufferedImage.TYPE_3BYTE_BGR)
                val graphics = typeconvertedBI.getGraphics
                graphics.drawImage(img, 0, 0, Color.WHITE,null);
                graphics.dispose()
                val byteArrayStream = new ByteArrayOutputStream()
                ImageIO.write(typeconvertedBI, "jpg", byteArrayStream )

                val encImg = s"data:image/jpg;base64," + Base64.getEncoder.encodeToString(byteArrayStream.toByteArray)

                val (scaledWidth: Int, scaledHeight: Int) = scaleDimensions(List(), height, width)
                
                val newImage = s"""<img src="$encImg" height="$scaledHeight" width="$scaledWidth" />"""
                
                Some((imageTag, newImage))
              }else{
                None
              }
            } catch {
              case e: Exception => {
                log.warn(s"replaceImageData: ${e.getMessage}")
                None
              }
            }
          })

          val externalImagesText = replaceExternalImagesByBase64(encodeExternalImageUrls.filter(_.isDefined).map(_.get), text)
          section.copy(text = Some(replaceImagesByBase64(scaledAndEncodedImages.filter(_.isDefined).map(_.get), externalImagesText)))
        } else {
          section
        }

      case _ => section
    }
  }

  private def htmlAttributes(image: String) = {
    val rx = s"""<img ([^ ]+(?: [^ ]+)*) */>""".r
    val attrs = image match {
      case rx(items) => items.split(" ").toList
      case _ => throw new RuntimeException("Missing image attributes")
    }
    val src = attrs.find(s => s.startsWith("src")).getOrElse("")
    (attrs, src)
  }

  private def imagefileNameAndExtension(src: String) = {
    log.debug(src)
    val idAndExtRegex = s"""src="$webAppPath/images/([a-zA-Z0-9-]+).(\\w+)"""".r
    val idList = idAndExtRegex.findAllMatchIn(src).map(matchedId => (matchedId.group(1), matchedId.group(2))).toList
    val ext = idList.head._2
    val id = idList.head._1
    val idWithExt = s"""$id.$ext"""
    (ext, idWithExt)
  }

  private def scaleDimensions(attrs: List[String], imgHeight: Int, imgWidth: Int) = {
    val maxWidth = 650
    val givenWidth: Int = ("[0-9]".r findAllIn attrs.find(s => s.startsWith("width")).getOrElse(s"$imgWidth") mkString) toInt
    val givenHeight: Int = ("[0-9]".r findAllIn attrs.find(s => s.startsWith("height")).getOrElse(s"$imgHeight") mkString) toInt
    val scaledWidth = if (givenWidth > maxWidth) maxWidth else givenWidth
    val scaledHeight = if (givenWidth > maxWidth) ((maxWidth.toDouble / givenWidth.toDouble) * givenHeight.toDouble).toInt else givenHeight
    (scaledWidth, scaledHeight)
  }

  private def imageAndDimensions(idWithExt: String) = {
    val imageFile = imageService.retrieveImage(idWithExt)
    val bufferedImage: BufferedImage = ImageIO read imageFile.toJava
    val imgHeight = bufferedImage.getHeight
    val imgWidth = bufferedImage.getWidth
    (imageFile, imgHeight, imgWidth)
  }

  def replaceIframes(section: Section): Section = {
    section.text match {
      case Some(text) =>
        if (section.text.get.contains("<iframe")) {
          val iframeRegex = s"""(<iframe src="[^ ]+" )([a-z]+="[0-9]+") ([a-z]+="[0-9]+")( [^>]*></iframe>)""".r
          val iframeDimensions = iframeRegex.findAllMatchIn(section.text.get).map(dimensions => (dimensions.group(2), dimensions.group(3))).toList
          section.copy(text = Some(replaceIframesWithBase64(iframeDimensions, text)))
        } else {
          section
        }
      case _ => section
    }
  }

  lazy val mediaImageBase64: String = retrieveMultimediaImageEncoded()

  @tailrec
  private def replaceIframesWithBase64(iframeDimensions: List[(String, String)], text: String): String = {
    if (iframeDimensions.nonEmpty) {
      val dimensions = iframeDimensions.head
      val iframeRegex = s"""(<iframe.*></iframe>)""".r
      val updatedText = iframeRegex.replaceFirstIn(text, s"""<img src=$mediaImageBase64 ${dimensions._1} ${dimensions._2}></img>""")
      replaceIframesWithBase64(iframeDimensions.tail, updatedText)
    } else {
      text
    }
  }

  private def retrieveMultimediaImageEncoded(): String = {
    val url = getClass.getResource("/multimedia.png")
    import java.nio.file.Paths
    val filePath = Paths.get(url.toURI)
    val imageFile: File = File(filePath)
    s"data:image/png;base64," + Base64.getEncoder.encodeToString(imageFile.byteArray)
  }
  
  @tailrec
  private def addSizeToExtrernalImages(images: List[(String, String)], text: String): String ={
    if (images.nonEmpty) {
      val (oldImage, newImage) = images.head
      val updatedText = text.replace(oldImage, newImage)
      addSizeToExtrernalImages(images.tail, updatedText)
    } else {
      text
    }
  }
  
  @tailrec
  private def replaceImagesByBase64(idsAndBase64: List[(String, String)], text: String): String = {
    if (idsAndBase64.nonEmpty) {
      val (id, encodedImage) = idsAndBase64.head
      val updatedText = if (id.contains("data:image")) {
        val imgIdRegex = s"""<img.*${id.replaceAll("\\+", "\\\\+")}[^>]*/>""".r
        imgIdRegex.replaceAllIn(text, encodedImage)
      } else {
        val imgIdAndExtRegex = s"""<img.*$webAppPath/images/$id[^>].*/>""".r
        imgIdAndExtRegex.replaceAllIn(text, encodedImage)
      }
      replaceImagesByBase64(idsAndBase64.tail, updatedText)
    } else {
      text
    }
  }

  @tailrec
  private def replaceExternalImagesByBase64(tagsAndBase64: List[(String, String)], text: String): String = {
    if (tagsAndBase64.nonEmpty) {
      val (tag, tagEncodedImage) = tagsAndBase64.head
      val updatedText = text.replace(tag, tagEncodedImage)
      replaceExternalImagesByBase64(tagsAndBase64.tail, updatedText)
    } else {
      text
    }
  }

}
