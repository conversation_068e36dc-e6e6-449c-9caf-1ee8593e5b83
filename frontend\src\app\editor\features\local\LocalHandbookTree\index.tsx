import { Card, Tree } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useCallback, useRef } from "react";
import { useParams } from "react-router-dom";
import { LocalHandbookNode } from "../LocalHandbookNode";
import { useAppDispatch } from "@/store";
import { selectLocalItem } from "@/store/slices/localTreeSlice";
import type { LocalTreeNodeWithChildren } from "@/types";
import {
  useLocalHandbooks,
  useLocalChapters,
  useLocalSections,
} from "@/store/services/handbook/hooks";
import {
  useGetLocalChapterQuery,
  useGetLocalSectionQuery,
} from "@/store/services/handbook/localHandbookApi";
import { transformLocalToTreeStructure } from "@/store/services/handbook/utils";
import { Spinner } from "@/shared/components/Spinner";

interface LocalHandbookTreeProps {
  moving: boolean;
}

export const LocalHandbookTree = ({ moving }: LocalHandbookTreeProps) => {
  const t = usePrefixedTranslation("editor.containers.LocalTree");
  const dispatch = useAppDispatch();
  const treeRef = useRef<HTMLDivElement>(null);
  const { chapterId, sectionId } = useParams<{ handbookId?: string; chapterId?: string; sectionId?: string }>();

  // Fetch moved item data when in moving mode
  const { data: movedChapter } = useGetLocalChapterQuery(chapterId!, {
    skip: !moving || !chapterId,
  });
  const { data: movedSection } = useGetLocalSectionQuery(sectionId!, {
    skip: !moving || !sectionId,
  });

  const movedItem = movedChapter || movedSection;

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (moving) return;

      switch (event.key) {
        case "ArrowUp":
        case "ArrowDown":
          break;
        case "Enter":
        case " ":
          break;
        default:
          return;
      }
    },
    [moving]
  );

  const {
    data: localHandbooks,
    error: localHandbooksError,
    isLoading: localHandbooksLoading,
  } = useLocalHandbooks();

  const {
    data: localChapters,
    error: localChaptersError,
    isLoading: localChaptersLoading,
  } = useLocalChapters();

  const {
    data: localSections,
    error: localSectionsError,
    isLoading: localSectionsLoading,
  } = useLocalSections();

  const handbooks =
    localHandbooks && localChapters && localSections
      ? transformLocalToTreeStructure(
          localHandbooks,
          localChapters,
          localSections
        )
      : [];

  const isLoading =
    localHandbooksLoading || localChaptersLoading || localSectionsLoading;

  const error = localHandbooksError || localChaptersError || localSectionsError;

  const handleSetSelectedItem = (item: LocalTreeNodeWithChildren) => {
    dispatch(selectLocalItem(item));
  };

  if (error) {
    return (
      <Card>
        <Card.Header>
          <Card.Title>{t("treeHeader")}</Card.Title>
        </Card.Header>
        <Card.Content>
          <div style={{ padding: "1rem", textAlign: "center", color: "red" }}>
            Feil ved lasting av lokale håndbøker
          </div>
        </Card.Content>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <Card.Header>
          <Card.Title>{t("treeHeader")}</Card.Title>
        </Card.Header>
        <Card.Content>
          <div style={{ padding: "1rem", textAlign: "center" }}>
            <Spinner text="Laster lokale håndbøker..." />
          </div>
        </Card.Content>
      </Card>
    );
  }

  const items = handbooks.map((book) => (
    <LocalHandbookNode
      key={book.id}
      handbook={book}
      moving={moving}
      onSetSelectedItem={moving ? handleSetSelectedItem : undefined}
      movedItem={moving ? movedItem : undefined}
    />
  ));

  return (
    <Card>
      <Card.Header>
        <Card.Title>Lokale håndbøker</Card.Title>
      </Card.Header>
      <Card.Content style={{ paddingLeft: 0, paddingRight: 0 }}>
        <div
          ref={treeRef}
          onKeyDown={handleKeyDown}
          tabIndex={0}
          role="tree"
          aria-label="Lokale håndbøker"
          style={{ outline: "none" }}
        >
          <Tree>{items}</Tree>
        </div>
      </Card.Content>
    </Card>
  );
};
