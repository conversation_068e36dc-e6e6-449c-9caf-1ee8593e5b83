# Handbook Version Management - Additional Details

## Synchronization Process Between Central and Local Handbooks

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  CENTRAL TO LOCAL HANDBOOK SYNCHRONIZATION                                  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  SYNCHRONIZATION PROCESS FLOW                                         │  │
│  │                                                                       │  │
│  │  1. Central handbook is published                                     │  │
│  │  2. New version of central handbook is created                        │  │
│  │  3. Batch job detects new central version                             │  │
│  │  4. For each local handbook based on this central handbook:           │  │
│  │     a. Compare local content with new central content                 │  │
│  │     b. Create versions of local content before applying changes       │  │
│  │     c. Apply automatic updates where possible                         │  │
│  │     d. Flag conflicts for manual resolution                           │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  SYNCHRONIZATION IMPLEMENTATION                                       │  │
│  │                                                                       │  │
│  │  // In HandbookSynchronizationService.scala                           │  │
│  │  override def synchronizeHandbooks(): Unit = {                        │  │
│  │    val candidates = inTransaction {                                   │  │
│  │      publicationRepository.getPublicationCandidates                   │  │
│  │    }                                                                  │  │
│  │                                                                       │  │
│  │    val extOrgsToBeReindexed = candidates.flatMap(publication => {     │  │
│  │      // Create new central handbook version                           │  │
│  │      val centralHandbook = inTransaction {                            │  │
│  │        centralHandbookRepository                                      │  │
│  │          .retrieveCentralHandbook(publication.handbookId).get         │  │
│  │      }                                                                │  │
│  │                                                                       │  │
│  │      val version = inTransaction {                                    │  │
│  │        persistCentralHandbookVersion(centralHandbook,                 │  │
│  │                                     publication.createdBy)            │  │
│  │      }                                                                │  │
│  │                                                                       │  │
│  │      // Get all local handbooks based on this central handbook        │  │
│  │      val localHandbooks = inTransaction {                             │  │
│  │        handbookRepository                                             │  │
│  │          .retrieveHandbooksBasedOnCentralHandbookId(                  │  │
│  │            publication.handbookId)                                    │  │
│  │      }                                                                │  │
│  │                                                                       │  │
│  │      // Synchronize each local handbook                               │  │
│  │      localHandbooks.map(handbook => {                                 │  │
│  │        synchronizeLocalHandbook(handbook.id.get,                      │  │
│  │                                publication.handbookId)                │  │
│  │        handbook.externalOrgId                                         │  │
│  │      })                                                               │  │
│  │    })                                                                 │  │
│  │                                                                       │  │
│  │    // Reindex affected organizations in ElasticSearch                 │  │
│  │    extOrgsToBeReindexed.distinct.foreach(extOrgId => {               │  │
│  │      searchService.doReindex(extOrgId)                               │  │
│  │    })                                                                 │  │
│  │  }                                                                    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Conflict Resolution Process

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  CONFLICT RESOLUTION BETWEEN CENTRAL AND LOCAL CHANGES                      │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  CONFLICT DETECTION                                                   │  │
│  │                                                                       │  │
│  │  // In HandbookSynchronizationService.scala                           │  │
│  │  private def synchronizeLocalHandbook(localHandbookId: String,        │  │
│  │                                     centralHandbookId: String): Unit = │  │
│  │    // Get central handbook info                                       │  │
│  │    val (latestCentralVersion, previousVersion, prevChaptersMap,       │  │
│  │         prevSectionsMap, centralChapters, centralSections) =          │  │
│  │      inTransaction {                                                  │  │
│  │        getCentralHandbookInfo(centralHandbookId)                      │  │
│  │      }                                                                │  │
│  │                                                                       │  │
│  │    // Get local handbook info                                         │  │
│  │    val (localHandbook, allLocalChapters, allLocalSections,            │  │
│  │         deletedLocalChapters, deletedLocalSections) =                 │  │
│  │      inTransaction {                                                  │  │
│  │        getLocalHandbookInfo(localHandbookId)                          │  │
│  │      }                                                                │  │
│  │                                                                       │  │
│  │    // Process each section to detect conflicts                        │  │
│  │    allLocalSections.foreach(section => {                              │  │
│  │      if (section.importedHandbookId.isDefined) {                      │  │
│  │        val centralSectionId = section.importedHandbookId.get          │  │
│  │        val centralSection = centralSectionVersionOfMap.get(centralSectionId)│
│  │                                                                       │  │
│  │        if (centralSection.isDefined) {                                │  │
│  │          // Check for conflicts                                       │  │
│  │          val titleChanged = section.localTitleChange &&               │  │
│  │            centralSection.get.title != prevSectionsMap(centralSectionId).title│
│  │                                                                       │  │
│  │          val textChanged = section.localTextChange &&                 │  │
│  │            centralSection.get.html != prevSectionsMap(centralSectionId).html│
│  │                                                                       │  │
│  │          if (titleChanged || textChanged) {                           │  │
│  │            // Create version before applying changes                  │  │
│  │            inTransaction {                                            │  │
│  │              localHandbookVersionRepository                           │  │
│  │                .insertLocalHandbookSectionVersion(section, DateTime.now)│  │
│  │            }                                                          │  │
│  │                                                                       │  │
│  │            // Mark as conflict for manual resolution                  │  │
│  │            inTransaction {                                            │  │
│  │              handbookRepository.markSectionAsConflict(                │  │
│  │                section.id.get, titleChanged, textChanged)             │  │
│  │            }                                                          │  │
│  │          } else {                                                     │  │
│  │            // No conflict, can update automatically                   │  │
│  │            // ...                                                     │  │
│  │          }                                                            │  │
│  │        }                                                              │  │
│  │      }                                                                │  │
│  │    })                                                                 │  │
│  │  }                                                                    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  CONFLICT RESOLUTION UI                                              │  │
│  │                                                                       │  │
│  │  ┌─────────────────────────────────────────────────────────────────┐ │  │
│  │  │ Conflict Resolution: Equipment Section                          │ │  │
│  │  │                                                                 │ │  │
│  │  │ This section has been updated in both the central handbook      │ │  │
│  │  │ and your local handbook. Please resolve the conflict.           │ │  │
│  │  │                                                                 │ │  │
│  │  │ ┌─────────────────────────┐  ┌─────────────────────────────┐   │ │  │
│  │  │ │ CENTRAL VERSION         │  │ LOCAL VERSION               │   │ │  │
│  │  │ │                         │  │                             │   │ │  │
│  │  │ │ Title: Safety Equipment │  │ Title: Equipment Safety     │   │ │  │
│  │  │ │                         │  │                             │   │ │  │
│  │  │ │ <p>Use protective gear  │  │ <p>Use protective gear at   │   │ │  │
│  │  │ │ at all times. Updated   │  │ all times. Inspect before   │   │ │  │
│  │  │ │ central guidelines.</p> │  │ each use. Follow local      │   │ │  │
│  │  │ │                         │  │ protocols.</p>              │   │ │  │
│  │  │ │                         │  │                             │   │ │  │
│  │  │ │ [Use Central Version]   │  │ [Keep Local Version]        │   │ │  │
│  │  │ └─────────────────────────┘  └─────────────────────────────┘   │ │  │
│  │  │                                                                 │ │  │
│  │  │ ┌─────────────────────────────────────────────────────────────┐ │ │  │
│  │  │ │ MERGE VERSIONS                                              │ │ │  │
│  │  │ │                                                             │ │ │  │
│  │  │ │ Title: [dropdown: Central/Local]                            │ │ │  │
│  │  │ │                                                             │ │ │  │
│  │  │ │ Content:                                                    │ │ │  │
│  │  │ │ [WYSIWYG Editor with merged content]                        │ │ │  │
│  │  │ │ <p>Use protective gear at all times. Updated central        │ │ │  │
│  │  │ │ guidelines. Inspect before each use. Follow local           │ │ │  │
│  │  │ │ protocols.</p>                                              │ │ │  │
│  │  │ │                                                             │ │ │  │
│  │  │ │ [Save Merged Version]                                       │ │ │  │
│  │  │ └─────────────────────────────────────────────────────────────┘ │ │  │
│  │  │                                                                 │ │  │
│  │  └─────────────────────────────────────────────────────────────────┘ │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## ElasticSearch Reindexing Process

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  ELASTICSEARCH REINDEXING PROCESS                                           │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  REINDEXING TRIGGERS                                                  │  │
│  │                                                                       │  │
│  │  1. Scheduled batch job (ElasticSearchReIndexJob)                     │  │
│  │  2. After handbook synchronization                                    │  │
│  │  3. Manual trigger via admin interface                                │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  BATCH JOB IMPLEMENTATION                                             │  │
│  │                                                                       │  │
│  │  // In ElasticSearchReIndexJob.scala                                  │  │
│  │  override def execute(context: JobExecutionContext): Unit = {         │  │
│  │    val jobName = "Elastic Search Re Index job"                        │  │
│  │                                                                       │  │
│  │    try {                                                              │  │
│  │      log.info("Starting " + jobName)                                  │  │
│  │                                                                       │  │
│  │      val externalOrgIds = localHandbookService.retrieveAllExternalOrgIds()│
│  │      externalOrgIds.foreach(id => log.info(id))                       │  │
│  │      externalOrgIds.foreach(id => searchService.doReindex(id))        │  │
│  │                                                                       │  │
│  │      log.info("Finished running " + jobName)                          │  │
│  │    } catch {                                                          │  │
│  │      case e: Exception =>                                             │  │
│  │        log.error("Error occurred during " + jobName, e)               │  │
│  │        throw new JobExecutionException(                               │  │
│  │          "Error occurred during " + jobName, e, false)                │  │
│  │    }                                                                  │  │
│  │  }                                                                    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  REINDEXING IMPLEMENTATION                                            │  │
│  │                                                                       │  │
│  │  // In SearchService.scala                                            │  │
│  │  override def doReindex(externalOrgId: String): Boolean = {           │  │
│  │    try {                                                              │  │
│  │      // Delete existing index entries for this organization           │  │
│  │      searchIndexService.deleteEntriesFromIndex(externalOrgId)         │  │
│  │                                                                       │  │
│  │      // Create new index for this organization                        │  │
│  │      searchIndexService.createIndexForOrg(externalOrgId)              │  │
│  │                                                                       │  │
│  │      // Index all documents for this organization                     │  │
│  │      searchIndexBuilderService.indexDocumentsForExtOrg(externalOrgId) │  │
│  │                                                                       │  │
│  │      true                                                             │  │
│  │    } catch {                                                          │  │
│  │      case e: Exception =>                                             │  │
│  │        log.error(s"Reindexing failed, $e")                            │  │
│  │        false                                                          │  │
│  │    }                                                                  │  │
│  │  }                                                                    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Database Schema Evolution

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  DATABASE SCHEMA EVOLUTION FOR VERSION MANAGEMENT                           │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  INITIAL SCHEMA (SIMPLIFIED)                                          │  │
│  │                                                                       │  │
│  │  handbook                                                             │  │
│  │  ┌───────────┬─────────────┬────────────────┬───────────────┐        │  │
│  │  │ id        │ title       │importedhandbook│external_org_id│        │  │
│  │  │           │             │_id             │               │        │  │
│  │  └───────────┴─────────────┴────────────────┴───────────────┘        │  │
│  │                                                                       │  │
│  │  chapter                                                              │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────────┐    │  │
│  │  │ id        │ title       │parent_ │handbook_ │importedhandbook│    │  │
│  │  │           │             │id      │id        │_id             │    │  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────────┘    │  │
│  │                                                                       │  │
│  │  section                                                              │  │
│  │  ┌───────────┬─────────────┬────────┬──────────┬────────────────┐    │  │
│  │  │ id        │ title       │parent_ │handbook_ │importedhandbook│    │  │
│  │  │           │             │id      │id        │_id             │    │  │
│  │  └───────────┴─────────────┴────────┴──────────┴────────────────┘    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  MIGRATION 5: ADDING CENTRAL CONTENT TABLES                           │  │
│  │                                                                       │  │
│  │  -- migrate-db-5.sql                                                  │  │
│  │  CREATE SCHEMA central_handbooks                                      │  │
│  │  CREATE TABLE central_handbooks.handbook(                             │  │
│  │    central_id VARCHAR(100),                                           │  │
│  │    title VARCHAR(2000) NOT NULL,                                      │  │
│  │    updated_date BIGINT NOT NULL,                                      │  │
│  │    PRIMARY KEY(central_id)                                            │  │
│  │  )                                                                    │  │
│  │                                                                       │  │
│  │  CREATE TABLE central_handbooks.chapter(                              │  │
│  │    central_id VARCHAR(100),                                           │  │
│  │    title VARCHAR(2000) NOT NULL,                                      │  │
│  │    central_parent_id VARCHAR(100),                                    │  │
│  │    central_handbook_id VARCHAR(100) NOT NULL,                         │  │
│  │    updated_date BIGINT NOT NULL,                                      │  │
│  │    PRIMARY KEY(central_id)                                            │  │
│  │  )                                                                    │  │
│  │                                                                       │  │
│  │  CREATE TABLE central_handbooks.section(                              │  │
│  │    central_id VARCHAR(100),                                           │  │
│  │    title VARCHAR(2000) NOT NULL,                                      │  │
│  │    central_parent_id VARCHAR(100) NOT NULL,                           │  │
│  │    central_handbook_id VARCHAR(100) NOT NULL,                         │  │
│  │    html TEXT,                                                         │  │
│  │    updated_date BIGINT NOT NULL,                                      │  │
│  │    PRIMARY KEY(central_id)                                            │  │
│  │  )                                                                    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  MIGRATION 24: ADDING SEPARATE TITLE AND HTML TRACKING                │  │
│  │                                                                       │  │
│  │  -- migrate-db-24-mssql.sql                                           │  │
│  │  ALTER TABLE central_handbooks.section ADD title_updated_date BIGINT; │  │
│  │  ALTER TABLE central_handbooks.section ADD html_updated_date BIGINT;  │  │
│  │  ALTER TABLE central_handbooks.section ADD title_updated_by VARCHAR(200);│
│  │  ALTER TABLE central_handbooks.section ADD html_updated_by VARCHAR(200);│
│  │                                                                       │  │
│  │  -- Update all columns in a single statement                          │  │
│  │  UPDATE central_handbooks.section                                     │  │
│  │  SET title_updated_date = updated_date,                               │  │
│  │      html_updated_date = updated_date,                                │  │
│  │      title_updated_by = updated_by,                                   │  │
│  │      html_updated_by = updated_by;                                    │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Complete Version Management System Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  VERSION MANAGEMENT SYSTEM ARCHITECTURE                                     │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  COMPONENTS                                                           │  │
│  │                                                                       │  │
│  │  1. Database Layer                                                    │  │
│  │     - Central handbook tables (central_handbooks schema)              │  │
│  │     - Local handbook tables                                           │  │
│  │     - Version tables for local content                                │  │
│  │                                                                       │  │
│  │  2. Repository Layer                                                  │  │
│  │     - CentralHandbookRepository                                       │  │
│  │     - HandbookRepository                                              │  │
│  │     - LocalHandbookVersionRepository                                  │  │
│  │                                                                       │  │
│  │  3. Service Layer                                                     │  │
│  │     - CentralHandbookService                                          │  │
│  │     - LocalHandbookService                                            │  │
│  │     - HandbookSynchronizationService                                  │  │
│  │     - LocalHandbookVersionService                                     │  │
│  │                                                                       │  │
│  │  4. Batch Jobs                                                        │  │
│  │     - SentralHandbookBatchJob                                         │  │
│  │     - ElasticSearchReIndexJob                                         │  │
│  │                                                                       │  │
│  │  5. REST API                                                          │  │
│  │     - CentralHandbookServlet                                          │  │
│  │     - LocalHandbookServlet                                            │  │
│  │                                                                       │  │
│  │  6. Frontend                                                          │  │
│  │     - Version history components                                      │  │
│  │     - Conflict resolution UI                                          │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
│  ┌───────────────────────────────────────────────────────────────────────┐  │
│  │                                                                       │  │
│  │  DATA FLOW                                                            │  │
│  │                                                                       │  │
│  │  1. Central Content Publication                                       │  │
│  │     Central Admin -> CentralHandbookServlet -> CentralHandbookService │  │
│  │     -> CentralHandbookRepository -> Database                          │  │
│  │                                                                       │  │
│  │  2. Local Content Editing                                             │  │
│  │     Local Editor -> LocalHandbookServlet -> LocalHandbookService      │  │
│  │     -> LocalHandbookVersionRepository (version creation)              │  │
│  │     -> HandbookRepository (content update) -> Database                │  │
│  │                                                                       │  │
│  │  3. Synchronization                                                   │  │
│  │     SentralHandbookBatchJob -> HandbookSynchronizationService         │  │
│  │     -> LocalHandbookVersionRepository (version creation)              │  │
│  │     -> HandbookRepository (content update) -> Database                │  │
│  │     -> SearchService (reindex) -> ElasticSearch                       │  │
│  │                                                                       │  │
│  │  4. Version Retrieval                                                 │  │
│  │     User -> LocalHandbookServlet -> LocalHandbookVersionService       │  │
│  │     -> LocalHandbookVersionRepository -> Database                     │  │
│  │                                                                       │  │
│  └───────────────────────────────────────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Key Benefits of the Version Management System

1. **Complete History Tracking**
   - Every change to handbook content is preserved
   - Versions are created automatically before content changes
   - Both central and local changes are tracked

2. **Conflict Management**
   - Conflicts between central and local changes are detected
   - UI for resolving conflicts manually
   - Options to use central version, keep local version, or merge

3. **Audit Capabilities**
   - Track who made changes and when
   - View the state of a handbook at any point in time
   - Understand how content has evolved

4. **Recovery Options**
   - Restore previous versions if needed
   - Recover from accidental changes or deletions
   - Compare different versions to see what changed

5. **Synchronization Transparency**
   - Clear tracking of which central content has been imported
   - Visibility into local customizations vs. central content
   - Ability to see when central content was last updated

This comprehensive version management system ensures that organizations can maintain their handbooks with confidence, knowing that all changes are tracked and can be reviewed or reverted if necessary.