package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.handboker.model.local.{Chapter, Section}
import no.kf.handboker.repository.LocalHandbookVersionRepositoryComponent
import no.kf.handboker.util.section.ImageUtil.replaceImageData
import no.kf.util.Logging
import org.joda.time.DateTime

trait LocalHandbookVersionServiceComponent extends TransactionManager {
  this: LocalHandbookVersionRepositoryComponent
  =>

  val localHandbookVersionService: LocalHandbookVersionService

  class LocalHandbookVersionServiceImpl extends LocalHandbookVersionService with Logging {
    override def retrieveChapterVersions(chapterId: String): List[Chapter] = inTransaction {
      localHandbookVersionRepository.retrieveVersionsOfChapter(chapterId)
    }

    override def retrieveChapterVersionsInHandbook(localHandbookId: String, time: DateTime): List[Chapter] = inTransaction {

      localHandbookVersionRepository.retrieveChapterVersionsInHandbook(localHandbookId, time)
    }

    override def retrieveSectionVersions(sectionId: String): List[Section] = inTransaction {
      localHandbookVersionRepository.retrieveVersionsOfSection(sectionId)
    }

    override def retrieveSectionVersionsInHandbook(localHandbookId: String, time: DateTime): List[Section] = inTransaction {
      localHandbookVersionRepository.retrieveSectionVersionsInHandbook(localHandbookId, time)
    }

    override def retrieveChapterVersion(versionId: String): Option[Chapter] = inTransaction {
      localHandbookVersionRepository.retrieveChapterVersion(versionId)
    }

    override def retrieveSectionVersion(versionId: String): Option[Section] = inTransaction {
      val version = localHandbookVersionRepository.retrieveSectionVersion(versionId)
      if (version.isDefined) Some(replaceImageData(version.get)) else None
    }
  }

}

trait LocalHandbookVersionService {

  def retrieveChapterVersions(chapterId: String): List[Chapter]

  def retrieveChapterVersionsInHandbook(localHandbookId: String, time: DateTime): List[Chapter]

  def retrieveChapterVersion(versionId: String): Option[Chapter]

  def retrieveSectionVersions(sectionId: String): List[Section]

  def retrieveSectionVersionsInHandbook(localHandbookId: String, time: DateTime): List[Section]

  def retrieveSectionVersion(versionId: String): Option[Section]

}
