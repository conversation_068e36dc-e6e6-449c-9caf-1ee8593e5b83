import type { LocalTreeNode, Chapter, Section } from "@/types";

export interface LocalMoveValidationResult {
  isValid: boolean;
  reason?: string;
}

export const localSameLocation = (
  selectedItem: LocalTreeNode,
  movedItem: Chapter | Section
): boolean => {
  if (selectedItem.type === "HANDBOOK") {
    return !movedItem.parentId && movedItem.handbookId === selectedItem.id;
  }
  return selectedItem.id === movedItem.parentId;
};

export const localSectionToHandbook = (
  selectedItem: LocalTreeNode,
  movedItem: Chapter | Section
): boolean => {
  return selectedItem.type === "HANDBOOK" && movedItem.type === "SECTION";
};

export const localSectionToSection = (
  selectedItem: LocalTreeNode,
  movedItem: Chapter | Section
): boolean => {
  return selectedItem.type === "SECTION" && movedItem.type === "SECTION";
};

export const isValidLocalMoveTarget = (
  movedItem: Chapter | Section,
  targetItem: LocalTreeNode
): LocalMoveValidationResult => {
  const sameLoc = localSameLocation(targetItem, movedItem);
  const sectionToHbook = localSectionToHandbook(targetItem, movedItem);
  const sectionToSection = localSectionToSection(targetItem, movedItem);
  const circularRef = wouldCreateCircularReference(targetItem, movedItem);
  const descendant = isDescendantOfMovedItem(targetItem, movedItem);

  if (sectionToHbook) {
    return {
      isValid: false,
      reason: "Du kan ikke plassere en seksjon direkte under en håndbok",
    };
  }

  if (sectionToSection) {
    return {
      isValid: false,
      reason: "Du kan ikke plassere en seksjon under en annen seksjon",
    };
  }

  if (sameLoc) {
    return {
      isValid: false,
      reason: `${movedItem.title} er allerede i ${targetItem.title}`,
    };
  }

  if (circularRef || descendant) {
    return {
      isValid: false,
      reason:
        "Du kan ikke flytte et kapittel til et av sine egne underkapitler",
    };
  }

  return { isValid: true };
};

export const wouldCreateCircularReference = (
  nodeItem: LocalTreeNode,
  movedItem: Chapter | Section
): boolean => {
  if (movedItem.type !== "CHAPTER" || nodeItem.type !== "CHAPTER") {
    return false;
  }

  return isDescendantOfMovedItem(nodeItem, movedItem);
};

export const isDescendantOfMovedItem = (
  nodeItem: LocalTreeNode,
  movedItem: Chapter | Section
): boolean => {
  if (movedItem.type === "CHAPTER") {
    if (nodeItem.type !== "HANDBOOK" && "parentId" in nodeItem) {
      if (nodeItem.parentId === movedItem.id) {
        return true;
      }
    }
  }
  return false;
};

export const shouldDisableLocalNode = (
  nodeItem: LocalTreeNode,
  movedItem: Chapter | Section,
  inheritedDisabled: boolean = false
): boolean => {
  if (inheritedDisabled) {
    return true;
  }

  if (nodeItem.id === movedItem.id) {
    return true;
  }

  if (nodeItem.type === "SECTION") {
    return true;
  }

  if (isDescendantOfMovedItem(nodeItem, movedItem)) {
    return true;
  }

  if (wouldCreateCircularReference(nodeItem, movedItem)) {
    return true;
  }

  return false;
};
