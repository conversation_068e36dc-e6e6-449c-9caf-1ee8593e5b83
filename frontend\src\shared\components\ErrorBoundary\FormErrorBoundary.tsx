import React, { type ReactNode } from "react";
import { BaseErrorBoundary } from "./BaseErrorBoundary";
import { FormErrorFallback } from "./ErrorFallback";
import type {
  ErrorBoundaryProps,
  ErrorFallbackProps,
} from "@/shared/types/errorBoundary";

export class FormErrorBoundary extends BaseErrorBoundary {
  protected getErrorCategory():
    | "ui"
    | "api"
    | "auth"
    | "data"
    | "network"
    | "unknown" {
    const { error } = this.state;

    if (!error) return "unknown";

    const message = error.message.toLowerCase();

    if (
      message.includes("validation") ||
      message.includes("required") ||
      message.includes("invalid") ||
      message.includes("format")
    ) {
      return "data";
    }

    if (
      message.includes("submit") ||
      message.includes("save") ||
      message.includes("create") ||
      message.includes("update")
    ) {
      return "api";
    }

    return "ui";
  }

  protected getErrorLevel(): "low" | "medium" | "high" | "critical" {
    const { error } = this.state;

    if (!error) return "low";

    const message = error.message.toLowerCase();

    if (
      message.includes("validation") ||
      message.includes("required") ||
      message.includes("invalid")
    ) {
      return "low";
    }

    if (message.includes("submit") || message.includes("save")) {
      return "medium";
    }

    return "low";
  }

  protected renderFallback(props: ErrorFallbackProps): ReactNode {
    return <FormErrorFallback {...props} />;
  }
}

interface FormErrorBoundaryWrapperProps
  extends Omit<ErrorBoundaryProps, "level"> {
  formName?: string;
}

export const FormErrorBoundaryWrapper: React.FC<
  FormErrorBoundaryWrapperProps
> = ({ formName, ...props }) => (
  <FormErrorBoundary
    {...props}
    level="form"
    name={formName ? `Form-${formName}` : "FormErrorBoundary"}
    showToast={true}
  />
);
