import { createSlice, type PayloadAction } from "@reduxjs/toolkit";

export interface PendingHandbook {
  handbookId: string;
  handbookTitle: string;
}

interface PendingPublicationsState {
  pendingHandbooks: PendingHandbook[];
  currentlyViewedHandbookId: string | null;
  lastNotificationTime: number;
  isGlobalPollingActive: boolean;
}

const initialState: PendingPublicationsState = {
  pendingHandbooks: [],
  currentlyViewedHandbookId: null,
  lastNotificationTime: 0,
  isGlobalPollingActive: false,
};

export const pendingPublicationsSlice = createSlice({
  name: "pendingPublications",
  initialState,
  reducers: {
    setPendingHandbooks: (state, action: PayloadAction<PendingHandbook[]>) => {
      const previousPending = state.pendingHandbooks;
      const newPending = action.payload;

      const completedHandbooks = previousPending.filter(
        (prevHandbook) =>
          !newPending.some(
            (newHandbook) => newHandbook.handbookId === prevHandbook.handbookId
          )
      );

      state.pendingHandbooks = newPending;

      if (completedHandbooks.length > 0) {
        state.lastNotificationTime = Date.now();
      }
    },

    setCurrentlyViewedHandbook: (
      state,
      action: PayloadAction<string | null>
    ) => {
      state.currentlyViewedHandbookId = action.payload;
    },

    setGlobalPollingActive: (state, action: PayloadAction<boolean>) => {
      state.isGlobalPollingActive = action.payload;
    },

    addPendingHandbook: (state, action: PayloadAction<PendingHandbook>) => {
      const exists = state.pendingHandbooks.some(
        (handbook) => handbook.handbookId === action.payload.handbookId
      );
      if (!exists) {
        state.pendingHandbooks.push(action.payload);
      }
    },

    removePendingHandbook: (state, action: PayloadAction<string>) => {
      const handbookId = action.payload;
      const wasPresent = state.pendingHandbooks.some(
        (handbook) => handbook.handbookId === handbookId
      );

      state.pendingHandbooks = state.pendingHandbooks.filter(
        (handbook) => handbook.handbookId !== handbookId
      );

      if (wasPresent) {
        state.lastNotificationTime = Date.now();
      }
    },

    clearAllPendingHandbooks: (state) => {
      state.pendingHandbooks = [];
    },
  },
});

export const {
  setPendingHandbooks,
  setCurrentlyViewedHandbook,
  setGlobalPollingActive,
  addPendingHandbook,
  removePendingHandbook,
  clearAllPendingHandbooks,
} = pendingPublicationsSlice.actions;

export default pendingPublicationsSlice.reducer;

export const selectPendingHandbooks = (state: {
  pendingPublications: PendingPublicationsState;
}) => state.pendingPublications.pendingHandbooks;

export const selectCurrentlyViewedHandbookId = (state: {
  pendingPublications: PendingPublicationsState;
}) => state.pendingPublications.currentlyViewedHandbookId;

export const selectIsGlobalPollingActive = (state: {
  pendingPublications: PendingPublicationsState;
}) => state.pendingPublications.isGlobalPollingActive;

export const selectLastNotificationTime = (state: {
  pendingPublications: PendingPublicationsState;
}) => state.pendingPublications.lastNotificationTime;

export const selectPendingHandbooksCount = (state: {
  pendingPublications: PendingPublicationsState;
}) => state.pendingPublications.pendingHandbooks.length;

export const selectShouldShowGlobalPolling = (state: {
  pendingPublications: PendingPublicationsState;
}) => state.pendingPublications.pendingHandbooks.length > 0;

export const selectPendingHandbooksExcludingCurrent = (state: {
  pendingPublications: PendingPublicationsState;
}) => {
  const { pendingHandbooks, currentlyViewedHandbookId } =
    state.pendingPublications;

  if (!currentlyViewedHandbookId) {
    return pendingHandbooks;
  }

  return pendingHandbooks.filter(
    (handbook) => handbook.handbookId !== currentlyViewedHandbookId
  );
};

export const selectIsHandbookPending =
  (handbookId: string) =>
  (state: { pendingPublications: PendingPublicationsState }) =>
    state.pendingPublications.pendingHandbooks.some(
      (handbook) => handbook.handbookId === handbookId
    );
