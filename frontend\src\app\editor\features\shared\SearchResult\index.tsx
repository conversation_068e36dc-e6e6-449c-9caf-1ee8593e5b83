import React from "react";
import { Link } from "react-router-dom";
import { Heading, Subtitle, Columns, Column, Title, Media, Icon } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { SearchResult as SearchResultType, SearchHit } from "@/types";
import { RenderHtml } from "../RenderHtml";

export interface SearchResultProps {
  result: SearchResultType;
  query: string;
  linkFunc: (hit: SearchHit) => string;
}

export const SearchResult: React.FC<SearchResultProps> = ({
  result,
  query,
  linkFunc,
}) => {
  const t = usePrefixedTranslation("common.components.SearchResult");

  if (result.results.length === 0) {
    return <EmptySearchResult query={query} />;
  }

  const totalPages = Math.ceil(result.totalHits / result.pageSize);

  return (
    <div>
      <Columns>
        <Column>
          <Media>
            <Media.Content>
              <Title as="p" size="6">
                {t("pageInfo", {
                  page: result.page,
                  totalPages,
                  totalHits: result.totalHits,
                })}
              </Title>
            </Media.Content>
          </Media>
        </Column>
      </Columns>
      <Columns>
        <Column>
          {result.results.map((hit) => (
            <SearchHitComponent key={hit.id} hit={hit} linkFunc={linkFunc} />
          ))}
        </Column>
      </Columns>
    </div>
  );
};

function getIcon(hit: SearchHit): string {
  if (hit.isChapter) {
    return "RegBookmark";
  }
  if (hit.isSection) {
    return "RegFileLines";
  }
  return "Book";
}

interface SearchHitProps {
  hit: SearchHit;
  linkFunc: (hit: SearchHit) => string;
}

const SearchHitComponent: React.FC<SearchHitProps> = ({ hit, linkFunc }) => (
  <Media>
    <Media.Left>
      <Icon icon={getIcon(hit)} size="small" />
    </Media.Left>
    <Media.Content>
      <Subtitle as="p">
        <Link to={linkFunc(hit)}>
          {hit.highlight ? <RenderHtml html={hit.highlight} /> : hit.title}
        </Link>
      </Subtitle>
      {hit.textHighlight && <RenderHtml html={hit.textHighlight} />}
    </Media.Content>
  </Media>
);

interface EmptySearchResultProps {
  query: string;
}

const EmptySearchResult: React.FC<EmptySearchResultProps> = ({ query }) => {
  const t = usePrefixedTranslation("common.components.SearchResult");

  return (
    <div>
      <Title as="p" textCentered size="4">
        {t("noResults", { query })}
      </Title>
      <Heading textCentered as="div">
        <Columns>
          <Column>{t("tips")}</Column>
        </Columns>
        <p>{t("tip1")}</p>
        <p>{t("tip2")}</p>
      </Heading>
    </div>
  );
};
