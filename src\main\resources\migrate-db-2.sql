CREATE TABLE handbooksection(id VARCHAR(37), title VA<PERSON>HAR(255) NOT NULL, html CLOB, importedhandbooksection_id VARCHAR(100), importedhandbook_id VARCHAR(100), handbook_id VARCHAR(37) NOT NULL, parent_chapter_id VARCHAR(37) NOT NULL, orderindex SMALLINT, manualmerge SMALLINT NOT NULL, pendingchanges SMALLINT NOT NULL, pending_changes_updated_date BIGINT NOT NULL, updated_date BIGINT NOT NULL, created_date BIGINT NOT NULL, updated_by VARCHAR(100), created_by VA<PERSON>HAR(100), deleted SMALLINT NOT NULL, PRIMARY KEY(id))
ALTER TABLE handbooksection ADD CONSTRAINT fk_handbooksection_handbook FOREIGN KEY (handbook_id) REFERENCES handbook(id)
ALTER TABLE handbooksection ADD CONSTRAINT fk_handbooksection_parentchapter FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)
