package no.kf.handboker.service

import com.novell.ldap.LDAPEntry
import no.kf.handboker.model.{LDAPUser => LDAPUserModel, User}
import no.kf.handboker.config._
import no.kf.ldap.{AbstractLDAPService, CommonKFLdapFunctions}
import no.kf.util.Logging

trait LDAPServiceComponent {
  this: AppSettingComponent =>

  val ldapService: LDAPService

  class LDAPServiceImpl extends AbstractLDAPService[LDAPUserModel] with LDAPService with Logging {

    override lazy val host = settings.settingFor(LDAPHost)
    override lazy val port = settings.settingFor(LDAPPort).toInt
    override lazy val useLdap = settings.settingFor(UseLDAP).toBoolean
    override lazy val ldapSearchFilter: String = settings.settingFor(LDAPSearchFilter)
    override lazy val searchBase: String = settings.settingFor(LDAPSearchBase)
    override lazy val ldapUserSetting: String = settings.settingFor(LDAPUser)
    override lazy val ldapPasswordSetting: String = settings.settingFor(LDAPPwd)
    override lazy val ldapPrefixFilter: String = ""
    override lazy val ldapMaxResults = settings.settingFor(LDAPMaxResults).toInt

    private val localAdminGroupDistinguishedName: String = "CN=Håndbøker-Administrator"
    private val accessGroupDistinguishedName: String = "CN=Håndbøker-Redaktør"

    override def toUser(ldapEntry: LDAPEntry): LDAPUserModel = {
      val email = getLDAPAttribute(ldapEntry, settings.settingFor(LDAPMailAttribute))

      val givenName = getLDAPAttribute(ldapEntry, "givenName").getOrElse("")
      val familyName = getLDAPAttribute(ldapEntry, "sn").getOrElse("")
      val name = s"$givenName $familyName".trim
      val fullName = if (name.isEmpty) None else Some(name)
      val preferredLanguage = getLDAPAttribute(ldapEntry, "preferredLanguage")

      val ldapUserInfo = CommonKFLdapFunctions.getLocalAdminGlobalAdminAndMunicipality(ldapEntry, localAdminGroupDistinguishedName)
      val localUser = ldapUserInfo.isMemberOf(accessGroupDistinguishedName)

      LDAPUserModel(email.getOrElse(""), fullName, ldapUserInfo.orgIdentifiers, preferredLanguage, localUser, ldapUserInfo.localAdmin, ldapUserInfo.globalAdmin)
    }
  }
}

trait LDAPService extends no.kf.ldap.LDAPService[LDAPUserModel] {
}

