// Session tracker - monitors user session and API activity

interface SessionState {
  isWarning: boolean;
  isExpired: boolean;
  remainingMinutes: number;
}

type SessionCallback = (state: SessionState) => void;

class SessionTracker {
  private worker: Worker | null = null;
  private callback: SessionCallback | null = null;
  private state: SessionState = {
    isWarning: false,
    isExpired: false,
    remainingMinutes: 0,
  };
  private isStopped = false;

  // Session configuration (in minutes)
  // Production values: 240 minutes session timeout, 230 minutes warning time (10 minutes warning)
  private readonly SESSION_TIMEOUT = 240;
  private readonly WARNING_TIME = 230;

  constructor() {
    this.initializeWorker();
    this.setupApiInterception();
  }

  private initializeWorker() {
    if (this.isStopped) return;

    this.worker = new Worker(
      new URL("../workers/sessionWorker.ts", import.meta.url),
      { type: "module" }
    );

    this.worker.onmessage = (event) => {
      if (this.isStopped) return;

      const { type, remainingMinutes } = event.data;
      console.log(
        `[SessionTracker] Worker message: ${type}`,
        remainingMinutes ? `(${remainingMinutes.toFixed(1)} min remaining)` : ""
      );

      switch (type) {
        case "WARNING":
          this.updateState({
            isWarning: true,
            isExpired: false,
            remainingMinutes,
          });
          break;

        case "EXPIRED":
          this.updateState({
            isWarning: false,
            isExpired: true,
            remainingMinutes: 0,
          });
          break;

        case "ACTIVE":
          this.updateState({
            isWarning: false,
            isExpired: false,
            remainingMinutes: 0,
          });
          break;
      }
    };

    this.worker.postMessage({
      type: "INIT",
      sessionTimeout: this.SESSION_TIMEOUT,
      warningTime: this.WARNING_TIME,
      timestamp: Date.now(),
    });
  }

  private setupApiInterception() {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const response = await originalFetch(...args);
      if (response.ok) {
        this.handleSuccessfulApiCall();
      }
      return response;
    };

    window.addEventListener("api-call-success", () => {
      this.handleSuccessfulApiCall();
    });
  }

  private handleSuccessfulApiCall() {
    if (this.isStopped) {
      this.restartTracking();
    } else {
      this.notifyApiCall();
    }
  }

  private updateState(newState: SessionState) {
    this.state = newState;
    if (this.callback) {
      this.callback(this.state);
    }
  }

  private notifyApiCall() {
    if (this.worker && !this.isStopped) {
      this.worker.postMessage({
        type: "API_CALL",
        timestamp: Date.now(),
      });
    }
  }

  onStateChange(callback: SessionCallback) {
    this.callback = callback;
    callback(this.state);
  }

  keepSessionAlive() {
    if (this.isStopped) return;

    fetch("/session", {
      method: "GET",
      credentials: "same-origin",
      signal: AbortSignal.timeout(10000),
    })
      .then((response) => {
        if (response.ok) {
          this.notifyApiCall();
        } else {
          console.warn(
            "Keep-alive request failed with status:",
            response.status
          );
          if (response.status === 401 || response.status === 403) {
            this.updateState({
              isWarning: false,
              isExpired: true,
              remainingMinutes: 0,
            });
          }
        }
      })
      .catch((error) => {
        console.warn("Keep-alive request failed:", error);
      });
  }

  stopTracking() {
    console.log("[SessionTracker] Stopping tracking");
    this.isStopped = true;
    if (this.worker) {
      this.worker.postMessage({ type: "STOP" });
      this.worker.terminate();
      this.worker = null;
    }
  }

  restartTracking() {
    console.log("[SessionTracker] Restarting tracking");
    this.isStopped = false;
    this.updateState({
      isWarning: false,
      isExpired: false,
      remainingMinutes: 0,
    });
    this.initializeWorker();
  }

  getState(): SessionState {
    return { ...this.state };
  }

  notifyApiSuccess() {
    window.dispatchEvent(new Event("api-call-success"));
  }

  destroy() {
    this.stopTracking();
    this.callback = null;
  }
}

const sessionTracker = new SessionTracker();

export default sessionTracker;
