package no.kf.handboker.model.api

import no.kf.handboker.model.local.{ChapterProperties, Section}
import org.joda.time.DateTime

case class TreeStructureChapter(
                                 id: Option[String],
                                 title: String,
                                 importedHandbookChapterId: Option[String],
                                 importedHandbookId: Option[String],
                                 handbookId: String,
                                 parentId: Option[String],
                                 sortOrder: Option[Int],
                                 chapters: List[TreeStructureChapter], // New attribute compared to ChapterProperties
                                 sections: List[Section], // New attribute compared to ChapterProperties
                                 override val localChange: Boolean = false,
                                 override val pendingChange: Boolean = false,
                                 override val pendingChangeUpdatedDate: Option[DateTime] = None,
                                 override val updatedDate: Option[DateTime] = None,
                                 override val createdDate: Option[DateTime] = None,
                                 override val updatedBy: Option[String] = None,
                                 override val createdBy: Option[String] = None,
                                 override val isDeleted: Option[<PERSON><PERSON><PERSON>] = None) extends ChapterProperties

