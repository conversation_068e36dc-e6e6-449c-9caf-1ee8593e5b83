package no.kf.handboker.rest

import no.kf.handboker.config.{Mat<PERSON><PERSON>ontainer, MatomoServer, WebAppPath}
import no.kf.util.{ApplicationNameReplacer, Logging}
import no.kf.handboker.rest.support.SessionSupport
import no.kf.handboker.util.{SSRStateInjector, TrackingTagTemplate}
import no.kf.rest.HistoryApiFallbackServlet
import no.kf.rest.support.JsonSupport
import org.json4s.jackson.Serialization.write

class MainServlet extends HistoryApiFallbackServlet with SessionSupport with Logging with JsonSupport {

  lazy val basename: String = componentRegistry.settings.settingFor(WebAppPath)

  lazy val matomoServer: String = componentRegistry.settings.settingFor(MatomoServer)
  lazy val matomoContainer: String = componentRegistry.settings.settingFor(MatomoContainer)

  get("/?") {
    contentType = "text/html"
    getIndexHtml
  }

  def getIndexHtml: String = {
    val html = new SSRStateInjector(servletContext.getResourceAsStream("/editor/index.html"))
      .addObject("__PRELOADED_SESSION_STATE__", write(currentSession))
      .addString("__BASENAME__", basename)
      .addString("__MATOMOBASE__", matomoServer)
      .addTrackingCode(TrackingTagTemplate.get(matomoServer, matomoContainer))
      .toString()
    ApplicationNameReplacer.replaceInLocalAbsoluteHrefAndSrc("<!DOCTYPE HTML>\n" + html, basename)
  }
}
