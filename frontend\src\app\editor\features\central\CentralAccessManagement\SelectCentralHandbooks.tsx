import React, { useState, useEffect } from "react";
import { isEqual, partition } from "lodash";
import { Button, Column, Menu, Columns, Group } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { CentralHandbook } from "@/types";
import { Spinner } from "@/shared/components/Spinner";

interface SelectCentralHandbooksProps {
  handbooks: CentralHandbook[];
  access: string[];
  isSaving: boolean;
  isLoading: boolean;
  onSave: (ids: string[]) => void;
}

interface SelectableListProps {
  items: CentralHandbook[];
  onSelect: (event: React.MouseEvent, item: CentralHandbook) => void;
  label: string;
}

interface SelectableItemProps {
  value: CentralHandbook;
  onSelect: (event: React.MouseEvent, item: CentralHandbook) => void;
}

function partitionHandbooks(handbooks: CentralHandbook[], access: string[]) {
  const partitioned = partition(handbooks, (h) => access.includes(h.id!));
  partitioned[0].sort((a, b) => a.title.localeCompare(b.title));
  partitioned[1].sort((a, b) => a.title.localeCompare(b.title));
  return partitioned;
}

const SelectableItem: React.FC<SelectableItemProps> = ({ value, onSelect }) => (
  <Menu.Item
    onClick={(event: React.MouseEvent) => onSelect(event, value)}
    role="button"
    href=""
  >
    {value.title}
  </Menu.Item>
);

const SelectableList: React.FC<SelectableListProps> = ({
  items,
  onSelect,
  label,
}) => (
  <Menu style={{ maxHeight: "35em", overflowY: "scroll" }}>
    <Menu.Label>{label}</Menu.Label>
    <Menu.List>
      {items.length ? (
        items.map((value) => (
          <SelectableItem key={value.id} value={value} onSelect={onSelect} />
        ))
      ) : (
        <em>Ingen</em>
      )}
    </Menu.List>
  </Menu>
);

export const SelectCentralHandbooks: React.FC<SelectCentralHandbooksProps> = ({
  handbooks,
  access,
  isSaving,
  isLoading,
  onSave,
}) => {
  const t = usePrefixedTranslation("editor.components.SelectCentralHandbooks");

  const [initialAvailable, setInitialAvailable] = useState<CentralHandbook[]>(
    []
  );
  const [available, setAvailable] = useState<CentralHandbook[]>([]);
  const [initialSelected, setInitialSelected] = useState<CentralHandbook[]>([]);
  const [selected, setSelected] = useState<CentralHandbook[]>([]);

  useEffect(() => {
    if (handbooks.length > 0) {
      const partitioned = partitionHandbooks(handbooks, access);
      setInitialSelected(partitioned[0]);
      setSelected(partitioned[0]);
      setInitialAvailable(partitioned[1]);
      setAvailable(partitioned[1]);
    }
  }, [handbooks, access]);

  const onSelect = (event: React.MouseEvent, item: CentralHandbook) => {
    event.preventDefault();
    setSelected((prev) =>
      [...prev, item].sort((a, b) => a.title.localeCompare(b.title))
    );
    setAvailable((prev) => prev.filter((i) => i.id !== item.id));
  };

  const onDeselect = (event: React.MouseEvent, item: CentralHandbook) => {
    event.preventDefault();
    setSelected((prev) => prev.filter((i) => i.id !== item.id));
    setAvailable((prev) =>
      [...prev, item].sort((a, b) => a.title.localeCompare(b.title))
    );
  };

  const onReset = () => {
    setAvailable(initialAvailable);
    setSelected(initialSelected);
  };

  const hasChanges = !isEqual(
    available.map((h) => h.id).sort(),
    initialAvailable.map((h) => h.id).sort()
  );

  if (isLoading) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  return (
    <div>
      <Columns>
        <Column>
          <SelectableList
            items={available}
            onSelect={onSelect}
            label={t("availableBooks")}
          />
        </Column>
        <Column>
          <SelectableList
            items={selected}
            onSelect={onDeselect}
            label={t("orgBooks")}
          />
        </Column>
      </Columns>
      <Columns>
        <Column>
          <Group>
            <Button
              control
              disabled={!hasChanges || isSaving}
              onClick={onReset}
            >
              {t("resetButton")}
            </Button>
            <Button
              control
              disabled={!hasChanges || isSaving}
              onClick={() => onSave(selected.map((h) => h.id!))}
              color="primary"
              loading={isSaving}
            >
              {t("saveButton")}
            </Button>
          </Group>
        </Column>
      </Columns>
    </div>
  );
};
