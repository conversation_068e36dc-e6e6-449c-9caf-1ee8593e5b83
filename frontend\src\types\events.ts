// Event handler types for React components
// These types provide proper typing for event handlers throughout the application

import type { 
  MouseEvent, 
  KeyboardEvent, 
  ChangeEvent, 
  FormEvent, 
  FocusEvent,
  DragEvent,
  TouchEvent,
  WheelEvent,
  ClipboardEvent,
  CompositionEvent,
  UIEvent,
  SyntheticEvent
} from 'react';

// Generic event handler types
export type EventHandler<T = Element> = (event: SyntheticEvent<T>) => void;
export type MouseEventHandler<T = Element> = (event: MouseEvent<T>) => void;
export type KeyboardEventHandler<T = Element> = (event: KeyboardEvent<T>) => void;
export type ChangeEventHandler<T = Element> = (event: ChangeEvent<T>) => void;
export type FormEventHandler<T = Element> = (event: FormEvent<T>) => void;
export type FocusEventHandler<T = Element> = (event: FocusEvent<T>) => void;
export type DragEventHandler<T = Element> = (event: DragEvent<T>) => void;
export type TouchEventHandler<T = Element> = (event: TouchEvent<T>) => void;
export type WheelEventHandler<T = Element> = (event: WheelEvent<T>) => void;
export type ClipboardEventHandler<T = Element> = (event: ClipboardEvent<T>) => void;
export type CompositionEventHandler<T = Element> = (event: CompositionEvent<T>) => void;
export type UIEventHandler<T = Element> = (event: UIEvent<T>) => void;

// Specific element event handlers
export type ButtonClickHandler = MouseEventHandler<HTMLButtonElement>;
export type LinkClickHandler = MouseEventHandler<HTMLAnchorElement>;
export type InputChangeHandler = ChangeEventHandler<HTMLInputElement>;
export type TextareaChangeHandler = ChangeEventHandler<HTMLTextAreaElement>;
export type SelectChangeHandler = ChangeEventHandler<HTMLSelectElement>;
export type FormSubmitHandler = FormEventHandler<HTMLFormElement>;
export type InputFocusHandler = FocusEventHandler<HTMLInputElement>;
export type InputBlurHandler = FocusEventHandler<HTMLInputElement>;

// Custom event handler types
export type ValueChangeHandler<T = any> = (value: T) => void;
export type IndexChangeHandler = (index: number) => void;
export type IdChangeHandler = (id: string) => void;
export type BooleanChangeHandler = (value: boolean) => void;
export type StringChangeHandler = (value: string) => void;
export type NumberChangeHandler = (value: number) => void;

// Async event handlers
export type AsyncEventHandler<T = Element> = (event: SyntheticEvent<T>) => Promise<void>;
export type AsyncMouseEventHandler<T = Element> = (event: MouseEvent<T>) => Promise<void>;
export type AsyncFormEventHandler<T = Element> = (event: FormEvent<T>) => Promise<void>;

// Event handler with data
export type DataEventHandler<T = any, E = Element> = (data: T, event: SyntheticEvent<E>) => void;
export type DataMouseEventHandler<T = any, E = Element> = (data: T, event: MouseEvent<E>) => void;

// Keyboard event helpers
export interface KeyboardEventInfo {
  key: string;
  code: string;
  altKey: boolean;
  ctrlKey: boolean;
  metaKey: boolean;
  shiftKey: boolean;
  repeat: boolean;
}

export type KeyboardEventWithInfo<T = Element> = (
  event: KeyboardEvent<T>,
  info: KeyboardEventInfo
) => void;

// Mouse event helpers
export interface MouseEventInfo {
  button: number;
  buttons: number;
  clientX: number;
  clientY: number;
  pageX: number;
  pageY: number;
  screenX: number;
  screenY: number;
  altKey: boolean;
  ctrlKey: boolean;
  metaKey: boolean;
  shiftKey: boolean;
}

export type MouseEventWithInfo<T = Element> = (
  event: MouseEvent<T>,
  info: MouseEventInfo
) => void;

// Drag and drop event types
export interface DragEventData<T = any> {
  draggedItem: T;
  draggedIndex?: number;
  targetItem?: T;
  targetIndex?: number;
  dropPosition?: 'before' | 'after' | 'inside';
}

export type DragStartHandler<T = any> = (data: DragEventData<T>, event: DragEvent) => void;
export type DragOverHandler<T = any> = (data: DragEventData<T>, event: DragEvent) => void;
export type DropHandler<T = any> = (data: DragEventData<T>, event: DragEvent) => void;
export type DragEndHandler<T = any> = (data: DragEventData<T>, event: DragEvent) => void;

// File input event types
export interface FileChangeEventData {
  files: FileList | null;
  fileArray: File[];
  isEmpty: boolean;
  count: number;
}

export type FileChangeHandler = (data: FileChangeEventData, event: ChangeEvent<HTMLInputElement>) => void;

// Search/filter event types
export interface SearchEventData {
  query: string;
  filters?: Record<string, any>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export type SearchHandler = (data: SearchEventData) => void;

// Pagination event types
export interface PaginationEventData {
  page: number;
  pageSize: number;
  totalPages: number;
  totalItems: number;
}

export type PageChangeHandler = (data: PaginationEventData) => void;

// Modal/Dialog event types
export type ModalOpenHandler = () => void;
export type ModalCloseHandler = (reason?: 'backdrop' | 'escape' | 'button') => void;
export type ModalConfirmHandler<T = unknown> = (data?: T) => void;
export type ModalCancelHandler = () => void;

// Navigation event types
export interface NavigationEventData {
  path: string;
  state?: Record<string, unknown>;
  replace?: boolean;
}

export type NavigationHandler = (data: NavigationEventData) => void;

// Generic callback types
export type VoidCallback = () => void;
export type AsyncVoidCallback = () => Promise<void>;
export type Callback<T = unknown> = (data: T) => void;
export type AsyncCallback<T = unknown> = (data: T) => Promise<void>;

// Error event types
export interface ErrorEventData {
  error: Error;
  errorInfo?: Record<string, unknown>;
  context?: string;
}

export type ErrorHandler = (data: ErrorEventData) => void;