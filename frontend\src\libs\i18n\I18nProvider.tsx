import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import type { ReactNode } from 'react';
import { IntlProvider } from 'react-intl';
import {
  DEFAULT_LOCALE,
  detectLocaleWithSession,
  saveLocale,
  getMessagesForLocale,
  SUPPORTED_LOCALES,
} from './config';
import type { SupportedLocale } from './config';
import type { SessionInfo } from '@/types';

interface I18nContextType {
  locale: SupportedLocale;
  setLocale: (locale: SupportedLocale) => void;
  availableLocales: readonly SupportedLocale[];
  updateSessionLocale: (session: SessionInfo | null) => void;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
  defaultLocale?: SupportedLocale;
  session?: SessionInfo | null; // Optional session for server-side rendering
}

export const I18nProvider: React.FC<I18nProviderProps> = ({
  children,
  defaultLocale = DEFAULT_LOCALE,
  session,
}) => {
  // Initialize locale with session-aware detection
  const [locale, setLocaleState] = useState<SupportedLocale>(() => {
    try {
      return detectLocaleWithSession(session);
    } catch {
      return defaultLocale;
    }
  });

  const setLocale = useCallback((newLocale: SupportedLocale) => {
    if (SUPPORTED_LOCALES.includes(newLocale)) {
      setLocaleState(newLocale);
      try {
        saveLocale(newLocale);
      } catch (error) {
        console.warn('Failed to save locale to localStorage:', error);
      }
    }
  }, []);

  // Update locale when session changes (mimics legacy selectLocale behavior)
  const updateSessionLocale = useCallback((newSession: SessionInfo | null) => {
    const sessionLocale = detectLocaleWithSession(newSession);
    if (sessionLocale !== locale) {
      setLocaleState(sessionLocale);
      // Don't save to localStorage for session-based changes
      // This preserves user's manual locale choice when session doesn't specify
    }
  }, [locale]);

  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'locale' && event.newValue) {
        const newLocale = event.newValue as SupportedLocale;
        if (SUPPORTED_LOCALES.includes(newLocale)) {
          setLocaleState(newLocale);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue: I18nContextType = useMemo(() => ({
    locale,
    setLocale,
    availableLocales: SUPPORTED_LOCALES,
    updateSessionLocale,
  }), [locale, setLocale, updateSessionLocale]);

  // Memoize messages to prevent unnecessary recalculations
  const messages = useMemo(() => getMessagesForLocale(locale), [locale]);

  return (
    <I18nContext.Provider value={contextValue}>
      <IntlProvider
        locale={locale}
        messages={messages}
        defaultLocale={defaultLocale}
        onError={(error) => {
          console.error('react-intl error:', error);
        }}
      >
        {children}
      </IntlProvider>
    </I18nContext.Provider>
  );
};

export const useI18nContext = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (!context) {
    throw new Error('useI18nContext must be used within an I18nProvider');
  }
  return context;
};

export const useLocale = (): SupportedLocale => {
  const { locale } = useI18nContext();
  return locale;
};

export const useSetLocale = () => {
  const { setLocale } = useI18nContext();
  return setLocale;
};

export const useAvailableLocales = () => {
  const { availableLocales } = useI18nContext();
  return availableLocales;
};

export const useUpdateSessionLocale = () => {
  const { updateSessionLocale } = useI18nContext();
  return updateSessionLocale;
};
