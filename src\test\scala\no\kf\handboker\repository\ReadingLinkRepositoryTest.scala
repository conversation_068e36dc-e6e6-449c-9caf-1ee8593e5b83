package no.kf.handboker.repository

import java.sql.SQLException

import no.kf.handboker.model.central.{ReadingLink, CentralHandbook, CentralChapter, CentralSection}
import no.kf.test.TimeTestHelp
import no.kf.util.Logging
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.FunSuite
import org.scalatest.junit.JUnitRunner

@RunWith(classOf[JUnitRunner])
class ReadingLinkRepositoryTest extends FunSuite with DbTestHandler with TimeTestHelp with Logging {

  val repository = componentRegistry.readingLinkRepository
  val centralHandbookRepository = componentRegistry.centralHandbookRepository
  val currentUser = "<EMAIL>"

  def persistLink(sectionId: String, validTo: Option[DateTime] = Some(DateTime.now.plusDays(7))): ReadingLink = {
    repository.persistReadingLink(ReadingLink(None, Some("TestLink"), sectionId, None, validTo.get))
  }

  transactedTest("That we can insert a reading link"){
    val testHandbook = centralHandbookRepository.persistCentralHandbook(CentralHandbook(None, "TestHandbook"), currentUser)
    val testChapter = centralHandbookRepository.persistCentralChapter(CentralChapter(None, "TestChapter", None, testHandbook.id.get), currentUser)
    val testSection = centralHandbookRepository.persistCentralSection(CentralSection(None, "TestSection", testChapter.id.get, testHandbook.id.get, Some("html")), currentUser)

    val savedLink = persistLink(testSection.id.get)
    assert(savedLink.id.isDefined)
    assert(savedLink.validTo.isAfter(DateTime.now))
  }

  transactedTest("That we cannot persist a reading link if its section does not exist") {
    assertThrows[SQLException](persistLink(sectionId = "nonExistentSection_id"))
  }

  transactedTest("That we can retrieve a reading link") {
    val testHandbook = centralHandbookRepository.persistCentralHandbook(CentralHandbook(None, "TestHandbook"), currentUser)
    val testChapter = centralHandbookRepository.persistCentralChapter(CentralChapter(None, "TestChapter", None, testHandbook.id.get), currentUser)
    val testSection = centralHandbookRepository.persistCentralSection(CentralSection(None, "TestSection", testChapter.id.get, testHandbook.id.get, Some("html")), currentUser)

    val savedLink = persistLink(testSection.id.get)
    val retrievedLink = repository.retrieveReadingLink(savedLink.id.get)

    assert(retrievedLink.isDefined)
    assert(retrievedLink.get.equals(savedLink))
  }

  transactedTest("That we can retrieve all reading links") {
    val testHandbook = centralHandbookRepository.persistCentralHandbook(CentralHandbook(None, "TestHandbook"), currentUser)
    val testChapter = centralHandbookRepository.persistCentralChapter(CentralChapter(None, "TestChapter", None, testHandbook.id.get), currentUser)
    val testSection1 = centralHandbookRepository.persistCentralSection(CentralSection(None, "TestSection", testChapter.id.get, testHandbook.id.get, Some("html")), currentUser)
    val testSection2 = centralHandbookRepository.persistCentralSection(CentralSection(None, "TestSection", testChapter.id.get, testHandbook.id.get, Some("html")), currentUser)

    val link1 = persistLink(testSection1.id.get)
    val link2 = persistLink(testSection2.id.get)

    val savedLinks = repository.retrieveAllValidReadingLinks()

    assert(savedLinks.length == 2)
    assert(savedLinks.contains(link1))
    assert(savedLinks.contains(link2))
  }

  transactedTest("That we cannot retrieve a reading link that does not exist") {
    val failedRetrieval = repository.retrieveReadingLink("fakeLinkId")
    assert(failedRetrieval.isEmpty)
  }

  transactedTest("That we can update the validTo date of a readingLink") {
    val testHandbook = centralHandbookRepository.persistCentralHandbook(CentralHandbook(None, "TestHandbook"), currentUser)
    val testChapter = centralHandbookRepository.persistCentralChapter(CentralChapter(None, "TestChapter", None, testHandbook.id.get), currentUser)
    val testSection = centralHandbookRepository.persistCentralSection(CentralSection(None, "TestSection", testChapter.id.get, testHandbook.id.get, Some("html")), currentUser)

    val savedLink = persistLink(testSection.id.get)
    val updatedLink = repository.persistReadingLink(savedLink.copy(validTo = DateTime.now.plusDays(3)))

    assert(updatedLink.id.equals(savedLink.id))
    assert(updatedLink.validTo.isBefore(savedLink.validTo))
    assert(updatedLink.link.equals(savedLink.link))
  }

  transactedTest("That we can delete a readingLink"){
    val testHandbook = centralHandbookRepository.persistCentralHandbook(CentralHandbook(None, "TestHandbook"), currentUser)
    val testChapter = centralHandbookRepository.persistCentralChapter(CentralChapter(None, "TestChapter", None, testHandbook.id.get), currentUser)
    val testSection = centralHandbookRepository.persistCentralSection(CentralSection(None, "TestSection", testChapter.id.get, testHandbook.id.get, Some("html")), currentUser)

    val savedLink = persistLink(testSection.id.get)
    assert(savedLink.id.isDefined)

    repository.deleteReadingLink(savedLink.id.get)
    val shouldBeEmpty = repository.retrieveReadingLink(savedLink.id.get)
    assert(shouldBeEmpty.isEmpty)
  }
}
