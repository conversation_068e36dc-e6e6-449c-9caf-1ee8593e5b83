import React from "react";
import { useParams } from "react-router-dom";
import { Container, Section, Subtitle, Title } from "kf-bui";
import parse from "html-react-parser";
import { InvalidLinkPage } from "@/shared/components/InvalidLinkPage";
import { useGetReadingLinkDataQuery } from "@/store/services/handbook/publicHandbookApi";
import { cleanHtml } from "@/utils/htmlParser";
import { Spinner } from "@/shared/components/Spinner";

interface ReadSectionPageParams extends Record<string, string | undefined> {
  linkId: string;
}

export const ReadSectionPage: React.FC = () => {
  const { linkId } = useParams<ReadSectionPageParams>();

  const {
    data: section,
    isLoading,
    isError,
  } = useGetReadingLinkDataQuery(linkId!, {
    skip: !linkId,
  });

  if (isLoading || !linkId) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (isError || !section) {
    return <InvalidLinkPage />;
  }

  return (
    <Section>
      <Container>
        <Title>{section.title}</Title>
        <Subtitle>{parse(cleanHtml(section.text || ""))}</Subtitle>
      </Container>
    </Section>
  );
};
