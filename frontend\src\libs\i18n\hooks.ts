import React from 'react';
import { useIntl } from 'react-intl';

export type TranslationFunction = (
  id: string,
  values?: Record<string, any>,
  options?: { defaultMessage?: string }
) => string;

export const useTranslation = (): TranslationFunction => {
  const intl = useIntl();
  return (id: string, values?: Record<string, any>, options?: { defaultMessage?: string }) => {
    try {
      return intl.formatMessage({ id, defaultMessage: options?.defaultMessage }, values);
    } catch (error) {
      console.warn(`Translation error for key "${id}":`, error);
      return options?.defaultMessage || id;
    }
  };
};

export const usePrefixedTranslation = (prefix: string): TranslationFunction => {
  const t = useTranslation();
  return (id: string, values?: Record<string, any>, options?: { defaultMessage?: string }) => {
    const fullId = prefix ? `${prefix}.${id}` : id;
    return t(fullId, values, options);
  };
};

export const useDateFormat = () => {
  const intl = useIntl();
  return {
    formatDate: (date: Date | number, options?: Intl.DateTimeFormatOptions) =>
      intl.formatDate(date, options),
    formatTime: (date: Date | number, options?: Intl.DateTimeFormatOptions) =>
      intl.formatTime(date, options),
    formatDateTime: (date: Date | number, options?: Intl.DateTimeFormatOptions) =>
      intl.formatDate(date, { ...options, timeStyle: 'short', dateStyle: 'short' }),
  };
};

export const useNumberFormat = () => {
  const intl = useIntl();
  return {
    formatNumber: (value: number, options?: Intl.NumberFormatOptions) =>
      intl.formatNumber(value, options),
    formatCurrency: (value: number, currency: string, options?: Intl.NumberFormatOptions) =>
      intl.formatNumber(value, { style: 'currency', currency, ...options }),
    formatPercent: (value: number, options?: Intl.NumberFormatOptions) =>
      intl.formatNumber(value, { style: 'percent', ...options }),
  };
};

export const useRelativeTimeFormat = () => {
  const intl = useIntl();
  return (value: number, unit: Intl.RelativeTimeFormatUnit, options?: Intl.RelativeTimeFormatOptions) => {
    try {
      return intl.formatRelativeTime(value, unit, options);
    } catch (error) {
      console.warn('Relative time formatting error:', error);
      return `${value} ${unit}`;
    }
  };
};

export const withTranslation = (prefix?: string) => {
  return <P extends object>(Component: React.ComponentType<P & { t: TranslationFunction }>) => {
    const WrappedComponent: React.FC<P> = (props: P) => {
      const t = prefix ? usePrefixedTranslation(prefix) : useTranslation();
      return React.createElement(Component, { ...props, t } as P & { t: TranslationFunction });
    };
    WrappedComponent.displayName = `withTranslation(${Component.displayName || Component.name})`;
    return WrappedComponent;
  };
};
