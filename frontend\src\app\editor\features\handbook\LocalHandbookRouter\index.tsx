import React from "react";
import { Routes, Route } from "react-router-dom";
import { LocalHandbookScreen } from "../LocalHandbookScreen";
import { ChapterRouter } from "../../chapter/ChapterRouter";
import { SectionRouter } from "../../section/SectionRouter";
import { LinkPage } from "../../links";
import { NoSelectionScreen } from "../NoSelectionScreen";

export const LocalHandbookRouter: React.FC = () => {
  return (
    <Routes>
      <Route index element={<NoSelectionScreen />} />
      <Route path=":handbookId" element={<LocalHandbookScreen />} />
      <Route path=":handbookId/chapter/*" element={<ChapterRouter />} />
      <Route path=":handbookId/section/*" element={<SectionRouter />} />
      <Route path=":handbookId/links" element={<LinkPage />} />
    </Routes>
  );
};
