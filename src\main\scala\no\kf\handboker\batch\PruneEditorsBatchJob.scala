package no.kf.handboker.batch

import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.LDAPSearchBase
import no.kf.handboker.model.LDAPUser
import no.kf.util.Logging
import org.quartz.{DisallowConcurrentExecution, Job, JobExecutionContext, JobExecutionException}

@DisallowConcurrentExecution
class PruneEditorsBatchJob extends Job with Logging {

  lazy val localHandbookService = ProductionRegistry.componentRegistry.localHandbookService
  lazy val subscriptionService = ProductionRegistry.componentRegistry.subscriptionService
  lazy val ldapService = ProductionRegistry.componentRegistry.ldapService

  override def execute(context: JobExecutionContext): Unit = {
    try {
      log.info("Starting PruneEditors batch job")

      val ldapEditors = getEditorsForAllOrganizations.getOrElse(throw new JobExecutionException("Unable to retrieve editors (or none) from ldap during PruneEditors batch job", false))
      val results = localHandbookService.pruneLocalEditors(ldapEditors)
      results.foreach(editor => log.info(s"Removed ${editor.rightsHolder} from handbook ${editor.handbookId}."))
      log.info(s"Finished running PruneEditors batch job. Removed ${results.size} editors.")
    } catch {
      case e: Exception =>
        log.error("Error occurred during PruneEditors batch job", e)
        throw new JobExecutionException("Error occured during PruneEditors batch job", e, false)
    }
  }

  def getEditorsForAllOrganizations: Option[List[LDAPUser]] = {
    val base = ProductionRegistry.componentRegistry.settings.settingFor(LDAPSearchBase)
    val editorGroupString = s"CN=Håndbøker-Redaktør,OU=Handboker,OU=KFApplications,$base"
    ldapService.findBrukerMemberOf(editorGroupString)
  }


}
