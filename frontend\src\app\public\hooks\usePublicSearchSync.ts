import { useEffect, useCallback } from "react";
import { useSearchParams } from "react-router-dom";
import { debounce } from "lodash";
import { useAppDispatch, useAppSelector } from "@/store";
import { setSearchQuery, setDebouncedQuery } from "@/store/slices/publicSearchSlice";

export const usePublicSearchSync = () => {
  const dispatch = useAppDispatch();
  const searchState = useAppSelector((state) => state.publicSearch);
  const [searchParams, setSearchParams] = useSearchParams();

  const debouncedSetQuery = useCallback(
    debounce((query: string) => {
      dispatch(setDebouncedQuery(query));
    }, 300),
    [dispatch]
  );

  useEffect(() => {
    debouncedSetQuery(searchState.query);
    return () => {
      debouncedSetQuery.cancel();
    };
  }, [searchState.query, debouncedSetQuery]);

  useEffect(() => {
    const urlQuery = searchParams.get("q");
    
    if (urlQuery && urlQuery !== searchState.query) {
      dispatch(setSearchQuery(urlQuery));
    }

    if (urlQuery && searchParams.size > 0) {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete("q");
      newSearchParams.delete("p");
      
      if (newSearchParams.toString()) {
        setSearchParams(newSearchParams, { replace: true });
      } else {
        setSearchParams({}, { replace: true });
      }
    }
  }, [searchParams, dispatch, searchState.query, setSearchParams]);

  return searchState;
};