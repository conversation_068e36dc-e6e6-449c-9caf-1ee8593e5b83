# Deep Dive: Elasticsearch Usage in Handbooks Project (Continued)

## 4. Indexing Process and Data Flow (Continued)

### 4.1 Document Indexing Workflow (Continued)

```
DOCUMENT INDEXING LIFECYCLE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ DATA SOURCE CHANGES                                                         │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                                                                     │   │
│ │ ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │   │
│ │ │   New Handbook  │  │  Updated Chapter │  │  New Section    │     │   │
│ │ │   Created       │  │  Modified       │  │  Added          │     │   │
│ │ └─────────────────┘  └─────────────────┘  └─────────────────┘     │   │
│ │          │                    │                    │               │   │
│ │          └────────────────────┼────────────────────┘               │   │
│ │                               │                                    │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                 │                                          │
│                                 ▼                                          │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                    SearchIndexService                               │   │
│ │                                                                     │   │
│ │ Individual Document Operations:                                     │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ def indexHandbook(handbook: Handbook) = {                   │   │   │
│ │ │   val doc = createHandbookDocument(handbook)                │   │   │
│ │ │   elasticClient.execute {                                   │   │   │
│ │ │     indexInto(handbook.externalOrgId)                       │   │   │
│ │ │       .doc(doc)                                             │   │   │
│ │ │       .id(handbook.id)                                      │   │   │
│ │ │   }                                                         │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ def indexChapter(chapter: Chapter) = {                      │   │   │
│ │ │   val doc = createChapterDocument(chapter)                  │   │   │
│ │ │   elasticClient.execute {                                   │   │   │
│ │ │     indexInto(chapter.externalOrgId)                        │   │   │
│ │ │       .doc(doc)                                             │   │   │
│ │ │       .id(chapter.id)                                       │   │   │
│ │ │   }                                                         │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ def indexSection(section: Section) = {                      │   │   │
│ │ │   val doc = createSectionDocument(section)                  │   │   │
│ │ │   elasticClient.execute {                                   │   │   │
│ │ │     indexInto(section.externalOrgId)                        │   │   │
│ │ │       .doc(doc)                                             │   │   │
│ │ │       .id(section.id)                                       │   │   │
│ │ │   }                                                         │   │   │
│ │ │ }                                                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                 │                                          │
│                                 ▼                                          │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │                SearchIndexBuilderService                            │   │
│ │                                                                     │   │
│ │ Bulk Operations for Full Reindexing:                               │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ def indexDocumentsForExtOrg(externalOrgId: String) = {      │   │   │
│ │ │                                                             │   │   │
│ │ │   // Step 1: Get all handbooks for organization            │   │   │
│ │ │   val handbooks = localHandbookService                     │   │   │
│ │ │     .getHandbooksForOrg(externalOrgId)                     │   │   │
│ │ │                                                             │   │   │
│ │ │   // Step 2: Build bulk request                            │   │   │
│ │ │   val bulkRequest = bulk(                                  │   │   │
│ │ │     handbooks.flatMap { handbook =>                        │   │   │
│ │ │       Seq(                                                 │   │   │
│ │ │         // Index handbook document                         │   │   │
│ │ │         indexInto(externalOrgId)                           │   │   │
│ │ │           .doc(createHandbookDocument(handbook))           │   │   │
│ │ │           .id(handbook.id),                                │   │   │
│ │ │                                                             │   │   │
│ │ │         // Index all chapters                              │   │   │
│ │ │         handbook.chapters.map { chapter =>                 │   │   │
│ │ │           indexInto(externalOrgId)                         │   │   │
│ │ │             .doc(createChapterDocument(chapter))           │   │   │
│ │ │             .id(chapter.id)                                │   │   │
│ │ │         },                                                 │   │   │
│ │ │                                                             │   │   │
│ │ │         // Index all sections                              │   │   │
│ │ │         handbook.sections.map { section =>                 │   │   │
│ │ │           indexInto(externalOrgId)                         │   │   │
│ │ │             .doc(createSectionDocument(section))           │   │   │
│ │ │             .id(section.id)                                │   │   │
│ │ │         }                                                  │   │   │
│ │ │       ).flatten                                            │   │   │
│ │ │     }                                                      │   │   │
│ │ │   )                                                        │   │   │
│ │ │                                                             │   │   │
│ │ │   // Step 3: Execute bulk request                          │   │   │
│ │ │   elasticClient.execute(bulkRequest)                       │   │   │
│ │ │ }                                                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.2 Reindexing Strategies

```
REINDEXING TRIGGER SCENARIOS
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ SCENARIO 1: SYNCHRONIZATION-TRIGGERED REINDEXING                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ When: After central handbook synchronization                        │   │
│ │ Trigger: LocalHandbookService.synchronizeWithCentral()             │   │
│ │                                                                     │   │
│ │ Flow:                                                               │   │
│ │ 1. User resolves pending changes                                    │   │
│ │ 2. Database updated with new content                                │   │
│ │ 3. extOrgsToBeReindexed.distinct.foreach(extOrgId => {             │   │
│ │      searchService.doReindex(extOrgId)                              │   │
│ │    })                                                               │   │
│ │ 4. Elasticsearch indices updated                                    │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ SCENARIO 2: SCHEDULED REINDEXING                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ When: Cron job execution (configurable schedule)                   │   │
│ │ Trigger: ElasticSearchReIndexJob                                    │   │
│ │                                                                     │   │
│ │ Flow:                                                               │   │
│ │ 1. Job starts: "Starting Elastic Search Re Index job"              │   │
│ │ 2. Get all organizations:                                           │   │
│ │    val externalOrgIds = localHandbookService                        │   │
│ │      .retrieveAllExternalOrgIds()                                   │   │
│ │ 3. Reindex each organization:                                       │   │
│ │    externalOrgIds.foreach(id => searchService.doReindex(id))        │   │
│ │ 4. Job completes: "Finished running Elastic Search Re Index job"   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ SCENARIO 3: MANUAL REINDEXING                                              │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ When: Admin panel or API call                                       │   │
│ │ Trigger: POST /search/reset or POST /search/index/                  │   │
│ │                                                                     │   │
│ │ API Endpoints:                                                      │   │
│ │ • resetIndexes() - Reindex all organizations                       │   │
│ │ • resetManyIndexes(externalOrgIds) - Reindex specific orgs         │   │
│ │                                                                     │   │
│ │ Frontend Integration:                                               │   │
│ │ import { resetIndexes, resetManyIndexes } from './api.js';          │   │
│ │                                                                     │   │
│ │ // Reset all indices                                                │   │
│ │ resetIndexes().then(() => {                                         │   │
│ │   console.log('All indices reset successfully');                   │   │
│ │ });                                                                 │   │
│ │                                                                     │   │
│ │ // Reset specific organizations                                     │   │
│ │ resetManyIndexes(['kf-bergen', 'kf-oslo']).then(() => {            │   │
│ │   console.log('Selected indices reset successfully');              │   │
│ │ });                                                                 │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.3 Reindexing Implementation Details

```
REINDEXING PROCESS IMPLEMENTATION
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ SearchService.doReindex() Implementation:                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ override def doReindex(externalOrgId: String): Boolean = {          │   │
│ │   try {                                                             │   │
│ │     log.info(s"Starting reindex for organization: $externalOrgId")  │   │
│ │                                                                     │   │
│ │     // Step 1: Delete existing index for this organization         │   │
│ │     searchIndexService.deleteEntriesFromIndex(externalOrgId)        │   │
│ │                                                                     │   │
│ │     // Step 2: Create new index for this organization              │   │
│ │     searchIndexService.createIndexForOrg(externalOrgId)             │   │
│ │                                                                     │   │
│ │     // Step 3: Index all documents for this organization           │   │
│ │     searchIndexBuilderService.indexDocumentsForExtOrg(externalOrgId)│   │
│ │                                                                     │   │
│ │     log.info(s"Reindex completed for organization: $externalOrgId") │   │
│ │     true                                                            │   │
│ │   } catch {                                                         │   │
│ │     case e: Exception =>                                            │   │
│ │       log.error(s"Reindexing failed for $externalOrgId: $e")       │   │
│ │       false                                                         │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ Index Management Operations:                                                │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SearchIndexService Operations:                                      │   │
│ │                                                                     │   │
│ │ 1. deleteEntriesFromIndex(externalOrgId):                          │   │
│ │    • Deletes entire index: DELETE /{externalOrgId}                 │   │
│ │    • Removes all documents for the organization                    │   │
│ │    • Clears mapping and settings                                   │   │
│ │                                                                     │   │
│ │ 2. createIndexForOrg(externalOrgId):                               │   │
│ │    • Creates new index: PUT /{externalOrgId}                       │   │
│ │    • Applies document mapping configuration                        │   │
│ │    • Sets up analyzers and field types                             │   │
│ │    • Configures index settings (shards, replicas)                  │   │
│ │                                                                     │   │
│ │ 3. Index Settings Example:                                          │   │
│ │    {                                                                │   │
│ │      "settings": {                                                  │   │
│ │        "number_of_shards": 1,                                       │   │
│ │        "number_of_replicas": 0,                                     │   │
│ │        "analysis": {                                                │   │
│ │          "analyzer": {                                              │   │
│ │            "custom_analyzer": {                                     │   │
│ │              "type": "custom",                                      │   │
│ │              "tokenizer": "standard",                               │   │
│ │              "filter": ["lowercase", "stop"]                       │   │
│ │            }                                                        │   │
│ │          }                                                          │   │
│ │        }                                                            │   │
│ │      }                                                              │   │
│ │    }                                                                │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. Error Handling and Resilience

### 5.1 Search Error Handling

```
SEARCH ERROR HANDLING STRATEGY
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ Error Scenarios and Responses:                                              │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SCENARIO 1: Elasticsearch Cluster Unavailable                      │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Error: Connection refused, timeout, or cluster down         │   │   │
│ │ │                                                             │   │   │
│ │ │ Response Strategy:                                          │   │   │
│ │ │ Try(elasticClient.execute { searchQuery }) match {         │   │   │
│ │ │   case Failure(exception) =>                               │   │   │
│ │ │     log.error("Elasticsearch unavailable", exception)      │   │   │
│ │ │     SearchResult.empty()  // Return empty result           │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ User Experience:                                            │   │   │
│ │ │ • Search returns "No results found"                        │   │   │
│ │ │ • Error logged for monitoring                              │   │   │
│ │ │ • Application remains functional                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SCENARIO 2: Index Not Found                                        │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Error: index_not_found_exception                            │   │   │
│ │ │ Cause: Organization index doesn't exist                    │   │   │
│ │ │                                                             │   │   │
│ │ │ Response Strategy:                                          │   │   │
│ │ │ case Failure(exception) =>                                 │   │   │
│ │ │   exception.getCause match {                               │   │   │
│ │ │     case indexNotFound: IndexNotFoundException =>          │   │   │
│ │ │       log.warn(s"Index not found: ${indexNotFound}")      │   │   │
│ │ │       // Trigger index creation                            │   │   │
│ │ │       searchIndexService.createIndexForOrg(externalOrgId)  │   │   │
│ │ │       SearchResult.empty()                                 │   │   │
│ │ │   }                                                         │   │   │
│ │ │                                                             │   │   │
│ │ │ Recovery Action:                                            │   │   │
│ │ │ • Create missing index automatically                       │   │   │
│ │ │ • Schedule reindexing for the organization                 │   │   │
│ │ │ • Return empty results for current search                  │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SCENARIO 3: Query Parsing Errors                                   │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Error: parsing_exception, illegal_argument_exception        │   │   │
│ │ │ Cause: Malformed query, invalid characters                 │   │   │
│ │ │                                                             │   │   │
│ │ │ Response Strategy:                                          │   │   │
│ │ │ case Failure(exception) =>                                 │   │   │
│ │ │   exception.getCause match {                               │   │   │
│ │ │     case queryError: QueryParsingException =>              │   │   │
│ │ │       log.warn(s"Query parsing failed: ${queryError}")    │   │   │
│ │ │       // Sanitize and retry with simpler query            │   │   │
│ │ │       val sanitizedQuery = sanitizeSearchQuery(query)     │   │   │
│ │ │       executeSimpleSearch(sanitizedQuery, externalOrgId)   │   │   │
│ │ │   }                                                         │   │   │
│ │ │                                                             │   │   │
│ │ │ Query Sanitization:                                         │   │   │
│ │ │ • Remove special characters: +, -, &&, ||, !, (, ), {, }   │   │   │
│ │ │ • Escape reserved words                                    │   │   │
│ │ │ • Fallback to simple match query                           │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 5.2 Indexing Error Handling

```
INDEXING ERROR HANDLING AND RECOVERY
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ Bulk Indexing Error Scenarios:                                             │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SCENARIO 1: Partial Bulk Indexing Failure                          │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Situation: Some documents in bulk request fail to index    │   │   │
│ │ │ Causes: Document size limits, mapping conflicts, etc.      │   │   │
│ │ │                                                             │   │   │
│ │ │ Detection and Response:                                     │   │   │
│ │ │ val bulkResponse = elasticClient.execute(bulkRequest)       │   │   │
│ │ │                                                             │   │   │
│ │ │ if (bulkResponse.result.hasFailures) {                     │   │   │
│ │ │   val failures = bulkResponse.result.failures              │   │   │
│ │ │   failures.foreach { failure =>                            │   │   │
│ │ │     log.error(s"Failed to index document ${failure.id}: " +│   │   │
│ │ │               s"${failure.error}")                         │   │   │
│ │ │                                                             │   │   │
│ │ │     // Retry individual document with error handling       │   │   │
│ │ │     retryDocumentIndexing(failure.id, failure.source)      │   │   │
│ │ │   }                                                         │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ Recovery Strategy:                                          │   │   │
│ │ │ • Log failed documents for investigation                   │   │   │
│ │ │ • Retry with individual requests                           │   │   │
│ │ │ • Continue with successful documents                       │   │   │
│ │ │ • Report partial success to monitoring                     │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ SCENARIO 2: Complete Reindexing Failure                            │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ Situation: doReindex() throws exception                    │   │   │
│ │ │ Causes: Cluster issues, memory problems, data corruption   │   │   │
│ │ │                                                             │   │   │
│ │ │ Error Handling in ElasticSearchReIndexJob:                 │   │   │
│ │ │ try {                                                       │   │   │
│ │ │   externalOrgIds.foreach(id => {                           │   │   │
│ │ │     try {                                                   │   │   │
│ │ │       searchService.doReindex(id)                          │   │   │
│ │ │       log.info(s"Successfully reindexed: $id")            │   │   │
│ │ │     } catch {                                               │   │   │
│ │ │       case e: Exception =>                                 │   │   │
│ │ │         log.error(s"Failed to reindex $id: $e")           │   │   │
│ │ │         // Continue with next organization                 │   │   │
│ │ │         // Don't fail entire job                           │   │   │
│ │ │     }                                                       │   │   │
│ │ │   })                                                        │   │   │
│ │ │ } catch {                                                   │   │   │
│ │ │   case e: Exception =>                                     │   │   │
│ │ │     log.error("Critical error in reindex job", e)         │   │   │
│ │ │     throw new JobExecutionException(                       │   │   │
│ │ │       "Error occurred during reindex job", e, false)      │   │   │
│ │ │ }                                                           │   │   │
│ │ │                                                             │   │   │
│ │ │ Recovery Strategy:                                          │   │   │
│ │ │ • Isolate failures to individual organizations             │   │   │
│ │ │ • Continue processing other organizations                  │   │   │
│ │ │ • Schedule retry for failed organizations                  │   │   │
│ │ │ • Alert administrators for manual intervention             │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. Performance Optimization

### 6.1 Query Performance Optimization

```
QUERY PERFORMANCE OPTIMIZATION STRATEGIES
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ 1. FIELD-SPECIFIC BOOSTING STRATEGY                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Relevance Hierarchy (Boost Values):                                │   │
│ │                                                                     │   │
│ │ handbook_title: 2.0    ← Highest relevance                         │   │
│ │ chapter_title:  1.5    ← High relevance                            │   │
│ │ section_title:  1.25   ← Medium relevance                          │   │
│ │ section_text:   1.0    ← Base relevance                            │   │
│ │                                                                     │   │
│ │ Rationale:                                                          │   │
│ │ • Titles are more descriptive than content                         │   │
│ │ • Handbook titles indicate primary topic                           │   │
│ │ • Chapter titles provide context                                   │   │
│ │ • Section titles are specific                                      │   │
│ │ • Section text provides detailed content                           │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ 2. SOURCE FILTERING FOR PERFORMANCE                                        │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Exclude Large Fields from Response:                                 │   │
│ │                                                                     │   │
│ │ "_source": {                                                        │   │
│ │   "excludes": ["section_text"]  // Don't return full text content  │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Benefits:                                                           │   │
│ │ • Reduced network transfer                                          │   │
│ │ • Faster JSON parsing                                               │   │
│ │ • Lower memory usage                                                │   │
│ │ • Improved response times                                           │   │
│ │                                                                     │   │
│ │ Note: Full text available via highlighting                          │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ 3. PAGINATION OPTIMIZATION                                                 │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Efficient Pagination Implementation:                                │   │
│ │                                                                     │   │
│ │ def getStartAtAndNumResults(page: Option[Int]): (Int, Int) = {      │   │
│ │   val pageNum = page.getOrElse(1)                                   │   │
│ │   val pageSize = 20  // Configurable page size                     │   │
│ │   val startAt = (pageNum - 1) * pageSize                           │   │
│ │   (startAt, pageSize)                                               │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Query Application:                                                  │   │
│ │ searchQuery                                                         │   │
│ │   .start(startAt)    // Elasticsearch "from" parameter             │   │
│ │   .limit(numResults) // Elasticsearch "size" parameter             │   │
│ │                                                                     │   │
│ │ Performance Considerations:                                         │   │
│ │ • Deep pagination (high offset) can be slow                        