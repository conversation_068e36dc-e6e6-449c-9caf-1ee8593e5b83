# Visual Explanation: Handbook Tree Structure and Sorting Process

## Overview

The handbook system uses a hierarchical tree structure to organize content. This document provides a visual explanation of how the tree structure works and how sorting is implemented across different components.

## Tree Structure Hierarchy

```
Handbook (TreeStructureHandbook)
├── Chapter 1 (TreeStructureChapter)
│   ├── Section 1.1 (Section)
│   ├── Section 1.2 (Section)
│   └── Sub-Chapter 1.3 (TreeStructureChapter)
│       ├── Section 1.3.1 (Section)
│       └── Section 1.3.2 (Section)
├── Chapter 2 (TreeStructureChapter)
│   ├── Section 2.1 (Section)
│   └── Section 2.2 (Section)
└── Chapter 3 (TreeStructureChapter)
    └── Section 3.1 (Section)
```

## Database Schema Visualization

### Table Structure with Sort Order Fields

```
┌─────────────────────────────────────────────────────────────┐
│                    HANDBOOK TABLE                          │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ VARCHAR(36)                         │
│ title                │ VARCHAR(255)                        │
│ external_org_id      │ VARCHAR(36)                         │
│ imported_handbook_id │ VARCHAR(36)                         │
│ created_date         │ TIMESTAMP                           │
│ updated_date         │ TIMESTAMP                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ 1:N
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    CHAPTER TABLE                           │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ VARCHAR(36)                         │
│ title                │ VARCHAR(255)                        │
│ handbook_id (FK)     │ VARCHAR(36)                         │
│ parent_id (FK)       │ VARCHAR(36) [NULL for root]        │
│ sort_order           │ SMALLINT    [SORTING KEY]          │
│ created_date         │ TIMESTAMP                           │
│ updated_date         │ TIMESTAMP                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ 1:N
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    SECTION TABLE                           │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ VARCHAR(36)                         │
│ title                │ VARCHAR(255)                        │
│ parent_id (FK)       │ VARCHAR(36) [Chapter ID]           │
│ handbook_id (FK)     │ VARCHAR(36)                         │
│ sort_order           │ SMALLINT    [SORTING KEY]          │
│ html                 │ TEXT                                │
│ created_date         │ TIMESTAMP                           │
│ updated_date         │ TIMESTAMP                           │
└─────────────────────────────────────────────────────────────┘
```

## Complete Tree Building Algorithm Flow

```
┌─────────────────────────────────────────────────────────────┐
│                INPUT: Flat Database Records                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Chapters:                    Sections:                    │
│  ┌─────────────────────┐      ┌─────────────────────┐      │
│  │ id: ch1             │      │ id: s1              │      │
│  │ title: "Intro"      │      │ title: "Overview"   │      │
│  │ parent_id: null     │      │ parent_id: ch1      │      │
│  │ sort_order: 1       │      │ sort_order: 1       │      │
│  └─────────────────────┘      └─────────────────────┘      │
│  ┌─────────────────────┐      ┌─────────────────────┐      │
│  │ id: ch2             │      │ id: s2              │      │
│  │ title: "Safety"     │      │ title: "Purpose"    │      │
│  │ parent_id: null     │      │ parent_id: ch1      │      │
│  │ sort_order: 2       │      │ sort_order: 2       │      │
│  └─────────────────────┘      └─────────────────────┘      │
│  ┌─────────────────────┐      ┌─────────────────────┐      │
│  │ id: ch3             │      │ id: s3              │      │
│  │ title: "Equipment"  │      │ title: "Rules"      │      │
│  │ parent_id: ch2      │      │ parent_id: ch2      │      │
│  │ sort_order: 1       │      │ sort_order: 1       │      │
│  └─────────────────────┘      └─────────────────────┘      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│              STEP 1: Group Sections by Chapter             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  chapters.map(chapter => {                                 │
│    val chapterSections = sections.filter(                  │
│      _.parentId == chapter.id                              │
│    )                                                       │
│    constructTreeStructureChapter(chapter, chapterSections) │
│  })                                                        │
│                                                             │
│  Result:                                                   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ TreeStructureChapter(ch1, "Intro", [s1, s2])       │   │
│  │ TreeStructureChapter(ch2, "Safety", [s3])          │   │
│  │ TreeStructureChapter(ch3, "Equipment", [])         │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│              STEP 2: Build Hierarchy (Tail Recursion)      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  @tailrec                                                  │
│  def builder(remaining: List[TreeStructureChapter],        │
│             processed: List[TreeStructureChapter]) = {     │
│                                                             │
│    Iteration 1:                                            │
│    remaining = [ch1, ch2, ch3]                             │
│    processed = []                                          │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ Find leaf chapters (no children in remaining):     │ │
│    │ ch1 ✓ (no children)                                │ │
│    │ ch2 ✗ (ch3 is child)                               │ │
│    │ ch3 ✓ (no children)                                │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    Iteration 2:                                            │
│    remaining = [ch2]                                       │
│    processed = [ch1, ch3]                                  │
│    ┌─────────────────────────────────────────────────────┐ │
│    │ ch2 becomes leaf, add ch3 as its child             │ │
│    │ ch2.chapters = [ch3]                               │ │
│    └─────────────────────────────────────────────────────┘ │
│                                                             │
│    Final:                                                  │
│    remaining = []                                          │
│    processed = [ch1, ch2(with ch3 as child)]              │
│  }                                                         │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    OUTPUT: Tree Structure                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  TreeStructureHandbook                                     │
│  ├── Chapter 1: "Intro" (sortOrder: 1)                    │
│  │   ├── Section 1: "Overview" (sortOrder: 1)            │
│  │   └── Section 2: "Purpose" (sortOrder: 2)             │
│  └── Chapter 2: "Safety" (sortOrder: 2)                   │
│      ├── Section 3: "Rules" (sortOrder: 1)               │
│      └── Sub-Chapter 3: "Equipment" (sortOrder: 1)        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Sorting Process Flow Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    USER INTERACTION                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  1. User clicks "Sort" button                              │
│     ┌─────────────────────────────────────────────────┐     │
│     │ HandbookScreen.jsx / ChapterScreen.jsx         │     │
│     │ toggleSort() → setState({ isSorting: true })   │     │
│     └─────────────────────────────────────────────────┘     │
│                              │                              │
│                              ▼                              │
│  2. Render SortChildrenScreen                              │
│     ┌─────────────────────────────────────────────────┐     │
│     │ <SortChildrenScreen                             │     │
│     │   items={chapters}                              │     │
│     │   sortFunction={sortItemsFun}                   │     │
│     │   onCancel={this.toggleSort}                    │     │
│     │ />                                              │     │
│     └─────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  DRAG & DROP INTERFACE                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Before Drag:                                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ [0] Chapter A (id: ch1, sortOrder: 1)              │   │
│  │ [1] Chapter B (id: ch2, sortOrder: 2)              │   │
│  │ [2] Chapter C (id: ch3, sortOrder: 3)              │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              │ User drags Chapter C to top  │
│                              ▼                              │
│  After Drag:                                               │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ [0] Chapter C (id: ch3, sortOrder: 3) ← moved      │   │
│  │ [1] Chapter A (id: ch1, sortOrder: 1)              │   │
│  │ [2] Chapter B (id: ch2, sortOrder: 2)              │   │
│  └─────────────────────────────────────────────────────┘   │
│                              │                              │
│                              │ arrayMove(items, 2, 0)       │
│                              ▼                              │
│  onSortEnd({ oldIndex: 2, newIndex: 0 })                  │
│  setState({ items: arrayMove(state.items, 2, 0) })        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    SAVE OPERATION                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  handleSave() {                                            │
│    const { items } = this.state;                          │
│    this.setState({ isSaving: true });                     │
│    this.props.sortFunction(items.map(i => i.id));         │
│  }                                                         │
│                                                             │
│  Sends to backend: ["ch3", "ch1", "ch2"]                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  BACKEND PROCESSING                        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  POST /api/local-handbooks/sort/                           │
│  Body: ["ch3", "ch1", "ch2"]                              │
│                              │                              │
│                              ▼                              │
│  LocalHandbookServlet.scala                                │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ post("/sort/?") {                                   │   │
│  │   val sortOrder = parsedBody.extract[List[String]]  │   │
│  │   handbookService.persistSortOrder(sortOrder)       │   │
│  │   204                                               │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                DATABASE UPDATE PROCESS                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  HandbookRepository.persistSortOrder(sortOrder)            │
│                                                             │
│  For each ID in ["ch3", "ch1", "ch2"]:                    │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Index 0: ch3 → sortOrder = 0                       │   │
│  │ Index 1: ch1 → sortOrder = 1                       │   │
│  │ Index 2: ch2 → sortOrder = 2                       │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  SQL Updates:                                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ UPDATE chapter SET sort_order = 0 WHERE id = 'ch3' │   │
│  │ UPDATE chapter SET sort_order = 1 WHERE id = 'ch1' │   │
│  │ UPDATE chapter SET sort_order = 2 WHERE id = 'ch2' │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  OR (if section):                                          │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ UPDATE section SET sort_order = ? WHERE id = ?     │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Database State Before and After Sorting

### Before Sorting Operation
```
CHAPTER TABLE:
┌──────┬─────────────┬─────────────┬───────────┬────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │
├──────┼─────────────┼─────────────┼───────────┼────────────┤
│ ch1  │ Introduction│    hb1      │   NULL    │     1      │
│ ch2  │ Safety Rules│    hb1      │   NULL    │     2      │
│ ch3  │ Appendices  │    hb1      │   NULL    │     3      │
│ ch4  │ Equipment   │    hb1      │    ch2    │     1      │
└──────┴─────────────┴─────────────┴───────────┴────────────┘

SECTION TABLE:
┌──────┬─────────────┬─────────────┬───────────┬────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │
├──────┼─────────────┼─────────────┼───────────┼────────────┤
│  s1  │ Overview    │    hb1      │    ch1    │     1      │
│  s2  │ Purpose     │    hb1      │    ch1    │     2      │
│  s3  │ General     │    hb1      │    ch2    │     1      │
│  s4  │ Emergency   │    hb1      │    ch2    │     2      │
└──────┴─────────────┴─────────────┴───────────┴────────────┘
```

### User Drags "Appendices" to First Position
```
Frontend sends: ["ch3", "ch1", "ch2"]
```

### After Sorting Operation
```
CHAPTER TABLE:
┌──────┬─────────────┬─────────────┬───────────┬────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │
├──────┼─────────────┼─────────────┼───────────┼────────────┤
│ ch3  │ Appendices  │    hb1      │   NULL    │     0      │ ← Updated
│ ch1  │ Introduction│    hb1      │   NULL    │     1      │ ← Updated  
│ ch2  │ Safety Rules│    hb1      │   NULL    │     2      │ ← Updated
│ ch4  │ Equipment   │    hb1      │    ch2    │     1      │ ← Unchanged
└──────┴─────────────┴─────────────┴───────────┴────────────┘

SECTION TABLE: (Unchanged - only root chapters were sorted)
┌──────┬─────────────┬─────────────┬───────────┬────────────┐
│  ID  │    TITLE    │ HANDBOOK_ID │ PARENT_ID │ SORT_ORDER │
├──────┼─────────────┼─────────────┼───────────┼────────────┤
│  s1  │ Overview    │    hb1      │    ch1    │     1      │
│  s2  │ Purpose     │    hb1      │    ch1    │     2      │
│  s3  │ General     │    hb1      │    ch2    │     1      │
│  s4  │ Emergency   │    hb1      │    ch2    │     2      │
└──────┴─────────────┴─────────────┴───────────┴────────────┘
```

## Frontend Rendering After Sort

### Tree Component Rendering Flow
```
┌─────────────────────────────────────────────────────────────┐
│                    FRONTEND RE-RENDER                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  1. Data Fetch (after sort save)                          │
│     ┌─────────────────────────────────────────────────┐     │
│     │ saga.js: fetchHandbooks()                       │     │
│     │ ↓                                               │     │
│     │ GET /api/handbooks/{id}/tree-structure          │     │
│     │ ↓                                               │     │
│     │ HandbookApiService.retrieveHandbookTreeStructure│     │
│     └─────────────────────────────────────────────────┘     │
│                              │                              │
│                              ▼                              │
│  2. Database Query with ORDER BY                           │
│     ┌─────────────────────────────────────────────────┐     │
│     │ SELECT * FROM chapter                           │     │
│     │ WHERE handbook_id = 'hb1'                       │     │
│     │ ORDER BY sort_order ASC                         │     │
│     │                                                 │     │
│     │ Result: [ch3, ch1, ch2, ch4]                   │     │
│     └─────────────────────────────────────────────────┘     │
│                              │                              │
│                              ▼                              │
│  3. Tree Structure Building                                │
│     ┌─────────────────────────────────────────────────┐     │
│     │ createTreeStructure() processes ordered data    │     │
│     │ ↓                                               │     │
│     │ TreeStructureHandbook {                         │     │
│     │   chapters: [                                   │     │
│     │     TreeStructureChapter(ch3, "Appendices"),   │     │
│     │     TreeStructureChapter(ch1, "Introduction"), │     │
│     │     TreeStructureChapter(ch2, "Safety", [ch4]) │     │
│     │   ]                                             │     │
│     │ }                                               │     │
│     └─────────────────────────────────────────────────┘     │
│                              │                              │
│                              ▼                              │
│  4. Component Rendering                                    │
│     ┌─────────────────────────────────────────────────┐     │
│     │ HandbookNode renders:                           │     │
│     │ ├── 📑 Appendices (sortOrder: 0)               │     │
│     │ ├── 📑 Introduction (sortOrder: 1)             │     │
│     │ │   ├── 📄 Overview                            │     │
│     │ │   └── 📄 Purpose                             │     │
│     │ └── 📑 Safety Rules (sortOrder: 2)             │     │
│     │     ├── 📄 General                             │     │
│     │     ├── 📄 Emergency                           │     │
│     │     └── 📑 Equipment                           │     │
│     └─────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

## Sort Order Computation Algorithm

### New Item Insertion
```
┌─────────────────────────────────────────────────────────────┐
│              COMPUTING SORT ORDER FOR NEW ITEMS            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Scenario: Adding new chapter to handbook                  │
│                                                             │
│  Current chapters:                                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ ch1: "Introduction" (sortOrder: 0)                  │   │
│  │ ch2: "Safety Rules" (sortOrder: 1)                  │   │
│  │ ch3: "Appendices"   (sortOrder: 2)                  │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Algorithm:                                                │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ def computeSortOrder(parentId: Option[String]) = {   │   │
│  │   val existingOrders = getChildrenSortOrders(parentId) │   │
│  │   val maxOrder = existingOrders.maxOption.getOrElse(0) │   │
│  │   maxOrder + 1                                      │   │
│  │ }                                                   │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  New chapter gets sortOrder: 3                            │
└─────────────────────────────────────────────────────────────┘
```

### Mixed Content Sorting (Chapters + Sections)
```
┌─────────────────────────────────────────────────────────────┐
│            SORTING CHAPTERS AND SECTIONS TOGETHER          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Parent: "Safety Rules" Chapter                           │
│                                                             │
│  Children before sort:                                     │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Type    │ ID  │ Title           │ sortOrder         │   │
│  ├─────────┼─────┼─────────────────┼───────────────────┤   │
│  │ Section │ s1  │ "General Rules" │ 1                 │   │
│  │ Chapter │ ch4 │ "Equipment"     │ 2                 │   │
│  │ Section │ s2  │ "Emergency"     │ 3                 │   │
│  │ Section │ s3  │ "Training"      │ 4                 │   │
│  └─────────┴─────┴─────────────────┴───────────────────┘   │
│                                                             │
│  User drags "Equipment" to last position:                 │
│  Frontend sends: ["s1", "s2", "s3", "ch4"]               │
│                                                             │
│  Database updates:                                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ UPDATE section SET sort_order = 0 WHERE id = 's1'   │   │
│  │ UPDATE section SET sort_order = 1 WHERE id = 's2'   │   │
│  │ UPDATE section SET sort_order = 2 WHERE id = 's3'   │   │
│  │ UPDATE chapter SET sort_order = 3 WHERE id = 'ch4'  │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Result tree:                                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Safety Rules                                        │   │
│  │ ├── 📄 General Rules (sortOrder: 0)                │   │
│  │ ├── 📄 Emergency (sortOrder: 1)                    │   │
│  │ ├── 📄 Training (sortOrder: 2)                     │   │
│  │ └── 📑 Equipment (sortOrder: 3)                    │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Error Handling and Edge Cases

### Concurrent Modification Handling
```
┌─────────────────────────────────────────────────────────────┐
│                  CONCURRENT MODIFICATION                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Scenario: Two users sorting same content simultaneously   │
│                                                             │
│  User A sorts: ["ch3", "ch1", "ch2"]                      │
│  User B sorts: ["ch2", "ch3", "ch1"]                      │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ Database Transaction Isolation:                     │   │
│  │                                                     │   │
│  │ 1. User A's transaction starts                     │   │
│  │ 2. User B's transaction starts                     │   │
│  │ 3. User A commits first → Success                  │   │
│  │ 4. User B commits → Last writer wins               │   │
│  │                                                     │   │
│  │ Result: User B's sort order is final               │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Frontend handling:                                        │
│  ┌─────────────────────────────────────────────────────┐   │
│  │ - Success toast: "Lagret sortering."               │   │
│  │ - Error toast: "Klarte ikke lagre sortering."     │   │
│  │ - Automatic refetch after successful sort          │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

This comprehensive visual explanation shows how the handbook tree structure is built, how sorting operations flow from frontend to database, and how the system maintains data consistency throughout the process.

