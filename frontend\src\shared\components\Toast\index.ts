import { toast as reactToastify, Slide } from "react-toastify";
import type { ToastOptions, ToastPosition, TypeOptions } from "react-toastify";

export interface ToastConfig extends Partial<ToastOptions> {
  autoClose?: number | false;
  position?: ToastPosition;
}

const DEFAULT_TOAST_CONFIG: ToastOptions = {
  position: "bottom-left",
  autoClose: 8000,
  hideProgressBar: true,
  closeOnClick: true,
  transition: Slide,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  icon: false,
};

const createToastConfig = (customConfig: ToastConfig = {}): ToastOptions => ({
  ...DEFAULT_TOAST_CONFIG,
  ...customConfig,
});

const showToast = (
  type: TypeOptions | "default",
  message: string,
  config: ToastConfig = {}
) => {
  const toastConfig = createToastConfig(config);

  if (type === "default") {
    return reactToastify(message, toastConfig);
  }

  return reactToastify[type](message, toastConfig);
};

export const toast = {
  success: (message: string, config?: ToastConfig) =>
    showToast("success", message, config),

  error: (message: string, config?: ToastConfig) =>
    showToast("error", message, config),

  info: (message: string, config?: ToastConfig) =>
    showToast("info", message, config),

  warning: (message: string, config?: ToastConfig) =>
    showToast("warning", message, config),

  default: (message: string, config?: ToastConfig) =>
    showToast("default", message, config),

  custom: (
    type: TypeOptions | "default",
    message: string,
    config?: ToastConfig
  ) => showToast(type, message, config),

  dismiss: (toastId?: string | number) => reactToastify.dismiss(toastId),
  dismissAll: () => reactToastify.dismiss(),
  isActive: (toastId: string | number) => reactToastify.isActive(toastId),
};

export const toastConfig = {
  getDefaults: (): ToastOptions => ({ ...DEFAULT_TOAST_CONFIG }),

  createPreset: (config: ToastConfig) => ({
    success: (message: string, overrides?: ToastConfig) =>
      toast.success(message, { ...config, ...overrides }),
    error: (message: string, overrides?: ToastConfig) =>
      toast.error(message, { ...config, ...overrides }),
    info: (message: string, overrides?: ToastConfig) =>
      toast.info(message, { ...config, ...overrides }),
    warning: (message: string, overrides?: ToastConfig) =>
      toast.warning(message, { ...config, ...overrides }),
    default: (message: string, overrides?: ToastConfig) =>
      toast.default(message, { ...config, ...overrides }),
  }),
};

export { ToastContainer, Slide, Zoom, Flip, Bounce } from "react-toastify";
export type { ToastOptions, ToastPosition, TypeOptions } from "react-toastify";
