export { BaseErrorBoundary } from "./BaseErrorBoundary";

export {
  Error<PERSON>allback,
  AppErrorFallback,
  RouteErrorFallback,
  FeatureErrorFallback,
  FormErrorFallback,
} from "./ErrorFallback";

export { AppErrorBoundary, AppErrorBoundaryWrapper } from "./AppErrorBoundary";
export {
  RouteErrorBoundary,
  RouteErrorBoundaryWrapper,
} from "./RouteErrorBoundary";
export {
  FeatureErrorBoundary,
  FeatureErrorBoundaryWrapper,
} from "./FeatureErrorBoundary";
export {
  FormErrorBoundary,
  FormErrorBoundaryWrapper,
} from "./FormErrorBoundary";

import React from "react";
import { AppErrorBoundaryWrapper } from "./AppErrorBoundary";
import { RouteErrorBoundaryWrapper } from "./RouteErrorBoundary";
import { FeatureErrorBoundaryWrapper } from "./FeatureErrorBoundary";
import { FormErrorBoundaryWrapper } from "./FormErrorBoundary";
import type { ErrorBoundaryProps } from "@/shared/types/errorBoundary";

interface GenericErrorBoundaryProps extends ErrorBoundaryProps {
  level: "app" | "route" | "feature" | "form";
}

export const ErrorBoundary: React.FC<GenericErrorBoundaryProps> = ({
  level,
  ...props
}) => {
  switch (level) {
    case "app":
      return <AppErrorBoundaryWrapper {...props} />;
    case "route":
      return <RouteErrorBoundaryWrapper {...props} />;
    case "feature":
      return <FeatureErrorBoundaryWrapper {...props} />;
    case "form":
      return <FormErrorBoundaryWrapper {...props} />;
    default:
      return <FeatureErrorBoundaryWrapper {...props} />;
  }
};

export const AppErrorBoundary_v2 = AppErrorBoundaryWrapper;
export const RouteErrorBoundary_v2 = RouteErrorBoundaryWrapper;
export const FeatureErrorBoundary_v2 = FeatureErrorBoundaryWrapper;
export const FormErrorBoundary_v2 = FormErrorBoundaryWrapper;
