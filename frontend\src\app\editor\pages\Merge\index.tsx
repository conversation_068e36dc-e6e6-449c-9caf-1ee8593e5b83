import { useEffect } from "react";
import { Routes, Route } from "react-router-dom";
import { Container, Section } from "kf-bui";
import { MergeHandbook } from "../../features/merge/MergeHandbook";
import { MergeChapter } from "../../features/merge/MergeChapter";
import { MergeSection } from "../../features/merge/MergeSection";

export const MergePage = () => {
  useEffect(() => {
    document.title = "Håndter endring - KF Håndbøker";
  }, []);

  return (
    <Section>
      <Container>
        <Routes>
          <Route path="handbook/:id" element={<MergeHandbook />} />
          <Route path="chapter/:id" element={<MergeChapter />} />
          <Route path="section/:id" element={<MergeSection />} />
          <Route
            path="*"
            element={
              <div style={{ padding: "2rem", textAlign: "center" }}>
                <h1>Merge Page</h1>
                <p>Select an item to merge from the pending changes list.</p>
              </div>
            }
          />
        </Routes>
      </Container>
    </Section>
  );
};
