package no.kf.handboker.model

import org.joda.time.DateTime

case class Publication(
                        id: Option[String],
                        publicationDate: DateTime,
                        notifyByEmail: Bo<PERSON>an,
                        createdDate: DateTime,
                        createdBy: String,
                        handbookId: String,
                        published: Boolean = false
                      )
