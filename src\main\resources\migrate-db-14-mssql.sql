delete from changenotification where chapter_id is not null or section_id is not null or change_html is not null
ALTER TABLE changenotification DROP COLUMN chapter_id
ALTER TABLE changenotification DROP COLUMN section_id
ALTER TABLE changenotification DROP COLUMN change_html
ALTER TABLE changenotification DROP COLUMN notify_in_gui
ALTER TABLE changenotification DROP COLUMN acknowledged_in_gui
ALTER TABLE changenotification DROP COLUMN concerns_title
ALTER TABLE changenotification DROP COLUMN deletion

update handbookchapter set importedhandbook_id = (select chapter.central_handbook_id from central_handbooks.chapter where central_id = handbookchapter.importedhandbookchapter_id) where importedhandbook_id is not null and not exists (select * from central_handbooks.handbook where central_id = handbookchapter.importedhandbook_id and version_of is null)
update handbooksection set importedhandbook_id = (select section.central_handbook_id from central_handbooks.section where id = handbooksection.importedhandbooksection_id) where importedhandbook_id is not null and not exists (select * from central_handbooks.handbook where central_id = handbooksection.importedhandbook_id and version_of is null)
