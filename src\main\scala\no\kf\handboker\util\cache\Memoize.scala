package no.kf.handboker.util.cache

class Memoize[R](f: () => R, maxAgeMs: Long) extends (() => R) {
  case class CacheValue(value: R, lastUpdated: Long) {
    def isExpired: Boolean = lastUpdated + maxAgeMs <= System.currentTimeMillis()
  }

  private[this] var cache: Option[CacheValue] = None

  def apply(): R = {
    cache match {
      case Some(cachedValue) if !cachedValue.isExpired => cachedValue.value
      case _ => {
        cache = Some(CacheValue(f(), System.currentTimeMillis()))
        cache.get.value
      }
    }
  }
}

object Memoize {
  def apply[R](f: () => R, maxAgeMs: Long = 600000) = new Memoize(f, maxAgeMs)
}
