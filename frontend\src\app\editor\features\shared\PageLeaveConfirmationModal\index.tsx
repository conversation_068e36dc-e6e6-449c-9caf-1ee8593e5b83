import React from 'react';
import { Button, Modal } from 'kf-bui';

interface PageLeaveConfirmationModalProps {
  isOpen: boolean;
  onCancel: () => void;
  onConfirm: () => void;
  message?: string;
  title?: string;
  t: (key: string) => string;
}

export const PageLeaveConfirmationModal: React.FC<PageLeaveConfirmationModalProps> = ({ 
  isOpen,
  onCancel, 
  onConfirm, 
  message, 
  title, 
  t 
}) => {
  const defaultTitle = title || t('leaveEditorConfirmationTitle');
  const defaultMessage = message || t('leaveEditorConfirmationMessage');

  return (
    <Modal isOpen={isOpen} onClose={onCancel}>
      <Modal.Header onClose={onCancel}>
        <Modal.Title>{defaultTitle}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p>{defaultMessage}</p>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={onCancel}>{t('leaveEditorConfirmationCancel')}</Button>
        <Button color="danger" outlined className="modal-leave-button" onClick={onConfirm}>
          {t('leaveEditorConfirmationLeave')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};