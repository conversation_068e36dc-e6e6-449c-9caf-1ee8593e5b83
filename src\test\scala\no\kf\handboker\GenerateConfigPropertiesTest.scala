package no.kf.handboker

import no.kf.handboker.config.AppSettingComponent
import org.junit.runner.RunWith
import org.scalatest.FunSuite
import org.scalatest.junit.JUnitRunner

/**
 * Not really a test. A utility for generating a new and empty property file for the app.
 * This is used as part of distribution of zip files. That is, the property file is wrapped up in a zip file with the war file as well.
 */
@RunWith(classOf[JUnitRunner])
class GenerateConfigPropertiesTest extends FunSuite {

  test("Generate empty property file in target dir") {
    try {
      new AppSettingComponent {
        val settings = new AppSettings("target")
      }
    }
    catch {
      case e: Throwable => println(e)
    }
  }

}
