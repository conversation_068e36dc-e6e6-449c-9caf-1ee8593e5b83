package no.kf.handboker.service

import better.files._
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.config.UploadFolder
import no.kf.handboker.model.KFFile
import org.scalatest.{BeforeAndAfterAll, FunSuite}
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.mockito.Mockito._

@RunWith(classOf[JUnitRunner])
class FileServiceTest extends FunSuite with DefaultTestDI with BeforeAndAfterAll {

  override val componentRegistry = new DefaultTestRegistry {
    override lazy val fileService: FileService = new FileServiceImpl
  }

  val fileService = componentRegistry.fileService
  val rootFolder = componentRegistry.settings.settingFor(UploadFolder)

  test("That we can persist, retrieve and delete Files") {
    val file = createFile()

    fileService.persistFile(file)

    val expectedContentFromDisk = buildOutputDir(file).lines.foldRight("")(_+_)
    assert(new String(file.file) === expectedContentFromDisk)

    val expectedContentFromService = new String(fileService.retrieveFile(file.createdName).byteArray)
    assert(new String(file.file) === expectedContentFromService)

    assert(fileService.deleteFile(file.createdName))
    assert(buildOutputDir(file).notExists)

  }

  def buildOutputDir(file: KFFile): File = {
    rootFolder / file.id.take(2) / file.createdName
  }

  def createFile(): KFFile = {
    KFFile("pdf", "test_pdf".getBytes)
  }


}
