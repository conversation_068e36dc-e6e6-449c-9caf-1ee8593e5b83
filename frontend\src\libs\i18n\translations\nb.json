{"common.components.NoAccessPage.header": "<PERSON><PERSON> tilgang", "common.components.NoAccessPage.message": "<PERSON><PERSON><PERSON>, du har ikke tilstrekkelige rettigheter til å åpne denne siden.", "common.components.NotFoundPage.header": "Fant ikke siden", "common.components.NotFoundPage.message": "<PERSON><PERSON><PERSON>, men siden du leter etter har blitt fly<PERSON>t, eller så eksisterer den ikke.", "common.components.InvalidLinkPage.header": "410", "common.components.InvalidLinkPage.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> lenke", "common.components.InvalidLinkPage.message": "<PERSON>ne lenken er utgått.", "common.components.OptOutModal.title": "Innstillinger for informasjonskapsler", "common.components.OptOutModal.description1": "Vi bruker informasjonskapsler til å samle inn informasjon om deg til ulike formål.", "common.components.OptOutModal.description2": "Noen er nødvendige å innhente for at du kan gjenkjennes og logge inn som bruker. <PERSON> er frivillige, og benyttes til å se bruksstatistikk.", "common.components.OptOutModal.description3": "KF bruke<PERSON>, en personvernvennlig statistikkløsning, til å analysere bruken av nettstedet. Statistikkrapportene Matomo gir KF, brukes til å analysere og forbedre brukervennligheten til nettsiden, og til oppfølging av kunder.", "common.components.OptOutModal.readMore": "Les mer om informasjonskapsler her.", "common.components.OptOutModal.acceptAll": "<PERSON>ta alle", "common.components.OptOutModal.acceptNecessary": "God<PERSON> bare nødvendige", "common.components.OptOutModal.showCookies": "Se hvilke informasjonskapsler vi benytter", "common.components.OptOutModal.necessary": "Nødvendige", "common.components.OptOutModal.optional": "Frivillige", "common.components.OptOutModal.saveButton": "Lagre", "common.components.OptOutModal.recipient": "<PERSON><PERSON><PERSON>", "common.components.OptOutModal.noMatomoInfo": "Kunne ikke hente informasjon fra statistikktjeneste.", "common.components.OptOutModal.cookies.jsessionid.description": "Benyttes til sesjonshåndtering når du er logget inn", "common.components.OptOutModal.cookies.xsrfToken.description": "Denne informasjonskapselen brukes når du er logget inn, og sikrer at andre ikke kan utgi seg for å være deg.", "common.components.OptOutModal.cookies.tgc.description": "Innlogging", "common.components.IdleTimeoutModal.title": "<PERSON><PERSON> u<PERSON><PERSON><PERSON> snart", "common.components.IdleTimeoutModal.message": "Du har vært inaktiv en stund. Økten din vil utløpe snart.", "common.components.IdleTimeoutModal.autoLogoutWarning": "<PERSON> blir automatisk logget ut når tiden er ute.", "common.components.IdleTimeoutModal.continueSession": "Fortsett økt", "common.components.IdleTimeoutModal.logout": "Logg ut", "common.components.OptOutModal.cookies.matomo.pkId": "Brukes til å skille brukere fra hverandre", "common.components.OptOutModal.cookies.matomo.pkRef": "Brukes til å se hvilket nettsted eller søkemotor brukeren kom fra", "common.components.OptOutModal.cookies.matomo.pkSes": "Brukes til å skille økter/besøk fra hverandre når bruker er logget inn i løsningen.", "common.components.OptOutModal.cookies.matomo.sessId": "Sikkerhet. Lagrer ingen brukerdata som kan identifisere besøkende.", "common.idleTimeout.sessionTimeoutTitle": "Sesjonen utlø<PERSON> snart", "common.idleTimeout.sessionTimeoutMessage": "Du har vært inaktiv en stund. Sesjonen din vil utløpe snart. Klikk på knappen nedenfor for å fortsette å være logget inn.", "common.idleTimeout.sessionExpiredTitle": "<PERSON><PERSON><PERSON><PERSON> har utløpt", "common.idleTimeout.sessionExpiredMessage": "Sesjonen din har utløpt på grunn av inaktivitet. Du må logge inn på nytt for å fortsette.", "common.idleTimeout.sessionExpiredDetails": "Av si<PERSON><PERSON><PERSON>hen<PERSON>n blir du automatisk logget ut etter en periode med inaktivitet.", "common.idleTimeout.sessionTimeoutRemaining": "<PERSON><PERSON> i<PERSON>n", "common.idleTimeout.minutes": "minutter", "common.idleTimeout.seconds": "<PERSON><PERSON>nder", "common.idleTimeout.keepSessionActive": "Hold sesjonen aktiv", "common.idleTimeout.logOut": "Logg ut", "common.idleTimeout.close": "Lukk", "common.authError.authErrorTitle": "Autentiseringsfeil", "common.authError.authErrorMessage": "Det oppstod en feil med innloggingen din. Dette kan skyldes at sesjonen har utl<PERSON><PERSON> eller at det er problemer med tilkoblingen.", "common.authError.generalErrorTitle": "<PERSON>e gikk galt", "common.authError.generalErrorMessage": "Det oppstod en uventet feil. Prøv å laste siden på nytt eller kontakt support hvis problemet vedvarer.", "common.authError.errorDetails": "Tekniske detaljer", "common.authError.retry": "<PERSON><PERSON><PERSON><PERSON> ig<PERSON>n", "common.authError.retrying": "Prøver igjen...", "common.authError.reloadPage": "Last siden på nytt", "common.authError.logout": "Logg ut", "common.authError.maxRetriesReached": "Maksimalt antall forsøk nådd. Last siden på nytt eller kontakt support.", "common.components.ReadingLinkModal.title": "<PERSON><PERSON>", "common.components.ReadingLinkModal.warning": "Obs! Alle som har lenken vil kunne åpne den. Lenken vil være gyldig til tidsrommet du velger er over.", "common.components.ReadingLinkModal.linkLabel": "Lesevisningslenke:", "common.components.ReadingLinkModal.copyLink": "<PERSON><PERSON><PERSON>", "common.components.ReadingLinkModal.validUntil": "Varer frem til:", "common.components.ReadingLinkModal.linkExpired": "Lenken er utgått", "common.components.ReadingLinkModal.generateNew": "Generer ny lenke", "common.components.ReadingLinkModal.generateLink": "<PERSON><PERSON> lenke", "common.components.ReadingLinkModal.deleteLink": "<PERSON><PERSON> lenke", "common.components.ReadingLinkModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "common.components.ReadingLinkModal.month": "må<PERSON>", "common.components.ReadingLinkModal.year": "<PERSON>r", "common.components.ReadingLinkModal.linkGenerated": "<PERSON><PERSON><PERSON><PERSON> generert", "common.components.ReadingLinkModal.linkDeleted": "<PERSON><PERSON><PERSON><PERSON> slettet", "common.components.ReadingLinkModal.errorGeneral": "En feil inntraff", "common.components.ReadingLinkModal.errorRetrieve": "<PERSON><PERSON><PERSON> ikke hente <PERSON>e", "common.components.ReadingLinkModal.errorCreate": "Klarte ikke opprette leselenke", "common.components.ReadingLinkModal.errorDelete": "<PERSON><PERSON><PERSON> ikke s<PERSON>e", "common.components.ReadingLinkModal.loading": "Laster...", "common.components.ReadingLinkModal.selectDuration": "Velg hvor lenge lenken skal være gyldig", "common.components.ReadingLinkModal.linkGeneratedAutomatically": "Lenke blir generert automatisk ved opprettelse", "common.components.ReadingLinkModal.noLinkToCopy": "Ingen lenke å kopiere", "common.components.ReadSectionPage.loading": "Laster innhold...", "common.components.ReadSectionPage.error": "Kunne ikke laste innhold", "common.components.SearchResult.pageInfo": "Side {page} av {totalPages}. Viser {totalHits} resultater.", "common.components.SearchResult.noResults": "Ingen treff funnet for «{query}»", "common.components.SearchResult.tips": "Tips", "common.components.SearchResult.tip1": "<PERSON><PERSON><PERSON><PERSON> å bruke mer generelle sø<PERSON>.", "common.components.SearchResult.tip2": "<PERSON>vis du filtrerer på håndbok, forsøk å fjerne eller justere filteret.", "common.components.SearchPagination.pageLabel": "Side {pageNumber}", "editor.components.Breadcrumb.rootLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.components.DeleteModal.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.components.DeleteModal.deleteButton": "<PERSON><PERSON>", "editor.components.Header.brand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.components.Header.editor": "Redaktø<PERSON>", "editor.components.Header.central": "<PERSON><PERSON><PERSON>", "editor.components.Header.central-editor": "Sentral editor", "editor.components.Header.pending": "Se endringer fra sentrale Håndbøker som må håndteres", "editor.components.Header.settings": "Innstillinger", "editor.containers.PendingPage.title": "<PERSON><PERSON><PERSON> endringer", "editor.containers.PendingPage.header": "Endringer i sentrale Håndbøker", "editor.containers.PendingPage.noChanges": "Ingen endringer i sentrale Håndbøker", "editor.components.MergeHandbookOrChapter.mergeChapter": "Det har vært en endring i det sentrale kapittelet.", "editor.components.MergeHandbookOrChapter.mergeChapterParagraph": "Du må håndtere dette ved å velge om du vil ta inn endringen eller beholde kapittelet slik det er.", "editor.components.MergeHandbookOrChapter.mergeManual": "Det har vært en endring i den sentrale håndboken.", "editor.components.MergeHandbookOrChapter.mergeManualParagraph": "Du må håndtere dette ved å velge om du vil ta inn endringen eller beholde håndboken slik den er.", "editor.components.MergeHandbookOrChapter.headerCentral": "<PERSON><PERSON> sentral tittel", "editor.components.MergeHandbookOrChapter.useCentral": "Bruk sentral tittel", "editor.components.MergeHandbookOrChapter.headerExisting": "Nåværende lokale tittel", "editor.components.MergeHandbookOrChapter.useLocal": "<PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON> tittel", "editor.components.MergeHandbookOrChapter.saveButton": "Lagre", "editor.components.MergeHandbookOrChapter.fetchCentralElementFail": "<PERSON><PERSON>te ikke hente sentralt innhold", "editor.components.MergeSection.lead.title": "Det har vært en endring i det sentrale avsnittet.", "editor.components.MergeSection.lead.text": "Du må håndtere dette ved å velge om du vil ta inn endringen eller beholde avsnittet slik det er.", "editor.components.MergeSection.central.header": "Sentralt avsnitt", "editor.components.MergeSection.central.text": "Sentral tittel", "editor.components.MergeSection.central.radio": "Bruk sentral tekst", "editor.components.MergeSection.local.header": "Lokalt avsnitt", "editor.components.MergeSection.local.text": "<PERSON>al tittel", "editor.components.MergeSection.local.radio": "Bruk lokal tekst", "editor.components.MergeSection.saveButton": "Lagre", "editor.components.MergeSection.fetchCentralElementFail": "<PERSON><PERSON>te ikke hente sentralt innhold", "editor.components.Metadata.created": "Opprettet av", "editor.components.Metadata.updated": "Tittel oppdatert av", "editor.components.Metadata.textUpdated": "Tekst oppdatert av", "editor.components.Metadata.centralBased": "Sentralbasert", "editor.components.Metadata.mergeLink": "Utestående sentral endring", "editor.components.Metadata.mergeLink.title": "<PERSON><PERSON><PERSON><PERSON> utest<PERSON><PERSON>e sentral endring", "editor.components.NoSelection.createButton": "<PERSON>y håndbok", "editor.components.NoSelection.text": "Velg et element i strukturen", "editor.components.SelectCentralHandbooks.availableBooks": "Tilgjengelige sentrale Håndbøker", "editor.components.SelectCentralHandbooks.orgBooks": "Organisasjonens sentrale Håndbøker", "editor.components.SelectCentralHandbooks.resetButton": "Tilbakestill", "editor.components.SelectCentralHandbooks.saveButton": "Lagre", "editor.components.SelectExternalOrganizationPage.continueButton": "Fortsett", "editor.components.SelectExternalOrganizationPage.title": "Velg organisasjon", "editor.components.SettingsPage.title": "Innstillinger", "editor.components.SortChildren.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.components.SortChildren.cancelButton.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> sortering", "editor.components.SortChildren.saveButton": "Lagre", "editor.components.SortChildren.saveButton.title": "Lagre sortering", "editor.containers.App.title": "KF Håndbøker", "editor.containers.App.application": "KF Håndbøker", "editor.containers.App.loggedIn": "Innlogget", "editor.containers.App.changedOrganization": "Byttet organisasjon", "editor.containers.App.changedOrganizationFail1": "En feil inntraff", "editor.containers.App.changedOrganizationFail2": "Klarte ikke velge organisasjon", "editor.containers.CentralHandbooksPage.title": "Tilgang til sentralt innhold", "editor.containers.CentralHandbooksPage.header": "Her kan du administere hvilke sentrale Håndbøker en organisasjon skal ha tilgang til", "editor.containers.CentralHandbooksPage.chooseOrganization": "Velg organisasjon", "editor.containers.CentralHandbooksPage.loading": "<PERSON><PERSON>...", "editor.containers.CentralHandbooksPage.saveSuccess": "<PERSON><PERSON><PERSON> tilgang for organisasjon.", "editor.containers.CentralHandbooksPage.saveError": "<PERSON>nne ikke lagre tilgang", "editor.containers.CentralHandbookEditorPage.title": "Editor for sentrale håndbøker", "editor.containers.CentralHandbookEditorPage.header": "Her kan du legge til, endre og slette sentrale håndbøker", "editor.containers.CentralHandbookEditorPage.createButton": "<PERSON>y håndbok", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.titleLabel": "<PERSON><PERSON><PERSON>", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.titlePlaceholder": "<PERSON><PERSON><PERSON>", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.titleRequired": "Håndboka må ha en tittel", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.saveButton": "Lagre", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.createTitle": "<PERSON><PERSON><PERSON><PERSON> ny sentral håndbok", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.editTitle": "Rediger sentral håndbok", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.deleteButton": "<PERSON><PERSON>", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.createSuccess": "Håndboka ble opprettet", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.updateSuccess": "Håndboka ble oppdatert", "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm.saveError": "<PERSON>nne ikke lagre håndboka", "editor.containers.ChapterSelection.newChapter": "Nytt kapittel", "editor.containers.ChapterSelection.newSection": "Nytt avsnitt", "editor.containers.ChapterSelection.addAttachment": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.addAttachment": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.SectionSelection.addAttachment": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.attachmentModalTitle": "Last opp vedlegg", "editor.containers.dragAndDrop": "<PERSON>a og slipp filen din her", "editor.containers.or": "eller", "editor.containers.clickToAdd": "Last opp vedlegg fra filer", "editor.containers.accestedFileTypes": "<PERSON><PERSON><PERSON>", "editor.containers.attachmentForSection": "vedlegg per avsnitt eller kapittel", "editor.containers.max": "<PERSON><PERSON>", "editor.containers.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.publish": "<PERSON><PERSON><PERSON>", "editor.containers.uploadedBy": "Lastet opp av", "editor.containers.readyToPublish": "Klar til å publiseres", "editor.containers.fileOversized": "Filen er større enn maksgrensen på 5 MB", "editor.containers.attachmentsAddedSuccessfully": "<PERSON><PERSON><PERSON>g er lagt ved", "editor.containers.attachmentsRemovedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> er fjernet", "editor.containers.attachmentsUpdatedSuccessfully": "Vedlegg er oppdatert", "editor.containers.fileSizeExceeded": "Filen du har valgt er større enn 5 MB. Vennligst bruk en fil som er mindre enn 5 MB.", "editor.containers.uploadLimitReached": "<PERSON> har n<PERSON><PERSON> for vedlegg. Du må fjerne vedlegg for å få plass til nye.", "editor.containers.ChapterSelection.editButton": "<PERSON><PERSON>", "editor.containers.ChapterSelection.moveButton": "<PERSON><PERSON>", "editor.containers.ChapterSelection.sortButton": "Sorter", "editor.containers.ChapterSelection.deleteButton": "<PERSON><PERSON>", "editor.containers.ChapterSelection.deleteTitle": "Vil du slette kapittel?", "editor.containers.ChapterSelection.deleteQuestion": "<PERSON>r du sikker på at du vil slette kapitlet {title}?", "editor.containers.ChapterSelection.deleteWarning": "Merk at dette også vil slette eventuelle underliggende kapitler og avsnitt.", "editor.containers.CentralTree.selectHandbookFail": "<PERSON><PERSON>te ikke hente sentralt innhold", "editor.containers.CentralTree.errorLoading": "<PERSON>il ved <PERSON> av hå<PERSON><PERSON><PERSON>", "editor.containers.CentralTree.loading": "Laster...", "editor.containers.CreateOrUpdateHandbook.createTitle": "Op<PERSON>rett ny håndbok", "editor.containers.CreateOrUpdateHandbook.editTitle": "<PERSON>iger håndbok", "editor.containers.CreateOrUpdateHandbook.centralBased": "<PERSON>elg dersom du vil basere ny håndbok på en sentral håndbok.", "editor.containers.CreateOrUpdateHandbook.autoSync": "Endringer i den sentrale håndboken blir automatisk synkronisert.", "editor.containers.CreateOrUpdateHandbook.manualSync": "<PERSON><PERSON><PERSON> fra sentral håndbok må vedlikeholdes manuelt.", "editor.containers.CreateOrUpdateHandbook.publicLabel": "Offentlig til<PERSON>g", "editor.containers.CreateOrUpdateHandbook.saveButton": "Lagre", "editor.containers.DeleteButton.deleteLocalWarning": "Hvis du sletter denne håndboken vil kundene fortsatt beholde sine lokale versjoner av denne håndboken. De får per nå ikke varsel om at sentral håndbok er slettet og at videre vedlikehold av håndboken må gjøres lokalt.", "editor.containers.DeleteButton.readLinkDeleteWarning": "Det er knyttet en lese-lenke til et av avsnittene i håndboken. Alle lese-lenker vil også slettes, og dermed slutte å virke, når du sletter håndboken.", "editor.containers.EditChapter.editTitle": "Rediger kapittel", "editor.containers.EditChapter.createTitle": "Opprett nytt kapittel", "editor.containers.EditChapter.createNew": "Opprett nytt kapittel", "editor.containers.EditChapter.titleLabel": "<PERSON><PERSON><PERSON>", "editor.containers.EditChapter.autoSync": "Endringer i det sentrale kapitlet blir automatisk synkronisert.", "editor.containers.EditChapter.manualSync": "Sen<PERSON>e endringer må håndteres manuelt.", "editor.containers.EditChapter.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.EditChapter.removeCentralSelection": "<PERSON><PERSON><PERSON> valg", "editor.containers.EditChapter.centralButton": "<PERSON><PERSON><PERSON> sent<PERSON>t kapittel", "editor.containers.EditChapter.centralTitle": "Sentralt kapittel", "editor.containers.EditChapter.saveButton": "Lagre", "editor.containers.EditSection.createTitle": "Opprett nytt avsnitt", "editor.containers.EditSection.editTitle": "Rediger avsnitt", "editor.containers.EditSection.titleLabel": "<PERSON><PERSON><PERSON>", "editor.containers.EditSection.centralRadio": "Bruk sentral tekst", "editor.containers.EditSection.localRadio": "Bruk sentral tekst med egne endringer", "editor.containers.EditSection.textLabel": "Tekst", "editor.containers.EditSection.manualSync": "Sen<PERSON>e endringer må håndteres manuelt.", "editor.containers.EditSection.autoSync": "Endringer i det sentrale avsnittet blir automatisk synkronisert.", "editor.containers.EditSection.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.EditSection.removeCentralSelection": "<PERSON><PERSON><PERSON> valg", "editor.containers.EditSection.centralButton": "Velg sentralt avsnitt", "editor.containers.EditSection.centralTitle": "Sentralt avsnitt", "editor.containers.EditSection.saveButton": "Lagre", "editor.containers.HandbookPage.components.LocalEditorsModal.title": "Gi lese- og skrivetilgang", "editor.containers.HandbookPage.components.LocalEditorsModal.accessTitle": "Gi tilgang til", "editor.containers.HandbookPage.components.LocalEditorsModal.accessHelp": "Du kan velge blant alle redaktører i din organisasjon. Du kan gi tilgang til en eller flere redaktører. Den du velger vil kunne se og redigere i håndboken, og kunne gi eller ta bort lese- og skriverettigheter til andre redaktører i din organisasjon.", "editor.containers.HandbookPage.components.LocalEditorsModal.accessListTitle": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.HandbookPage.components.LocalEditorsModal.selfDeleteWarning": "Du har fjernet din egen tilgang", "editor.containers.HandbookPage.components.LocalEditorsModal.noEditorsWarning": "OBS: <PERSON><PERSON> <PERSON>er har tilgang til denne håndboka!", "editor.containers.HandbookSelection.newChapter": "Nytt kapittel", "editor.containers.HandbookSelection.sortButton": "Sorter", "editor.containers.HandbookSelection.subscribeCheck": "<PERSON><PERSON><PERSON><PERSON> på sentrale endringer", "editor.containers.HandbookSelection.publishedLink": "Se publis<PERSON> h<PERSON>", "editor.containers.HandbookSelection.deleteButton": "<PERSON><PERSON>", "editor.containers.HandbookSelection.editButton": "<PERSON><PERSON>", "editor.containers.HandbookSelection.subscribeCheckMouseOver": "Få e-postvarsel når den sentrale handboken blir oppdatert", "editor.containers.HandbookSelection.deleteTitle": "Vil du slette håndboken?", "editor.containers.HandbookSelection.deleteQuestion": "<PERSON>r du sikker på at du vil slette håndboken {title}?", "editor.containers.HandbookSelection.deleteWarning": "Merk at dette også vil slette alle underliggende kapitler og avsnitt.", "editor.containers.HandbookPage.components.CommentModal.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.HandbookPage.components.CommentModal.closeButton": "Lukk", "editor.containers.HandbookPage.components.CommentModal.newCommentButton": "Ny kommentar", "editor.containers.HandbookPage.components.CommentModal.saveButton": "Lagre", "editor.containers.HandbookPage.components.CommentModal.title": "Interne redaksjonelle kommentarer", "editor.containers.HandbookPage.components.CommentCard.deleteButton": "<PERSON><PERSON>", "editor.containers.HandbookPage.components.CommentCard.editButton": "<PERSON><PERSON>", "editor.containers.HandbookPage.components.ExportModal.close": "Lukk", "editor.containers.HandbookPage.components.ExportModal.dateTitle": "Velg dato for versjonen du vil eksportere", "editor.containers.HandbookPage.components.ExportModal.exportPDF": "Eksporter som PDF", "editor.containers.HandbookPage.components.ExportModal.exportQuestion": "Vil du eksportere dokumentet?", "editor.containers.HandbookPage.components.ExportModal.title": "Eksport av ", "editor.containers.HandbookPage.components.HandbookScreen.exportButton": "Eksporter", "editor.containers.HandbookPage.components.HandbookScreen.internalComments": "Interne kommentarer", "editor.containers.HandbookPage.components.HandbookScreen.readLink": "SE LESEVISNING AV HÅNDBOK", "editor.containers.LocalTree.treeHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.LocalTree.errorLoading": "<PERSON>il ved <PERSON> av hå<PERSON><PERSON><PERSON>", "editor.containers.LocalTree.loading": "Laster...", "editor.containers.CentralTree.treeHeader": "<PERSON><PERSON><PERSON>", "editor.containers.MergePage.title": "<PERSON><PERSON><PERSON><PERSON> endring", "editor.containers.MergePage.loading": "Laster endring", "editor.containers.MoveChapterOrSelection.moveTo": "Flytt inn under {title}?", "editor.containers.MoveChapterOrSelection.moveElement": "Velg elementet i strukturen hvor du vil flytte {title}...", "editor.containers.MoveChapterOrSelection.cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.MoveChapterOrSelection.moveButton": "<PERSON><PERSON>", "editor.containers.SectionPage.components.OldVersionView.pickVersion": "Velg hvilken versjon av avsnittet du vil se", "editor.containers.SectionPage.components.OldVersionView.title": "Tidligere versjoner", "editor.containers.SectionSelection.sectionText": "Avsnittstekst", "editor.containers.SectionSelection.editButton": "<PERSON><PERSON>", "editor.containers.SectionSelection.moveButton": "<PERSON><PERSON>", "editor.containers.SectionSelection.deleteTitle": "Vil du slette avsnittet?", "editor.containers.SectionSelection.deleteButton": "<PERSON><PERSON>", "editor.containers.SectionSelection.deleteQuestion": "<PERSON>r du sikker på at du vil slette avsnittet {title}?", "editor.containers.SectionSelection.editor.error.sectionNotFound": "Avsnitt ikke funnet", "editor.containers.SectionSelection.editor.success.sectionDeleted": "Avs<PERSON>tt s<PERSON>t", "editor.containers.SectionSelection.editor.error.sectionDeleteFailed": "Kunne ikke slette avsnitt", "editor.containers.SelectOrganizationPage.changeOrganizationTitle": "<PERSON>tt organ<PERSON>", "editor.containers.SelectOrganizationPage.setOrganizationTitle": "Velg organisasjon", "editor.containers.SelectOrganizationPage.changeOrganizationButton": "<PERSON><PERSON>", "editor.containers.SelectOrganizationPage.setOrganizationButton": "Velg", "editor.containers.SelectOrganizationPage.selectOrganization": "<PERSON><PERSON><PERSON> du kan gå videre må du velge en organisasjon...", "editor.containers.WithBreadcrumb.toSearchButton": "Søk i håndboka", "public.components.Header.brand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "public.components.WelcomePage.heading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "public.components.WelcomePage.message": "På grunn av sikkerhetsmessige årsaker kan håndbøker kun aksesseres via direkte lenker.", "public.components.Footer.accessibility": "Tilgjengelighetserklæring", "public.components.Footer.cookies": "Informasjonskapsler", "public.components.Footer.cookieSettings": "Innstillinger for informasjonskapsler", "public.containers.App.title": "KF Håndbøker", "public.containers.App.application": "KF Håndbøker", "public.containers.HandbookPage.title": "Håndbok", "public.containers.HandbookPage.placeholder": "Dette er en midlertidig implementering av HandbookPage", "public.containers.ChapterPage.chapterNotFound": "Kapittel ikke funnet", "public.components.Attachments.loading": "Laster inn vedlegg. Vennligst vent...", "public.components.Attachments.errorLoading": "Feil ved lasting av vedlegg", "public.components.Attachments.noAttachments": "Ingen vedlegg funnet", "public.containers.HandbookPage.loading": "Laster <PERSON>...", "public.containers.HandbookPage.error": "<PERSON>il ved <PERSON> av håndbok", "public.components.MobileTree.toggleTree": "Vis innhold", "public.components.MobileTree.toggleTreeLabel": "<PERSON><PERSON>ne eller lukk innholdsfortegnelse", "public.components.MobileTree.closeTree": "Lukk innholdsfortegnelse", "public.components.MobileTree.treeNavigationLabel": "Innholdsfortegnelse navigasjon", "public.components.Tree.noHandbook": "Ingen håndbok lastet", "public.components.Tree.treeTitle": "Navigasjonstruktur", "public.components.Tree.noChapters": "Ingen kapitler funnet", "public.containers.SearchPage.title": "Søk - KF Håndbøker", "public.containers.SearchPage.placeholderTitle": "Søkefunksjonalitet - Midlertidig implementering", "public.containers.SearchPage.placeholderMessage": "Dette er en forenklet implementering av søkefunksjonaliteten. Full implementering krever:", "public.containers.SearchPage.searchPlaceholder": "Søk i håndboken...", "public.containers.SearchPage.search": "<PERSON><PERSON><PERSON>", "public.containers.SearchPage.searching": "<PERSON><PERSON><PERSON>...", "public.containers.SearchPage.searchError": "En feil oppstod under s<PERSON><PERSON>", "public.containers.SearchPage.searchResults": "Søkeresultater", "public.containers.SearchPage.noResults": "Ingen resultater funnet", "public.containers.SearchPage.noResultsHint": "<PERSON><PERSON><PERSON><PERSON> andre sø<PERSON> eller sjekk stavemåten", "public.containers.SearchPage.popularSearches": "<PERSON><PERSON><PERSON><PERSON> søk", "public.containers.SearchPage.searchTime": "<PERSON><PERSON><PERSON><PERSON>", "public.components.SearchResultItem.chapter": "Kapittel", "public.components.SearchResultItem.section": "Seksjon", "public.components.SearchResultItem.content": "Innhold", "public.components.SearchResultItem.relevance": "<PERSON><PERSON><PERSON>", "public.components.SearchFilters.filtersTitle": "Filtrer resultater", "public.components.SearchFilters.totalResults": "Totalt antall resultater", "public.components.SearchFilters.contentType": "Innholdstype", "public.components.SearchFilters.allTypes": "Alle typer", "public.components.SearchFilters.chaptersOnly": "<PERSON><PERSON> kapitler", "public.components.SearchFilters.sectionsOnly": "<PERSON><PERSON> se<PERSON><PERSON>", "public.components.SearchFilters.sortBy": "Sorter etter", "public.components.SearchFilters.relevance": "<PERSON><PERSON><PERSON>", "public.components.SearchFilters.title": "<PERSON><PERSON><PERSON>", "public.components.SearchFilters.date": "Da<PERSON>", "public.components.SearchFilters.sortOrder": "Rekkefølge", "public.components.SearchFilters.descending": "Synkende", "public.components.SearchFilters.ascending": "Stigende", "public.components.SearchFilters.resetFilters": "Tilbakestill filtre", "public.components.SearchFilters.searchTips": "Søketips", "public.components.SearchFilters.tip1": "Bruk anførselstegn for eksakte fraser", "public.components.SearchFilters.tip2": "<PERSON><PERSON><PERSON><PERSON> <PERSON>er eller <PERSON> ord", "public.components.SearchFilters.tip3": "Kortere søkeord gir bredere resultater", "editor.containers.LinkPage.components.LinkForm.srTitleLabel": "Len<PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkForm.titleInputPlaceholder": "<PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkForm.srUrlLabel": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkForm.cancelButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkForm.addButtonText": "Legg til", "editor.containers.LinkPage.components.Link.edit": "<PERSON><PERSON>", "editor.containers.LinkPage.components.Link.delete": "<PERSON><PERSON>", "editor.containers.LinkPage.components.Link.deleteQuestion": "<PERSON><PERSON> du sikker på at du vil slette lenken \"{title}\"?", "editor.containers.LinkPage.components.LinkCollection.changeName": "<PERSON><PERSON> navn", "editor.containers.LinkPage.components.LinkCollection.saveSorting": "Lagre sortering", "editor.containers.LinkPage.components.LinkCollection.startSorting": "Start sortering", "editor.containers.LinkPage.components.LinkCollection.resetSorting": "Resett sortering", "editor.containers.LinkPage.components.LinkCollection.deleteLinkCollection": "<PERSON><PERSON> lenkes<PERSON>ling", "editor.containers.LinkPage.components.LinkCollection.noLinksInCollection": "Det er ingen lenker i lenkesamlingen", "editor.containers.LinkPage.components.LinkCollectionForm.srTitleLabel": "Len<PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkCollectionForm.srUrlLabel": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkCollectionForm.titleInputPlaceholder": "<PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkCollectionForm.cancelButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.LinkPage.components.LinkCollectionForm.addButtonText": "Legg til", "editor.containers.LinkPage.noLinkCollections": "Det finnes ingen lenkesamlinger. Opprett en ny i skjemaet over.", "generic.close": "Lukk", "generic.save": "Lagre", "generic.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generic.delete": "<PERSON><PERSON>", "editor.containers.EditSection.leaveEditorConfirmationCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.containers.EditSection.leaveEditorConfirmationLeave": "Fortsett uten å lagre", "editor.containers.EditSection.leaveEditorConfirmationModalTitle": "<PERSON><PERSON><PERSON> lagrede endringer", "editor.containers.EditSection.leaveEditorConfirmationMessage": "Du har ikke lagret endringene dine. Vil du fortsette, uten å lagre?", "editor.containers.EditSection.loading": "Laster...", "editor.containers.EditSection.loadError": "Kunne ikke laste avsnitt", "editor.containers.EditSection.titleRequired": "Tittel er påkrevd", "editor.containers.EditSection.parentRequired": "Foreldreelement er påkrevd", "editor.containers.EditSection.sectionUpdated": "Avsnitt oppdatert", "editor.containers.EditSection.sectionCreated": "Avsnitt opprettet", "editor.containers.EditSection.updateError": "Kunne ikke oppdatere avsnitt", "editor.containers.EditSection.createError": "Kunne ikke opprette avsnitt", "editor.containers.EditSection.sectionNotFound": "Avsnitt ikke funnet", "editor.containers.EditSection.editing": "<PERSON><PERSON><PERSON>", "editor.containers.EditSection.createNew": "Opprett nytt avsnitt", "editor.containers.EditSection.titlePlaceholder": "Skriv inn tittel...", "editor.containers.EditSection.updating": "Oppdaterer...", "editor.containers.EditSection.creating": "Oppretter...", "editor.containers.EditSection.updateButton": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.EditSection.createButton": "<PERSON><PERSON><PERSON><PERSON>", "editor.containers.EditSection.parentInfo": "Dette avsnittet vil bli opprettet under det valgte kapittelet", "editor.containers.EditSection.unsavedChanges": "<PERSON><PERSON><PERSON><PERSON>er", "editor.containers.EditSection.leaveConfirmation": "Du har ulagrede endringer. Vil du fortsette uten å lagre?", "editor.containers.EditSection.leaveWithoutSaving": "Fortsett uten å lagre", "editor.containers.EditSection.createSuccess": "Avsnitt opprettet", "editor.containers.EditSection.updateSuccess": "Avsnitt oppdatert", "editor.containers.App.sessionInactiveTitle": "Hmm, nå har du vært inaktiv lenge!", "editor.containers.App.sessionInactiveMessage": "Trykk på knappen under for å slippe å bli logget ut.", "editor.containers.App.sessionExpiredTitle": "<PERSON><PERSON> har du vært inaktiv litt for lenge", "editor.containers.App.sessionExpiredMessage": "Logg inn på nytt for å fortsette. Har du endringer som ikke er lagret? Lukk dette vinduet og kopier arbeidet ditt før du logger inn igjen.", "editor.containers.App.sessionTimeoutRemaining": "G<PERSON>nst<PERSON>ende tid", "editor.containers.App.minutes": "min", "editor.containers.App.seconds": "sek", "editor.containers.App.close": "Lukk", "editor.containers.App.logOut": "Logg ut", "editor.containers.App.keepSessionActive": "Fortsett å jobbe", "editor.containers.PendingPage.centralChange": "Sentral endring", "editor.components.MergeHandbookOrChapter.centralChange": "Sentral endring", "editor.containers.PendingPage.lastModified": "<PERSON>st endret", "editor.components.MergeHandbookOrChapter.lastModified": "<PERSON>st endret", "editor.components.MergeSection.lastModified": "<PERSON>st endret", "common.locale.select": "<PERSON><PERSON><PERSON>", "examples.title": "Oversettelseseksempler", "examples.basic.title": "Grunnleggende oversettelser", "examples.prefixed.title": "<PERSON><PERSON><PERSON><PERSON> oversette<PERSON>er", "examples.formatting.title": "Dato- og tidsformatering", "examples.numbers.title": "Tallformatering", "examples.relative.title": "Relativ tid", "examples.legacy.title": "Legacy komponent eks<PERSON>pel", "examples.legacy.description": "Denne komponenten bruker den gamle withTranslation HOC for bakoverkompatibilitet.", "examples.legacy.section": "Legacy kompatibilitetseksempler", "common.loading": "Laster...", "common.loadingWithEllipsis": "Laster vedlegg. Vennligst vent...", "common.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "common.save": "Lagre", "common.delete": "<PERSON><PERSON>", "common.edit": "<PERSON><PERSON>", "common.close": "Lukk", "editor.error.chapterLoadFailed": "Feil ved lasting av kapittel", "editor.error.sectionLoadFailed": "Feil ved lasting av avsnitt", "editor.error.attachmentLoadFailed": "Feil ved lasting av vedlegg", "editor.error.chapterNotFound": "Kapittel med ID {id} ble ikke funnet", "editor.error.sectionNotFound": "Avsnitt med ID {id} ble ikke funnet", "editor.error.chapterDeleteFailed": "Feil ved sletting av kapittel", "editor.error.sectionDeleteFailed": "Feil ved sletting av avsnitt", "editor.error.sortFailed": "Feil ved sortering av elementer", "editor.error.fileUploadFailed": "Feil ved opplasting av fil: {filename}", "editor.error.attachmentSaveFailed": "Feil ved lagring av vedlegg", "editor.error.fileDownloadFailed": "Feil ved nedlasting av fil", "editor.error.titleRequired": "Tittel er påkrevd", "editor.error.parentChapterRequired": "Overordnet kapittel er påkrevd", "editor.error.noOrganizationSelected": "Ingen organisasjon valgt. Kan ikke opprette håndbok.", "editor.error.handbookCreateFailed": "Feil ved opprettelse av håndbok", "editor.error.handbookUpdateFailed": "Feil ved oppdatering av håndbok", "editor.success.chapterDeleted": "Kapittel slettet", "editor.success.sectionDeleted": "Avs<PERSON>tt s<PERSON>t", "editor.success.itemsSorted": "<PERSON>ementer sortert", "editor.success.handbookCreated": "Håndbok opprettet", "editor.success.handbookUpdated": "Håndbok oppdatert", "editor.success.changesMerged": "<PERSON><PERSON><PERSON>", "editor.success.chapterMerged": "Kapittel sammenslått", "editor.success.sectionMerged": "Avsnitt sammenslått", "editor.labels.loadingEditor": "Laster editor...", "editor.labels.attachmentsLoading": "<PERSON><PERSON><PERSON><PERSON> lastes opp", "editor.status.titlePendingChanges": "Tittel har ventende endringer", "editor.status.textPendingChanges": "<PERSON><PERSON><PERSON> har ventende endringer", "editor.status.uploadFailed": "Opplasting mislyktes. Prøv å laste opp igjen.", "editor.containers.ChapterSelection.noChildren": "<PERSON>te kapittelet har ingen underkapitler eller avsnitt", "editor.merge.loadingMergeView": "<PERSON><PERSON>...", "editor.merge.handbookNotFound": "<PERSON><PERSON> ikke finne håndbok", "editor.merge.chapterNotFound": "Kunne ikke finne kapittel", "editor.merge.sectionNotFound": "Kunne ikke finne avsnitt", "editor.merge.selectLocal": "Velg lokal versjon", "editor.merge.selectCentral": "<PERSON>elg sentral versjon", "editor.merge.completeMerge": "<PERSON><PERSON><PERSON><PERSON>", "editor.merge.mergeTitle": "Sammenslå endringer", "editor.merge.localVersion": "Lokal versjon", "editor.merge.centralVersion": "Sentral versjon", "editor.merge.centralChangesAvailable": "Sentrale endringer er tilgjengelige", "editor.merge.centralDetailsPlaceholder": "Detaljer om sentrale endringer vises her", "editor.merge.centralContentPlaceholder": "<PERSON><PERSON>t innhold ville vises her ved fullstendig implementering", "editor.merge.errorMergingHandbook": "<PERSON>il ved sammensl<PERSON>ing av endringer", "editor.merge.errorMergingChapter": "Feil ved sammenslåing av kapittel", "editor.merge.errorMergingSection": "Feil ved sammenslåing av avsnitt", "editor.common.leaveEditorConfirmationTitle": "<PERSON><PERSON><PERSON> lagrede endringer", "editor.common.leaveEditorConfirmationMessage": "Du har ikke lagret endringene dine. Vil du fortsette uten å lagre?", "editor.common.leaveEditorConfirmationCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editor.common.leaveEditorConfirmationLeave": "Fortsett uten å lagre", "editor.containers.SearchPage.title": "Søk - KF Håndbøker", "session.warning.title": "Du blir snart logget ut", "session.warning.expiresIn": "<PERSON><PERSON><PERSON> din utløper om {time}", "session.warning.inactivityMessage": "Siden du har vært inaktiv en stund blir du snart logget ut.", "session.warning.keepSessionMessage": "Trykk på knappen under for å ikke bli logget ut.", "session.warning.keepActiveButton": "Forbli innlogget", "session.expired.title": "Du er logget ut", "session.expired.message": "<PERSON><PERSON> har du vært inaktiv litt for lenge og er derfor logget ut.", "session.expired.copyMessage": "Logg inn på nytt for å fortsette.\nHar du endringer som ikke er lagret? Lukk dette vinduet og kopier arbeidet ditt før du logger inn igjen.", "session.expired.closeButton": "Lukk"}