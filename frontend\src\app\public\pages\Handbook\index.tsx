import React, { useState, useCallback } from "react";
import { Routes, Route, useParams, Navigate } from "react-router-dom";
import { Section as BuiSection, Container, Columns, Column } from "kf-bui";
import Sticky from "react-stickynode";

import {
  useFetchPublicHandbookQuery,
  useFetchPublicConfigQuery,
} from "@/store/services/handbook/publicHandbookApi";
import { ChapterPage } from "./ChapterPage";
import { SearchPage } from "../Search";
import { Tree } from "../../components/Tree";
import { MobileTree } from "../../components/MobileTree";
import { ErrorBoundary } from "../../components/ErrorBoundary";
import { useHandbookNavigationLegacy } from "../../hooks/useHandbookNavigationLegacy";
import { Spinner } from "@/shared/components/Spinner";

interface HandbookState {
  showMobileTree: boolean;
}

export const HandbookPage: React.FC = () => {
  const { externalOrgId, handbookId } = useParams<{
    externalOrgId: string;
    handbookId: string;
  }>();

  const [state, setState] = useState<HandbookState>({
    showMobileTree: false,
  });

  useFetchPublicConfigQuery();

  const {
    data: handbookData,
    isLoading,
    error,
  } = useFetchPublicHandbookQuery(
    { externalOrgId: externalOrgId!, handbookId: handbookId! },
    { skip: !externalOrgId || !handbookId }
  );

  const {
    activeSections,
    expandedChapters,
    waypointsEnabled,
    handleChapterExpand,
    handleChapterNavigate,
    handleSectionNavigate,
    addSection,
    removeSection,
  } = useHandbookNavigationLegacy(
    handbookData?.chapters || [],
    handbookData?.sections || []
  );

  const firstChapterUrl = React.useMemo(() => {
    const chapters = handbookData?.chapters || [];
    if (!chapters.length) return null;

    const rootChapters = chapters
      .filter((chapter) => !chapter.parentId)
      .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

    if (rootChapters[0]) {
      return `/${externalOrgId}/${handbookId}/chapter/${rootChapters[0].id}`;
    }
    return null;
  }, [handbookData?.chapters, externalOrgId, handbookId]);

  const toggleMobileTree = useCallback(() => {
    setState((prevState) => ({
      ...prevState,
      showMobileTree: !prevState.showMobileTree,
    }));
  }, []);

  if (
    error &&
    "status" in error &&
    (error.status === 403 || error.status === 401)
  ) {
    return <Navigate to="/forbidden" replace />;
  }

  if (isLoading) {
    return (
      <BuiSection>
        <Container>
          <div style={{ textAlign: "center", padding: "2rem" }}>
            <Spinner text="Laster håndbok..." />
          </div>
        </Container>
      </BuiSection>
    );
  }

  if (error) {
    return (
      <BuiSection>
        <Container>
          <div style={{ textAlign: "center", padding: "2rem", color: "red" }}>
            Error loading handbook
          </div>
        </Container>
      </BuiSection>
    );
  }

  if (!handbookData?.handbook) {
    return (
      <BuiSection>
        <Container>
          <div style={{ textAlign: "center", padding: "2rem" }}>
            Handbook not found
          </div>
        </Container>
      </BuiSection>
    );
  }

  const { handbook, chapters, sections } = handbookData;

  if (typeof document !== "undefined") {
    document.title = `${handbook.title} - KF Håndbøker`;
  }

  return (
    <ErrorBoundary>
      <BuiSection>
        <Container>
          <Routes>
            <Route
              path="/search"
              element={
                <SearchPage
                  handbookId={handbookId!}
                  externalOrgId={externalOrgId!}
                />
              }
            />
            <Route
              path="/*"
              element={
                <Columns>
                  <MobileTree
                    toggleTree={toggleMobileTree}
                    showTree={state.showMobileTree}
                    activeSections={activeSections}
                    expandedChapters={expandedChapters}
                    onChapterExpand={handleChapterExpand}
                    onChapterNavigate={handleChapterNavigate}
                    onSectionNavigate={handleSectionNavigate}
                    handbook={handbook}
                    chapters={chapters}
                    sections={sections}
                    externalOrgId={externalOrgId!}
                    handbookId={handbookId!}
                  />

                  <Column size="1/3" hiddenMobile>
                    <Sticky
                      top={30}
                      enabled={true}
                      activeClass="sticky-active"
                      releasedClass="sticky-released"
                      innerClass="sticky-inner"
                      enableTransforms={true}
                      bottomBoundary={0}
                      innerZ={1000}
                    >
                      <div
                        id="scrollable-tree"
                        style={{
                          transition: "all 0.3s ease",
                          overflowY: "scroll",
                          maxHeight: "calc(100vh - 280px)",
                        }}
                      >
                        <Tree
                          activeSections={activeSections}
                          expandedChapters={expandedChapters}
                          onChapterExpand={handleChapterExpand}
                          onChapterNavigate={handleChapterNavigate}
                          onSectionNavigate={handleSectionNavigate}
                          handbook={handbook}
                          chapters={chapters}
                          sections={sections}
                          externalOrgId={externalOrgId!}
                          handbookId={handbookId!}
                        />
                      </div>
                    </Sticky>
                  </Column>

                  {!state.showMobileTree && (
                    <Column size="2/3">
                      <Routes>
                        <Route
                          path="chapter/:chapterId"
                          element={
                            <ChapterPage
                              chapters={chapters}
                              sections={sections}
                              onSectionEnter={addSection}
                              onSectionLeave={removeSection}
                              waypointsEnabled={waypointsEnabled}
                            />
                          }
                        />
                        {firstChapterUrl && (
                          <Route
                            path="*"
                            element={<Navigate to={firstChapterUrl} replace />}
                          />
                        )}
                      </Routes>
                    </Column>
                  )}
                </Columns>
              }
            />
          </Routes>
        </Container>
      </BuiSection>
    </ErrorBoundary>
  );
};
