import './Spinner.css';

interface SpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  textPosition?: 'below' | 'inline';
}

export const Spinner = ({ 
  size = 'medium', 
  text, 
  textPosition = 'below' 
}: SpinnerProps) => {
  const containerClass = `spinner-container spinner-container--${textPosition}`;
  const iconClass = `spinner-icon spinner-icon--${size}`;
  const textClass = `spinner-text spinner-text--${size}`;

  return (
    <div className={containerClass}>
      <div className={iconClass} />
      {text && <span className={textClass}>{text}</span>}
    </div>
  );
};
