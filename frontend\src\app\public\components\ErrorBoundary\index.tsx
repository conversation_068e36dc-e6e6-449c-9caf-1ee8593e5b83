import { Component, type ErrorInfo, type ReactNode } from "react";
import { Section, Container, Title, Subtitle } from "kf-bui";
import { reportComponentError } from "@/shared/utils/errorReporting";

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Public app error:", error, errorInfo);

    reportComponentError(error, errorInfo, {
      category: "ui",
      level: "medium",
      showToast: false,
      context: {
        component: "PublicLegacyErrorBoundary",
      },
    });
  }

  public render() {
    if (this.state.hasError) {
      return (
        <Section>
          <Container>
            <Title>Something went wrong</Title>
            <Subtitle>
              An error occurred while loading the handbook. Please try
              refreshing the page.
            </Subtitle>
            {process.env.NODE_ENV === "development" && this.state.error && (
              <div
                style={{
                  marginTop: "1rem",
                  padding: "1rem",
                  backgroundColor: "#f8f9fa",
                  border: "1px solid #dee2e6",
                }}
              >
                <strong>Error details:</strong>
                <pre style={{ fontSize: "0.875rem", marginTop: "0.5rem" }}>
                  {this.state.error.toString()}
                </pre>
              </div>
            )}
          </Container>
        </Section>
      );
    }

    return this.props.children;
  }
}
