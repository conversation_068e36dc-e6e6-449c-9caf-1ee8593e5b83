import { useState, useCallback, useMemo } from "react";
import {
  Modal,
  Title,
  Field,
  Label,
  Button,
  Group,
  Icon,
  Tree,
  Select,
} from "kf-bui";

import {
  useGetPublishedCentralHandbooksQuery,
  useGetPublishedCentralHandbookContentQuery,
  type PublishedCentralHandbook,
  type PublishedCentralChapter,
  type PublishedCentralSection,
} from "@/store/services/handbook/centralHandbookApi";

interface CentralTreeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onChapterSelect?: (chapter: PublishedCentralChapter) => void;
  onSectionSelect?: (section: PublishedCentralSection) => void;
  title?: string;
  allowSectionSelection?: boolean;
}

export const CentralTreeModal = ({
  isOpen,
  onClose,
  onChapterSelect,
  onSectionSelect,
  title = "Velg sentralt kapittel",
  allowSectionSelection = false,
}: CentralTreeModalProps) => {
  const [selectedHandbook, setSelectedHandbook] =
    useState<PublishedCentralHandbook | null>(null);

  const { data: publishedHandbooks = [], isLoading: isLoadingHandbooks } =
    useGetPublishedCentralHandbooksQuery();

  const { data: handbookContent, isLoading: isLoadingChapters } =
    useGetPublishedCentralHandbookContentQuery(selectedHandbook?.id || "", {
      skip: !selectedHandbook,
    });

  const chapters = useMemo(() => {
    return handbookContent?.chapters || [];
  }, [handbookContent]);

  const sections = useMemo(() => {
    return handbookContent?.sections || [];
  }, [handbookContent]);

  const rootChapters = useMemo(() => {
    return chapters.filter((chapter) => !chapter.parentId);
  }, [chapters]);

  const getChildrenForParent = useCallback(
    (parentId: string) => {
      const childChapters = chapters.filter(
        (chapter) => chapter.parentId === parentId
      );
      const childSections = sections.filter(
        (section) => section.parentId === parentId
      );

      const allChildren = [...childChapters, ...childSections];
      return allChildren.sort(
        (a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)
      );
    },
    [chapters, sections]
  );

  const handleChapterClick = useCallback(
    (chapter: PublishedCentralChapter) => {
      if (onChapterSelect) {
        onChapterSelect(chapter);
        onClose();
      }
    },
    [onChapterSelect, onClose]
  );

  const handleSectionClick = useCallback(
    (section: PublishedCentralSection) => {
      if (allowSectionSelection) {
        onSectionSelect?.(section);
        onClose();
      }
    },
    [onSectionSelect, onClose, allowSectionSelection]
  );

  const ChapterNode = useCallback(
    ({ chapter }: { chapter: PublishedCentralChapter }) => {
      const children = getChildrenForParent(chapter.id);

      const childItems = children.map((child) => {
        if ("parentId" in child && chapters.some((c) => c.id === child.id)) {
          const childChapter = child as PublishedCentralChapter;
          return <ChapterNode key={childChapter.id} chapter={childChapter} />;
        } else {
          const section = child as PublishedCentralSection;
          return (
            <Tree.Item
              key={section.id}
              id={section.id}
              onClick={() => handleSectionClick(section)}
              style={{
                cursor: allowSectionSelection ? "pointer" : "default",
                opacity: allowSectionSelection ? 1 : 0.7,
              }}
            >
              <Icon
                icon="RegFileLines"
                size="small"
                style={{ marginRight: "4px" }}
              />
              <span>{section.title}</span>
            </Tree.Item>
          );
        }
      });

      return (
        <Tree.Item
          key={chapter.id}
          id={chapter.id}
          items={childItems.length > 0 ? childItems : undefined}
          onClick={
            onChapterSelect ? () => handleChapterClick(chapter) : undefined
          }
          style={{ cursor: onChapterSelect ? "pointer" : "default" }}
        >
          <Icon
            icon="RegBookmark"
            size="small"
            style={{ marginRight: "4px" }}
          />
          <span>{chapter.title}</span>
        </Tree.Item>
      );
    },
    [
      chapters,
      getChildrenForParent,
      handleChapterClick,
      handleSectionClick,
      allowSectionSelection,
      onChapterSelect,
    ]
  );

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} autoFocus={false}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>
          <Title size="4">{title}</Title>
        </Modal.Title>
      </Modal.Header>

      <Modal.Body
        style={{ minHeight: "400px", maxHeight: "600px", overflow: "unset" }}
      >
        <Field>
          <Label>Sentral håndbok</Label>
          {isLoadingHandbooks ? (
            <div>Laster håndbøker...</div>
          ) : (
            <Select
              placeholder="Velg en sentral håndbok..."
              options={publishedHandbooks}
              value={selectedHandbook}
              onChange={(handbook: PublishedCentralHandbook | null) => {
                setSelectedHandbook(handbook);
              }}
              valueKey="id"
              labelKey="title"
              clearable
            />
          )}
        </Field>

        {selectedHandbook && (
          <div style={{ marginTop: "1rem" }}>
            {isLoadingChapters ? (
              <div style={{ textAlign: "center", padding: "2rem" }}>
                <Icon icon="spinner" spin />
                <span style={{ marginLeft: "0.5rem" }}>Laster kapitler...</span>
              </div>
            ) : rootChapters.length === 0 ? (
              <div
                style={{
                  textAlign: "center",
                  padding: "2rem",
                  color: "#666",
                }}
              >
                <Icon icon="info-circle" size="small" />
                <span style={{ marginLeft: "0.5rem" }}>
                  Ingen kapitler funnet i denne håndboka
                </span>
              </div>
            ) : (
              <div style={{ maxHeight: "350px", overflowY: "auto" }}>
                <Tree>
                  {rootChapters
                    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                    .map((chapter) => (
                      <ChapterNode key={chapter.id} chapter={chapter} />
                    ))}
                </Tree>
              </div>
            )}
          </div>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Group position="right">
          <Button onClick={onClose}>Avbryt</Button>
        </Group>
      </Modal.Footer>
    </Modal>
  );
};
