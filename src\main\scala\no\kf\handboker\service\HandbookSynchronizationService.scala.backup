package no.kf.handboker.service

import no.kf.db.TransactionManager
import no.kf.handboker.ProductionRegistry.componentRegistry.publicationRepository
import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import no.kf.handboker.model.local.{CentralChangeNotification, Chapter, Handbook, Section}
import no.kf.handboker.repository.{CentralHandbookRepositoryComponent, CentralNotificationRepositoryComponent, HandbookRepositoryComponent, LocalHandbookVersionRepositoryComponent, SubscriptionRepositoryComponent}
import no.kf.handboker.service.search.SearchServiceComponent
import org.joda.time.DateTime

import scala.annotation.tailrec
import scala.collection.immutable.HashMap
import scala.collection.mutable.ListBuffer

trait HandbookSynchronizationServiceComponent extends TransactionManager {
  this: HandbookRepositoryComponent
    with CentralNotificationRepositoryComponent
    with CentralHandbookRepositoryComponent
    with LocalHandbookVersionRepositoryComponent
    with CentralHandbookServiceComponent
    with SearchServiceComponent
    with SubscriptionRepositoryComponent
    with OrganizationDataServiceComponent =>

  val handbookSynchronizationService: HandbookSynchronizationService

  class HandbookSynchronizationServiceImpl extends HandbookSynchronizationService {

    private def persistCentralHandbookVersion(handbook: CentralHandbook, user: String): (CentralHandbook, List[CentralChapter], List[CentralSection]) = inTransaction {

      def persistCentralChildChapters(parents: Set[CentralChapter], centralChapters: Set[CentralChapter], handbookId: String, user: String): (List[CentralChapter], Map[String, String]) = {

        @tailrec
        def persistCentralChapters(parents: Set[CentralChapter],
                                   originalChapters: Map[Option[String], Set[CentralChapter]],
                                   persistedChapters: List[CentralChapter] = Nil,
                                   chapterMappings: Map[String, String] = HashMap.empty): (List[CentralChapter], Map[String, String]) = {

          val newChapterMappings = scala.collection.mutable.HashMap.empty[String, String]
          val persistedParentChapters = parents.map(chapter => {
            val newParentId = if (chapter.parentId.isDefined) chapterMappings.get(chapter.parentId.get) else None
            val persistedChapter = centralHandbookRepository.insertCentralChapterCopy(chapter.copy(id = None, centralHandbookId = handbookId, parentId = newParentId, versionOf = chapter.id, sortOrder = chapter.sortOrder), currentUser = user)
            newChapterMappings += (chapter.id.get -> persistedChapter.id.get)
            persistedChapter
          })

          val childChapters = parents.flatMap { parent =>
            originalChapters.getOrElse(parent.id, Nil).map { child => child }
          }

          if (childChapters.isEmpty) {
            return (persistedChapters ++ persistedParentChapters, (chapterMappings ++ newChapterMappings).map(m => m))
          }

          val rest = originalChapters -- persistedParentChapters.map(_.id)
          persistCentralChapters(childChapters, rest, persistedChapters ++ persistedParentChapters, (chapterMappings ++ newChapterMappings).map(m => m))
        }

        persistCentralChapters(parents, centralChapters.groupBy(_.parentId))
      }

      def persistCentralChildSections(centralSections: List[CentralSection], chapterMapping: Map[String, String], handbook: CentralHandbook, user: String): List[CentralSection] = {
        val persistedSections = scala.collection.mutable.ListBuffer[CentralSection]()
        centralSections.foreach(section => {
          persistedSections += centralHandbookRepository.insertCentralSectionCopy(section.copy(id = None, centralHandbookId = handbook.id.get, parentId = chapterMapping(section.parentId), versionOf = section.id, sortOrder = section.sortOrder), currentUser = user)
        })
        persistedSections.toList
      }

      def retrieveCentralChaptersAndSectionsWithText(handbookId: String): (List[CentralChapter], List[CentralSection]) = inTransaction {
        val chapters = centralHandbookRepository.retrieveChaptersByHandbookId(handbookId).map(c => CentralChapter(c.id, c.title, c.parentId, c.centralHandbookId, c.versionOf, c.createdDate, c.updatedDate, c.updatedDateBeforePublish, c.createdBy, c.updatedBy, c.sortOrder))
        val sections = centralHandbookRepository.retrieveSectionsByHandbookId(handbookId).map(
          s =CentralSection(s.id, s.title, s.parentId, s.centralHandbookId, s.html, s.versionOf, s.createdDate, s.registeredDate, s.updatedDate, s.titleUpdatedDate, s.htmlUpdatedDate, s.createdBy, s.updatedBy, s.titleUpdatedBy, s.htmlUpdatedBy, s.sortOrder))
        (chapters, sections)
      }

      val newHandbookVersion = centralHandbookService.persistCentralHandbook(handbook.copy(id = None, versionOf = handbook.id), user)
      val bookContent = retrieveCentralChaptersAndSectionsWithText(handbook.id.get)
      val centralChapters = bookContent._1.toSet
      val centralSections = bookContent._2.sortBy(_.sortOrder)
      val centralRootChapters = centralChapters.filter(_.parentId.isEmpty)
      val (persistedChildChapters, chapterMap) = persistCentralChildChapters(centralRootChapters, centralChapters -- centralRootChapters, newHandbookVersion.id.get, user)
      val persistedChildSections = persistCentralChildSections(centralSections, chapterMap, newHandbookVersion, user)
      (newHandbookVersion, persistedChildChapters, persistedChildSections)
    }


    private def synchronizeLocalHandbook(localHandbookId: String, centralhandbookId: String): Unit = {

      val (latestCentralHandbookVersion, previousHandbookVersion, previousVersionChaptersMap, previousVersionSectionsMap, centralChapters, centralSections) = inTransaction {
        println("inside transaction")
        getCentralHandbookInfo(centralhandbookId)
      }
      println("After transaction")
      val centralChapterIdMap = centralChapters.map(c => (c.id.get, c)).toMap
      val centralChapterVersionOfMap = centralChapters.map(c => (c.versionOf.get, c)).toMap
      val centralSectionVersionOfMap = centralSections.map(c => (c.versionOf.get, c)).toMap

      val (localHandbook, allLocalChapters, allLocalSections, deletedLocalChapters, deletedLocalSections) = inTransaction {
        getLocalHandbookInfo(localHandbookId)
      }
      val pureLocalChapters = allLocalChapters.filter(c => c.importedHandbookId.isEmpty)
      val importedLocalChapters = allLocalChapters.filter(c => c.importedHandbookId.isDefined)
      val pureLocalSections = allLocalSections.filter(s => s.importedHandbookId.isEmpty)
      val importedLocalSections = allLocalSections.filterNot(s => s.importedHandbookSectionId.isEmpty)
      val localChapterIdMap = importedLocalChapters.map(c => (c.id.get, c)).toMap
      val localChapterImportedIdMap = importedLocalChapters.groupBy(_.importedHandbookChapterId.get)
      val localSectionImportedIdMap = importedLocalSections.map(c => (c.importedHandbookSectionId.get, c)).toMap
      val localChapterParentMap = importedLocalChapters.filter(c => c.parentId.isDefined).groupBy(_.parentId.get)
      val localSectionParentMap = importedLocalSections.groupBy(_.parentId)

      val existingNotifications = inTransaction {
        centralNotificationRepository.retrieveNotifications(localHandbook.id.get)
      }
      val chapterNotifications = existingNotifications.filter(cn => cn.chapterId.isDefined).map(cn => (cn.chapterId.get, cn)).toMap
      val sectionNotifications = existingNotifications.filter(cn => cn.sectionId.isDefined).map(cn => (cn.sectionId.get, cn)).toMap

      handleChangeConflicts()
      handleAdditions()
      handleDeletions()

      def handleDeletions() {
        
        
        
        
        // Chapter deletions
        importedLocalChapters.filter(chapter => chapter.importedHandbookChapterId.isDefined).foreach(chapter => inTransaction {
          val centralChapterDoesNotExist = !centralChapterVersionOfMap.contains(chapter.importedHandbookChapterId.get)
          val chapterExistsInPreviousVersion = if (previousVersionChaptersMap.contains(chapter.importedHandbookChapterId.get)) true else false
          if (centralChapterDoesNotExist && chapterExistsInPreviousVersion) {
            val changeDesc = s"Kapittel ${chapter.title} er blitt slettet i sentral håndbok ${localHandbook.title}"
            if (localChange(chapter, localChapterParentMap, localSectionParentMap, pureLocalChapters, pureLocalSections)) {
              handbookRepository.persistChapter(chapter.copy(pendingDeletion = true, pendingChangeUpdatedDate = Some(DateTime.now)))
              if (chapterNotifications.contains(chapter.id.get)) {
                centralNotificationRepository.persistCentralChangeNotification(chapterNotifications(chapter.id.get).copy(changeDescription = changeDesc, changedDate = DateTime.now, deletion = true))
              } else {
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDesc, localHandbook.id.get, DateTime.now, chapter.id, None, None, deletion = true))
              }
            } else {
              localHandbookVersionRepository.insertLocalHandbookChapterVersion(chapter, DateTime.now)
              handbookRepository.deleteChapter(chapter.id.get)
              if (chapterNotifications.contains(chapter.id.get)) {
                centralNotificationRepository.persistCentralChangeNotification(chapterNotifications(chapter.id.get).copy(changeDescription = changeDesc, changedDate = DateTime.now, deletion = true))
              } else {
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDesc, localHandbook.id.get, DateTime.now, chapter.id, None, None, deletion = true))
              }
            }
          }
        })

        // Section deletions
        importedLocalSections.filter(section => section.importedHandbookSectionId.isDefined).foreach(section => inTransaction {
          val centralSectionDoesNotExist = !centralSectionVersionOfMap.contains(section.importedHandbookSectionId.get)
          val sectionExistsInPreviousVersion = if (previousVersionSectionsMap.contains(section.importedHandbookSectionId.get)) true else false
          if (centralSectionDoesNotExist && sectionExistsInPreviousVersion) {
            val parentTitle = if (localChapterIdMap.contains(section.parentId))
              localChapterIdMap(section.parentId)
            else if (pureLocalChapters.exists(c => c.id.get == section.parentId))
              pureLocalChapters.find(c => c.id.get == section.parentId)
            else ""

            val changeDesc = s"Avsnitt ${section.title} i kapittel ${parentTitle} er blitt slettet"
            if (localChange(section)) {
              handbookRepository.persistSection(section.copy(pendingDeletion = true, pendingChangeUpdatedDate = Some(DateTime.now)))
              if (sectionNotifications.contains(section.id.get)) {
                centralNotificationRepository.persistCentralChangeNotification(sectionNotifications(section.id.get).copy(changeDescription = changeDesc, changedDate = DateTime.now, deletion = true))
              } else {
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDesc, localHandbook.id.get, DateTime.now, None, section.id, None, deletion = true))
              }
            } else {
              localHandbookVersionRepository.insertLocalHandbookSectionVersion(section, DateTime.now)
              handbookRepository.deleteSection(section.id.get)
              if (sectionNotifications.contains(section.id.get)) {
                centralNotificationRepository.persistCentralChangeNotification(sectionNotifications(section.id.get).copy(changeDescription = changeDesc, changedDate = DateTime.now, deletion = true))
              } else {
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDesc, localHandbook.id.get, DateTime.now, None, section.id, None, deletion = true))
              }
            }
          }
        }
        )
      }

      def handleAdditions() {
        val mutableLocalChapterImportedIdMap = collection.mutable.Map(localChapterImportedIdMap.toSeq: _*)
        val mutableLocalSectionList = ListBuffer(allLocalSections: _*)
        var newChapterIds = ListBuffer[String]()

        // Parent before child
        def insertableOrder(c1: CentralChapter, c2: CentralChapter): Boolean = {
          if (c1.parentId.isEmpty && c2.parentId.isEmpty) true
          else if  (c1.parentId.isEmpty && c2.parentId.isDefined) true
          else if  (c1.parentId.isDefined && c2.parentId.isEmpty) false
          else (c1.id.get == c2.parentId.get)
        }
        val chaptersInInsertOrder = centralChapters.sortBy(_.sortOrder).sortWith((c1, c2) => insertableOrder(c1, c2))

        def computeSortOrder(localParentChapter: Option[Chapter]) = {
          val chapterOrderList = mutableLocalChapterImportedIdMap.toList.flatMap(e => e._2)
            .filter(c => c.parentId == (if (localParentChapter.isDefined) localParentChapter.get.id else None))
            .map(chapter => chapter.sortOrder.getOrElse(0))
          val sectionOrderList = mutableLocalSectionList
            .filter(s => s.parentId == (if (localParentChapter.isDefined) localParentChapter.get.id.get else None))
            .map(s => s.sortOrder.getOrElse(0))
          val chapterMax = if (chapterOrderList.isEmpty) 0 else chapterOrderList.max
          val sectionMax = if (sectionOrderList.isEmpty) 0 else sectionOrderList.max
          if (chapterOrderList.isEmpty && sectionOrderList.isEmpty) 0 else chapterMax.max(sectionMax) + 1
        }

        def findLocalParentChapter(centralChapter: CentralChapter): Option[List[Chapter]] = {
          val versionParent = centralChapters.find(cc => cc.id == centralChapter.parentId)
          if (mutableLocalChapterImportedIdMap.contains(versionParent.get.versionOf.get)) {
            Some(mutableLocalChapterImportedIdMap(versionParent.get.versionOf.get))
          } else {
            None
          }
        }

        def parentChapterIsNew(localParentChapter: Option[Chapter]) = {
          newChapterIds.contains(localParentChapter.get.id.get)
        }

        def addChapter(centralChapter: CentralChapter): Unit = {

          val (localParentChapter, storeChapter) = if (centralChapter.parentId.isDefined) {
            val localParent = findLocalParentChapter(centralChapter)
            if (localParent.isDefined) {
              (localParent, true)
            } else {
              log.info(s"Ignorerte opprettelse av sentralt kapittel ${centralChapter.title} i lokal håndbok ${localHandbook.title}. Forelder-kapittel er slettet.")
              (None, false)
            }
          } else {
            (None, true)
          }

          if (storeChapter) {
            val maintainSortOrder = if (localParentChapter.isDefined) parentChapterIsNew(localParentChapter.getOrElse(List()).headOption) else false
            val sortOrder = if (maintainSortOrder) centralChapter.sortOrder else {
              computeSortOrder(localParentChapter.getOrElse(List()).headOption)
            }
            if (localParentChapter.isDefined) {
              localParentChapter.get.foreach(localParentChapter => {
                val stored = handbookRepository.persistChapter(
                  Chapter(None, centralChapter.title, centralChapter.versionOf, Some(centralhandbookId), localHandbookId, localParentChapter.id, Some(sortOrder), createdBy = Some("KF"), updatedBy = Some("KF")),
                  computeSortOrder = false)
                val localChapters = mutableLocalChapterImportedIdMap.get(centralChapter.versionOf.get)
                if (localChapters.nonEmpty)
                  mutableLocalChapterImportedIdMap.update(centralChapter.versionOf.get, stored :: localChapters.get)
                else
                  mutableLocalChapterImportedIdMap += (centralChapter.versionOf.get -> List(stored))
                newChapterIds += stored.id.get
                val changeDescription = s"Nytt kapittel ${stored.title} ble opprettet i kapittel ${localParentChapter.title}"
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDescription, localHandbook.id.get, DateTime.now, stored.id, None, Some(stored.title)))
              })
            } else {
              val stored = handbookRepository.persistChapter(
                Chapter(None, centralChapter.title, centralChapter.versionOf, Some(centralhandbookId), localHandbookId, None, Some(sortOrder), createdBy = Some("KF"), updatedBy = Some("KF")),
                computeSortOrder = false)
              val localChapters = mutableLocalChapterImportedIdMap.get(centralChapter.versionOf.get)
              if (localChapters.nonEmpty)
                mutableLocalChapterImportedIdMap.update(centralChapter.versionOf.get, stored :: localChapters.get)
              else
                mutableLocalChapterImportedIdMap += (centralChapter.versionOf.get -> List(stored))
              newChapterIds += stored.id.get
              val changeDescription = s"Nytt kapittel ${stored.title} ble opprettet i håndbok ${localHandbook.title}"
              centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDescription, localHandbook.id.get, DateTime.now, stored.id, None, Some(stored.title)))
            }
          }
        }

        // Only require imported handbook if chapter/section is empty
        def parentExistsOrImportedHandbook(centralParentId: Option[String]): Boolean = {
          if (centralParentId.isEmpty) {
            localHandbook.importedHandbookId.isDefined && localHandbook.importedHandbookId.get == centralhandbookId
          } else {
            val centralParent = centralChapterIdMap(centralParentId.get)
            mutableLocalChapterImportedIdMap.contains(centralParent.versionOf.get)
          }
        }

        // Handle new chapters
        chaptersInInsertOrder.foreach(centralChapter => {
          val localChapterDoesNotExist = !mutableLocalChapterImportedIdMap.contains(centralChapter.versionOf.get)
          val chapterIsDeleted = deletedLocalChapters.exists(c => c.importedHandbookChapterId == centralChapter.versionOf)
          if (parentExistsOrImportedHandbook(centralChapter.parentId) && localChapterDoesNotExist && !chapterIsDeleted) inTransaction {
            addChapter(centralChapter)
          }
        })

        // Handle new sections
        centralSections.sortBy(_.sortOrder).foreach(centralSection => {

          val localSectionDoesNotExist = !localSectionImportedIdMap.contains(centralSection.versionOf.get)
          val sectionIsDeleted = deletedLocalSections.exists(c => c.importedHandbookSectionId == centralSection.versionOf)
          if (parentExistsOrImportedHandbook(Some(centralSection.parentId)) && localSectionDoesNotExist && !sectionIsDeleted) {
            val versionedCentralChapter = centralChapterIdMap(centralSection.parentId)
            val localParentChapter = if (mutableLocalChapterImportedIdMap.contains(versionedCentralChapter.versionOf.get)) Option(mutableLocalChapterImportedIdMap(versionedCentralChapter.versionOf.get)) else None
            if (localParentChapter.isDefined) inTransaction {
              val maintainSortOrder = newChapterIds.contains(localParentChapter.get.head.id.get)
              val sortOrder = if (maintainSortOrder) centralSection.sortOrder else {
                computeSortOrder(localParentChapter.getOrElse(List()).headOption)
              }

              localParentChapter.get.foreach(localParentChapter => {
                val stored = handbookRepository.persistSection(
                  Section(None, centralSection.title, centralSection.html, centralSection.versionOf, Some(centralhandbookId), localHandbookId, localParentChapter.id.get, Some(sortOrder), createdDate = Some(DateTime.now), createdBy = Some("KF"), updatedBy = Some("KF"), textUpdatedBy = Some("KF")),
                  computeSortOrder = false)
                mutableLocalSectionList += stored
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, s"Nytt avsnitt ${centralSection.title} ble opprettet i kapittel ${stored.title}", localHandbook.id.get, DateTime.now, None, stored.id, Some(stored.title)))
              })
            } else {
                log.info(s"Ignorerte opprettelse av sentralt avsnitt ${centralSection.title} i lokal håndbok ${localHandbook.title}. Forelder-kapittel er slettet.")
            }

          }
        })
      }

      def handleChangeConflicts() {

        // Handbook title
        if (localHandbook.importedHandbookId.isDefined && localHandbook.importedHandbookId.get == centralhandbookId) {
          val latestTitle = latestCentralHandbookVersion.get.title
          val centralHandbookTitleIsChanged = previousHandbookVersion.isEmpty || previousHandbookVersion.get.title != latestTitle
          if (centralHandbookTitleIsChanged && latestTitle != localHandbook.title) inTransaction {
            val handbookNotification = existingNotifications.find(n => n.handbookId == localHandbook.id.get && n.chapterId.isEmpty && n.sectionId.isEmpty)
            val changeDescription = s"Håndboken $localHandbook.title endret navn til $latestTitle"
            if (localHandbook.localChange) {
              handbookRepository.persistHandbook(localHandbook.copy(pendingChange = true, pendingChangeUpdatedDate = Some(DateTime.now)))
              if (handbookNotification.isDefined) {
                centralNotificationRepository.persistCentralChangeNotification(handbookNotification.get.copy(changeHTML = Some(latestTitle), changeDescription = changeDescription))
              } else {
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDescription, localHandbook.id.get, DateTime.now, changeHTML = Some(latestTitle)))
              }
            } else {
              handbookRepository.persistHandbook(localHandbook.copy(title = latestTitle, pendingChange = false))
              centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, changeDescription, localHandbook.id.get, DateTime.now, changeHTML = Some(latestTitle)))
            }
          }
        }

        // Chapter.title
        importedLocalChapters.filter(c => centralChapterVersionOfMap.contains(c.importedHandbookChapterId.get)).foreach(chapter => inTransaction {
          val latestTitle = centralChapterVersionOfMap(chapter.importedHandbookChapterId.get).title
          val previousTitle = if (previousVersionChaptersMap.contains(chapter.importedHandbookChapterId.get)) Some(previousVersionChaptersMap(chapter.importedHandbookChapterId.get).title) else None
          val centralChapterTitleChanged = previousTitle.isEmpty || previousTitle.get != latestTitle
          if (centralChapterTitleChanged) {
            if (chapter.title != latestTitle) {
              val titleChangeDesc = s"Tittel for kapittel ${chapter.title} er endret til $latestTitle"
              if (chapter.localChange) {
                handbookRepository.persistChapter(chapter.copy(pendingChange = true, pendingDeletion = false, pendingChangeUpdatedDate = Some(DateTime.now)))
                if (chapterNotifications.contains(chapter.id.get)) {
                  centralNotificationRepository.persistCentralChangeNotification(chapterNotifications(chapter.id.get).copy(changeHTML = Some(latestTitle), changeDescription = titleChangeDesc))
                } else {
                  centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, titleChangeDesc, localHandbook.id.get, DateTime.now, chapter.id, None, Some(latestTitle)))
                }
              } else {
                localHandbookVersionRepository.insertLocalHandbookChapterVersion(chapter, DateTime.now)
                handbookRepository.persistChapter(chapter.copy(title = latestTitle, updatedBy = Some("KF")))
                centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, titleChangeDesc, localHandbook.id.get, DateTime.now, chapter.id, None, Some(latestTitle)))
              }
            }
          }
        })

        importedLocalSections.foreach(section => inTransaction {

          var sectionChanged = false

          // Section.title
          if (centralSectionVersionOfMap.contains(section.importedHandbookSectionId.get)) {
            val latestTitle = centralSectionVersionOfMap(section.importedHandbookSectionId.get).title
            val previousTitle = if (previousVersionSectionsMap.contains(section.importedHandbookSectionId.get)) Some(previousVersionSectionsMap(section.importedHandbookSectionId.get).title) else None
            val centralSectionTitleChanged = previousTitle.isEmpty || previousTitle.get != latestTitle
            if (centralSectionTitleChanged) {
              if (section.title != latestTitle) {
                val titleChangeDesc = s"Tittel for avsnitt ${section.title} er endret til $latestTitle"
                if (section.localTitleChange) {
                  handbookRepository.persistSection(section.copy(pendingTitleChange = true, pendingChangeUpdatedDate = Some(DateTime.now)))
                  if (sectionNotifications.contains(section.id.get)) {
                    centralNotificationRepository.persistCentralChangeNotification(sectionNotifications(section.id.get).copy(changeHTML = Some(latestTitle), changeDescription = titleChangeDesc))
                  } else {
                    centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, titleChangeDesc, localHandbook.id.get, DateTime.now, None, section.id, Some(latestTitle)))
                  }
                } else {
                  sectionChanged = true
                  handbookRepository.persistSection(section.copy(title = latestTitle, updatedBy = Some("KF"), updatedDate = Some(DateTime.now)))
                  centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, titleChangeDesc, localHandbook.id.get, DateTime.now, None, section.id, Some(latestTitle)))
                }
              }
            }

            // Section.text
            val latestText = centralSectionVersionOfMap(section.importedHandbookSectionId.get).html
            val previousText = if (previousVersionSectionsMap.contains(section.importedHandbookSectionId.get)) previousVersionSectionsMap(section.importedHandbookSectionId.get).html else None
            // KNOWIT-30 - Spesifisert at konverteringen av lenkene må IKKE generere endringsvarsler for kunde
            val sectionWithReplacedLinks = organizationDataService.replaceLinksInSection(previousText)
            val centralSectionTextChanged = previousText.isEmpty || (latestText != sectionWithReplacedLinks && previousText != latestText)
            if (centralSectionTextChanged) {
              if (section.text != latestText) {
                val textChangeDesc = s"Tekst for avsnitt ${section.text} er endret til $latestText"
                if (section.localTextChange) {
                  // Retrieve before update since we may also have had a change of title
                  handbookRepository.persistSection(handbookRepository.retrieveSection(section.id.get).get.copy(pendingTextChange = true, pendingChangeUpdatedDate = Some(DateTime.now)))
                  if (sectionNotifications.contains(section.id.get)) {
                    centralNotificationRepository.persistCentralChangeNotification(sectionNotifications(section.id.get).copy(changeHTML = latestText, changeDescription = textChangeDesc))
                  } else {
                    centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, textChangeDesc, localHandbook.id.get, DateTime.now, None, section.id, latestText))
                  }
                } else {
                  sectionChanged = true
                  handbookRepository.persistSection(handbookRepository.retrieveSection(section.id.get).get.copy(text = latestText, textUpdatedBy = Some("KF"), textUpdatedDate = Some(DateTime.now)))
                  centralNotificationRepository.persistCentralChangeNotification(CentralChangeNotification(None, textChangeDesc, localHandbook.id.get, DateTime.now, None, section.id, latestText))
                }
              }
            }
          }

          if (sectionChanged) localHandbookVersionRepository.insertLocalHandbookSectionVersion(section, DateTime.now)

        })
      }
    }

    private def getCentralHandbookInfo(centralhandbookId: String) = {
      val latestCentralHandbookVersion = centralHandbookRepository.retrieveLastestVersion(centralhandbookId)
      println("Version: " + latestCentralHandbookVersion)
      if (latestCentralHandbookVersion.isEmpty){
        println("Dint find last handbook")
        throw new RuntimeException(s"Central handbook with id $centralhandbookId is not published")
      }
      val previousHandbookVersion = centralHandbookRepository.retrieveSecondLastestVersion(centralhandbookId)
      val centralChapters = centralHandbookRepository.retrieveChaptersByHandbookId(latestCentralHandbookVersion.get.id.get)
      val centralSections = centralHandbookRepository.retrieveSectionsByHandbookId(latestCentralHandbookVersion.get.id.get)
      val (previousChaptersMap, previousSectionsMap) = if (previousHandbookVersion.isEmpty) {
        (Map.empty[String, CentralChapter], Map.empty[String, CentralSection])
      } else {
        (centralHandbookRepository.retrieveChaptersByHandbookId(previousHandbookVersion.get.id.get).map(c => (c.versionOf.get, c)).toMap,
          centralHandbookRepository.retrieveSectionsByHandbookId(previousHandbookVersion.get.id.get).map(c => (c.versionOf.get, c)).toMap)
      }
      (latestCentralHandbookVersion, previousHandbookVersion, previousChaptersMap, previousSectionsMap, centralChapters, centralSections)
    }

    private def getLocalHandbookInfo(localHandbookId: String) = {
      val localHandbook = handbookRepository.retrieveHandbook(localHandbookId).get
      val chapters = handbookRepository.retrieveDeletedAndNotDeletedChaptersForHandbook(localHandbookId)
      val sections = handbookRepository.retrieveDeletedAndNotDeletedSectionsForHandbook(localHandbookId)
      val allLocalChapters = chapters.filter(c => !c.isDeleted.getOrElse(false))
      val allLocalSections = sections.filter(s => !s.isDeleted.getOrElse(false))
      val deletedLocalChapters = chapters.filter(c => c.isDeleted.getOrElse(false))
      val deletedLocalSections = sections.filter(s => s.isDeleted.getOrElse(false))
      (localHandbook, allLocalChapters, allLocalSections, deletedLocalChapters, deletedLocalSections)
    }

    private def localChange(chapter: Chapter,
                            localChapterParentMap: Map[String, List[Chapter]],
                            localSectionParentMap: Map[String, List[Section]],
                            unimportedChapters: List[Chapter],
                            unimportedSections: List[Section]): Boolean = {

      def changed(chapter: Chapter): Boolean = {
        chapterIsChanged(chapter) ||
          chapterContainsChangedChapter(localChapterParentMap, chapter) ||
          chapterContainsPureLocalChapter(chapter, unimportedChapters) ||
          chapterContainsChangedSection(localSectionParentMap, chapter) ||
          chapterContainsPureLocalSection(chapter, unimportedSections) ||
          subchapterIsChanged(localChapterParentMap, localSectionParentMap, changed, chapter)
      }

      changed(chapter)
    }

    private def chapterIsChanged(chapter: Chapter) = {
      chapter.localChange
    }

    private def subchapterIsChanged(localChapterParentMap: Map[String, List[Chapter]], localSectionParentMap: Map[String, List[Section]], changed: Chapter => Boolean, chapter: Chapter) = {
      (localSectionParentMap.contains(chapter.id.get) && localChapterParentMap.contains(chapter.id.get) && localChapterParentMap(chapter.id.get).exists(ch => changed(ch)))
    }

    private def chapterContainsPureLocalSection(chapter: Chapter, unimportedSections: List[Section]) = {
      unimportedSections.exists(us => us.parentId == chapter.id.get)
    }

    private def chapterContainsChangedSection(localSectionParentMap: Map[String, List[Section]], chapter: Chapter) = {
      (localSectionParentMap.contains(chapter.id.get) && localSectionParentMap(chapter.id.get).exists(sec => sec.localTitleChange || sec.importedHandbookSectionId.isEmpty))
    }

    private def chapterContainsPureLocalChapter(chapter: Chapter, unimportedChapters: List[Chapter]) = {
      unimportedChapters.exists(uc => uc.parentId == chapter.id)
    }

    private def chapterContainsChangedChapter(localChapterParentMap: Map[String, List[Chapter]], chapter: Chapter) = {
      (localChapterParentMap.contains(chapter.id.get) && localChapterParentMap(chapter.id.get).exists(ch => ch.localChange || ch.importedHandbookChapterId.isEmpty))
    }

    private def localChange(section: Section) = section.localTitleChange || section.localTextChange

    override def synchronizeHandbooks(): Unit = {
      val candidates = inTransaction {
        publicationRepository.getPublicationCandidates
      }
      val extOrgsToBeReindexed = candidates.flatMap(publication => {
        log.info(s"Publiserer håndbok ${publication.handbookId}")

        val centralHandbook = inTransaction {
          centralHandbookRepository.retrieveCentralHandbook(publication.handbookId).get
        }

        val version = inTransaction {
          persistCentralHandbookVersion(centralHandbook, publication.createdBy)
        }
        log.info("Opprettet håndbok-versjon " + version._1.id.get)

        val localHandbooks = inTransaction {
          handbookRepository.retrieveHandbooksBasedOnCentralHandbookId(publication.handbookId)
        }

        localHandbooks.foreach(handbook => {
          log.info(s"Synkroniserer lokal håndbok ${handbook.title} (${handbook.id})")
          synchronizeLocalHandbook(handbook.id.get, publication.handbookId)
          log.info(s"Synkronisering av ${handbook.title} (${handbook.id}) ferdig")
        })

        inTransaction {
          publicationRepository.persistPublication(publication.copy(published = true))
          subscriptionRepository.updateChangeNotificationAsCentralPublished(publication.handbookId)
        }
        log.info(s"Publisering av håndbok ${publication.handbookId} ferdig")
        localHandbooks.map(_.externalOrgId)
      }).distinct

      extOrgsToBeReindexed.foreach(searchService.doReindex)
    }
  }
}

trait HandbookSynchronizationService {
  def synchronizeHandbooks()
}
