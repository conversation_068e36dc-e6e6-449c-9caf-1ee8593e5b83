package no.kf.handboker.mail

import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.SiteUrl
import no.kf.handboker.model.local.ChangeNotification
import no.kf.mail.MailMessage
import org.htmlcleaner.HtmlCleaner
import org.joda.time.DateTime

object MailBuilder {

  val templateDir = "htmlTemplates"
  val dateFormat = "dd.MM.yyyy"
  val sc="ø"
  val title="K<PERSON> H<PERSON>nd<PERSON>øker"
  lazy val handbokerUrl = ProductionRegistry.componentRegistry.settings.settingFor(SiteUrl)

  def createChangeNotificationMessagePerUser(emailAddress: String, notes: List[ChangeNotification]): MailMessage = {
    val subject = "Endringer i KF Håndbøker"

    val changesByHandbook = notes.groupBy(_.handbook).map(t => generateMessageForOneHandbook(t._2)).toList
    val plainText = generateHtml(changesByHandbook)
    MailMessage(subject, "", emailAddress, Some(plainText))
  }

  private def generateMessageForOneHandbook(notes: List[ChangeNotification]): String = {
    val handbook = notes.head.handbook
    val centralHandbook = notes.head.centralHandbook
    //, endret: ${n.changedDate.toString(dateFormat)}
    val changes = notes.map(n => s"""<p style="font-size:11pt;margin:0;"><span style="font-size:10pt;">&nbsp;</span></p> <p style="font-size:11pt;margin:0;"><span style="font-size:10pt;">${n.changeDescription}</span></p>""").mkString("")
    //s"<h3 style='background: #ededed !important;padding: 10px;'><span>Der gjort endringer i ${centralHandbook.title}.</span></h3>$changes<br/>" //Til Håndbok: ${generateLink(handbook.id.get)}"
    s"""
       |<table style="width:100%;" cellpadding="0" border="0">
       |<tbody><tr>
       |<td style="background-color:#EDEDED;padding:15pt;" colspan="2">
       |<p style="font-size:11pt;margin:0;"><b><span style="color:black;font-size:10.5pt;">Det er gjort endringer i ${centralHandbook.title}</span></b><b><span style="font-size:10.5pt;"></span></b></p></td></tr>
       |<tr>
       |<td style="padding:4.5pt 18pt;">
       |$changes
       |</td>
       |</tr></tbody></table>
       |""".stripMargin
  }

  private def generateHtml(handbookChanges: List[String]): String = {
    val now = new DateTime
    s"""
       |<div style="width:100%;height:100%">
       |<table style="width:100%;" cellpadding="0" cellspacing="0" border="0">
       |<tbody><tr>
       |<td style="background-color:#050037;padding:15pt;">
       |<p style="font-size:11pt;margin:0;"><b><span style="color:white;">${title}</span></b><span style="color:white;"></span></p></td>
       |<td style="background-color:#050037;padding:15pt;">
       |<p style="font-size:11pt;text-align:right;margin:0;" align="right"><span style="color:white;font-size:10.5pt;">Dato: ${now.toString(dateFormat)}</span></p></td></tr>
       |<tr>
       |<td style="padding:15pt;" colspan="2">
       |<p><span style="font-size:10pt;">Hei,</span></p>
       |<p><span style="font-size:10pt;">KF har gjort endringer i en sentral håndbok som din organisasjon abonnerer på. </span><span style="font-size:10.5pt;"></span></p></td></tr></tbody></table>
       |
       |${handbookChanges.mkString("<br/>")}
       |<table style="width:100%;" cellpadding="0" border="0">
       |<tbody><tr>
       |<td style="background-color:#EDEDED;padding:15pt;" colspan="2">
       |<p style="font-size:11pt;margin:0;"><b>
       |<span style="color:black;font-size:10.5pt;">Du kan logge inn i ${title} og se endringene.&nbsp; </span>
       |<br/><a data-auth="NotApplicable" rel="noopener noreferrer" target="_blank" href="https://handboker.kf.no/" data-linkindex="0"><span style="font-size:10.5pt;">Logg inn i ${title} her</span></a>
       |<br><span style="color:black;font-size:10.5pt;">Merk at navnet og innholdet i håndboken kan ha blitt endret lokalt.</span>
       |</b><b><span style="font-size:10.5pt;"></span></b></p></td></tr>
       |</tbody></table>
       |<table style="background-color:#050037;width:100%;" cellpadding="0" border="0">
       |<tbody><tr>
       |<td style="padding:15pt;">
       |<p style="font-size:11pt;margin:0;">
       |</span><span style="color:white;font-size:9pt;">Dersom du ikke lenger ${sc}nsker beskjed om sentrale endringer som er gjennomf${sc}rt av KF kan du logge inn og fjerne haken ved e-postvarsel om sentrale endringer.</span><span style="font-size:9pt;"></span></p>
       |<p><span style="color:white;font-size:9pt;">Hilsen KF<br>
       |</span><span style="color:white;font-size:10pt;"><a data-auth="NotApplicable" rel="noopener noreferrer" target="_blank" href="http://www.kf.no" data-linkindex="1"><span style="font-size:9pt;color:white;">www.kf.no</span></a></span><span style="color:white;font-size:9pt;"> <br>
       |</span><span style="color:white;font-size:10pt;"><a href="mailto:<EMAIL>" data-linkindex="2"><span style="font-size:9pt;color:white;"><EMAIL></span></a> </span><span style="font-size:10pt;"></span></p></td></tr></tbody></table>
       |
       |</div>
       |""".stripMargin
  }

  private def generatePlainText(handbookChanges: List[String]): String = {
    val message = "KF har gjort endringer i en sentral håndbok som din kommune abonnerer på."

    s"""Hei
        |
        |$message
        |
        |
        |${handbookChanges.mkString("\n\n")}
        |
        |
        |Du kan logge inn i KF Håndbøker og se endringene. Logg inn i KF Håndbøker her.
        |
        |Merk at navnet og innholdet i håndboken kan ha blitt endret lokalt. http://handboker.kf.no/
        |
        |Dersom du ikke lenger ønsker beskjed om sentrale endringer som er gjennomført av KF kan du logge inn og fjerne haken ved e-postvarsel om sentrale endringer.
        |
        |Hilsen KF
        |
        |www.kf.no
        |
        |<EMAIL>
        |""".stripMargin
  }

  def generateLink(handbookId: String): String = {
    s"$handbokerUrl/editor/$handbookId/"
  }

}
