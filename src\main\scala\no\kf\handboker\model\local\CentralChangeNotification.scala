package no.kf.handboker.model.local

import org.joda.time.DateTime

case class CentralChangeNotification(id: Option[String],
                                     changeDescription: String,
                                     handbookId: String,
                                     changedDate: DateTime,
                                     chapterId: Option[String] = None,
                                     sectionId: Option[String] = None,
                                     changeHTML: Option[String] = None,
                                     concernsTitle: Boolean = false,
                                     deletion: Boolean = false)
