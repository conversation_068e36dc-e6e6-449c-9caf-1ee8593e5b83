import { Card, Icon, Field } from "kf-bui";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type { Link } from "@/types";
import { LinkComponent } from "../Link";

interface SortableLinkProps {
  link: Link;
  onSave: (link: Link) => void;
  onDelete: (linkId: string) => void;
}

export const SortableLink = ({ link, onSave, onDelete }: SortableLinkProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: link.id! });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <Card style={{ marginBottom: "0.5rem", cursor: "grab" }}>
        <Card.Content as={Field} style={{ padding: "1rem", display: "flex" }}>
          <Icon
            icon="bars"
            size="small"
            style={{ marginRight: "0.5rem", alignSelf: "center" }}
          />
          <div style={{ flex: 1 }}>
            <LinkComponent
              link={link}
              onSave={onSave}
              onDelete={() => onDelete(link.id!)}
              showActions={false}
            />
          </div>
        </Card.Content>
      </Card>
    </div>
  );
};
