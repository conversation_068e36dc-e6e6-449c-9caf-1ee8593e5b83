import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Title, Subtitle, Columns, Column, Button } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useAppSelector, useAppDispatch } from "@/store";
import {
  selectSelectedCentralItem,
  clearSelectedCentralItem,
} from "@/store/slices/centralTreeSlice";
import { Spinner } from "@/shared/components/Spinner";
import { toast } from "@/shared/components/Toast";
import {
  useGetCentralChapterByIdQuery,
  useGetCentralSectionByIdQuery,
  useUpdateCentralChapterMutation,
  useUpdateCentralSectionMutation,
} from "@/store/services/handbook/centralHandbookApi";
import type { CentralChapter, CentralSection } from "@/types";
import { sameLocation, differentHandbook } from "../../../utils/moveValidation";

export const MoveCentralChapterOrSection: React.FC = () => {
  const t = usePrefixedTranslation("editor.containers.MoveChapterOrSelection");
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { handbookId, chapterId, sectionId } = useParams<{
    handbookId?: string;
    chapterId?: string;
    sectionId?: string;
  }>();

  const selectedCentralItem = useAppSelector(selectSelectedCentralItem);
  const [moveError, setMoveError] = useState<string | null>(null);

  // Clear selection when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearSelectedCentralItem());
    };
  }, [dispatch]);

  const {
    data: chapter,
    isLoading: chapterLoading,
    error: chapterError,
  } = useGetCentralChapterByIdQuery(
    { handbookId: handbookId!, chapterId: chapterId! },
    { skip: !chapterId || !handbookId }
  );

  const {
    data: section,
    isLoading: sectionLoading,
    error: sectionError,
  } = useGetCentralSectionByIdQuery(
    { handbookId: handbookId!, sectionId: sectionId! },
    { skip: !sectionId || !handbookId }
  );

  const [updateCentralChapter, { isLoading: chapterUpdateLoading }] =
    useUpdateCentralChapterMutation();
  const [updateCentralSection, { isLoading: sectionUpdateLoading }] =
    useUpdateCentralSectionMutation();

  const movedItem = chapter || section;
  const isLoading = chapterLoading || sectionLoading;
  const hasError = chapterError || sectionError;
  const isUpdating = chapterUpdateLoading || sectionUpdateLoading;

  if (isLoading) {
    return (
      <div>
        <Title>Flytt</Title>
        <div className="loading-container">
          <Spinner />
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div>
        <Title>Flytt</Title>
        <Subtitle>Feil ved lasting av data</Subtitle>
        <Button onClick={() => navigate(-1)} size="medium">
          Gå tilbake
        </Button>
      </div>
    );
  }

  if (!movedItem) {
    return (
      <div>
        <Title>Flytt</Title>
        <Subtitle>Fant ikke elementet som skal flyttes</Subtitle>
        <Button onClick={() => navigate(-1)} size="medium">
          Gå tilbake
        </Button>
      </div>
    );
  }

  const sameLoc =
    selectedCentralItem && sameLocation(selectedCentralItem, movedItem);
  const diffHandbook =
    selectedCentralItem && differentHandbook(selectedCentralItem, movedItem);
  const sectionToHandbook =
    selectedCentralItem &&
    selectedCentralItem.type === "HANDBOOK" &&
    movedItem.type === "SECTION";

  const showMoveButton =
    selectedCentralItem && !sameLoc && !diffHandbook && !sectionToHandbook;

  const handleMove = async () => {
    if (!selectedCentralItem || !showMoveButton) return;

    setMoveError(null);

    try {
      const targetIsHandbook = selectedCentralItem.type === "HANDBOOK";
      const targetIsChapter = selectedCentralItem.type === "CHAPTER";

      const targetHandbookId = targetIsHandbook
        ? selectedCentralItem.id
        : selectedCentralItem.centralHandbookId;

      const newParentId = targetIsChapter ? selectedCentralItem.id : undefined;

      const oldParentId = movedItem.parentId;
      const oldHandbookId = movedItem.centralHandbookId;
      const isMoved =
        oldParentId !== newParentId || oldHandbookId !== targetHandbookId;

      let result;

      if (movedItem.type === "SECTION") {
        const section = movedItem as CentralSection;
        if (!section.id) throw new Error("Section ID is required for update");
        if (!newParentId)
          throw new Error("Sections must have a parent chapter");

        const sectionUpdate = {
          id: section.id,
          title: section.title,
          html: section.html,
          centralHandbookId: section.centralHandbookId,
          parentId: newParentId,
          versionOf: section.versionOf,
          registeredDate: section.registeredDate,
          titleUpdatedDate: section.titleUpdatedDate,
          htmlUpdatedDate: section.htmlUpdatedDate,
          titleUpdatedBy: section.titleUpdatedBy,
          htmlUpdatedBy: section.htmlUpdatedBy,
          sortOrder: section.sortOrder,
          createdDate: section.createdDate,
          updatedDate: section.updatedDate,
          createdBy: section.createdBy,
          updatedBy: section.updatedBy,
        };

        result = await updateCentralSection(sectionUpdate).unwrap();
        toast.success("Avsnittet ble flyttet.");
      } else {
        const chapter = movedItem as CentralChapter;
        if (!chapter.id) throw new Error("Chapter ID is required for update");

        const chapterUpdate = {
          id: chapter.id,
          title: chapter.title,
          centralHandbookId: targetHandbookId!,
          parentId: newParentId,
          versionOf: chapter.versionOf,
          updatedDateBeforePublish: chapter.updatedDateBeforePublish,
          sortOrder: chapter.sortOrder,
          createdDate: chapter.createdDate,
          updatedDate: chapter.updatedDate,
          createdBy: chapter.createdBy,
          updatedBy: chapter.updatedBy,
        };

        result = await updateCentralChapter(chapterUpdate).unwrap();
        toast.success("Kapittelet ble flyttet.");
      }

      if (isMoved) {
        const itemType = movedItem.type === "SECTION" ? "section" : "chapter";
        const newPath = `/central-editor/${targetHandbookId}/${itemType}/${result.id}/`;
        navigate(newPath, { replace: true });
      } else {
        navigate(-1);
      }
    } catch (error: unknown) {
      console.error("Failed to move item:", error);

      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data
          ?.message ||
        (error as { message?: string })?.message ||
        "Det oppstod en feil ved flytting av elementet.";
      setMoveError(errorMessage);
      toast.error(`Feil ved flytting: ${errorMessage}`);
    }
  };

  const handleCancel = () => {
    navigate(-1);
  };

  return (
    <div>
      <Title>Flytt</Title>
      <Subtitle>
        {t("moveElement", {
          title: <em>{movedItem.title}</em>,
        })}
      </Subtitle>
      <hr />

      {selectedCentralItem ? (
        <Subtitle>Ny plassering valgt: {selectedCentralItem.title}</Subtitle>
      ) : null}

      {moveError && (
        <Subtitle style={{ color: "red" }}>Feil: {moveError}</Subtitle>
      )}

      {sectionToHandbook && (
        <Subtitle>
          Du kan ikke plassere en seksjon direkte under en håndbok
        </Subtitle>
      )}

      {sameLoc && (
        <Subtitle>
          {movedItem.title} er allerede i {selectedCentralItem?.title}
        </Subtitle>
      )}

      {diffHandbook && (
        <Subtitle>Du kan ikke flytte mellom forskjellige håndbøker</Subtitle>
      )}

      <Columns responsive="mobile">
        <Column>
          <Button onClick={handleCancel}>Avbryt</Button>
        </Column>

        <Column narrow>
          <Button
            color="primary"
            onClick={handleMove}
            loading={isUpdating}
            disabled={isUpdating || !showMoveButton}
          >
            Flytt
          </Button>
        </Column>
      </Columns>
    </div>
  );
};
