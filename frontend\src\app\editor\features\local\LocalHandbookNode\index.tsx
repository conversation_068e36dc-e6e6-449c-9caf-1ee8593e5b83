import React from "react";
import { I<PERSON>, Tree } from "kf-bui";
import { useLocation } from "react-router-dom";
import type { LocalHandbookWithChildren } from "@/store/services/handbook/utils";
import type { LocalTreeNodeWithChildren, Chapter, Section } from "@/types";
import { useAppSelector } from "@/store";
import { selectSelectedLocalItem } from "@/store/slices/localTreeSlice";
import { shouldDisableLocalNode } from "../../../utils/localMoveValidation";
import { LocalChapterNode } from "../LocalChapterNode";

interface LocalHandbookNodeProps {
  handbook: LocalHandbookWithChildren;
  moving?: boolean;
  onSetSelectedItem?: (item: LocalTreeNodeWithChildren) => void;
  movedItem?: Chapter | Section;
  disabled?: boolean;
}

export const LocalHandbookNode: React.FC<LocalHandbookNodeProps> = ({
  handbook,
  moving = false,
  onSetSelectedItem,
  movedItem,
  disabled = false,
}) => {
  const location = useLocation();
  const chapters = handbook.chapters || [];
  const selectedLocalItem = useAppSelector(selectSelectedLocalItem);

  const isActive = React.useMemo(() => {
    if (moving) return false;

    const currentPath = location.pathname;
    const handbookPath = `/editor/${handbook.id}`;

    return currentPath.startsWith(handbookPath);
  }, [location.pathname, handbook.id, moving]);

  const items = chapters.map((chapter) => (
    <LocalChapterNode
      key={chapter.id}
      chapter={chapter}
      onSetSelectedItem={onSetSelectedItem}
      moving={moving}
      movedItem={movedItem}
      disabled={disabled}
    />
  ));

  if (moving && movedItem) {
    const isDisabled = shouldDisableLocalNode(handbook, movedItem, disabled);
    const isSelected = selectedLocalItem?.id === handbook.id;
    const isBeingMoved =
      handbook.id === movedItem.handbookId && movedItem.parentId === undefined;
    const containsMovedItem = movedItem.handbookId === handbook.id;

    return (
      <Tree.Item
        id={handbook.id!}
        key={handbook.id}
        items={items}
        onClick={() => onSetSelectedItem?.(handbook)}
        disabled={isDisabled}
        style={{
          opacity: isDisabled ? 0.5 : 1,
          cursor: isDisabled ? "not-allowed" : "pointer",
          fontWeight: isBeingMoved || isSelected ? 600 : undefined,
          color: isBeingMoved ? "#1976d2" : isSelected ? "#2e7d32" : undefined,
          backgroundColor: isBeingMoved
            ? "#e3f2fd"
            : isSelected
              ? "#e8f5e8"
              : containsMovedItem
                ? "#f5f5f5"
                : undefined,
          padding:
            isBeingMoved || isSelected || containsMovedItem
              ? "2px 4px"
              : undefined,
          borderRadius:
            isBeingMoved || isSelected || containsMovedItem ? "4px" : undefined,
          border: isSelected
            ? "2px solid #2e7d32"
            : isBeingMoved
              ? "2px solid #1976d2"
              : undefined,
        }}
      >
        <Icon icon="book" size="small" style={{ marginRight: "4px" }} />
        {handbook.title}
        {isBeingMoved && (
          <span
            style={{ marginLeft: "8px", fontSize: "12px", color: "#1976d2" }}
          >
            ← flytter
          </span>
        )}
        {isSelected && (
          <span
            style={{ marginLeft: "8px", fontSize: "12px", color: "#2e7d32" }}
          >
            ← valgt som mål
          </span>
        )}
      </Tree.Item>
    );
  }

  if (moving) {
    return (
      <Tree.Item
        id={handbook.id!}
        key={handbook.id}
        items={items}
        onClick={() => onSetSelectedItem?.(handbook)}
      >
        <Icon icon="book" size="small" style={{ marginRight: "4px" }} />
        {handbook.title}
      </Tree.Item>
    );
  }

  return (
    <Tree.ItemLink
      key={handbook.id}
      exact
      items={items}
      to={`/editor/${handbook.id}`}
      id={handbook.id!}
      style={isActive ? { fontWeight: "600", color: "#050037" } : undefined}
    >
      <Icon icon="book" size="small" style={{ marginRight: "4px" }} />
      {handbook.title}
    </Tree.ItemLink>
  );
};
