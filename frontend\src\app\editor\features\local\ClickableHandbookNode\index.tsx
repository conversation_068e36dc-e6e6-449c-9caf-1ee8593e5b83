import React from "react";
import { I<PERSON>, <PERSON> } from "kf-bui";
import type { LocalHandbookWithChildren } from "@/store/services/handbook/utils";
import type { LocalTreeNodeWithChildren, Chapter, Section } from "@/types";
import { useAppSelector } from "@/store";
import { selectSelectedLocalItem } from "@/store/slices/localTreeSlice";
import { shouldDisableLocalNode } from "../../../utils/localMoveValidation";
import { ClickableChapterNode } from "../ClickableChapterNode";

interface ClickableHandbookNodeProps {
  handbook: LocalHandbookWithChildren;
  onSetSelectedItem?: (item: LocalTreeNodeWithChildren) => void;
  movedItem?: Chapter | Section;
  disabled?: boolean;
}

export const ClickableHandbookNode: React.FC<ClickableHandbookNodeProps> = ({
  handbook,
  onSetSelectedItem,
  movedItem,
  disabled = false,
}) => {
  const chapters = handbook.chapters || [];
  const selectedLocalItem = useAppSelector(selectSelectedLocalItem);

  const isDisabled = movedItem
    ? shouldDisableLocalNode(handbook, movedItem, disabled)
    : false;

  const isSelected = selectedLocalItem?.id === handbook.id;
  const isBeingMoved = false;
  const containsMovedItem = movedItem && movedItem.handbookId === handbook.id;

  const items = chapters.map((chapter) => (
    <ClickableChapterNode
      key={chapter.id}
      chapter={chapter}
      onSetSelectedItem={onSetSelectedItem}
      movedItem={movedItem}
      disabled={isDisabled}
    />
  ));

  const renderHandbookContent = () => (
    <>
      <Icon icon="book" size="small" style={{ marginRight: "4px" }} />
      {handbook.title}
      {isBeingMoved && (
        <span style={{ marginLeft: "8px", fontSize: "12px", color: "#1976d2" }}>
          ← flytter
        </span>
      )}
      {isSelected && (
        <span style={{ marginLeft: "8px", fontSize: "12px", color: "#2e7d32" }}>
          ← valgt som mål
        </span>
      )}
    </>
  );

  return (
    <Tree.Item
      id={handbook.id!}
      key={handbook.id}
      items={items}
      onClick={() => !isDisabled && onSetSelectedItem?.(handbook)}
      disabled={isDisabled}
      style={{
        opacity: isDisabled ? 0.5 : 1,
        cursor: isDisabled ? "not-allowed" : "pointer",
        fontWeight: isBeingMoved || isSelected ? 600 : undefined,
        color: isBeingMoved ? "#1976d2" : isSelected ? "#2e7d32" : undefined,
        backgroundColor: isBeingMoved
          ? "#e3f2fd"
          : isSelected
            ? "#e8f5e8"
            : containsMovedItem
              ? "#f5f5f5"
              : undefined,
        padding:
          isBeingMoved || isSelected || containsMovedItem
            ? "2px 4px"
            : undefined,
        borderRadius:
          isBeingMoved || isSelected || containsMovedItem ? "4px" : undefined,
        border: isSelected
          ? "2px solid #2e7d32"
          : isBeingMoved
            ? "2px solid #1976d2"
            : undefined,
      }}
    >
      {renderHandbookContent()}
    </Tree.Item>
  );
};
