/**
 * Utility functions for attachment handling
 * Based on legacy implementation from LEGACY-frontend/app/editor/util/form.js
 */

import { 
  PngFile, 
  JpgFile, 
  DocxFile, 
  XlsxFile, 
  PptxFile, 
  PdfFile 
} from "../AttachmentIcons";

/**
 * Convert bytes to kilobytes
 */
export const bytesToKB = (bytes: number): number => {
  return Math.round(bytes / 1024);
};

/**
 * Format file size to human readable string
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * Format kilobytes to human readable string
 */
export const formatKilobytes = (kb: number): string => {
  if (kb < 1024) {
    return `${kb} KB`;
  }
  
  const mb = kb / 1024;
  return `${mb.toFixed(1)} MB`;
};

/**
 * Download file from blob
 */
export const downloadFileFromBlob = (blob: Blob, fileName: string): void => {
  const url = window.URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(url);
};

/**
 * Validate file type
 */
export const isValidFileType = (fileName: string): boolean => {
  const acceptedTypes = ['.docx', '.xlsx', '.pdf', '.pptx', '.png', '.jpg', '.jpeg'];
  const extension = '.' + fileName.split('.').pop()?.toLowerCase();
  return acceptedTypes.includes(extension);
};

/**
 * Check if file size is within limit
 */
export const isValidFileSize = (size: number, maxSize: number = 5 * 1024 * 1024): boolean => {
  return size <= maxSize;
};

/**
 * File type mapping for icons
 */
export const getFileTypeIcon = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'png':
      return PngFile;
    case 'jpg':
    case 'jpeg':
      return JpgFile;
    case 'docx':
      return DocxFile;
    case 'xlsx':
    case 'xls':
      return XlsxFile;
    case 'pptx':
      return PptxFile;
    case 'pdf':
      return PdfFile;
    default:
      return PngFile; // Default icon
  }
};

/**
 * Constants from legacy implementation
 */
export const ATTACHMENT_CONSTANTS = {
  MAX_FILE_COUNT: 10,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ACCEPTED_FILE_TYPES: '.docx, .xlsx, .pdf, .pptx, .png, .jpg',
  DATE_FORMAT: 'DD.MM.YYYY'
} as const;