/* <PERSON><PERSON>sent Modal Styles */

.modal-card-body.comments {
  max-height: 380px;
  padding: 1.25rem;
}

.modal-card-body.comments label {
  font-weight: bold;
}

.cookie-desc {
  margin-bottom: 12px;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc {
  text-decoration: underline;
  color: #363636;
  font-size: 16px;
}

.cookie-link-desc:hover {
  color: #3273dc;
}

.cookie-type-section-container {
  display: flex;
  gap: 16px;
  margin-top: 24px;
  margin-bottom: 24px;
  justify-content: center;
}

.cookie-type-section-container label {
  display: flex;
  cursor: pointer;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.cookie-type-section-container label input {
  position: absolute;
  left: -9999px;
}

.cookie-type-section-container label input:checked + span {
  background-color: #050037;
  color: #ffffff;
}

.cookie-type-section-container label input:checked + span:before {
  box-shadow: inset 0 0 0 0.4375em #00005c;
}

.cookie-type-section-container label span {
  display: flex;
  align-items: center;
  border-radius: 99em;
  transition: 0.25s ease;
  padding: 8px 32px;
  justify-content: center;
  background-color: #d9d9d9;
  font-size: 16px;
  font-weight: 400;
  line-height: 19.36px;
  text-align: left;
  color: #021815;
}

.cookie-type-section-container label span:hover {
  background-color: #d6d6e5;
}

.toggle-cookies-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
  width: fit-content;
  transition: background-color 0.1s ease;
  padding-right: 6px;
  border-radius: 4px;
}

.toggle-cookies-container:hover {
  background-color: #d6d6e5;
}

.toggle-cookies-container .rotate {
  transform: rotate(180deg);
  transition: transform 0.6s ease;
}

.toggle-cookies-container .rotate-0 {
  transform: rotate(0deg);
  transition: transform 0.6s ease;
}

.toggle-cookies-button {
  font-size: 16px;
  font-weight: 700;
  line-height: 19.36px;
  text-align: left;
  background-color: transparent;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #363636;
}

.cookies-wrapper {
  overflow: hidden;
  transition: max-height 0.6s ease;
}

.cookie-type-title {
  margin: 24px 0;
  font-size: 16px;
  font-weight: 700;
  text-align: left;
}

.cookie-item {
  display: flex;
  gap: 16px;
  margin-top: 20px;
}

.cookie-details {
  flex: 1;
}

.cookie-title {
  margin: 0 0 8px 0;
}

.cookie-title .cookie-name {
  font-weight: 700;
}

.cookie-provider {
  font-weight: 500;
  margin: 4px 0;
}

.cookie-sub-list {
  margin-top: 8px;
}

.cookie-sub-item {
  margin-top: 4px;
}

.cookie-sub-name {
  font-weight: 600;
  margin-right: 6px;
}

.cookie-sub-details {
  color: #666666;
}

.cookie-settings-btn {
  background-color: transparent;
  border: none;
  color: #0000ff;
  text-decoration: underline;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  cursor: pointer;
  padding: 0;
}

.no-matomo-message {
  color: #666;
  font-style: italic;
}

.optout-iframe {
  border: 0;
  height: 0;
}

/* Toggle Button Styles */
.toggle-button {
  position: relative;
  display: inline-block;
  color: #fff;
}

.toggle-button label {
  display: inline-block;
  text-transform: uppercase;
  cursor: pointer;
  text-align: left;
}

.toggle-button input {
  display: none;
}

.toggle-button__icon {
  cursor: pointer;
  pointer-events: none;
}

.toggle-button__icon:before,
.toggle-button__icon:after {
  content: "";
  position: absolute;
  top: 45%;
  left: 35%;
  transition: 0.2s ease-out;
}

.toggle-button--aava {
  position: relative;
  width: 50px;
  height: 26px;
}

.toggle-button--aava input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-button--aava input:checked + label .toggle-button__icon {
  left: calc(100% - 22px);
  background-color: #050037;
}

.toggle-button--aava input:disabled + label {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-button--aava label {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 26px;
  background-color: #ccc;
  border-radius: 26px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-button--aava label[data-on-text]:after {
  content: attr(data-off-text);
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  color: #666;
}

.toggle-button--aava .toggle-button__icon {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 22px;
  height: 22px;
  background-color: white;
  border-radius: 50%;
  transition:
    left 0.3s,
    background-color 0.3s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}
