import { Card, Tree } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useParams } from "react-router-dom";
import { useCallback, useRef } from "react";
import { CentralHandbookNode } from "../CentralHandbookNode";
import { useAppDispatch } from "@/store";
import { setSelectedCentralItem } from "@/store/slices/centralTreeSlice";
import type { CentralTreeNodeWithChildren } from "@/types";
import {
  useGetCentralHandbooksQuery,
  useGetCentralChaptersQuery,
  useGetCentralSectionsQuery,
  useGetCentralChapterByIdQuery,
  useGetCentralSectionByIdQuery,
} from "@/store/services/handbook/centralHandbookApi";
import { transformToTreeStructure } from "@/store/services/handbook/utils";
import { Spinner } from "@/shared/components/Spinner";

interface CentralHandbookTreeProps {
  moving: boolean;
}

export const CentralHandbookTree = ({ moving }: CentralHandbookTreeProps) => {
  const t = usePrefixedTranslation("editor.containers.CentralTree");
  const { handbookId, chapterId, sectionId } = useParams<{ handbookId?: string; chapterId?: string; sectionId?: string }>();
  const dispatch = useAppDispatch();

  const treeRef = useRef<HTMLDivElement>(null);

  const { data: movedChapter } = useGetCentralChapterByIdQuery(
    { handbookId: handbookId!, chapterId: chapterId! },
    { skip: !moving || !chapterId || !handbookId }
  );
  const { data: movedSection } = useGetCentralSectionByIdQuery(
    { handbookId: handbookId!, sectionId: sectionId! },
    { skip: !moving || !sectionId || !handbookId }
  );

  const movedItem = movedSection || movedChapter;

  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (moving) return;

      switch (event.key) {
        case "ArrowUp":
        case "ArrowDown":
          break;
        case "Enter":
        case " ":
          break;
        default:
          return;
      }
    },
    [moving]
  );

  const {
    data: centralHandbooks,
    error: centralHandbooksError,
    isLoading: centralHandbooksLoading,
  } = useGetCentralHandbooksQuery();

  const {
    data: centralChapters,
    error: centralChaptersError,
    isLoading: centralChaptersLoading,
  } = useGetCentralChaptersQuery();

  const {
    data: centralSections,
    error: centralSectionsError,
    isLoading: centralSectionsLoading,
  } = useGetCentralSectionsQuery();

  const handbooks =
    centralHandbooks && centralChapters && centralSections
      ? transformToTreeStructure(
          centralHandbooks,
          centralChapters,
          centralSections
        )
      : [];

  const isLoading =
    centralHandbooksLoading || centralChaptersLoading || centralSectionsLoading;

  const error =
    centralHandbooksError || centralChaptersError || centralSectionsError;

  const handleSetSelectedItem = (item: CentralTreeNodeWithChildren) => {
    dispatch(setSelectedCentralItem(item));
  };

  if (error) {
    return (
      <Card>
        <Card.Header>
          <Card.Title>{t("treeHeader")}</Card.Title>
        </Card.Header>
        <Card.Content>
          <div style={{ padding: "1rem", textAlign: "center", color: "red" }}>
            Feil ved lasting av sentrale håndbøker
          </div>
        </Card.Content>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card>
        <Card.Header>
          <Card.Title>{t("treeHeader")}</Card.Title>
        </Card.Header>
        <Card.Content>
          <div style={{ padding: "1rem", textAlign: "center" }}>
            <Spinner text="Laster sentrale håndbøker..." />
          </div>
        </Card.Content>
      </Card>
    );
  }

  const items = handbooks.map((book) => (
    <CentralHandbookNode
      key={book.id}
      handbook={book}
      moving={moving}
      onSetSelectedItem={moving ? handleSetSelectedItem : undefined}
      movedItem={moving ? movedItem : undefined}
    />
  ));

  return (
    <Card>
      <Card.Header>
        <Card.Title>Sentrale håndbøker</Card.Title>
      </Card.Header>
      <Card.Content style={{ paddingLeft: 0, paddingRight: 0 }}>
        <div
          ref={treeRef}
          onKeyDown={handleKeyDown}
          tabIndex={0}
          role="tree"
          aria-label="Sentrale håndbøker"
          style={{ outline: "none" }}
        >
          <Tree>{items}</Tree>
        </div>
      </Card.Content>
    </Card>
  );
};
