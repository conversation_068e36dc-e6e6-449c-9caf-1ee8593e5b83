import { baseApi, publicApi, API_BASE_URLS } from "@/store/api";
import type { SearchResult, SearchRequest } from "@/types";

export const searchApi = baseApi.injectEndpoints({
  overrideExisting: process.env.NODE_ENV === "development",
  endpoints: (builder) => ({
    searchEditor: builder.query<SearchResult, SearchRequest>({
      query: ({ query, page = 1, handbookId }) => ({
        url: API_BASE_URLS.SEARCH,
        params: {
          query: query.toLowerCase(),
          page,
          handbookId,
        },
      }),
      providesTags: ["Search"],
    }),

    resetSearchIndexes: builder.mutation<void, void>({
      query: () => ({
        url: `${API_BASE_URLS.SEARCH}/reset`,
        method: "POST",
      }),
      invalidatesTags: ["Search"],
    }),

    resetManySearchIndexes: builder.mutation<
      [string[], string[]],
      string[]
    >({
      query: (externalOrgs) => ({
        url: `${API_BASE_URLS.SEARCH}/index/`,
        method: "POST",
        body: externalOrgs,
      }),
      invalidatesTags: ["Search"],
    }),
  }),
});

export const publicSearchApi = publicApi.injectEndpoints({
  overrideExisting: process.env.NODE_ENV === "development",
  endpoints: (builder) => ({
    searchPublic: builder.query<
      SearchResult,
      SearchRequest & { externalOrgId: string; handbookTitle?: string }
    >({
      query: ({ query, page = 1, handbookId, externalOrgId }) => ({
        url: "/public/search",
        params: {
          query: query.toLowerCase(),
          page,
          handbookId,
          externalOrgId,
        },
      }),
      transformResponse: (response: SearchResult, _meta, arg) => {
        // Track search with Matomo (matches legacy behavior)
        try {
          if (typeof window !== 'undefined' && (window as any)._paq) {
            (window as any)._paq.push([
              'trackSiteSearch', 
              arg.query, 
              arg.handbookTitle || 'Unknown Handbook',
              response.results.length
            ]);
          }
        } catch (error) {
          console.warn('Failed to track search with Matomo:', error);
        }
        return response;
      },
      providesTags: ["Search"],
    }),
  }),
});

export const {
  useLazySearchEditorQuery,
  useResetSearchIndexesMutation,
  useResetManySearchIndexesMutation,
} = searchApi;

export const { 
  useSearchPublicQuery,
  useLazySearchPublicQuery 
} = publicSearchApi;
