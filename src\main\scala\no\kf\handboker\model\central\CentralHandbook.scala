package no.kf.handboker.model.central

import no.kf.handboker.model.local.HandbookProperties
import org.joda.time.DateTime
/*
  created/updatedDate and
  created/updatedBy are set as default None
  because they are set in repository level.
 */
case class CentralHandbook (id: Option[String],
                            title: String,
                            versionOf: Option[String] = None,
                            createdDate: Option[DateTime] = None,
                            updatedDate: Option[DateTime] = None,
                            createdBy: Option[String] = None,
                            updatedBy: Option[String] = None,
                            isPublished: Boolean = false
                           )
