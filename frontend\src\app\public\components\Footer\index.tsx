import React from "react";
import { Footer as <PERSON><PERSON><PERSON>er, Container, Level } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";

interface FooterProps {
  application: string;
  showOptOutModal: () => void;
}

export const Footer: React.FC<FooterProps> = ({ application, showOptOutModal }) => {
  const t = usePrefixedTranslation("public.components.Footer");

  return (
    <KFFooter>
      <Container>
        <Level>
          <Level.Item textCentered flexible style={{ flexDirection: 'column' }}>
            <a 
              href="http://www.kf.no/" 
              target="_blank" 
              rel="noopener noreferrer"
            >
              KF
            </a>
            <a
              href="https://filer.kf-infoserie.no/veiledninger/Tilgjengelighet_KF_Handbok.html"
              target="_blank"
              rel="noopener noreferrer"
            >
              {t("accessibility")}
            </a>
            <a
              href="https://filer.kf-infoserie.no/veiledninger/Informasjonskapsler_KF_Håndbøker.html"
              target="_blank"
              rel="noopener noreferrer"
            >
              {t("cookies")}
            </a>
            <button 
              type="button" 
              className="cookie-settings-btn" 
              onClick={showOptOutModal}
            >
              {t("cookieSettings")}
            </button>
          </Level.Item>
          <Level.Item textCentered>
            {application}
          </Level.Item>
        </Level>
      </Container>
    </KFFooter>
  );
};