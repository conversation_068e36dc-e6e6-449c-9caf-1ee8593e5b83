import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { Columns, Column } from "kf-bui";

import { useSearchPublicQuery } from "@/store/services/search";
import { useFetchPublicHandbookQuery } from "@/store/services/handbook/publicHandbookApi";
import type { SearchHit, Chapter, Section } from "@/types";
import { usePublicSearchSync } from "../../hooks/usePublicSearchSync";
import { toast } from "@/shared/components/Toast";

import { SearchResult as SearchResultComponent } from "../../features/shared/SearchResult";
import { SearchPagination } from "../../features/shared/SearchPagination";
import { Spinner } from "@/shared/components/Spinner";

function createPublicLink(
  hit: SearchHit,
  chapters: Chapter[],
  sections: Section[],
  externalOrgId: string,
  handbookId: string
): { pathname: string; search?: string } {
  function getRootParentId(chaptersArray: Chapter[], id: string): string {
    let current = chaptersArray.find((c) => c.id === id);
    while (current?.parentId) {
      const parent = chaptersArray.find((c) => c.id === current!.parentId);
      current = parent;
    }
    const rootId = current?.id || id;
    return rootId;
  }

  if (hit.isChapter) {
    const rootChapterId = getRootParentId(chapters, hit.id);

    if (hit.id !== rootChapterId) {
      const firstSection = sections.find((s) => s.parentId === hit.id);

      if (firstSection) {
        return {
          pathname: `/${externalOrgId}/${handbookId}/chapter/${rootChapterId}`,
          search: `id=${firstSection.id}`,
        };
      }
    }

    return {
      pathname: `/${externalOrgId}/${handbookId}/chapter/${rootChapterId}`,
      search: undefined,
    };
  }

  if (hit.isSection) {
    const section = sections.find((s) => s.id === hit.id);

    if (section) {
      const parentChapter = chapters.find((c) => c.id === section.parentId);

      if (parentChapter) {
        const rootChapterId = getRootParentId(chapters, parentChapter.id!);

        return {
          pathname: `/${externalOrgId}/${handbookId}/chapter/${rootChapterId}`,
          search: `id=${hit.id}`,
        };
      }
    }
  }

  return { pathname: `/${externalOrgId}/${handbookId}/` };
}

export interface PublicSearchPageProps {
  externalOrgId?: string;
  handbookId?: string;
  chapters?: Chapter[];
  sections?: Section[];
  handbookTitle?: string;
}

export const SearchPage: React.FC<PublicSearchPageProps> = ({
  externalOrgId: propExternalOrgId,
  handbookId: propHandbookId,
  chapters: propChapters,
  sections: propSections,
  handbookTitle: propHandbookTitle,
}) => {
  const params = useParams<{ externalOrgId: string; handbookId: string }>();
  const searchState = usePublicSearchSync();

  const externalOrgId = propExternalOrgId || params.externalOrgId!;
  const handbookId = propHandbookId || params.handbookId!;
  const query = searchState.query;
  const debouncedQuery = searchState.debouncedQuery;

  const { data: handbookData } = useFetchPublicHandbookQuery(
    { externalOrgId, handbookId },
    { skip: !externalOrgId || !handbookId }
  );

  const chapters = handbookData?.chapters || propChapters || [];
  const sections = handbookData?.sections || propSections || [];
  const handbookTitle =
    handbookData?.handbook?.title || propHandbookTitle || "KF Håndbok";

  const [currentPage, setCurrentPage] = useState(1);
  const [lastSearchedQuery, setLastSearchedQuery] = useState("");

  const effectivePage = debouncedQuery !== lastSearchedQuery ? 1 : currentPage;

  const {
    data: searchResult,
    isFetching: isSearching,
    error: searchError,
  } = useSearchPublicQuery(
    {
      query: debouncedQuery.toLowerCase(),
      page: effectivePage,
      handbookId,
      externalOrgId,
      handbookTitle,
    },
    {
      skip: !debouncedQuery.trim(),
    }
  );

  useEffect(() => {
    if (searchError) {
      toast.error("En feil oppstod under søk");
    }
  }, [searchError]);

  useEffect(() => {
    if (debouncedQuery !== lastSearchedQuery) {
      setCurrentPage(1);
      setLastSearchedQuery(debouncedQuery);
    }
  }, [debouncedQuery, lastSearchedQuery]);

  const onPaginationClick = (page: number) => {
    if (page >= 1 && searchResult) {
      const maxPage = Math.ceil(searchResult.totalHits / searchResult.pageSize);
      if (page <= maxPage) {
        setCurrentPage(page);
      }
    }
  };

  const createLinkForHit = (hit: SearchHit): string => {
    const linkObj = createPublicLink(
      hit,
      chapters,
      sections,
      externalOrgId,
      handbookId
    );

    return linkObj.search
      ? `${linkObj.pathname}?${linkObj.search}`
      : linkObj.pathname;
  };

  document.title = "Søk - KF Håndbøker";

  if (!query.trim()) {
    return (
      <div style={{ textAlign: "center", padding: "2rem" }}>
        <p>Skriv inn et søkeord for å søke i håndboken.</p>
      </div>
    );
  }

  return (
    <div>
      <Columns>
        <Column>
          {isSearching && debouncedQuery.trim() && (
            <div style={{ textAlign: "center", padding: "2rem" }}>
              <Spinner text="Søker..." />
            </div>
          )}

          {searchResult && !isSearching && (
            <SearchResultComponent
              query={debouncedQuery}
              result={searchResult}
              linkFunc={createLinkForHit}
            />
          )}

          {!isSearching && !searchResult && debouncedQuery.trim() && (
            <div style={{ textAlign: "center", padding: "2rem" }}>
              <p>Ingen resultater funnet.</p>
            </div>
          )}
        </Column>
      </Columns>

      <Columns>
        <Column>
          {searchResult && !isSearching && searchResult.results.length > 0 && (
            <SearchPagination
              onPageClick={onPaginationClick}
              result={searchResult}
            />
          )}
        </Column>
      </Columns>
    </div>
  );
};
