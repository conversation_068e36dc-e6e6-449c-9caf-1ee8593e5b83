import React, { useState, useEffect, useCallback, useRef } from "react";
import { Button, Icon, Modal, Title } from "kf-bui";
import { useDropzone } from "react-dropzone";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import moment from "moment";

import {
  useGetAttachmentsQuery,
  useDeleteFileMutation,
  useSaveAttachmentsMutation,
} from "@/store/services/handbook/localHandbookApi";
import { uploadFileWithProgress } from "@/store/services/handbook/uploadWithProgress";
import type { AttachmentFile } from "@/types";
import { WarningIcon } from "../AttachmentIcons";
import { CircularProgress } from "../CircularProgress";
import {
  bytesToKB,
  formatFileSize,
  formatKilobytes,
  isValidFileSize,
  ATTACHMENT_CONSTANTS,
} from "../AttachmentUtils";

import "./styles.css";

interface AttachmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  sectionId: string;
  sectionType: "CHAPTER" | "SECTION";
  onAttachmentCountChange?: () => void;
}

export const AttachmentModal: React.FC<AttachmentModalProps> = ({
  isOpen,
  onClose,
  sectionId,
  sectionType,
  onAttachmentCountChange,
}) => {
  const t = usePrefixedTranslation("editor.containers");
  const fileListRef = useRef<HTMLDivElement>(null);

  const [files, setFiles] = useState<AttachmentFile[]>([]);
  const [uploadedFilesData, setUploadedFilesData] = useState<AttachmentFile[]>(
    []
  );
  const [oversizedFiles, setOversizedFiles] = useState<AttachmentFile[]>([]);
  const [removedFileIds, setRemovedFileIds] = useState<string[]>([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [isPublishDisabled, setIsPublishDisabled] = useState(true);
  const [isCleaning, setIsCleaning] = useState(false);

  const {
    data: existingFiles = [],
    isLoading: isLoadingFiles,
    error: filesError,
  } = useGetAttachmentsQuery({
    type: sectionType.toLowerCase() as "chapter" | "section",
    id: sectionId,
  });

  const [deleteFile] = useDeleteFileMutation();
  const [saveAttachments, { isLoading: isSaving }] =
    useSaveAttachmentsMutation();

  useEffect(() => {
    if (existingFiles.length > 0) {
      const formattedFiles = existingFiles.map((file) => ({
        ...file,
        status: "uploaded" as const,
        fileSize: formatKilobytes(file.size),
      }));
      setFiles(formattedFiles);
    }
  }, [existingFiles]);

  useEffect(() => {
    if (filesError) {
      console.error("Error loading attachments:", filesError);
      toast.error("Feil ved lasting av vedlegg");
    }
  }, [filesError]);

  useEffect(() => {
    setIsPublishDisabled(
      uploadedFilesData.length === 0 && removedFileIds.length === 0
    );
  }, [uploadedFilesData, removedFileIds]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const currentFileCount = files.length;
      const validFiles: File[] = [];
      const newOversizedFiles: AttachmentFile[] = [];
      setErrorMessage("");

      acceptedFiles.forEach((file) => {
        if (!isValidFileSize(file.size, ATTACHMENT_CONSTANTS.MAX_FILE_SIZE)) {
          newOversizedFiles.push({
            id: `temp-${Date.now()}-${Math.random()}`,
            title: file.name,
            belongsTo: sectionType,
            ownerId: sectionId,
            url: "",
            size: file.size,
            sortOrder: 0,
            status: "oversized",
            fileSize: formatFileSize(file.size),
          });
        } else {
          validFiles.push(file);
        }
      });

      if (
        currentFileCount + validFiles.length >
        ATTACHMENT_CONSTANTS.MAX_FILE_COUNT
      ) {
        setErrorMessage(t("uploadLimitReached"));
        return;
      }

      if (newOversizedFiles.length > 0) {
        setFiles((prev) => [...prev, ...newOversizedFiles]);
        setOversizedFiles((prev) => [...prev, ...newOversizedFiles]);
      }

      validFiles.forEach((file, index) => {
        const tempId = `temp-${Date.now()}-${index}`;

        const uploadingFile: AttachmentFile = {
          id: tempId,
          title: file.name,
          belongsTo: "",
          ownerId: sectionId,
          url: "",
          size: file.size,
          sortOrder: currentFileCount + index + 1,
          status: "uploading",
          progress: 0,
          fileSize: formatFileSize(file.size),
        };

        setFiles((prev) => [...prev, uploadingFile]);

        uploadFileWithProgress(file, (progress) => {
          setFiles((prev) =>
            prev.map((f) => (f.id === tempId ? { ...f, progress } : f))
          );
        })
          .then((response) => {
            const uploadedFile: AttachmentFile = {
              id: response.location,
              title: file.name,
              belongsTo: "",
              ownerId: sectionId,
              url: response.location,
              size: file.size,
              sortOrder: currentFileCount + index + 1,
              status: "uploaded",
              fileSize: formatFileSize(file.size),
              progress: 100,
            };

            setFiles((prev) =>
              prev.map((f) => (f.id === tempId ? uploadedFile : f))
            );

            setUploadedFilesData((prev) => [...prev, uploadedFile]);
          })
          .catch((error) => {
            console.error("Error uploading file:", file.name, error);
            toast.error(`Feil ved opplasting av fil: ${file.name}`);

            setFiles((prev) =>
              prev.map((f) =>
                f.id === tempId ? { ...f, status: "error" as const } : f
              )
            );
          });
      });

      // Scroll to bottom
      setTimeout(() => {
        if (fileListRef.current) {
          fileListRef.current.scrollTo({
            top: fileListRef.current.scrollHeight,
            behavior: "smooth",
          });
        }
      }, 200);
    },
    [files, t, sectionId, sectionType]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        [".docx"],
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
      "application/vnd.ms-excel": [".xls"],
      "application/pdf": [".pdf"],
      "application/vnd.openxmlformats-officedocument.presentationml.presentation":
        [".pptx"],
      "image/png": [".png"],
      "image/jpeg": [".jpg", ".jpeg"],
    },
    maxFiles: ATTACHMENT_CONSTANTS.MAX_FILE_COUNT,
  });

  const handleRemoveFile = useCallback(
    async (fileToRemove: AttachmentFile) => {
      setFiles((prev) => prev.filter((file) => file !== fileToRemove));

      setUploadedFilesData((prev) =>
        prev.filter((data) => data.id !== fileToRemove.id)
      );

      setOversizedFiles((prev) => prev.filter((file) => file !== fileToRemove));

      if (!fileToRemove.belongsTo) {
        try {
          const fileName = fileToRemove.id?.split("/").pop();
          if (fileName && fileName !== fileToRemove.id) {
            await deleteFile(fileName);
          }
        } catch (error) {
          console.error("Error deleting file:", fileToRemove.title, error);
        }
      } else {
        if (fileToRemove.id) {
          setRemovedFileIds((prev) => [...prev, fileToRemove.id!]);
        }
      }
    },
    [deleteFile]
  );

  const handlePublishUploadedFiles = useCallback(async () => {
    try {
      const prevUploadedFilesDataLength = uploadedFilesData.length;
      const prevRemovedFileIdsLength = removedFileIds.length;

      const payload = {
        belongsTo: sectionType,
        ownerId: sectionId,
        newlyAdded: uploadedFilesData.map((file) => ({
          title: file.title,
          url: file.url,
          sortOrder: file.sortOrder,
          size: bytesToKB(file.size),
        })),
        removed: removedFileIds,
      };

      const updatedFiles = await saveAttachments(payload).unwrap();

      const formattedFiles = updatedFiles.map((file) => ({
        ...file,
        status: "uploaded" as const,
        fileSize: formatKilobytes(file.size),
      }));

      setFiles(formattedFiles);
      setUploadedFilesData([]);
      setRemovedFileIds([]);
      setOversizedFiles([]);

      if (prevUploadedFilesDataLength === 0 && prevRemovedFileIdsLength > 0) {
        toast.success(t("attachmentsRemovedSuccessfully"));
      } else if (
        prevUploadedFilesDataLength > 0 &&
        prevRemovedFileIdsLength === 0
      ) {
        toast.success(t("attachmentsAddedSuccessfully"));
      } else if (
        prevUploadedFilesDataLength > 0 &&
        prevRemovedFileIdsLength > 0
      ) {
        toast.success(t("attachmentsUpdatedSuccessfully"));
      }

      onAttachmentCountChange?.();

      setTimeout(() => {
        onClose();
      }, 500);
    } catch (error) {
      console.error("Error saving attachments:", error);
    }
  }, [
    uploadedFilesData,
    removedFileIds,
    sectionType,
    sectionId,
    saveAttachments,
    t,
    onAttachmentCountChange,
    onClose,
  ]);

  const handleGetFile = useCallback(
    async (fileId: string, fileName: string) => {
      try {
        const response = await fetch(`/files/${fileId}`, {
          credentials: "same-origin",
        });

        if (!response.ok) {
          throw new Error(
            `Download failed: ${response.status} ${response.statusText}`
          );
        }

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Error downloading file:", error);
        toast.error("Feil ved nedlasting av fil");
      }
    },
    []
  );

  const handleOnClose = useCallback(async () => {
    // Clean up unpublished files
    if (uploadedFilesData.length > 0) {
      setIsCleaning(true);
      try {
        const deletePromises = uploadedFilesData.map(async (fileData) => {
          const fileName = fileData.id?.split("/").pop();
          if (fileName && fileName !== fileData.id) {
            await deleteFile(fileName);
          }
        });

        await Promise.all(deletePromises);
      } catch (error) {
        console.error("Error cleaning up files:", error);
      } finally {
        setIsCleaning(false);
      }
    }

    setFiles([]);
    setUploadedFilesData([]);
    setOversizedFiles([]);
    setRemovedFileIds([]);
    setErrorMessage("");

    onClose();
  }, [uploadedFilesData, deleteFile, onClose]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} autoFocus={false}>
      <Modal.Body>
        <Title>{t("attachmentModalTitle")}</Title>
        {files.length < ATTACHMENT_CONSTANTS.MAX_FILE_COUNT && (
          <div className="file-upload-wrapper">
            <div
              {...getRootProps()}
              className={`drag-drop-area ${isDragActive ? "active" : ""}`}
            >
              <input {...getInputProps()} />
              <p>{t("dragAndDrop")}</p>
              <p className="or-text">{t("or")}</p>

              <button type="button" className="upload-link">
                {t("clickToAdd")}
              </button>
              
              <p>
                {t("accestedFileTypes")}:{" "}
                {ATTACHMENT_CONSTANTS.ACCEPTED_FILE_TYPES}
              </p>
              <p>
                {`${t("max")} 5MB og maks ${ATTACHMENT_CONSTANTS.MAX_FILE_COUNT} ${t("attachmentForSection")}`}
              </p>
            </div>
          </div>
        )}

        {files.length >= ATTACHMENT_CONSTANTS.MAX_FILE_COUNT && (
          <div className="error-message-wrapper">
            <WarningIcon />
            <p className="error-message">{t("uploadLimitReached")}</p>
          </div>
        )}

        {errorMessage && (
          <div className="error-message-wrapper">
            <WarningIcon />
            <p className="error-message">{errorMessage}</p>
          </div>
        )}

        <div className="file-list-title">
          <h3>{t("addAttachment")}</h3>
        </div>

        <div className="file-list" ref={fileListRef}>
          {isLoadingFiles ? (
            <div className="attachment-loading-message">
              <span className="loader" />
              <p>Laster vedlegg. Vennligst vent...</p>
            </div>
          ) : (
            files
              .sort((a, b) => a.sortOrder - b.sortOrder)
              .map((file) => {
                const isOversized = oversizedFiles.includes(file);
                const isNewItem = !file.belongsTo && !isOversized;

                return (
                  <div
                    key={file.id}
                    className={`file-item ${isOversized ? "oversized-file" : ""} ${
                      isNewItem ? "new-item" : ""
                    }`}
                  >
                    <div
                      className="attachment-file-details"
                      onClick={() => {
                        if (
                          !isOversized &&
                          file.status === "uploaded" &&
                          file.belongsTo
                        ) {
                          handleGetFile(
                            file.url.split("/").pop() || file.url,
                            file.title
                          );
                        }
                      }}
                      onKeyDown={(e) => {
                        if (
                          (e.key === "Enter" || e.key === " ") &&
                          !isOversized &&
                          file.status === "uploaded" &&
                          file.belongsTo
                        ) {
                          handleGetFile(
                            file.url.split("/").pop() || file.url,
                            file.title
                          );
                        }
                      }}
                      role="button"
                      tabIndex={0}
                      style={{
                        cursor:
                          !isOversized &&
                          file.status === "uploaded" &&
                          file.belongsTo
                            ? "pointer"
                            : "default",
                      }}
                    >
                      <h4 className="attachment-file-name">
                        {file.title} {file.fileSize && `(${file.fileSize})`}
                      </h4>

                      {isOversized && (
                        <div className="file-oversized-error-wrapper">
                          <WarningIcon />
                          <h4 className="file-oversized-error">
                            {t("fileOversized")}
                          </h4>
                        </div>
                      )}

                      {file.status === "error" && (
                        <div className="file-oversized-error-wrapper">
                          <WarningIcon />
                          <h4 className="file-oversized-error">
                            Opplasting mislyktes. Prøv å laste opp igjen.
                          </h4>
                        </div>
                      )}

                      {!isOversized && file.status === "uploaded" && (
                        <h4 className="file-more-info">
                          {file.updatedBy
                            ? `${t("uploadedBy")}: ${file.updatedBy}, ${moment(
                                file.updatedDate
                              ).format(ATTACHMENT_CONSTANTS.DATE_FORMAT)}`
                            : t("readyToPublish")}
                        </h4>
                      )}

                      {!isOversized && file.status === "uploading" && (
                        <p className="attachment-uploading-progress">
                          Vedlegg lastes opp
                        </p>
                      )}
                    </div>

                    {isOversized ||
                    file.status === "uploaded" ||
                    file.status === "error" ? (
                      <button
                        type="button"
                        className="remove-file-button"
                        onClick={() => handleRemoveFile(file)}
                      >
                        <Icon icon="Xmark" size="small" />
                      </button>
                    ) : (
                      <div className="attachment-upload-percentage">
                        <CircularProgress percentage={file.progress || 0} />
                      </div>
                    )}
                  </div>
                );
              })
          )}
        </div>

        <div className="attachments-action-wrapper">
          <Button color="primary" onClick={handleOnClose} loading={isCleaning}>
            {t("cancel")}
          </Button>
          <Button
            className="attachment-publish-btn"
            onClick={handlePublishUploadedFiles}
            disabled={isPublishDisabled}
            loading={isSaving}
          >
            {t("publish")}
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};
