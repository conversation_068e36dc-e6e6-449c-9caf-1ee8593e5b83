# Search Indexing Process

After handbook synchronization, the system must update search indices to reflect the changes:

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│  SEARCH INDEXING PROCESS                                                    │
│                                                                             │
│  ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐│
│  │ Delete      │     │ Create      │     │ Index       │     │ Optimize    ││
│  │ Existing    │────►│ New         │────►│ All         │────►│ Index       ││
│  │ Index       │     │ Index       │     │ Documents   │     │             ││
│  └─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘│
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Implementation in SearchService

The reindexing process is implemented in `SearchService`:

```scala
override def doReindex(externalOrgId: String): Boolean = {
  try {
    // Step 1: Delete existing index for this organization
    searchIndexService.deleteEntriesFromIndex(externalOrgId)
    
    // Step 2: Create new index for this organization
    searchIndexService.createIndexForOrg(externalOrgId)
    
    // Step 3: Index all documents for this organization
    searchIndexBuilderService.indexDocumentsForExtOrg(externalOrgId)
    
    true
  } catch {
    case e: Exception =>
      log.error(s"Reindexing failed, $e")
      false
  }
}
```

## Scheduled Reindexing

In addition to on-demand reindexing after synchronization, the system runs a scheduled job:

```scala
private def initializeElasticSearchReIndexJob = {
  scheduler.start()

  val job = newJob(classOf[ElasticSearchReIndexJob])
    .withIdentity("ElasticSearchReIndex")
    .build()

  val trigger = newTrigger()
    .withIdentity("ElasticSearchReIndexTrigger")
    .startNow()
    .withSchedule(cronSchedule(componentRegistry.settings.settingFor(ElasticSearchReIndexCron)))
    .forJob(job)
    .build()

  scheduler.scheduleJob(job, trigger)
}
```

The job implementation:

```scala
override def execute(context: JobExecutionContext): Unit = {
  val jobName = "Elastic Search Re Index job"

  try {
    log.info("Starting " + jobName)

    // Get all organization IDs
    val externalOrgIds = localHandbookService.retrieveAllExternalOrgIds()
    
    // Reindex each organization
    externalOrgIds.foreach(id => searchService.doReindex(id))

    log.info("Finished running " + jobName)
  } catch {
    case e: Exception =>
      log.error("Error occurred during " + jobName, e)
      throw new JobExecutionException("Error occurred during " + jobName, e, false)
  }
}
```

This ensures that search indices remain up-to-date even if synchronization processes miss some updates.