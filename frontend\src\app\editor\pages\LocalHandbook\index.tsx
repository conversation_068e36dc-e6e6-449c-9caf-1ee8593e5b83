import { useState } from "react";
import { Routes, Route } from "react-router-dom";
import {
  Container,
  Title,
  Button,
  ImageHero,
  Hero,
  Column,
  Columns,
  Section,
} from "kf-bui";
import { useSession } from "@/store/services/session/hooks";
import { LocalHandbookTree, ClickableTree } from "../../features/local";
import { LocalHandbookRouter } from "../../features/handbook/LocalHandbookRouter";
import { CreateOrUpdateLocalHandbookModal } from "../../features/handbook/LocalHandbookScreen/CreateOrUpdateLocalHandbookModal";
import { FormattedMessage } from "react-intl";
import { FeatureErrorBoundaryWrapper } from "@/shared/components/ErrorBoundary";

export const LocalHandbook = () => {
  const { bannerUrl, session } = useSession();
  const [showModal, setShowModal] = useState(false);

  const toggleModal = () => {
    setShowModal(!showModal);
  };

  return (
    <Routes>
      <Route
        path="/*"
        element={
          <>
            <ImageHero
              color="primary"
              imageUrl={bannerUrl}
              className="local-hb-banner"
            >
              <Hero.Body>
                <Container>
                  <Columns>
                    <Column>
                      <Title>{session?.organization?.name || ""}</Title>
                    </Column>
                    <Column narrow>
                      <Button
                        onClick={toggleModal}
                        inverted
                        color="primary"
                        icon="plus"
                      >
                        <FormattedMessage id="editor.components.NoSelection.createButton" />
                      </Button>
                      {showModal && (
                        <FeatureErrorBoundaryWrapper featureName="Create Handbook Modal">
                          <CreateOrUpdateLocalHandbookModal
                            isOpen={showModal}
                            onHide={toggleModal}
                          />
                        </FeatureErrorBoundaryWrapper>
                      )}
                    </Column>
                  </Columns>
                </Container>
              </Hero.Body>
            </ImageHero>

            <Section>
              <Container>
                <Columns>
                  <Column size="1/3">
                    <Routes>
                      <Route
                        path=":handbookId/chapter/:chapterId/move"
                        element={<ClickableTree />}
                      />
                      <Route
                        path=":handbookId/section/:sectionId/move"
                        element={<ClickableTree />}
                      />
                      <Route
                        path="*"
                        element={<LocalHandbookTree moving={false} />}
                      />
                    </Routes>
                  </Column>
                  <Column size="2/3">
                    <LocalHandbookRouter />
                  </Column>
                </Columns>
              </Container>
            </Section>
          </>
        }
      />
    </Routes>
  );
};
