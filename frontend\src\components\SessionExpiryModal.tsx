import { useEffect, useState } from "react";
import { useIntl } from "react-intl";
import sessionTracker from "@/utils/sessionTracker";

interface SessionState {
  isWarning: boolean;
  isExpired: boolean;
  remainingMinutes: number;
}

export function SessionExpiryModal() {
  const intl = useIntl();
  const [state, setState] = useState<SessionState>({
    isWarning: false,
    isExpired: false,
    remainingMinutes: 0,
  });
  const [isKeepingAlive, setIsKeepingAlive] = useState(false);
  const [isManuallyHidden, setIsManuallyHidden] = useState(false);
  const [isExpiredAndClosed, setIsExpiredAndClosed] = useState(false);

  useEffect(() => {
    sessionTracker.onStateChange((newState) => {
      setState(newState);

      if (newState.isWarning) {
        setIsManuallyHidden(false);
        setIsExpiredAndClosed(false);
      } else if (newState.isExpired && !isExpiredAndClosed) {
        setIsManuallyHidden(false);
      } else if (!newState.isWarning && !newState.isExpired) {
        setIsManuallyHidden(false);
        setIsExpiredAndClosed(false);
      }
    });
  }, [isExpiredAndClosed]);

  const showModal =
    (state.isWarning || (state.isExpired && !isExpiredAndClosed)) &&
    !isManuallyHidden;

  if (!showModal) return null;

  const formatTime = (minutes: number): string => {
    const totalSeconds = Math.max(0, Math.floor(minutes * 60));
    const mins = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const handleKeepAlive = async () => {
    setIsKeepingAlive(true);
    sessionTracker.keepSessionAlive();
    setTimeout(() => setIsKeepingAlive(false), 1000);
  };

  const handleClose = () => {
    if (state.isExpired) {
      sessionTracker.stopTracking();
      setIsExpiredAndClosed(true);
    } else {
      setIsManuallyHidden(true);
    }
  };

  if (state.isExpired) {
    return (
      <div className="session-modal-backdrop">
        <div className="session-modal session-modal-expired">
          <div className="session-modal-header session-modal-header-expired">
            🚫 {intl.formatMessage({ id: "session.expired.title" })}
          </div>
          <div className="session-modal-content">
            <p className="session-expired-message-header">
              <strong>
                {intl.formatMessage({ id: "session.expired.message" })}
              </strong>
            </p>
            <p style={{ whiteSpace: "pre-line" }}>
              {intl.formatMessage({ id: "session.expired.copyMessage" })}
            </p>
          </div>
          <div className="session-modal-actions">
            <button
              className="session-modal-button session-modal-button-close"
              onClick={handleClose}
            >
              {intl.formatMessage({ id: "session.expired.closeButton" })}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="session-modal-backdrop">
      <div className="session-modal session-modal-warning">
        <div className="session-modal-header session-modal-header-warning">
          ⚠️ {intl.formatMessage({ id: "session.warning.title" })}
        </div>

        <div className="session-modal-countdown">
          {intl.formatMessage({ id: "session.warning.expiresIn" }, { time: formatTime(state.remainingMinutes) })}
        </div>

        <div className="session-modal-content">
          <p>{intl.formatMessage({ id: "session.warning.inactivityMessage" })}</p>
          <p>{intl.formatMessage({ id: "session.warning.keepSessionMessage" })}</p>
        </div>

        <div className="session-modal-actions">
          <button
            className="session-modal-button session-modal-button-keep-alive"
            onClick={handleKeepAlive}
            disabled={isKeepingAlive}
          >
            {isKeepingAlive ? "Keeping Active..." : intl.formatMessage({ id: "session.warning.keepActiveButton" })}
          </button>
        </div>
      </div>
    </div>
  );
}
