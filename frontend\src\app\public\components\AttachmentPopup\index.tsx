import React, { useEffect } from "react";
import { Icon } from "kf-bui";
import {
  useGetPublicChapterAttachmentsQuery,
  useGetPublicSectionAttachmentsQuery,
  useDownloadPublicAttachmentMutation,
} from "@/store/services/handbook/publicHandbookApi";
import {
  DoxFile,
  ExelFile,
  JpgFile,
  PdfFile,
  PngFile,
  PptFile,
} from "../AttachmentIcons";
import { toast } from "@/shared/components/Toast";
import { Spinner } from "@/shared/components/Spinner";

const fileTypeMapping = {
  png: PngFile,
  jpg: JpgFile,
  jpeg: JpgFile,
  docx: DoxFile,
  xlsx: ExelFile,
  xls: ExelFile,
  pptx: PptFile,
  pdf: PdfFile,
};

interface AttachmentPopupProps {
  sectionId: string;
  type: "chapter" | "section";
  onClose: () => void;
}

export const AttachmentPopup: React.FC<AttachmentPopupProps> = ({
  sectionId,
  type,
  onClose,
}) => {
  const { data: chapterAttachments, isLoading: isChapterLoading } =
    useGetPublicChapterAttachmentsQuery(sectionId, {
      skip: type !== "chapter",
    });

  const { data: sectionAttachments, isLoading: isSectionLoading } =
    useGetPublicSectionAttachmentsQuery(sectionId, {
      skip: type !== "section",
    });

  const [downloadAttachment] = useDownloadPublicAttachmentMutation();

  const attachments =
    type === "chapter" ? chapterAttachments : sectionAttachments;
  const isLoading = type === "chapter" ? isChapterLoading : isSectionLoading;

  const getFileType = (fileName: string) => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return (
      fileTypeMapping[extension as keyof typeof fileTypeMapping] || PngFile
    );
  };

  const handleGetFile = async (fileId: string, fileName: string) => {
    try {
      const file = fileId.split("/").pop() || fileId;
      const blob = await downloadAttachment(file).unwrap();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      onClose();
    } catch (error) {
      console.error("Error getting file:", error);
      toast.error(
        `Kunne ikke laste ned filen "${fileName}". Prøv igjen senere.`
      );
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const popup = target.closest(".attachment-popup");
      if (!popup) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose]);

  return (
    <div className="attachment-popup">
      <div className="arrow" />
      {isLoading ? (
        <div className="attachment-loading-message">
          <Spinner text="Laster inn vedlegg. Vennligst vent..." />
        </div>
      ) : (
        <div className="attachment-public-content">
          {attachments && attachments.length > 0 ? (
            [...attachments]
              .sort((a, b) => a.sortOrder - b.sortOrder)
              .map((file) => {
                const FileTypeIcon = getFileType(file.title);
                return (
                  <button
                    type="button"
                    key={file.id}
                    className="attachment-link"
                    onClick={() => handleGetFile(file.url, file.title)}
                  >
                    <FileTypeIcon />
                    <span>{file.title}</span>
                  </button>
                );
              })
          ) : (
            <div className="attachment-loading-message">
              <Icon icon="info" />
              <p>Fant ingen vedlegg</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
