import { useState, useEffect, useRef } from "react";
import { Title, Icon, Card, Button, Group } from "kf-bui";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
  type DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { LinkCollection, Link } from "@/types";
import { LinkCollectionForm } from "../LinkCollectionForm";
import { LinkForm } from "../LinkForm";
import { SortableLink } from "../SortableLink";
import { NonSortableLink } from "../NonSortableLink";

interface LinkCollectionCardProps {
  linkCollection: LinkCollection;
  onSaveLink: (link: Link, linkCollectionId: string) => void;
  onDeleteLink: (linkId: string) => void;
  onDeleteLinkCollection: (linkCollectionId: string) => void;
  onSaveLinkCollection: (linkCollection: LinkCollection) => void;
  refreshLinkCollections: () => void;
}

export const LinkCollectionCard = ({
  linkCollection,
  onSaveLink,
  onDeleteLink,
  onDeleteLinkCollection,
  onSaveLinkCollection,
}: LinkCollectionCardProps) => {
  const t = usePrefixedTranslation(
    "editor.containers.LinkPage.components.LinkCollection"
  );
  const [links, setLinks] = useState<Link[]>([]);
  const [isSorting, setIsSorting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    const sortedLinks = [...linkCollection.links].sort(
      (a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)
    );
    setLinks(sortedLinks);
  }, [linkCollection.links]);

  useEffect(() => {
    setIsEditing(false);
  }, [linkCollection.title]);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setLinks((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over?.id);
        return arrayMove(items, oldIndex, newIndex);
      });
    }

    setActiveId(null);
  };

  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };

  const saveLinkCollection = async (updatedCollection?: LinkCollection) => {
    const linksWithSortOrder = links.map((link, index) => ({
      ...link,
      sortOrder: index,
    }));

    const newLinkCollection: LinkCollection = {
      ...linkCollection,
      title: updatedCollection?.title || linkCollection.title,
      links: linksWithSortOrder,
    };

    await onSaveLinkCollection(newLinkCollection);
  };

  const toggleSorting = async (reset?: boolean) => {
    if (isSorting) {
      if (reset === true) {
        const originalLinks = [...linkCollection.links].sort(
          (a, b) => (a.sortOrder || 0) - (b.sortOrder || 0)
        );
        setLinks(originalLinks);
        setIsSorting(false);
      } else {
        await saveLinkCollection();
        setIsSorting(false);
      }
    } else {
      setIsSorting(true);
    }
  };

  const handleSaveLink = (link: Link) => {
    onSaveLink(link, linkCollection.id!);
  };

  const handleDeleteLink = (linkId: string) => {
    onDeleteLink(linkId);
  };

  const handleDeleteCollection = () => {
    onDeleteLinkCollection(linkCollection.id!);
  };

  const linkIds = links.filter((link) => link.id).map((link) => link.id!);
  const activeLink = activeId
    ? links.find((link) => link.id === activeId)
    : null;

  return (
    <Card style={{ marginBottom: "1rem" }}>
      <Card.Header>
        <div className="link-collection-header">
          {isEditing ? (
            <LinkCollectionForm
              handbookId={linkCollection.handbookId}
              sortOrder={linkCollection.sortOrder}
              linkCollection={linkCollection}
              onCancel={toggleEditing}
              onSave={async (updatedCollection) => {
                await saveLinkCollection(updatedCollection);
                setIsEditing(false);
              }}
            />
          ) : (
            <Title style={{ marginBottom: "0px", flexShrink: 1 }}>
              {linkCollection.title}
            </Title>
          )}

          <Group>
            {!isEditing && (
              <Button size="small" onClick={toggleEditing} icon="pencil">
                {t("changeName")}
              </Button>
            )}

            {isSorting ? (
              <Button
                color="primary"
                size="small"
                onClick={() => toggleSorting()}
                icon="save"
              >
                {t("saveSorting")}
              </Button>
            ) : (
              <Button
                color="primary"
                size="small"
                onClick={() => toggleSorting()}
                icon="sort"
              >
                {t("startSorting")}
              </Button>
            )}

            {isSorting ? (
              <Button
                color="primary"
                size="small"
                onClick={() => toggleSorting(true)}
                icon="ArrowsRotate"
              >
                {t("resetSorting")}
              </Button>
            ) : (
              <Button
                color="danger"
                size="small"
                onClick={handleDeleteCollection}
                icon="trash"
              >
                {t("deleteLinkCollection")}
              </Button>
            )}
          </Group>
        </div>
      </Card.Header>

      <Card.Content>
        {isSorting ? (
          <div ref={containerRef} style={{ position: "relative" }}>
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              modifiers={[
                (args) => {
                  const { transform } = args;
                  if (!containerRef.current) return transform;

                  const containerRect =
                    containerRef.current.getBoundingClientRect();
                  const constrainedTransform = { ...transform };

                  const maxHorizontalOffset = 50;
                  constrainedTransform.x = Math.max(
                    -maxHorizontalOffset,
                    Math.min(maxHorizontalOffset, transform.x)
                  );

                  const verticalBuffer = containerRect.height * 0.5;
                  constrainedTransform.y = Math.max(
                    -containerRect.height - verticalBuffer,
                    Math.min(containerRect.height + verticalBuffer, transform.y)
                  );

                  return constrainedTransform;
                },
              ]}
            >
              <SortableContext
                items={linkIds}
                strategy={verticalListSortingStrategy}
              >
                {links.map((link) => (
                  <SortableLink
                    key={link.id}
                    link={link}
                    onSave={handleSaveLink}
                    onDelete={handleDeleteLink}
                  />
                ))}
              </SortableContext>
              <DragOverlay>
                {activeLink ? (
                  <Card
                    style={{
                      cursor: "grabbing",
                      boxShadow: "0 5px 15px rgba(0, 0, 0, 0.15)",
                    }}
                  >
                    <Card.Content style={{ padding: "1rem", display: "flex" }}>
                      <Icon
                        icon="bars"
                        size="small"
                        style={{ marginRight: "0.5rem", alignSelf: "center" }}
                      />
                      <div style={{ flex: 1 }}>
                        <div style={{ fontWeight: "bold" }}>
                          {activeLink.title}
                        </div>
                        <div style={{ fontSize: "0.875rem", color: "#666" }}>
                          {activeLink.url}
                        </div>
                      </div>
                    </Card.Content>
                  </Card>
                ) : null}
              </DragOverlay>
            </DndContext>
          </div>
        ) : (
          <div>
            {links.map((link) => (
              <NonSortableLink
                key={link.id}
                link={link}
                onSave={handleSaveLink}
                onDelete={handleDeleteLink}
              />
            ))}
          </div>
        )}

        {links.length === 0 && (
          <span
            style={{
              marginBottom: "1rem",
              display: "block",
            }}
          >
            {t("noLinksInCollection")}
          </span>
        )}

        <LinkForm sortOrder={links.length} onSave={handleSaveLink} />
      </Card.Content>
    </Card>
  );
};
