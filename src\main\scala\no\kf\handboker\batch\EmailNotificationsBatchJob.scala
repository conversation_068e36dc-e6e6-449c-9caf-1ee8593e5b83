package no.kf.handboker.batch

import no.kf.handboker.ProductionRegistry
import no.kf.util.Logging
import org.quartz.{DisallowConcurrentExecution, Job, JobExecutionContext, JobExecutionException}

@DisallowConcurrentExecution
class EmailNotificationsBatchJob extends Job with Logging {

  lazy val subscriptionService = ProductionRegistry.componentRegistry.subscriptionService
  lazy val mailService = ProductionRegistry.componentRegistry.mailService

  override def execute(context: JobExecutionContext): Unit = {
    try {
      log.info("Starting EmailNotifications batch job")

      val results = mailService.sendChangeNotificationMessages()

      log.info(s"Finished running EmailNotifications batch job. ${results.count(!_)} failed messages out of ${results.size} total.")
    } catch {
      case e: Exception =>
        log.error("Error occurred during EmailNotifications batch job", e)
        throw new JobExecutionException("Error occured during EmailNotifications batch job", e, false)
    }
  }

}
