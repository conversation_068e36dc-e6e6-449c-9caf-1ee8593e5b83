import React, { useState, useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { useNavigate } from "react-router-dom";
import { Button, Modal, Field, Input, Label, Form, Help } from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { CentralHandbook } from "@/types";
import {
  useCreateCentralHandbookMutation,
  useUpdateCentralHandbookMutation,
} from "@/store/services/handbook/centralHandbookApi";
import { PageLeaveConfirmationModal } from "../../shared/PageLeaveConfirmationModal";

export interface CentralHandbookModalProps {
  isOpen: boolean;
  type: "create" | "edit";
  onHide: (val: boolean) => void;
  centralHandbook?: CentralHandbook;
}

const validationSchema = Yup.object().shape({
  title: Yup.string().required("titleRequired"),
});

interface FormData {
  title: string;
}

export const CentralHandbookModal: React.FC<CentralHandbookModalProps> = ({
  isOpen,
  onHide,
  type,
  centralHandbook,
}) => {
  const navigate = useNavigate();
  const t = usePrefixedTranslation(
    "editor.containers.CentralHandbookEditorPage.components.CentralHandbookForm"
  );
  const tCommon = usePrefixedTranslation("editor.common");

  const [showLeaveConfirmation, setShowLeaveConfirmation] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const [createCentralHandbook, { isLoading: isCreating }] =
    useCreateCentralHandbookMutation();
  const [updateCentralHandbook, { isLoading: isUpdating }] =
    useUpdateCentralHandbookMutation();

  const {
    handleSubmit,
    formState: { errors, isDirty },
    watch,
    setValue,
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      title: centralHandbook?.title || "",
    },
  });

  const titleValue = watch("title");

  useEffect(() => {
    setHasUnsavedChanges(isDirty);
  }, [isDirty]);

  useEffect(() => {
    if (isOpen) {
      reset({
        title: centralHandbook?.title || "",
      });
      setHasUnsavedChanges(false);
    }
  }, [isOpen, centralHandbook, reset]);

  const isLoading = isCreating || isUpdating;

  const onSubmit = async (data: FormData) => {
    try {
      if (type === "create") {
        const newHandbook = await createCentralHandbook({
          title: data.title,
        }).unwrap();
        setHasUnsavedChanges(false);
        onHide(false);

        if (newHandbook?.id) {
          setTimeout(() => {
            navigate(`/central-editor/${newHandbook.id}`);
          }, 100);
        }
      } else if (centralHandbook) {
        await updateCentralHandbook({
          id: centralHandbook.id!,
          title: data.title,
          createdDate: centralHandbook.createdDate!,
          updatedDate: centralHandbook.updatedDate!,
          createdBy: centralHandbook.createdBy!,
          updatedBy: centralHandbook.updatedBy!,
          isPublished: centralHandbook.isPublished,
          pendingChange: centralHandbook.pendingChange,
        }).unwrap();
        setHasUnsavedChanges(false);
        onHide(false);
      }
    } catch (error) {
      console.error("Failed to save handbook:", error);
      toast.error(t("saveError"));
    }
  };

  const handleClose = useCallback(() => {
    if (hasUnsavedChanges && !isLoading) {
      setShowLeaveConfirmation(true);
    } else {
      onHide(false);
    }
  }, [hasUnsavedChanges, isLoading, onHide]);

  const handleConfirmLeave = useCallback(() => {
    setShowLeaveConfirmation(false);
    setHasUnsavedChanges(false);
    onHide(false);
  }, [onHide]);

  const handleCancelLeave = useCallback(() => {
    setShowLeaveConfirmation(false);
  }, []);

  return (
    <>
      <Modal isOpen={isOpen} onClose={handleClose} autoFocus={false}>
        <Form onSubmit={handleSubmit(onSubmit)}>
          <Modal.Header onClose={handleClose}>
            <Modal.Title>
              {type === "create" ? t("createTitle") : t("editTitle")}
            </Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Field>
              <Label htmlFor="title">{t("titleLabel")}</Label>
              <Input
                id="title"
                name="title"
                placeholder={t("titlePlaceholder")}
                autoFocus
                value={titleValue}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setValue("title", e.target.value)
                }
              />
              {errors.title && (
                <Help color="danger">{t(errors.title.message as string)}</Help>
              )}
            </Field>
          </Modal.Body>
          <Modal.Footer>
            <Button onClick={handleClose} disabled={isLoading}>
              {t("cancelButton")}
            </Button>
            <Button
              type="submit"
              color="success"
              loading={isLoading}
              disabled={isLoading}
            >
              {t("saveButton")}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>

      <PageLeaveConfirmationModal
        isOpen={showLeaveConfirmation}
        onCancel={handleCancelLeave}
        onConfirm={handleConfirmLeave}
        title={tCommon("leaveEditorConfirmationTitle")}
        message={tCommon("leaveEditorConfirmationMessage")}
        t={tCommon}
      />
    </>
  );
};
