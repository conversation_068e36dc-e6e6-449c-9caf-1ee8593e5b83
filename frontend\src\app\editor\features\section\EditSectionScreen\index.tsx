import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Field,
  Input,
  Label,
  Title,
  Subtitle,
  Icon,
  Radio,
  Help,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import {
  useGetLocalSectionQuery,
  useSaveLocalSectionMutation,
} from "@/store/services/handbook/localHandbookApi";
import { type PublishedCentralSection } from "@/store/services/handbook/centralHandbookApi";
import { Wysiwyg, type WysiwygRef } from "../../shared/Wysiwyg";
import { CentralTreeModal } from "../../../shared/CentralTreeModal";
import { UnsavedChangesModal } from "@/shared/components/UnsavedChangesModal";
import { useUnsavedChangesGuard } from "@/shared/hooks/useUnsavedChangesGuard";
import type { Section } from "@/types";

export const EditSectionScreen = () => {
  const { handbookId, sectionId } = useParams() as {
    handbookId: string;
    sectionId?: string;
  };
  const navigate = useNavigate();
  const location = useLocation();
  const t = usePrefixedTranslation("editor.containers.EditSection");

  const searchParams = new URLSearchParams(location.search);
  const parentId = searchParams.get("parent");

  const isEditing = !!sectionId;
  const [title, setTitle] = useState("");
  const [text, setText] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const [selectedCentralSection, setSelectedCentralSection] =
    useState<PublishedCentralSection | null>(null);
  const [showCentralSectionModal, setShowCentralSectionModal] = useState(false);
  const [textSource, setTextSource] = useState<"central" | "local">("central");
  const [isWysiwygDirty, setIsWysiwygDirty] = useState(false);

  const initialValuesRef = useRef({ title: "", text: "" });
  const wysiwygRef = useRef<WysiwygRef>(null);

  const {
    data: section,
    error: sectionError,
    isLoading: isLoadingSection,
  } = useGetLocalSectionQuery(sectionId!, {
    skip: !isEditing,
  });

  const [saveLocalSection] = useSaveLocalSectionMutation();

  const { showModal, handleStay, handleLeave, bypassGuard } =
    useUnsavedChangesGuard({
      isDirty: isWysiwygDirty,
    });

  useEffect(() => {
    if (section) {
      const sectionTitle = section.title || "";
      const sectionText = section.text || "";

      setTitle(sectionTitle);
      setText(sectionText);

      initialValuesRef.current = {
        title: sectionTitle,
        text: sectionText,
      };
    }
  }, [section]);

  useEffect(() => {
    if (sectionError && isEditing) {
      console.error("Error loading section:", sectionError);
      toast.error(t("loadError"));
    }
  }, [sectionError, isEditing, t]);

  const handleTextChange = (newText: string) => {
    setText(newText);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    const titleValue = typeof title === "string" ? title : String(title || "");
    if (!titleValue.trim()) {
      toast.error(t("titleRequired"));
      return;
    }

    if (!parentId && !isEditing) {
      toast.error(t("parentRequired"));
      return;
    }

    setIsSaving(true);

    try {
      const titleValue =
        typeof title === "string" ? title : String(title || "");

      let finalText: string;
      if (textSource === "central" && selectedCentralSection) {
        finalText = selectedCentralSection.text || "";
      } else {
        finalText = wysiwygRef.current
          ? await wysiwygRef.current.uploadImagesAndGetContent()
          : text;
      }

      const sectionData: Section =
        isEditing && section
          ? {
              ...section,
              title: titleValue.trim(),
              text: finalText,
              localTitleChange: section.title !== titleValue.trim(),
              localTextChange: section.text !== finalText,
            }
          : {
              type: "SECTION",
              title: titleValue.trim(),
              text: finalText,
              handbookId,
              parentId: parentId!,
              importedHandbookSectionId:
                selectedCentralSection?.importedHandbookSectionId || undefined,
              importedHandbookId:
                selectedCentralSection?.importedHandbookId || undefined,
              localTitleChange: Boolean(
                selectedCentralSection &&
                  selectedCentralSection.title !== titleValue.trim()
              ),
              localTextChange: Boolean(
                selectedCentralSection && textSource === "local"
              ),
              pendingTitleChange: false,
              pendingTextChange: false,
              pendingDeletion: false,
            };

      const result = await saveLocalSection({
        section: sectionData,
        centralChange: Boolean(selectedCentralSection),
        centralTextChange:
          textSource === "central" && Boolean(selectedCentralSection),
      }).unwrap();

      toast.success(isEditing ? t("sectionUpdated") : t("sectionCreated"));
      setIsWysiwygDirty(false);
      bypassGuard();
      const sectionIdToNavigate = isEditing ? section!.id : result.id;
      navigate(`/editor/${handbookId}/section/${sectionIdToNavigate}/`);
    } catch (error) {
      console.error("Error saving section:", error);
      toast.error(isEditing ? t("updateError") : t("createError"));
    } finally {
      setIsSaving(false);
    }
  };

  const getAbortLink = () => {
    if (isEditing && section) {
      return `/editor/${section.handbookId}/section/${section.id}/`;
    } else if (parentId) {
      return `/editor/${handbookId}/chapter/${parentId}`;
    } else {
      return `/editor/${handbookId}`;
    }
  };

  const abortLink = getAbortLink();

  const handleCentralSectionSelect = (
    centralSection: PublishedCentralSection
  ) => {
    setSelectedCentralSection(centralSection);
    if (centralSection.title) {
      setTitle(String(centralSection.title));
    }
    if (centralSection.text) {
      setText(String(centralSection.text));
      setTextSource("central");
    }
    setShowCentralSectionModal(false);
  };

  const handleRemoveCentralSection = () => {
    setSelectedCentralSection(null);
    setText("");
    setTextSource("local");
  };

  const onRadioChange = (value: string | number) => {
    if (value !== textSource) {
      setTextSource(String(value) as "central" | "local");
      if (value === "central" && selectedCentralSection?.text) {
        setText(String(selectedCentralSection.text));
      }
    }
  };

  if (isEditing && isLoadingSection) {
    return <div>{t("loading")}</div>;
  }

  if (isEditing && !section && !isLoadingSection) {
    toast.error(t("sectionNotFound"));
    navigate(`/editor/${handbookId}`);
    return null;
  }

  return (
    <>
      <form onSubmit={handleSubmit}>
        <Columns>
          <Column>
            <Title>
              {isEditing ? "Rediger avsnitt" : "Opprett nytt avsnitt"}
            </Title>
            {isEditing && section && <Subtitle>{section.title}</Subtitle>}
          </Column>
        </Columns>

        {!isEditing && (
          <Columns>
            <Column>
              <Field>
                <Label>{t("centralTitle")}</Label>
                <Columns responsive="mobile" vCentered>
                  <Column narrow>
                    <Button
                      type="button"
                      onClick={() => setShowCentralSectionModal(true)}
                    >
                      {selectedCentralSection
                        ? selectedCentralSection.title
                        : t("centralButton")}
                    </Button>
                  </Column>
                  {selectedCentralSection && (
                    <Column>
                      <Button
                        type="button"
                        onClick={handleRemoveCentralSection}
                        size="small"
                        color="danger"
                        title={t("removeCentralSelection")}
                        aria-label={t("removeCentralSelection")}
                      >
                        <Icon icon="times" size="small" />
                      </Button>
                    </Column>
                  )}
                </Columns>
              </Field>
            </Column>
          </Columns>
        )}

        <Columns>
          <Column>
            <Field>
              <Label htmlFor="section-title">Tittel</Label>
              <Input
                id="section-title"
                name="title"
                placeholder="Tittel"
                readOnly={isSaving}
                value={title}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setTitle(e.target.value)
                }
                required
              />
            </Field>
          </Column>
        </Columns>

        {selectedCentralSection && !isEditing && (
          <Columns>
            <Column>
              <Field>
                <Radio
                  id="radio-central"
                  value="central"
                  checked={textSource === "central"}
                  onChange={onRadioChange}
                >
                  {t("centralRadio")}
                </Radio>
                &nbsp;&nbsp;
                <Radio
                  id="radio-local"
                  value="local"
                  checked={textSource === "local"}
                  onChange={onRadioChange}
                >
                  {t("localRadio")}
                </Radio>
              </Field>
            </Column>
          </Columns>
        )}

        <Columns>
          <Column>
            <Field>
              <Label>{t("textLabel")}</Label>
              <Wysiwyg
                ref={wysiwygRef}
                disabled={selectedCentralSection && textSource === "central"}
                value={text}
                onChange={handleTextChange}
                onDirtyStateChange={setIsWysiwygDirty}
              />
              {selectedCentralSection && (
                <Help>
                  {textSource === "central" &&
                  selectedCentralSection.title === title ? (
                    t("autoSync")
                  ) : (
                    <span>
                      <Icon icon="warning" size="small" /> {t("manualSync")}
                    </span>
                  )}
                </Help>
              )}
            </Field>
          </Column>
        </Columns>

        <Columns responsive="mobile">
          <Column>
            <Button type="button" onClick={() => navigate(abortLink)}>
              {t("cancelButton")}
            </Button>
          </Column>
          <Column narrow>
            <Button
              loading={isSaving}
              disabled={!title || !title.toString().trim()}
              type="submit"
              color="primary"
            >
              {t("saveButton")}
            </Button>
          </Column>
        </Columns>
      </form>

      {showCentralSectionModal && (
        <CentralTreeModal
          isOpen={showCentralSectionModal}
          onClose={() => setShowCentralSectionModal(false)}
          onSectionSelect={handleCentralSectionSelect}
          title="Velg sentralt avsnitt"
          allowSectionSelection={true}
        />
      )}

      <UnsavedChangesModal
        isOpen={showModal}
        onCancel={handleStay}
        onConfirm={handleLeave}
      />
    </>
  );
};
