import React from "react";
import { Icon, Tree } from "kf-bui";
import { useLocation } from "react-router-dom";
import type { Section, Chapter } from "@/types";
import { useAppSelector } from "@/store";
import { selectSelectedLocalItem } from "@/store/slices/localTreeSlice";
import { shouldDisableLocalNode } from "../../../utils/localMoveValidation";

interface LocalSectionNodeProps {
  section: Section;
  moving?: boolean;
  onSetSelectedItem?: (section: Section) => void;
  disabled?: boolean;
  movedItem?: Chapter | Section;
}

export const LocalSectionNode: React.FC<LocalSectionNodeProps> = ({
  section,
  moving = false,
  onSetSelectedItem,
  disabled = false,
  movedItem,
}) => {
  const location = useLocation();
  const selectedLocalItem = useAppSelector(selectSelectedLocalItem);

  const isActive = React.useMemo(() => {
    if (moving) return false;

    const currentPath = location.pathname;
    const sectionPath = `/section/${section.id}`;

    return currentPath.includes(sectionPath);
  }, [location.pathname, section.id, moving]);

  const getLocalSectionUrl = () => {
    return `/editor/${section.handbookId}/section/${section.id}/`;
  };

  if (moving && movedItem) {
    const isDisabled = shouldDisableLocalNode(section, movedItem, disabled);
    const isSelected = selectedLocalItem?.id === section.id;
    const isBeingMoved = section.id === movedItem.id;

    return (
      <Tree.Item
        id={section.id!}
        key={section.id}
        disabled={isDisabled}
        onClick={() => onSetSelectedItem?.(section)}
        style={{
          opacity: (isDisabled && !isBeingMoved && !isSelected) ? 0.5 : 1,
          cursor: (isDisabled && !isBeingMoved && !isSelected) ? 'not-allowed' : (isBeingMoved ? 'default' : 'pointer'),
          fontWeight: (isBeingMoved || isSelected) ? 600 : undefined,
          color: isBeingMoved ? '#1976d2' : (isSelected ? '#2e7d32' : undefined),
          backgroundColor: isBeingMoved ? '#e3f2fd' : (isSelected ? '#e8f5e8' : undefined),
          padding: (isBeingMoved || isSelected) ? '2px 4px' : undefined,
          borderRadius: (isBeingMoved || isSelected) ? '4px' : undefined,
          border: isSelected ? '2px solid #2e7d32' : (isBeingMoved ? '2px solid #1976d2' : undefined),
        }}
      >
        <Icon icon="RegFileLines" size="small" style={{ marginRight: "4px" }} />
        {section.title}
        {isBeingMoved && <span style={{ marginLeft: '8px', fontSize: '12px', color: '#1976d2' }}>← flytter</span>}
        {isSelected && <span style={{ marginLeft: '8px', fontSize: '12px', color: '#2e7d32' }}>← valgt som mål</span>}
      </Tree.Item>
    );
  }
  
  if (moving) {
    return (
      <Tree.Item
        id={section.id!}
        key={section.id}
        disabled={disabled}
        onClick={() => onSetSelectedItem?.(section)}
      >
        <Icon icon="RegFileLines" size="small" style={{ marginRight: "4px" }} />
        {section.title}
      </Tree.Item>
    );
  }

  return (
    <Tree.ItemLink
      to={getLocalSectionUrl()}
      key={section.id}
      id={section.id!}
      style={isActive ? { fontWeight: "600", color: "#050037" } : undefined}
    >
      <Icon icon="RegFileLines" size="small" style={{ marginRight: "4px" }} />
      {section.title}
    </Tree.ItemLink>
  );
};
