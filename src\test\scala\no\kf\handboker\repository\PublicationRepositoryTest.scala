package no.kf.handboker.repository

import no.kf.handboker.model.Publication
import no.kf.handboker.model.central.CentralHandbook
import org.joda.time.DateTime
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.scalatest.{BeforeAndAfterEach, FunSuite}

@RunWith(classOf[JUnitRunner])
class PublicationRepositoryTest extends FunSuite with DbTestHandler with BeforeAndAfterEach {

  val publicationRepository = componentRegistry.publicationRepository
  val handbookRepository = componentRegistry.centralHandbookRepository

  transactedTest("that we can store a publication") {
    val now = DateTime.now
    val centralHandbook = handbookRepository.persistCentralHandbook(CentralHandbook(None, "The Title", None, Some(DateTime.now), None, None, None), "aUser")
    val storedPublication = publicationRepository.persistPublication(Publication(None, now, notifyByEmail = true, now, "me", centralHandbook.id.get))
    assert(storedPublication.id.isDefined)
    assert(storedPublication.notifyByEmail)
    assert(storedPublication.createdDate === now)
    assert(storedPublication.publicationDate === now)
    assert(storedPublication.createdBy === "me")
    assert(storedPublication.published === false)
  }

  transactedTest("that we can delete a publication") {
    val now = DateTime.now
    val centralHandbook = handbookRepository.persistCentralHandbook(CentralHandbook(None, "The Title", None, Some(DateTime.now), None, None, None), "aUser")
    val storedPublication = publicationRepository.persistPublication(Publication(None, now, notifyByEmail = true, now, "me", centralHandbook.id.get))
    publicationRepository.deletePublication(storedPublication.id.get)
  }

  transactedTest("that we can correctly get publication candidates") {
    val centralHandbook1 = handbookRepository.persistCentralHandbook(CentralHandbook(None, "Handbook 1", None, Some(DateTime.now), None, None, None), "aUser")
    val centralHandbook2 = handbookRepository.persistCentralHandbook(CentralHandbook(None, "Handbook 2", None, Some(DateTime.now), None, None, None), "aUser")
    val publication1 = publicationRepository.persistPublication(Publication(None, DateTime.now, notifyByEmail = true, DateTime.now, "me", centralHandbook1.id.get))
    publicationRepository.persistPublication(Publication(None, DateTime.now, notifyByEmail = true, DateTime.now, "me", centralHandbook2.id.get))
    assert(publicationRepository.getPublicationCandidates.nonEmpty)
    assert(publicationRepository.getPublicationCandidates.size == 2)

    val now = DateTime.now
    val changedPublication = publicationRepository.persistPublication(publication1.copy(publicationDate = now, published = true, notifyByEmail = true))
    assert(publicationRepository.getPublicationCandidates.nonEmpty)
    assert(publicationRepository.getPublicationCandidates.size == 1)
    assert(changedPublication.publicationDate == now)
    assert(changedPublication.published)
    assert(changedPublication.notifyByEmail)
  }

  transactedTest(("That we do not store more than one unpublished publication per central handbook")) {
    assert(publicationRepository.getPublicationCandidates.isEmpty)
    val now = DateTime.now.minusWeeks(1)
    val centralHandbook = handbookRepository.persistCentralHandbook(CentralHandbook(None, "The Title", None, Some(DateTime.now), None, None, None), "aUser")
    publicationRepository.persistPublication(Publication(None, now, notifyByEmail = true, DateTime.now, "me", centralHandbook.id.get))
    publicationRepository.persistPublication(Publication(None, now.plusDays(1), notifyByEmail = true, DateTime.now, "me", centralHandbook.id.get))
    assert(publicationRepository.getPublicationCandidates.size == 1)
    assert(publicationRepository.getPublicationCandidates.head.publicationDate == now.plusDays(1))
  }
}
