package no.kf.handboker.service.search

import com.sksamuel.elastic4s.ElasticDsl._
import com.sksamuel.elastic4s.requests.searches.queries.Query
import com.sksamuel.elastic4s.requests.searches.queries.compound.BoolQuery
import com.sksamuel.elastic4s.{Hit, HitReader}
import com.sksamuel.elastic4s.requests.searches.{SearchRequest, SearchHit => sksHit}
import no.kf.handboker.config.{AppSettingComponent, ElasticSearchPageSize}
import no.kf.handboker.model._
import no.kf.util.Logging
import org.elasticsearch.index.IndexNotFoundException

import scala.concurrent.Future
import scala.concurrent.ExecutionContext.Implicits.global
import scala.util.{Failure, Success, Try}

/**
  * SearchService supplies search functionality itself to the REST API
  */
trait SearchServiceComponent {
  this: ElasticClientManagerServiceComponent
    with SearchIndexServiceComponent
    with SearchIndexBuilderServiceComponent
    with AppSettingComponent =>

  val searchService: SearchService

  class SearchServiceImpl extends SearchService with Logging {
    import SearchIndexDef._

    lazy val pageSize: Int = settings.settingFor(ElasticSearchPageSize).toInt

    implicit object DocumentHitAs extends HitReader[SearchHit] {
      override def read(hit: Hit): Try[SearchHit] = {
        val source = hit.sourceAsMap

        val (textHighlight, handbookTitleHighlight, chapterTitleHighlight, sectionTitleHighlight) = hit match {
          case richHit: sksHit =>
            val handbookTitleHighlight = richHit.highlightFragments(handbook_title).headOption
            val chapterTitleHighlight = richHit.highlightFragments(chapter_title).headOption
            val sectionTitleHighlight = richHit.highlightFragments(section_title).headOption
            val textHighlight = richHit.highlightFragments(section_text).headOption
            (textHighlight, handbookTitleHighlight, chapterTitleHighlight, sectionTitleHighlight)
          case _ =>
            (None, None, None, None)
        }

        // hit.type is not used anymore, probably make a common "Hit" class
        //hit.`type` match {
        source.get("document_type") match {
          case Some(doc_type) => {
            doc_type match {
              case HANDBOOK_TYPE => {
                Success(HandbookHit(
                  hit.id,
                  source(handbook_title).toString,
                  handbookTitleHighlight
                ))
              }
              case CHAPTER_TYPE => {
                Success(ChapterHit(
                  hit.id,
                  source(chapter_title).toString,
                  chapterTitleHighlight,
                  Option(source(handbook_id).toString)
                ))
              }
              case SECTION_TYPE => {
                Success(SectionHit(
                  hit.id,
                  source(section_title).toString,
                  sectionTitleHighlight,
                  textHighlight,
                  Option(source(handbook_id).toString)
                ))
              }
              case _ => Failure(new IllegalArgumentException("Unknown type match in Hit, neither handbook-, chapter- or section-type matched: " + hit.`type`))
            }
          }
          case None => Failure(new IllegalArgumentException("Unknown type match in Hit, neither handbook-, chapter- or section-type matched: " + hit.`type`))
        }
      }
    }

    private def createDefinitionWithQuery(externalOrgId: String, handbookId: Option[String], searchQuery: Query): SearchRequest = {
      // Only search in handbooks if we aren't searching in a specific handbook
//      val types = if (handbookId.isDefined) {
//        Seq(CHAPTER_TYPE, SECTION_TYPE)
//      } else {
//        Seq(HANDBOOK_TYPE, CHAPTER_TYPE, SECTION_TYPE)
//      }

//      val indexesAndTypes = IndexesAndTypes(Seq(externalOrgId), types)

//      search(indexesAndTypes) query {
//        must(searchQuery)
//      } highlighting (
//        highlight(section_text) numberOfFragments 1 fragmentSize 200 noMatchSize 200
//      ) sourceExclude(section_text) // Don't include the full section text in the search results

      search(externalOrgId).query(searchQuery).highlighting (
        highlight(section_text).numberOfFragments(1).fragmentSize(200).noMatchSize(200)
      ).sourceExclude(section_text) // Don't include the full section text in the search results

    }

    override def doSearch(externalOrgId: String, query: String, handbookId: Option[String], page: Option[Int]): SearchResult = {
      log.debug(s"doSearch with values: $externalOrgId, $handbookId, $query, $page")
      val searchQuery = buildBoolSearchQuery(externalOrgId, query, handbookId)
      val searchDefinitionWithQuery = createDefinitionWithQuery(externalOrgId, handbookId, searchQuery)
      log.debug(s"Calling executeSearch...")
      val result = executeSearch(searchDefinitionWithQuery, page)
      result
    }

    private def buildBoolSearchQuery(extOrgId: String, query: String, handbookId: Option[String]): BoolQuery = {
      val mustHaveQuery = if (handbookId.isEmpty) {
        Seq( matchQuery(externalOrgId, extOrgId))
      } else {
        Seq(
          matchQuery(externalOrgId, extOrgId),
          matchQuery(handbook_id, handbookId.get)
        )
      }

      val shouldHaveQuery = should(
        matchQuery(handbook_title, query).boost(2.0),
        matchQuery(chapter_title, query).boost(1.5),
        matchQuery(section_title, query).boost(1.25),
        matchQuery(section_text, query)
      )

      /**
        * This query is built with must(must ++ should).
        * It forces the query to only return the should-clause if the second must-clause is fulfilled(!)
        */
      must(mustHaveQuery ++ Seq(shouldHaveQuery))
    }

    private def executeSearch(searchQuery: SearchRequest, page: Option[Int]): SearchResult = {
      val (startAt, numResults) = getStartAtAndNumResults(page)

      Try(elasticClient.execute { searchQuery start startAt limit numResults }.await ) match {
        case Success(response) => SearchResult(response.result.totalHits, response.result.took, page.getOrElse(1), numResults, response.result.to[SearchHit])
        case Failure(exception) => errorHandler(exception.getCause)
      }
    }

    private def errorHandler(exception: Throwable) = {
      exception match {
        case ex: IndexNotFoundException =>
          log.error(s"Could not find index: ${ex.getMessage}")
          log.info("Scheduling index building...")
          scheduleIndexDocuments()
      }
      throw exception
    }

    def scheduleIndexDocuments(): Unit = {
      val f = Future {
        searchIndexBuilderService.indexDocuments()
      }
      f onFailure {
        case t => log.error("Unable to create index: ", t)
      }
    }

    def getStartAtAndNumResults(page: Option[Int]): (Int, Int) = {
      val numResults = pageSize

      val startAt = page match {
        case Some(start) => (start - 1).max(0) * numResults // Guaranteed to have 0 as the lowest possible value
        case None => 0
      }

      (startAt, numResults)
    }

    override def doReindex(externalOrgId: String): Boolean = {
      try {
        searchIndexService.deleteEntriesFromIndex(externalOrgId)
        searchIndexService.createIndexForOrg(externalOrgId)
        searchIndexBuilderService.indexDocumentsForExtOrg(externalOrgId)
        true
      } catch {
        case e: Exception =>
          log.error(s"Reindexing failed, $e")
          false
      }
    }
  }
}

trait SearchService {
  def doSearch(externalOrgId: String, query: String, handbookId: Option[String], page: Option[Int]): SearchResult
  def doReindex(externalOrgId: String): Boolean
}
