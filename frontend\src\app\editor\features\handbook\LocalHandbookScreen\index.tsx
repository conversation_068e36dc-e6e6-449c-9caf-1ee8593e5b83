import { useState, useCallback, useEffect } from "react";
import { <PERSON>, useParams, useNavigate } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Group,
  Icon,
  Menu,
  Title,
  Heading,
} from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useSession } from "@/store/services/session/hooks";
import { Spinner } from "@/shared/components/Spinner";

import {
  useDeleteLocalHandbookMutation,
  useSortLocalItemsMutation,
  useGetLocalSubscriptionsQuery,
  useToggleLocalSubscriptionMutation,
  useLazyGetLocalEditorsQuery,
} from "@/store/services/handbook/localHandbookApi";
import {
  useLocalHandbooks,
  useLocalChapters,
} from "@/store/services/handbook/hooks";
import { NoSelectionScreen } from "../NoSelectionScreen";
import { LocalSortChildrenScreen as SortChildrenScreen } from "../../local/LocalSortChildrenScreen";
import { LocalDeleteButton as DeleteButton } from "../../local/LocalDeleteButton";
import { PendingChangeWarning } from "../../PendingChangeWarning";
import { LocalMetadata as Metadata } from "../../local/LocalMetadata";
import { CommentModal } from "./CommentModal";
import { LocalEditorsModal } from "./LocalEditorsModal";
import { EditLocalHandbookButton } from "./EditLocalHandbookButton";
import { LocalHandbookExportModal } from "./LocalHandbookExportModal";
import { FormattedMessage } from "react-intl";

export const LocalHandbookScreen = () => {
  const { handbookId } = useParams() as { handbookId: string };
  const t = usePrefixedTranslation("editor.containers.HandbookSelection");
  const navigate = useNavigate();
  const { session } = useSession();

  const [isSorting, setIsSorting] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showLocalEditorsModal, setShowLocalEditorsModal] = useState(false);

  const {
    data: handbooks = [],
    error: handbooksError,
    isLoading: isLoadingHandbooks,
    isFetching: isFetchingHandbooks,
  } = useLocalHandbooks();

  const {
    data: allChapters = [],
    error: chaptersError,
    isLoading: isLoadingChapters,
  } = useLocalChapters();

  const { data: subscriptions = [], error: subscriptionsError } =
    useGetLocalSubscriptionsQuery();

  const [sortLocalItems, { isLoading: isSortingLoading }] =
    useSortLocalItemsMutation();

  const [deleteLocalHandbook] = useDeleteLocalHandbookMutation();
  const [toggleLocalSubscription] = useToggleLocalSubscriptionMutation();
  const [fetchLocalEditors] = useLazyGetLocalEditorsQuery();

  const localHandbook = handbooks.find(
    (handbook) => handbook.id === handbookId
  );

  const chapters = allChapters
    .filter((chapter) => chapter.handbookId === handbookId && !chapter.parentId)
    .map((ch) => ({ ...ch, type: "LOCAL_CHAPTER" as const }))
    .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

  const toggleSubscribe = useCallback(
    async (checked: boolean) => {
      if (!handbookId) return;

      try {
        await toggleLocalSubscription({
          handbookId,
          subscribe: checked,
        }).unwrap();
        toast.success(checked ? "Abonnert på håndbok" : "Avmeldt fra håndbok");
      } catch (error) {
        console.error("Error toggling subscription:", error);
        toast.error("Feil ved endring av abonnement");
      }
    },
    [handbookId, toggleLocalSubscription]
  );

  const toggleSort = useCallback(() => {
    setIsSorting((prev) => !prev);
  }, []);

  // Reset sort mode when handbook changes
  useEffect(() => {
    setIsSorting(false);
  }, [handbookId]);

  const handleDeleteHandbook = useCallback(async () => {
    if (!localHandbook?.id) return;

    try {
      await deleteLocalHandbook(localHandbook.id).unwrap();
      toast.success("Håndbok slettet");
      navigate("/editor");
    } catch (error) {
      console.error("Error deleting handbook:", error);
      toast.error("Feil ved sletting av håndbok");
    }
  }, [deleteLocalHandbook, localHandbook?.id, navigate]);

  const renderChildren = useCallback(() => {
    if (isSorting) {
      return (
        <SortChildrenScreen
          items={chapters}
          onCancel={() => setIsSorting(false)}
          sortFunction={async (itemIds) => {
            try {
              await sortLocalItems(itemIds).unwrap();
              toast.success("Lagret sortering.");
              setIsSorting(false);
            } catch (error) {
              console.error("Error sorting chapters:", error);
              toast.error("Feil ved sortering av kapitler");
            }
          }}
          isSaving={isSortingLoading}
        />
      );
    }

    if (!chapters.length) {
      return <div className="no-chapters">Denne håndboka har ingen kapitler</div>;
    }

    const sortedChapters = [...chapters].sort(
      (c1, c2) => (c1.sortOrder || 0) - (c2.sortOrder || 0)
    );

    return (
      <Menu>
        <Menu.List>
          {sortedChapters.map((chapter) => (
            <Menu.Item
              key={chapter.id}
              as={Link}
              to={`/editor/${chapter.handbookId}/chapter/${chapter.id}/`}
            >
              <Icon
                icon="RegBookmark"
                size="small"
                style={{ marginRight: "4px" }}
              />
              {chapter.title}
            </Menu.Item>
          ))}
        </Menu.List>
      </Menu>
    );
  }, [chapters, isSorting, isSortingLoading, setIsSorting, sortLocalItems]);

  const publicUrl = useCallback(() => {
    if (!localHandbook?.externalOrgId || !localHandbook?.id) return "#";

    let base = session?.publicBasename || "/public/";
    if (!base.endsWith("/")) base += "/";

    if (process.env.NODE_ENV === "development") {
      base = "/public/";
    }

    return `${base}${localHandbook.externalOrgId}/${localHandbook.id}`;
  }, [localHandbook, session]);

  if (handbooksError) {
    console.error("Error loading handbooks:", handbooksError);
    toast.error("Feil ved lasting av håndbøker");
  }

  if (chaptersError) {
    console.error("Error loading chapters:", chaptersError);
    toast.error("Feil ved lasting av kapitler");
  }

  if (subscriptionsError) {
    console.error("Error loading subscriptions:", subscriptionsError);
  }

  if (isLoadingHandbooks || isLoadingChapters) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  if (isFetchingHandbooks && !localHandbook) {
    return (
      <div className="loading-container">
        <Spinner textPosition="below" text="Laster håndbok..." />
      </div>
    );
  }

  if (!handbookId) {
    return <NoSelectionScreen />;
  }

  if (!localHandbook) {
    return <NoSelectionScreen />;
  }

  const isSubscribed = subscriptions.includes(handbookId);

  return (
    <>
      <Columns>
        <Column>
          <Title>
            <Icon icon="book" size="medium" style={{ marginRight: "1rem" }} />
            <span>{localHandbook.title}</span>

            <Heading style={{ fontSize: "14px" }}>
              <a
                target="_blank"
                rel="noopener noreferrer"
                href={publicUrl()}
                title={t("publishedLink")}
              >
                <FormattedMessage id="editor.containers.HandbookPage.components.HandbookScreen.readLink" />
                <Icon icon="ArrowUpRightFromSquare" />
              </a>
            </Heading>
          </Title>
        </Column>
      </Columns>

      <Columns multiline>
        <Column narrow>
          <EditLocalHandbookButton handbook={localHandbook} />
        </Column>

        <Column narrow>
          <Button
            control
            onClick={async () => {
              await fetchLocalEditors(localHandbook.id!);
              setShowLocalEditorsModal(true);
            }}
            size="small"
            icon="user-plus"
          >
            <FormattedMessage id="editor.containers.HandbookPage.components.LocalEditorsModal.accessTitle" />
          </Button>

          {showLocalEditorsModal && (
            <LocalEditorsModal
              handbookId={localHandbook.id!}
              onHide={() => setShowLocalEditorsModal(false)}
              isOpen={showLocalEditorsModal}
            />
          )}
        </Column>

        <Column narrow>
          <Button
            control
            as={Link}
            to={`/editor/${localHandbook.id}/chapter/add-new`}
            size="small"
            icon="plus"
          >
            {t("newChapter")}
          </Button>
        </Column>

        <Column narrow>
          <Button
            control
            as={Link}
            to={`/editor/${localHandbook.id}/links`}
            size="small"
            icon="link"
          >
            Opprett lenkesamling
          </Button>
        </Column>

        <Column narrow>
          <Button
            control
            onClick={() => setShowComments(true)}
            size="small"
            icon="RegComment"
          >
            <FormattedMessage id="editor.containers.HandbookPage.components.HandbookScreen.internalComments" />
          </Button>

          {showComments && (
            <CommentModal
              handbookId={localHandbook.id!}
              onHide={() => setShowComments(false)}
              isOpen={showComments}
            />
          )}
        </Column>

        <Column narrow>
          <Button control size="small" onClick={() => setShowExportModal(true)}>
            <FormattedMessage id="editor.containers.HandbookPage.components.HandbookScreen.exportButton" />
          </Button>

          {showExportModal && (
            <LocalHandbookExportModal
              handbook={localHandbook}
              onHide={() => setShowExportModal(false)}
              isOpen={showExportModal}
            />
          )}
        </Column>

        <Column narrow>
          <PendingChangeWarning
            element={localHandbook}
            mergeLink={`/merge/handbook/${localHandbook.id}/`}
          />
        </Column>

        <Column narrow>
          <Group>
            <Button
              control
              active={isSorting}
              onClick={toggleSort}
              disabled={chapters.length <= 1}
              title="Sorter kapitlene til Håndboken"
              size="small"
            >
              {t("sortButton")}
            </Button>

            <DeleteButton
              toDelete={{
                id: localHandbook.id!,
                title: localHandbook.title,
                type: "LOCAL_HANDBOOK",
              }}
              onDelete={handleDeleteHandbook}
            />
          </Group>
        </Column>
      </Columns>

      <Columns>
        <Column>
          <hr />
          <Metadata
            element={localHandbook}
            append={
              <label htmlFor="subscribe" className="checkbox subscribe-label">
                <Heading htmlFor="subscribe">{t("subscribeCheck")}</Heading>
                <input
                  id="subscribe"
                  type="checkbox"
                  onChange={(e) => toggleSubscribe(e.target.checked)}
                  checked={isSubscribed}
                  title={t("subscribeCheckMouseOver")}
                />
              </label>
            }
          />
          <hr style={{ marginBottom: "0" }} />
        </Column>
      </Columns>

      {renderChildren()}
    </>
  );
};
