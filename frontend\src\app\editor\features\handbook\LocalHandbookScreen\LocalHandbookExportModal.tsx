import { useState, Fragment } from "react";
import { Modal, Button, Icon, Field, Label, DatePicker } from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { Handbook } from "@/types";

interface LocalHandbookExportModalProps {
  handbook: Handbook;
  onHide: () => void;
  isOpen: boolean;
}

export const LocalHandbookExportModal = ({
  handbook,
  onHide,
  isOpen,
}: LocalHandbookExportModalProps) => {
  const t = usePrefixedTranslation(
    "editor.containers.HandbookPage.components.ExportModal"
  );
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  const makeUrl = (fileType: string, id: string, date: Date): string => {
    const BASE_URL = "/handboker/handbooks/download/local";
    return `${BASE_URL}/${fileType}/${id}/${date.toJSON()}`;
  };

  const handleDateChange = (selectedDates: Date[]) => {
    const selectedDate = selectedDates[0] || null;
    setSelectedDate(selectedDate);
  };

  const handleDownload = async (fileType: string) => {
    if (!selectedDate) {
      toast.error("Du må velge en dato først");
      return;
    }

    if (!handbook.id) {
      toast.error("Håndbok-ID mangler. Kan ikke eksportere.");
      return;
    }

    if (selectedDate > new Date()) {
      toast.error("Du kan ikke velge en dato i fremtiden");
      return;
    }

    try {
      const url = makeUrl(fileType, handbook.id, selectedDate);

      const response = await fetch(url, {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      const objectUrl = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = objectUrl;
      link.download = `${handbook?.title || "handbook"}.${fileType === "pdf" ? "pdf" : "docx"}`;
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(objectUrl);
    } catch (error) {
      console.error("Download failed:", error);
      toast.error(`Feil ved nedlasting av ${fileType.toUpperCase()}-fil.`);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onHide} autoFocus={false}>
      <Modal.Header onClose={onHide}>
        <Modal.Title>
          {t("title")}
          {handbook.title}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div style={{ minHeight: "10rem" }}>
          <Field>
            <Label htmlFor="handbook-date">{t("dateTitle")}</Label>
            <DatePicker
              id="handbook-date"
              placeholder="Velg dato og tid for versjonen av håndboka du vil eksportere"
              value={selectedDate}
              onChange={handleDateChange}
              maxDate={new Date()}
              enableTime={true}
              dateFormat="d.m.Y H:i"
              showIcon
              icon={<Icon icon="CalendarDays" />}
            />
          </Field>

          {selectedDate && (
            <Fragment>
              <p style={{ marginBottom: "1rem", marginTop: "1rem" }}>
                {t("exportQuestion")}
              </p>
              <Button onClick={() => handleDownload("pdf")} control>
                <Icon icon="RegFilePdf" size="small" />
                <span>{t("exportPDF")}</span>
              </Button>
            </Fragment>
          )}
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button onClick={onHide}>{t("close")}</Button>
      </Modal.Footer>
    </Modal>
  );
};
