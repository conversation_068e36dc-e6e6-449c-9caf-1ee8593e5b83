import type { CentralTreeNode, CentralChapter, CentralSection } from "@/types";

export interface MoveValidationResult {
  isValid: boolean;
  reason?: string;
}

export const sameLocation = (
  selectedItem: CentralTreeNode,
  movedItem: CentralChapter | CentralSection
): boolean => {
  if (selectedItem.type === "HANDBOOK") {
    return (
      !movedItem.parentId && movedItem.centralHandbookId === selectedItem.id
    );
  }
  return selectedItem.id === movedItem.parentId;
};

export const differentHandbook = (
  selectedItem: CentralTreeNode,
  movedItem: CentralChapter | CentralSection
): boolean => {
  if (selectedItem.type === "HANDBOOK") {
    return selectedItem.id !== movedItem.centralHandbookId;
  }
  return selectedItem.centralHandbookId !== movedItem.centralHandbookId;
};

export const sectionToHandbook = (
  selectedItem: CentralTreeNode,
  movedItem: CentralChapter | CentralSection
): boolean => {
  return selectedItem.type === "HANDBOOK" && movedItem.type === "SECTION";
};

export const isValidMoveTarget = (
  movedItem: CentralChapter | CentralSection,
  targetItem: CentralTreeNode
): MoveValidationResult => {
  const sameLoc = sameLocation(targetItem, movedItem);
  const diffHandbook = differentHandbook(targetItem, movedItem);
  const sectionToHbook = sectionToHandbook(targetItem, movedItem);

  if (sectionToHbook) {
    return {
      isValid: false,
      reason: "Du kan ikke plassere en seksjon direkte under en håndbok",
    };
  }

  if (sameLoc) {
    return {
      isValid: false,
      reason: `${movedItem.title} er allerede i ${targetItem.title}`,
    };
  }

  if (diffHandbook) {
    return {
      isValid: false,
      reason: "Du kan ikke flytte mellom forskjellige håndbøker",
    };
  }

  return { isValid: true };
};

export const shouldDisableNode = (
  nodeItem: CentralTreeNode,
  movedItem: CentralChapter | CentralSection,
  inheritedDisabled: boolean = false
): boolean => {
  if (inheritedDisabled) {
    return true;
  }

  if (nodeItem.id === movedItem.id) {
    return true;
  }

  if (nodeItem.type === "SECTION") {
    return true;
  }

  return false;
};
