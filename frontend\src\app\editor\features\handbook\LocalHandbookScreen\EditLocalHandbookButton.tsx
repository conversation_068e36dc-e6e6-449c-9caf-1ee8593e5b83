import { useState } from "react";
import { Button } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { Handbook } from "@/types";
import { CreateOrUpdateLocalHandbookModal } from "./CreateOrUpdateLocalHandbookModal";

interface EditLocalHandbookButtonProps {
  handbook: Handbook;
}

export const EditLocalHandbookButton = ({
  handbook,
}: EditLocalHandbookButtonProps) => {
  const t = usePrefixedTranslation("editor.containers.HandbookSelection");
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <Button
        control
        size="small"
        onClick={() => setShowModal(true)}
        title="Rediger denne håndboken"
        icon="pencil"
      >
        {t("editButton")}
      </Button>

      {showModal && (
        <CreateOrUpdateLocalHandbookModal
          handbook={handbook}
          isOpen={showModal}
          onHide={() => setShowModal(false)}
        />
      )}
    </>
  );
};
