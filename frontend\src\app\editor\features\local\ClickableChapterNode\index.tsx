import React from "react";
import { Icon, Tree } from "kf-bui";
import type { LocalChapterWithChildren } from "@/store/services/handbook/utils";
import {
  sortChaptersAndSections,
  isChapter,
} from "@/store/services/handbook/utils";
import type { LocalTreeNodeWithChildren, Section, Chapter } from "@/types";
import { useAppSelector } from "@/store";
import { selectSelectedLocalItem } from "@/store/slices/localTreeSlice";
import { shouldDisableLocalNode } from "../../../utils/localMoveValidation";
import { ClickableSectionNode } from "../ClickableSectionNode";

interface ClickableChapterNodeProps {
  chapter: LocalChapterWithChildren;
  onSetSelectedItem?: (item: LocalTreeNodeWithChildren) => void;
  movedItem?: Chapter | Section;
  disabled?: boolean;
}

export const ClickableChapterNode: React.FC<ClickableChapterNodeProps> = ({
  chapter,
  onSetSelectedItem,
  movedItem,
  disabled = false,
}) => {
  const childChapters = chapter.chapters || [];
  const sections = chapter.sections || [];
  const selectedLocalItem = useAppSelector(selectSelectedLocalItem);

  const isNodeDisabled = movedItem
    ? shouldDisableLocalNode(chapter, movedItem, disabled)
    : false;

  const isSelected = selectedLocalItem?.id === chapter.id;
  const isBeingMoved = movedItem && chapter.id === movedItem.id;
  const isParentOfMoved = movedItem && movedItem.parentId === chapter.id;

  const sortedItems = sortChaptersAndSections(childChapters, sections);

  const items = sortedItems.map((item) => {
    if (isChapter(item)) {
      return (
        <ClickableChapterNode
          key={item.id}
          chapter={item as LocalChapterWithChildren}
          onSetSelectedItem={onSetSelectedItem}
          disabled={isNodeDisabled}
          movedItem={movedItem}
        />
      );
    } else {
      return (
        <ClickableSectionNode
          key={item.id}
          section={item as Section}
          onSetSelectedItem={onSetSelectedItem}
          disabled={isNodeDisabled}
          movedItem={movedItem}
        />
      );
    }
  });

  return (
    <Tree.Item
      items={items}
      key={chapter.id}
      id={chapter.id!}
      disabled={isNodeDisabled}
      onClick={() => !isNodeDisabled && onSetSelectedItem?.(chapter)}
      style={{
        opacity: isNodeDisabled && !isBeingMoved && !isSelected ? 0.5 : 1,
        cursor: isNodeDisabled ? "not-allowed" : "pointer",
        fontWeight: isBeingMoved || isSelected ? 600 : undefined,
        color: isBeingMoved
          ? "#1976d2"
          : isSelected
            ? "#2e7d32"
            : isParentOfMoved
              ? "#666"
              : undefined,
        backgroundColor: isBeingMoved
          ? "#e3f2fd"
          : isSelected
            ? "#e8f5e8"
            : isParentOfMoved
              ? "#f5f5f5"
              : undefined,
        padding:
          isBeingMoved || isSelected || isParentOfMoved ? "2px 4px" : undefined,
        borderRadius:
          isBeingMoved || isSelected || isParentOfMoved ? "4px" : undefined,
        border: isSelected
          ? "2px solid #2e7d32"
          : isBeingMoved
            ? "2px solid #1976d2"
            : undefined,
      }}
    >
      <Icon icon="RegBookmark" size="small" style={{ marginRight: "4px" }} />
      {chapter.title}
      {isBeingMoved && (
        <span style={{ marginLeft: "8px", fontSize: "12px", color: "#1976d2" }}>
          ← flytter
        </span>
      )}
      {isSelected && (
        <span style={{ marginLeft: "8px", fontSize: "12px", color: "#2e7d32" }}>
          ← valgt som mål
        </span>
      )}
    </Tree.Item>
  );
};
