import { useDispatch, useSelector } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import { combineReducers } from "@reduxjs/toolkit";
import { baseApi, publicApi } from "./api";
import centralTreeReducer from "./slices/centralTreeSlice";
import localTreeReducer from "./slices/localTreeSlice";
import pendingPublicationsReducer from "./slices/pendingPublicationsSlice";
import publicSearchReducer from "./slices/publicSearchSlice";
import "./services/session/sessionApi";
import "./services/handbook/centralHandbookApi";
import "./services/handbook/localHandbookApi";
import "./services/handbook/publicHandbookApi";

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["pendingPublications"],
};

const rootReducer = combineReducers({
  [baseApi.reducerPath]: baseApi.reducer,
  [publicApi.reducerPath]: publicApi.reducer,
  centralTree: centralTreeReducer,
  localTree: localTreeReducer,
  pendingPublications: pendingPublicationsReducer,
  publicSearch: publicSearchReducer,
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          "persist/PERSIST",
          "persist/REHYDRATE",
          "persist/PAUSE",
          "persist/PURGE",
          "persist/REGISTER",
        ],
      },
    }).concat(baseApi.middleware, publicApi.middleware),
  devTools:
    process.env.NODE_ENV !== "production"
      ? {
          name: "KF Handboker Store",
          trace: true,
          traceLimit: 25,
        }
      : false,
});

export const persistor = persistStore(store);

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = useDispatch.withTypes<AppDispatch>();
export const useAppSelector = useSelector.withTypes<RootState>();
