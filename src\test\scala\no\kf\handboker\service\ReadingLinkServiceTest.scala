package no.kf.handboker.service

import no.kf.db.testing.TransactionManagerMock
import no.kf.handboker.{ComponentRegistry, DefaultTestDI}
import no.kf.handboker.model.central.ReadingLink
import org.joda.time.DateTime
import org.mockito.Mockito.{reset, times, verify, when}
import org.scalatest.{BeforeAndAfterEach, FunSuite}

class ReadingLinkServiceTest extends FunSuite with DefaultTestDI with BeforeAndAfterEach{

  override val componentRegistry: ComponentRegistry = new DefaultTestRegistry with TransactionManagerMock {
    override lazy val readingLinkService: ReadingLinkService = new ReadingLinkServiceImpl
  }

  private val repository = componentRegistry.readingLinkRepository
  private val service = componentRegistry.readingLinkService

  override def beforeEach() {
    reset(repository)
  }

  test("That we cannot update the validTo date of an expired readingLink"){
    val testLink = ReadingLink(Some("linkId"), Some("linkUrl"), "sectionId", None, DateTime.now())

    service.persistReadingLink(testLink)
    verify(repository, times(0)).persistReadingLink(testLink)
  }
}
