<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <groupId>no.knowit.kf</groupId>
    <artifactId>handboker</artifactId>
    <version>0.1</version>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>
    <name>KF Handboker</name>

	<properties>
        <scala.version>2.12.10</scala.version>
        <scala.major.version>2.12</scala.major.version>
        <mockito.version>1.10.19</mockito.version>
		<scala-plugin.version>3.2.1</scala-plugin.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<commonVersion>4.7.15</commonVersion>
        <commonScalatraVersion>3.2.10</commonScalatraVersion>
        <brukerAdmApiVersion>2.0.1</brukerAdmApiVersion>
        <applicationVersion>2.2.34-qa4</applicationVersion>
		<skipFrontend>false</skipFrontend>
		<coverage>false</coverage>
		<scoverage.plugin.version>1.3.0</scoverage.plugin.version>
		<scoverage.scalacPluginVersion>1.3.0</scoverage.scalacPluginVersion>
		<project-info-reports.plugin.version>2.9</project-info-reports.plugin.version>
		<dockerfile.maven.plugin.version>1.3.7</dockerfile.maven.plugin.version>
	</properties>

    <repositories>
        <repository>
            <id>kf-release</id>
			<url>https://nexus.knowit.no/nexus/content/repositories/kf-release</url>
        </repository>
        <repository>
            <id>kf-avvik</id>
            <name>KF Avvik Project Repository Group</name>
            <url>https://nexus.knowit.no/nexus/content/repositories/kf-avvik</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>kf-avvik</id>
            <name>KF Avvik Project Repository Group</name>
            <url>https://nexus.knowit.no/nexus/content/repositories/kf-avvik</url>
        </pluginRepository>
    </pluginRepositories>

	<dependencies>
		<dependency>
			<groupId>no.knowit.kf</groupId>
			<artifactId>kf-common-scalatra</artifactId>
			<version>${commonScalatraVersion}</version>
		</dependency>
		<dependency>
			<groupId>no.knowit.kf</groupId>
			<artifactId>brukeradm-api</artifactId>
			<version>${brukerAdmApiVersion}</version>
		</dependency>
		<dependency>
			<groupId>org.scalatest</groupId>
			<artifactId>scalatest_${scala.major.version}</artifactId>
			<version>3.0.1</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>no.knowit.kf</groupId>
			<artifactId>kf-common-server</artifactId>
			<version>${commonVersion}</version>
		</dependency>
		<dependency>
			<groupId>no.knowit.kf</groupId>
			<artifactId>kf-common-test</artifactId>
			<version>${commonVersion}</version>
			<scope>test</scope>
		</dependency>
		<!-- Used for stripping HTML before indexing for search -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.11.3</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
			<version>4.12</version>
		</dependency>
		<dependency>
			<groupId>org.apache.derby</groupId>
			<artifactId>derby</artifactId>
			<version>*********</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.2.3</version>
		</dependency>
        <dependency>
			<groupId>org.jasig.cas.client</groupId>
			<artifactId>cas-client-core</artifactId>
			<version>3.5.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.aspectj</groupId>
					<artifactId>aspectjrt</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.aspectj</groupId>
					<artifactId>aspectjweaver</artifactId>
				</exclusion>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjrt</artifactId>
			<version>1.8.6</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>net.bull.javamelody</groupId>
			<artifactId>javamelody-core</artifactId>
			<version>1.77.0</version>
		</dependency>

		<!--these need to be added explicitly for scalatra to work -->
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-reflect</artifactId>
			<version>${scala.version}</version>
		</dependency>
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>${scala.version}</version>
		</dependency>
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-compiler</artifactId>
			<version>${scala.version}</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-jcs-core</artifactId>
			<version>2.0-beta-1</version>
		</dependency>
		<dependency>
			<groupId>com.github.pathikrit</groupId>
			<artifactId>better-files_${scala.major.version}</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>com.sksamuel.elastic4s</groupId>
			<artifactId>elastic4s-core_${scala.major.version}</artifactId>
			<version>7.16.3</version>
		</dependency>
		<dependency>
			<groupId>com.sksamuel.elastic4s</groupId>
			<artifactId>elastic4s-client-esjava_2.12</artifactId>
			<version>7.16.3</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch</artifactId>
			<version>7.16.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.9</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.13.1</version>
		</dependency>

		<!--
		KFHB-123: We set up xerces as a dependency to avoid 'xerces hell' which could lead to runtime errors.
		 -->
		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>2.12.0</version>
		</dependency>
		<dependency>
			<groupId>org.docx4j</groupId>
			<artifactId>docx4j-ImportXHTML</artifactId>
			<version>3.3.6-1</version>
			<exclusions>
				<!-- Exclude log4j 1 as we now rely on log4j 2 from kf-common -->
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<!-- Exclude slf4j for log4j12 as we need the one for log4j2, which we get from common -->
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<!-- Exclude the jcl to slf4j bridge, as common gives us a jcl to log4j2 bridge -->
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf</artifactId>
			<version>9.1.20</version>
			<exclusions>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcprov-jdk14</artifactId>
				</exclusion>
				<exclusion>
					<groupId>bouncycastle</groupId>
					<artifactId>bcmail-jdk14</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.scalatra</groupId>
			<artifactId>scalatra-swagger_2.12</artifactId>
			<version>2.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.json4s</groupId>
			<artifactId>json4s-native_2.12</artifactId>
			<version>3.5.5</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>handboker</finalName>
		<resources>
			<resource>
				<directory>src/main/webapp</directory>
			</resource>
			<resource>
				<filtering>true</filtering>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/default.properties</include>
					<include>**/log4j2.xml</include>
				</includes>
			</resource>
			<resource>
				<filtering>false</filtering>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>**/default.properties</exclude>
					<exclude>**/log4j2.xml</exclude>
				</excludes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>net.alchim31.maven</groupId>
				<artifactId>scala-maven-plugin</artifactId>
				<version>${scala-plugin.version}</version>
				<configuration>
					<recompileMode>incremental</recompileMode>
					<useZincServer>true</useZincServer>
				</configuration>
				<executions>
					<execution>
						<id>scala-compile-first</id>
						<phase>process-resources</phase>
						<goals>
							<goal>add-source</goal>
							<goal>compile</goal>
						</goals>
					</execution>
					<execution>
						<id>scala-test-compile</id>
						<phase>process-test-resources</phase>
						<goals>
							<goal>testCompile</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.3</version>
				<executions>
					<execution>
						<phase>compile</phase>
						<goals>
							<goal>compile</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.4.1</version>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<webResources>
						<resource>
							<directory>frontend/build</directory>
						</resource>
					</webResources>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>cobertura-maven-plugin</artifactId>
				<version>2.5.1</version>
				<configuration>
					<formats>
						<format>xml</format>
						<format>html</format>
					</formats>
					<check />
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.eclipse.jetty</groupId>
				<artifactId>jetty-maven-plugin</artifactId>
				<version>9.4.8.v20171121</version>
				<configuration>
					<loginServices>
						<loginService implementation="org.eclipse.jetty.security.HashLoginService">
							<name>Monitoring</name>
							<config>${basedir}/src/test/resources/hash-loginService.properties</config>
						</loginService>
					</loginServices>
					<webAppConfig>
						<contextPath>/handboker</contextPath>
						<resourceBases>
							<resourceBase>src/main/webapp</resourceBase>
							<resourceBase>frontend/build</resourceBase>
						</resourceBases>
					</webAppConfig>
					<httpConnector>
						<port>5600</port>
						<idleTimeout>60000</idleTimeout>
					</httpConnector>
					<useTestClasspath>true</useTestClasspath>
					<systemProperties>
						<SystemProperty>
							<name>handboker.config.dir</name>
							<value>src/test/resources/</value>
						</SystemProperty>
					</systemProperties>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<profiles>
        <profile>
			<id>testcoverage</id>
			<activation>
				<property>
					<name>coverage</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.scoverage</groupId>
						<artifactId>scoverage-maven-plugin</artifactId>
						<version>${scoverage.plugin.version}</version>
						<configuration>
							<highlighting>true</highlighting>
							<minimumCoverage>10</minimumCoverage>
							<failOnMinimumCoverage>true</failOnMinimumCoverage>
						</configuration>
						<executions>
							<execution>
								<goals>
									<goal>check</goal>
								</goals>
								<phase>prepare-package</phase>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>prod</id>
            <properties>
				<coverage>true</coverage>
			</properties>
			<build>
				<plugins>
					<plugin>
						<!-- Only packaging war file with property file into a zip with version
							number. -->
						<artifactId>maven-assembly-plugin</artifactId>
						<version>2.2.1</version>
						<configuration>
							<appendAssemblyId>false</appendAssemblyId>
						</configuration>
						<executions>
							<execution>
								<configuration>
									<finalName>handboker-${applicationVersion}</finalName>
									<descriptors>
										<descriptor>src/main/assembly/war-distribution.xml</descriptor>
									</descriptors>
								</configuration>
								<id>make-assembly</id>
								<phase>package</phase>
								<goals>
									<goal>single</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>dependency-check</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>

			<dependencies>
				<dependency>
					<groupId>org.owasp</groupId>
					<artifactId>dependency-check-maven</artifactId>
					<version>3.2.1</version>
				</dependency>
			</dependencies>
			<build>
				<plugins>
					<plugin>
						<groupId>org.owasp</groupId>
						<artifactId>dependency-check-maven</artifactId>
						<version>3.2.1</version>
						<executions>
							<execution>
								<goals>
									<goal>check</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>docker</id>
            <activation>
				<activeByDefault>false</activeByDefault>
			</activation>
            <properties>
				<skipFrontend>false</skipFrontend>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>com.spotify</groupId>
						<artifactId>dockerfile-maven-plugin</artifactId>
						<version>${dockerfile.maven.plugin.version}</version>
						<executions>
							<execution>
								<id>default</id>
								<goals>
									<goal>build</goal>
								</goals>
							</execution>
						</executions>
						<configuration>
							<repository>kommuneforlaget/handboker</repository>
							<tag>${applicationVersion}</tag>
							<buildArgs>
								<APPLICATION_NAME>handboker</APPLICATION_NAME>
							</buildArgs>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>frontend</id>
			<activation>
				<property>
					<name>!skipFrontend</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
				<groupId>com.github.eirslett</groupId>
				<artifactId>frontend-maven-plugin</artifactId>
				<version>1.15.0</version>

                        <configuration>
                            <workingDirectory>frontend/</workingDirectory>
                        </configuration>

                        <executions>
                            <execution>
                                <id>install node and npm</id>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                                <configuration>
									<nodeVersion>v24.3.0</nodeVersion>
									<npmVersion>11.4.2</npmVersion>
                                </configuration>
                            </execution>
                            <execution>
                                <id>npm install</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>install</arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>npm build</id>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <phase>generate-resources</phase>
                                <configuration>
                                    <arguments>run build</arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

	<reporting>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-project-info-reports-plugin</artifactId>
				<version>${project-info-reports.plugin.version}</version>
				<configuration>
					<dependencyLocationsEnabled>false</dependencyLocationsEnabled>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.scoverage</groupId>
				<artifactId>scoverage-maven-plugin</artifactId>
				<version>${scoverage.plugin.version}</version>
				<configuration>
					<highlighting>true</highlighting>
					<minimumCoverage>10</minimumCoverage>
					<failOnMinimumCoverage>true</failOnMinimumCoverage>
				</configuration>
			</plugin>
		</plugins>
	</reporting>
</project>
