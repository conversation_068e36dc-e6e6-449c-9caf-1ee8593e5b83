# this property file sets some properties that are only there for testing purposes.
WebAppPath=/handboker
PublicWebAppPath=/handboker/public

#Port must be changed
SiteUrl=http://localhost:5600/handboker
WebAppPort=5600

### database settings ###
MockDataScript=dummy-data.sql
Database=DERBYMEM
#Database=MSSQL
#DatabaseHost=127.0.0.1:1433
#DatabaseUsername=sa
#DatabasePassword=myPassw0rd
#DatabaseName=handboker

ImageFolder=target/application-work-files/img
UploadFolder=target/application-work-files/uploads
PersistenceStore=target/application-work-files
LogStore=target/application-work-files

### settings for user auth ###
CasConfig=internal-demo
UseLDAP=false
LDAPSearchFilter=(mail=$EPOST)
LDAPSearchBase=dc=kinn,dc=id,dc=local
LDAPHost=************
LDAPPort=389
LDAPUser=CN=DelegeringAppUser,OU=KFApplicationUsers,DC=kinn,DC=id,DC=local
LDAPPwd=testtest
LDAPMailAttribute=mail
LDAPMaxResults=5000

MockBrukerAdm=false
BrukerAdmBaseUrl=https://kfdemo.knowit.no/brukeradm
ApplicationId=handboker
AccessKey=123

RunMigrationScripts=true
RunBatchJobs=true

# Every midnight
HandbooksSyncCron=0 0 0 1/1 * ? *

ElasticSearchReIndexCron=0 0 0 ? * Sun

# External organizations cache invalidatmvn testion time in minutes
CacheTimeLimit=5

# Pruning editors
PruneEditorsCron=0 0 0 1/1 * ? *

#Email
EmailNotificationsCron=0 0 0 1/1 * ? *
SMTPSendMailToRecipients=false
SMTPHost=smtp.gmail.com
SMTPPort=587
SMTPUser=<EMAIL>
SMTPPwd=C786Y##!
SMTPSentEmailFrom=<EMAIL>

# Elastic Search
ElasticSearchClusterName=elasticsearch_alexanbj
ElasticSearchHost=127.0.0.1
#ElasticSearchHost=host.docker.internal
ElasticSearchPort=9300
MockElasticSearch=true

# Local values:
# name = handbokertest
# port = 9200
# mock = false

# Number of hits per page
ElasticSearchPageSize=10

HandbookApiKey.9900=?Hei1234!

#Swagger
SwaggerUiHost=http://localhost

# Start date for versioning of sections and chapters
VersioningStart=01.08.2020

# Start date for correct versioning of deleted content
VersioningDeletedStart=01.09.2021

#Matomo server and container
MatomoServer=matomodev.kf.no
MatomoContainer=container_mdI8DPL5_dev_40dca4355976fcc6d7d398a7
