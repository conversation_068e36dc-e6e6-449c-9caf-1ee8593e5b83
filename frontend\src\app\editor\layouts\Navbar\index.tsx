import React, { useState } from "react";
import { Link, NavLink, useLocation } from "react-router-dom";
import { Icon, Nav, Container, Tag, SrOnly } from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import type { SessionInfo } from "@/types";

export interface NavbarProps {
  session: SessionInfo;
  pendingCount: number;
  hasCentralAccess: boolean;
}

export const Navbar: React.FC<NavbarProps> = ({
  session,
  pendingCount,
  hasCentralAccess,
}) => {
  const t = usePrefixedTranslation("editor.components.Header");
  const location = useLocation();
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const toggleOpen = () => setIsOpen((prev) => !prev);

  const hasOrganization = !!session?.organization;

  const isActiveLink = (path: string) => {
    const currentPath = location.pathname;

    if (path === "/select" || path === "/search" || path === "/pending") {
      return currentPath === path;
    }

    if (path === "/central" && currentPath.startsWith("/central-editor")) {
      return false;
    }

    if (path === "/editor" && currentPath.startsWith("/central")) {
      return false;
    }

    return currentPath.startsWith(path);
  };

  return (
    <Nav shadow>
      <Container>
        <Nav.Brand>
          <Nav.Item as={Link} to="/editor">
            {t("brand")}
          </Nav.Item>
          <Nav.Burger active={isOpen} onClick={toggleOpen} color="white" />
        </Nav.Brand>
        <Nav.Menu active={isOpen}>
          <Nav.Left style={{ justifyContent: "center" }}>
            {hasOrganization && (
              <Nav.Item
                hiddenMobile
                to="/editor"
                as={NavLink}
                className={isActiveLink("/editor") ? "is-active" : ""}
              >
                {t("editor")}
              </Nav.Item>
            )}

            {session.isKfAdmin && hasOrganization && (
              <Nav.Item
                hiddenMobile
                to="/central"
                as={NavLink}
                className={isActiveLink("/central") ? "is-active" : ""}
              >
                {t("central")}
              </Nav.Item>
            )}
            {session.isKfAdmin && hasOrganization && (
              <Nav.Item
                hiddenMobile
                to="/central-editor"
                as={NavLink}
                className={isActiveLink("/central-editor") ? "is-active" : ""}
              >
                {t("central-editor")}
              </Nav.Item>
            )}
          </Nav.Left>

          <Nav.Right>
            {hasOrganization && (
              <Nav.Item
                hiddenTablet
                to="/editor"
                as={NavLink}
                className={isActiveLink("/editor") ? "is-active" : ""}
              >
                {t("editor")}
              </Nav.Item>
            )}

            {session.isKfAdmin && hasOrganization && (
              <Nav.Item
                hiddenTablet
                to="/central"
                as={NavLink}
                className={isActiveLink("/central") ? "is-active" : ""}
              >
                {t("central")}
              </Nav.Item>
            )}

            {session.isKfAdmin && hasOrganization && (
              <Nav.Item
                hiddenTablet
                to="/central-editor"
                as={NavLink}
                className={isActiveLink("/central-editor") ? "is-active" : ""}
              >
                {t("central-editor")}
              </Nav.Item>
            )}

            {hasOrganization && (
              <Nav.Item
                as={NavLink}
                to="/search"
                className={isActiveLink("/search") ? "is-active" : ""}
                title="Søk"
                aria-label="Søk"
              >
                <Icon icon="search" />
              </Nav.Item>
            )}

            {hasOrganization && (pendingCount > 0 || hasCentralAccess) && (
              <Nav.Item
                to="/pending"
                title={t("pending")}
                as={NavLink}
                className={isActiveLink("/pending") ? "is-active" : ""}
              >
                <Icon icon="inbox" fw />{" "}
                {pendingCount > 0 && (
                  <Tag color="danger">
                    <SrOnly>Varsler </SrOnly>
                    {pendingCount}
                  </Tag>
                )}
              </Nav.Item>
            )}

            {session.userOrgsWithAccess.length > -1 && (
              <Nav.Item
                as={NavLink}
                to="/select"
                title={t("settings")}
                aria-label={t("settings")}
                className={isActiveLink("/select") ? "is-active" : ""}
              >
                <Icon icon="cog" />
              </Nav.Item>
            )}
            {session.ssoLogOutUrl && (
              <Nav.Item
                title="Logg ut"
                aria-label="Logg ut"
                href={session.ssoLogOutUrl}
                rel="noopener noreferrer"
              >
                <Icon icon="sign-out" />
                <span>Logg ut </span>
              </Nav.Item>
            )}
          </Nav.Right>
        </Nav.Menu>
      </Container>
    </Nav>
  );
};
