// Central Handbooks Feature Types
import type { Handbook, Organization } from '@/store/api/types';

// Page props
export interface CentralHandbooksPageProps {
  // Page-level props if needed
}

export interface CentralHandbookEditorPageProps {
  handbookId?: string;
}

// Component props
export interface CentralHandbooksListProps {
  handbooks: Handbook[];
  isLoading: boolean;
  onCreateHandbook: () => void;
  onEditHandbook: (handbook: Handbook) => void;
  onPublishHandbook: (handbook: Handbook) => void;
  onDeleteHandbook: (handbookId: string) => void;
}

export interface PublishModalProps {
  isOpen: boolean;
  handbook: Handbook | null;
  organizations: Organization[];
  onClose: () => void;
  onPublish: (handbookId: string, organizationIds: string[]) => void;
  isLoading: boolean;
}

// Local state types
export interface CentralHandbooksState {
  selectedHandbook: Handbook | null;
  showCreateModal: boolean;
  showPublishModal: boolean;
  searchQuery: string;
  filterBy: 'all' | 'published' | 'draft';
}

// Form types
export interface CreateCentralHandbookFormData {
  title: string;
  description: string;
}

export interface PublishHandbookFormData {
  organizationIds: string[];
  notifyUsers: boolean;
  publishDate?: Date;
} 