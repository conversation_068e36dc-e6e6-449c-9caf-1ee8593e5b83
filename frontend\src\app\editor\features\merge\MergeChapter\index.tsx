import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Title,
  Subtitle,
  Card,
  Field,
  Label,
  Input,
} from "kf-bui";
import moment from "moment";
import { toast } from "@/shared/components/Toast";
import { Spinner } from "@/shared/components/Spinner";
import { usePrefixedTranslation } from "@/libs/i18n";
import {
  useGetLocalChapterQuery,
  useSaveLocalChapterMutation,
} from "@/store/services/handbook/localHandbookApi";
import { useGetLatestCentralChapterVersionQuery } from "@/store/services/handbook/centralHandbookApi";
import type { Chapter } from "@/types";
import type { PublishedCentralChapter } from "@/store/services/handbook/centralHandbookApi";

function mergeTitle(
  element: Chapter,
  centralElement: PublishedCentralChapter,
  title: string
): Chapter {
  return {
    ...element,
    title,
    pendingChange: false,
    pendingChangeUpdatedDate: new Date().toISOString(),
    localChange: title !== centralElement.title,
  };
}

export const MergeChapter = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const t = usePrefixedTranslation("editor.components.MergeHandbookOrChapter");

  const [title, setTitle] = useState<string>("");
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [centralElement, setCentralElement] = useState<
    PublishedCentralChapter | undefined
  >(undefined);

  const { data: localChapter } = useGetLocalChapterQuery(id!, {
    skip: !id,
  });

  const { data: centralChapterData, error: centralError } =
    useGetLatestCentralChapterVersionQuery(
      {
        handbookId: localChapter?.importedHandbookId || "",
        chapterId: localChapter?.importedHandbookChapterId || "",
      },
      {
        skip:
          !localChapter?.importedHandbookId ||
          !localChapter?.importedHandbookChapterId,
      }
    );

  const [saveChapter] = useSaveLocalChapterMutation();

  useEffect(() => {
    if (centralChapterData) {
      setCentralElement(centralChapterData);
    } else if (centralError) {
      toast.error(t("fetchCentralElementFail"));
    }
  }, [centralChapterData, centralError, t]);

  const handleSave = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!localChapter || !centralElement) return;

    setIsSaving(true);

    try {
      const merged = mergeTitle(localChapter, centralElement, title);
      const centralChange = centralElement.title === title ? "KF" : "local";

      await saveChapter({
        chapter: merged,
        centralChange: centralChange === "KF",
      }).unwrap();

      navigate(`/editor/${localChapter.handbookId}/chapter/${id}`);
    } catch (error) {
      console.error("Error merging chapter:", error);
      toast.error("Feil ved lagring av kapittel");
    } finally {
      setIsSaving(false);
    }
  };

  if (!localChapter || !centralElement) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  const abortLink = `/editor/${localChapter.handbookId}/chapter/${localChapter.id}`;

  return (
    <div>
      <Columns>
        <Column>
          <Title className="text-center">{t("mergeChapter")}</Title>
          <Subtitle>{t("mergeChapterParagraph")}</Subtitle>
          <hr />
        </Column>
      </Columns>

      <Columns>
        <Column>
          <Card>
            <Card.Header title={t("headerCentral")} />
            <Card.Content>
              <Title as="em" size="5">
                {centralElement.title}
              </Title>
              <div className="pending-timestamp">
                {`${t("lastModified")} ${moment(centralElement.centralChapterUpdatedDateBeforePublish).format("DD.MM.YYYY")}`}
              </div>
            </Card.Content>
            <Card.Footer>
              <Card.Footer.Item
                as="a"
                role="button"
                href=""
                onClick={(event) => {
                  event.preventDefault();
                  setTitle(centralElement.title);
                }}
              >
                {t("useCentral")}
              </Card.Footer.Item>
            </Card.Footer>
          </Card>
        </Column>

        <Column>
          <Card>
            <Card.Header title={t("headerExisting")} />
            <Card.Content>
              <Title as="em" size="5">
                {localChapter.title}
              </Title>
              <div className="pending-timestamp">
                {`${t("lastModified")} ${moment(localChapter.localChapterUpdatedDate).format("DD.MM.YYYY")}`}
              </div>
            </Card.Content>
            <Card.Footer>
              <Card.Footer.Item
                as="a"
                role="button"
                href=""
                onClick={(event) => {
                  event.preventDefault();
                  setTitle(localChapter.title);
                }}
              >
                {t("useLocal")}
              </Card.Footer.Item>
            </Card.Footer>
          </Card>
        </Column>
      </Columns>

      <hr />

      <form onSubmit={handleSave}>
        <Field>
          <Label htmlFor="title">Tittel</Label>
          <Input
            id="title"
            placeholder="Tittel"
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
            value={title}
            required
            readOnly={isSaving}
          />
        </Field>

        <Columns responsive="mobile">
          <Column>
            <Button as={Link} to={abortLink}>
              Avbryt
            </Button>
          </Column>
          <Column narrow>
            <Button
              disabled={!title}
              loading={isSaving}
              onClick={handleSave}
              color="primary"
              type="submit"
            >
              {t("saveButton")}
            </Button>
          </Column>
        </Columns>
      </form>
    </div>
  );
};
