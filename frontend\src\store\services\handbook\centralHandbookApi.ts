import { baseApi, API_BASE_URLS } from "@/store/api";
import { generateTempId } from "./optimisticUpdateHooks";
import type {
  CentralHandbook,
  CentralChapter,
  CentralSection,
  Publication,
  ReadingLink,
} from "@/types";

export interface CreateCentralHandbookRequest {
  title: string;
}

export interface UpdateCentralHandbookRequest
  extends Omit<CentralHandbook, "type"> {
  id: string;
}

export interface UpdateCentralChapterRequest
  extends Omit<CentralChapter, "type"> {
  id: string;
}

export interface UpdateCentralSectionRequest
  extends Omit<CentralSection, "type"> {
  id: string;
}

export interface CreateReadingLinkRequest {
  centralSectionId: string;
  validTo: Date | string;
}

export interface DeleteReadingLinkRequest {
  id: string;
  centralSectionId: string;
}

export interface CentralPublishRequest {
  handbookId: string;
  publicationDate?: string;
}

export interface CreateCentralChapterRequest {
  parentId?: string;
  centralHandbookId: string;
  title: string;
}

export interface CentralChapterDeleteRequest {
  chapterId: string;
}

export interface CreateCentralSectionRequest {
  centralHandbookId: string;
  title: string;
  html?: string;
  parentId: string;
}

export interface CentralSectionDeleteRequest {
  sectionId: string;
}

export interface FetchAccessRequest {
  externalOrgId: string;
}

export interface SaveAccessRequest {
  externalOrgId: string;
  handbookIds: string[];
}

// Response types for public central handbook APIs
export interface PublishedCentralHandbook {
  id: string;
  title: string;
  createdDate: string;
  updatedDate: string;
  createdBy: string;
  updatedBy: string;
  isPublished: boolean;
}

export interface PublishedCentralHandbookContent {
  chapters: PublishedCentralChapter[];
  sections: PublishedCentralSection[];
}

export interface PublishedCentralChapter {
  id: string;
  title: string;
  importedHandbookChapterId?: string;
  importedHandbookId?: string;
  handbookId: string;
  parentId?: string;
  sortOrder: number;
  localChange: boolean;
  pendingChange: boolean;
  pendingDeletion: boolean;
  centralChapterUpdatedDateBeforePublish?: string;
}

export interface PublishedCentralSection {
  id: string;
  title: string;
  text?: string;
  importedHandbookSectionId?: string;
  importedHandbookId?: string;
  handbookId: string;
  parentId: string;
  sortOrder: number;
  localTitleChange: boolean;
  pendingTitleChange: boolean;
  localTextChange: boolean;
  pendingTextChange: boolean;
  pendingDeletion: boolean;
  updatedDate?: string;
  textUpdatedDate?: string;
}

export const centralHandbookApi = baseApi.injectEndpoints({
  overrideExisting: process.env.NODE_ENV === "development",
  endpoints: (builder) => ({

    getPublishedCentralHandbooks: builder.query<
      PublishedCentralHandbook[],
      void
    >({
      query: () => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central`,
        method: "GET",
      }),
      keepUnusedDataFor: 600,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: "CentralHandbook" as const,
                id,
              })),
              "CentralHandbook",
            ]
          : ["CentralHandbook"],
    }),

    getPublishedCentralHandbookContent: builder.query<
      PublishedCentralHandbookContent,
      string
    >({
      query: (handbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/${handbookId}/content`,
        method: "GET",
      }),
      providesTags: (_result, _error, handbookId) => [
        { type: "CentralHandbook", id: handbookId },
        "CentralChapter",
        "CentralSection",
      ],
    }),


    createCentralHandbook: builder.mutation<
      CentralHandbook,
      CreateCentralHandbookRequest
    >({
      query: (newHandbook) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/handbook`,
        method: "POST",
        body: newHandbook,
      }),
      onQueryStarted: async (newHandbook, { dispatch, queryFulfilled }) => {
        // Optimistic update for getCentralHandbooks query
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralHandbooks",
            undefined,
            (draft) => {
              const tempHandbook = {
                id: generateTempId("central"),
                title: newHandbook.title,
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString(),
                createdBy: "current-user", // This would come from session
                updatedBy: "current-user",
                isPublished: false,
              } as CentralHandbook;
              draft.unshift(tempHandbook);
            }
          )
        );

        try {
          const { data: savedHandbook } = await queryFulfilled;

          // Update the temporary handbook with the real one
          if (savedHandbook) {
            dispatch(
              centralHandbookApi.util.updateQueryData(
                "getCentralHandbooks",
                undefined,
                (draft) => {
                  const tempIndex = draft.findIndex((h) =>
                    h.id?.startsWith("temp-")
                  );
                  if (tempIndex !== -1) {
                    draft[tempIndex] = savedHandbook;
                  }
                }
              )
            );
          }
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: ["CentralHandbook"],
    }),

    updateCentralHandbook: builder.mutation<
      CentralHandbook,
      UpdateCentralHandbookRequest
    >({
      query: (handbook) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/handbook`,
        method: "POST",
        body: handbook,
      }),
      onQueryStarted: async (updatedHandbook, { dispatch, queryFulfilled }) => {
        // Optimistic update for getCentralHandbooks query
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralHandbooks",
            undefined,
            (draft) => {
              const index = draft.findIndex((h) => h.id === updatedHandbook.id);
              if (index !== -1) {
                draft[index] = {
                  ...draft[index],
                  ...updatedHandbook,
                  updatedDate: new Date().toISOString(),
                };
              }
            }
          )
        );

        // Also update individual handbook query if it exists
        const individualPatchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralHandbookById",
            updatedHandbook.id,
            (draft) => {
              Object.assign(draft, updatedHandbook, {
                updatedDate: new Date().toISOString(),
              });
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic updates on error
          patchResult.undo();
          individualPatchResult.undo();
        }
      },
      invalidatesTags: (_result, error, { id }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? [{ type: "CentralHandbook", id }] : [];
      },
    }),

    getCentralHandbooks: builder.query<CentralHandbook[], void>({
      query: () => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/handbooks`,
        method: "GET",
      }),
      transformResponse: (response: Omit<CentralHandbook, "type">[]) => {
        return response.map((handbook) => ({
          ...handbook,
          type: "HANDBOOK" as const,
        }));
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: "CentralHandbook" as const,
                id,
              })),
              "CentralHandbook",
            ]
          : ["CentralHandbook"],
    }),

    getCentralChapters: builder.query<CentralChapter[], void>({
      query: () => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/chapters`,
        method: "GET",
      }),
      transformResponse: (response: Omit<CentralChapter, "type">[]) => {
        // Add type property and sort consistently
        return response
          .map((chapter) => ({
            ...chapter,
            type: "CHAPTER" as const,
          }))
          .sort((a, b) => {
            const sortOrderA = a.sortOrder || 0;
            const sortOrderB = b.sortOrder || 0;
            if (sortOrderA !== sortOrderB) {
              return sortOrderA - sortOrderB;
            }
            return a.title.localeCompare(b.title);
          });
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: "CentralChapter" as const,
                id,
              })),
              "CentralChapter",
            ]
          : ["CentralChapter"],
    }),

    getCentralSections: builder.query<CentralSection[], void>({
      query: () => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/sections`,
        method: "GET",
      }),
      transformResponse: (response: Omit<CentralSection, "type">[]) => {
        // Add type property and sort consistently
        return response
          .map((section) => ({
            ...section,
            type: "SECTION" as const,
          }))
          .sort((a, b) => {
            const sortOrderA = a.sortOrder || 0;
            const sortOrderB = b.sortOrder || 0;
            if (sortOrderA !== sortOrderB) {
              return sortOrderA - sortOrderB;
            }
            return a.title.localeCompare(b.title);
          });
      },
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({
                type: "CentralSection" as const,
                id,
              })),
              "CentralSection",
            ]
          : ["CentralSection"],
    }),

    sortCentralItems: builder.mutation<void, string[]>({
      query: (ids) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/sort/`,
        method: "POST",
        body: ids,
      }),
      onQueryStarted: async (sortedIds, { dispatch, queryFulfilled }) => {
        const chaptersPatch = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralChapters",
            undefined,
            (draft) => {
              sortedIds.forEach((id, index) => {
                const chapter = draft.find((chapter) => chapter.id === id);
                if (chapter) {
                  chapter.sortOrder = index;
                }
              });
              draft.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
            }
          )
        );

        const sectionsPatch = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralSections",
            undefined,
            (draft) => {
              sortedIds.forEach((id, index) => {
                const section = draft.find((section) => section.id === id);
                if (section) {
                  section.sortOrder = index;
                }
              });
              draft.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          chaptersPatch.undo();
          sectionsPatch.undo();
        }
      },
      invalidatesTags: (_result, error) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? ["CentralChapter", "CentralSection"] : [];
      },
    }),

    deleteCentralHandbook: builder.mutation<void, string>({
      query: (handbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/handbook/${handbookId}`,
        method: "DELETE",
      }),
      onQueryStarted: async (handbookId, { dispatch, queryFulfilled }) => {
        // Optimistic update - remove handbook from list
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralHandbooks",
            undefined,
            (draft) => {
              return draft.filter((h) => h.id !== handbookId);
            }
          )
        );

        // Also remove from published list if it exists
        const publishedPatchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getPublishedCentralHandbooks",
            undefined,
            (draft) => {
              return draft.filter((h) => h.id !== handbookId);
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic updates on error
          patchResult.undo();
          publishedPatchResult.undo();
        }
      },
      invalidatesTags: (_result, error, handbookId) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? [{ type: "CentralHandbook", id: handbookId }] : [];
      },
    }),

    publishCentralHandbook: builder.mutation<
      Publication,
      CentralPublishRequest
    >({
      query: ({ handbookId, publicationDate }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/publish/${handbookId}`,
        method: "POST",
        body: publicationDate || null,
      }),
      onQueryStarted: async (
        { handbookId },
        { dispatch, getState, queryFulfilled }
      ) => {
        try {
          await queryFulfilled;

          // Add this handbook to pending publications tracking
          // We'll get the handbook title from the cache
          const state = getState() as any;
          const handbooks =
            centralHandbookApi.endpoints.getCentralHandbooks.select()(state)
              ?.data || [];
          const handbook = handbooks.find(
            (h: CentralHandbook) => h.id === handbookId
          );

          if (handbook) {
            // Import the action dynamically to avoid circular imports
            const { addPendingHandbook } = await import(
              "@/store/slices/pendingPublicationsSlice"
            );
            dispatch(
              addPendingHandbook({
                handbookId,
                handbookTitle: handbook.title,
              })
            );
          }
        } catch (error) {
          // Silently continue on error
        }
      },
      invalidatesTags: (_result, error, { handbookId }) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error
          ? [{ type: "CentralHandbook", id: handbookId }, "PendingPublications"]
          : [];
      },
    }),

    getCentralHandbookVersions: builder.query<CentralHandbook[], string>({
      query: (centralHandbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/versions/${centralHandbookId}`,
        method: "GET",
      }),
      transformResponse: (response: Omit<CentralHandbook, "type">[]) => {
        return response.map((handbook) => ({
          ...handbook,
          type: "HANDBOOK" as const,
        }));
      },
      providesTags: (_result, _error, centralHandbookId) => [
        { type: "CentralHandbook", id: centralHandbookId },
      ],
    }),

    getCentralHandbookById: builder.query<CentralHandbook, string>({
      query: (handbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/handbook/${handbookId}`,
        method: "GET",
      }),
      transformResponse: (response: Omit<CentralHandbook, "type">) => ({
        ...response,
        type: "HANDBOOK" as const,
      }),
      providesTags: (_result, _error, handbookId) => [
        { type: "CentralHandbook", id: handbookId },
      ],
    }),

    getCentralChapterById: builder.query<
      CentralChapter,
      { handbookId: string; chapterId: string }
    >({
      query: ({ handbookId, chapterId }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/${handbookId}/chapter/${chapterId}`,
        method: "GET",
      }),
      transformResponse: (response: any, _meta, arg) => {
        // Extract fields that belong to CentralChapter interface
        const cleanedResponse: CentralChapter = {
          id: response.id,
          title: response.title,
          parentId: response.parentId,
          centralHandbookId: arg.handbookId, // Always use the handbookId from the query arg
          versionOf: response.versionOf,
          updatedDateBeforePublish: response.updatedDateBeforePublish,
          sortOrder: response.sortOrder || 0,
          createdDate: response.createdDate,
          updatedDate: response.updatedDate,
          createdBy: response.createdBy,
          updatedBy: response.updatedBy,
          type: "CHAPTER" as const,
        };
        return cleanedResponse;
      },
      providesTags: (_result, _error, { chapterId }) => [
        { type: "CentralChapter", id: chapterId },
      ],
    }),

    getCentralSectionById: builder.query<
      CentralSection,
      { handbookId: string; sectionId: string }
    >({
      query: ({ handbookId, sectionId }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/${handbookId}/section/${sectionId}`,
        method: "GET",
      }),
      transformResponse: (response: any, _meta, arg) => {
        const cleanedResponse: CentralSection = {
          id: response.id,
          title: response.title,
          parentId: response.parentId,
          centralHandbookId: arg.handbookId,
          html: response.html,
          versionOf: response.versionOf,
          registeredDate: response.registeredDate,
          titleUpdatedDate: response.titleUpdatedDate,
          htmlUpdatedDate: response.htmlUpdatedDate,
          titleUpdatedBy: response.titleUpdatedBy,
          htmlUpdatedBy: response.htmlUpdatedBy,
          sortOrder: response.sortOrder || 0,
          createdDate: response.createdDate,
          updatedDate: response.updatedDate,
          createdBy: response.createdBy,
          updatedBy: response.updatedBy,
          type: "SECTION" as const,
        };
        return cleanedResponse;
      },
      providesTags: (_result, _error, { sectionId }) => [
        { type: "CentralSection", id: sectionId },
      ],
    }),

    getReadingLinks: builder.query<ReadingLink[], void>({
      query: () => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/readingLinks`,
        method: "GET",
      }),
      providesTags: ["CentralHandbook"],
    }),

    getReadingLinkBySectionId: builder.query<ReadingLink, string>({
      query: (centralSectionId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/readinglink/link/${centralSectionId}`,
        method: "GET",
      }),
      providesTags: (_result, _error, centralSectionId) => [
        { type: "CentralSection", id: centralSectionId },
      ],
    }),

    createReadingLink: builder.mutation<ReadingLink, CreateReadingLinkRequest>({
      query: (readingLink) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/readinglink/`,
        method: "POST",
        body: readingLink,
      }),
      invalidatesTags: (_result, error, { centralSectionId }) => {
        // Only invalidate if there was an error
        return error ? [{ type: "CentralSection", id: centralSectionId }] : [];
      },
    }),

    deleteReadingLink: builder.mutation<void, DeleteReadingLinkRequest>({
      query: ({ id }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/readinglink/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (_result, error, { centralSectionId }) => {
        // Only invalidate if there was an error
        return error ? [{ type: "CentralSection", id: centralSectionId }] : [];
      },
    }),

    getPendingPublications: builder.query<boolean, string>({
      query: (handbookId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/${handbookId}/pending-publications`,
        method: "GET",
      }),
      providesTags: (_result, _error, handbookId) => [
        { type: "CentralHandbook", id: handbookId },
        "PendingPublications",
      ],
    }),

    // Initial discovery of pending publications
    discoverAllPendingPublications: builder.query<
      { handbookId: string; handbookTitle: string }[],
      void
    >({
      queryFn: async (_arg, _queryApi, _extraOptions, fetchWithBQ) => {
        try {
          // First get all central handbooks
          const handbooksResult = await fetchWithBQ({
            url: `${API_BASE_URLS.HANDBOOKS}/central/handbooks`,
            method: "GET",
          });

          if (handbooksResult.error) {
            return { error: handbooksResult.error };
          }

          const handbooks = handbooksResult.data as CentralHandbook[];
          const pendingHandbooks: {
            handbookId: string;
            handbookTitle: string;
          }[] = [];

          // Check each handbook for pending publications
          await Promise.all(
            handbooks.map(async (handbook) => {
              try {
                const pendingResult = await fetchWithBQ({
                  url: `${API_BASE_URLS.HANDBOOKS}/central/${handbook.id}/pending-publications`,
                  method: "GET",
                });

                if (!pendingResult.error && pendingResult.data === true) {
                  pendingHandbooks.push({
                    handbookId: handbook.id!,
                    handbookTitle: handbook.title,
                  });
                }
              } catch (error) {
                // Continue on error - handbook is not pending
              }
            })
          );

          return { data: pendingHandbooks };
        } catch (error) {
          return {
            error: {
              status: "FETCH_ERROR",
              error: "Failed to discover pending publications",
            },
          };
        }
      },
      providesTags: ["PendingPublications", "CentralHandbook"],
      keepUnusedDataFor: 0, // Don't cache - for initial discovery only
    }),

    // Check pending publications for specific handbooks
    checkPendingPublications: builder.query<
      { handbookId: string; handbookTitle: string; isPending: boolean }[],
      { handbookId: string; handbookTitle: string }[]
    >({
      queryFn: async (
        handbooksToCheck,
        _queryApi,
        _extraOptions,
        fetchWithBQ
      ) => {
        if (!handbooksToCheck || handbooksToCheck.length === 0) {
          return { data: [] };
        }

        try {
          const results: {
            handbookId: string;
            handbookTitle: string;
            isPending: boolean;
          }[] = [];

          await Promise.all(
            handbooksToCheck.map(async (handbook) => {
              try {
                const pendingResult = await fetchWithBQ({
                  url: `${API_BASE_URLS.HANDBOOKS}/central/${handbook.handbookId}/pending-publications`,
                  method: "GET",
                });

                results.push({
                  handbookId: handbook.handbookId,
                  handbookTitle: handbook.handbookTitle,
                  isPending:
                    !pendingResult.error && pendingResult.data === true,
                });
              } catch (error) {
                results.push({
                  handbookId: handbook.handbookId,
                  handbookTitle: handbook.handbookTitle,
                  isPending: true,
                });
              }
            })
          );

          return { data: results };
        } catch (error) {
          return {
            error: {
              status: "FETCH_ERROR",
              error: "Failed to check pending publications",
            },
          };
        }
      },
      providesTags: ["PendingPublications"],
      keepUnusedDataFor: 5,
    }),

    createCentralChapter: builder.mutation<
      CentralChapter,
      CreateCentralChapterRequest
    >({
      query: (newChapter) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/chapter`,
        method: "POST",
        body: newChapter,
      }),
      onQueryStarted: async (newChapter, { dispatch, queryFulfilled }) => {
        // Optimistic update for getCentralChapters query
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralChapters",
            undefined,
            (draft) => {
              const tempId = generateTempId("central-chapter");
              const chapterWithTempId: CentralChapter = {
                ...newChapter,
                id: tempId,
                type: "CHAPTER",
                sortOrder: 0, // Default sort order for optimistic update
              };
              draft.unshift(chapterWithTempId);
            }
          )
        );

        try {
          const { data: savedChapter } = await queryFulfilled;

          // Update the temporary ID with the real one
          if (savedChapter.id) {
            dispatch(
              centralHandbookApi.util.updateQueryData(
                "getCentralChapters",
                undefined,
                (draft) => {
                  const tempChapter = draft.find((c) =>
                    c.id?.startsWith("temp-central-chapter-")
                  );
                  if (tempChapter) {
                    Object.assign(tempChapter, savedChapter);
                  }
                }
              )
            );
          }
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error) => {
        // Only invalidate if there was an error, as optimistic updates handle success case
        return error ? ["CentralChapter"] : [];
      },
    }),

    updateCentralChapter: builder.mutation<
      CentralChapter,
      UpdateCentralChapterRequest
    >({
      query: (chapter) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/chapter`,
        method: "POST",
        body: chapter,
      }),
      onQueryStarted: async (chapterUpdate, { dispatch, queryFulfilled }) => {
        // Optimistic update for getCentralChapters query
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralChapters",
            undefined,
            (draft) => {
              const chapterIndex = draft.findIndex(
                (c) => c.id === chapterUpdate.id
              );
              if (chapterIndex !== -1) {
                // Update the chapter with new data, preserving the type and other chapter-specific fields
                const existingChapter = draft[chapterIndex];
                draft[chapterIndex] = {
                  ...existingChapter,
                  // Update fields that can change during a move
                  title: chapterUpdate.title,
                  centralHandbookId: chapterUpdate.centralHandbookId,
                  parentId: chapterUpdate.parentId,
                  sortOrder: chapterUpdate.sortOrder,
                  updatedDate: chapterUpdate.updatedDate,
                  updatedBy: chapterUpdate.updatedBy,
                  // Ensure type remains as CHAPTER
                  type: "CHAPTER" as const,
                };
              }
            }
          )
        );

        try {
          const { data: updatedChapter } = await queryFulfilled;

          // Update with server response data
          dispatch(
            centralHandbookApi.util.updateQueryData(
              "getCentralChapters",
              undefined,
              (draft) => {
                const chapterIndex = draft.findIndex(
                  (c) => c.id === updatedChapter.id
                );
                if (chapterIndex !== -1) {
                  draft[chapterIndex] = updatedChapter;
                }
              }
            )
          );

          // Also update individual chapter cache if it exists
          dispatch(
            centralHandbookApi.util.updateQueryData(
              "getCentralChapterById",
              {
                handbookId: updatedChapter.centralHandbookId,
                chapterId: updatedChapter.id!,
              },
              () => updatedChapter
            )
          );

          // Refresh all chapters to ensure correct tree structure
          dispatch(centralHandbookApi.util.invalidateTags(["CentralChapter"]));
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, { id }) => {
        // Only invalidate if there was an error
        return error ? [{ type: "CentralChapter", id }] : [];
      },
    }),

    deleteCentralChapter: builder.mutation<void, string>({
      query: (chapterId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/chapter/${chapterId}`,
        method: "DELETE",
      }),
      onQueryStarted: async (chapterId, { dispatch, queryFulfilled }) => {
        // Optimistic update - remove chapter from list
        const chaptersPatch = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralChapters",
            undefined,
            (draft) => {
              return draft.filter((chapter) => chapter.id !== chapterId);
            }
          )
        );

        // Also remove any sections that belong to this chapter
        const sectionsPatch = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralSections",
            undefined,
            (draft) => {
              return draft.filter((section) => section.parentId !== chapterId);
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic updates on error
          chaptersPatch.undo();
          sectionsPatch.undo();
        }
      },
      invalidatesTags: (_result, error, chapterId) => {
        // Only invalidate if there was an error
        return error ? [{ type: "CentralChapter", id: chapterId }] : [];
      },
    }),

    createCentralSection: builder.mutation<
      CentralSection,
      CreateCentralSectionRequest
    >({
      query: (newSection) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/section`,
        method: "POST",
        body: newSection,
      }),
      onQueryStarted: async (newSection, { dispatch, queryFulfilled }) => {
        // Optimistic update for getCentralSections query
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralSections",
            undefined,
            (draft) => {
              const tempSection: CentralSection = {
                id: generateTempId("central-section"),
                type: "SECTION",
                title: newSection.title,
                html: newSection.html || "",
                centralHandbookId: newSection.centralHandbookId,
                parentId: newSection.parentId,
                sortOrder: 999, // Will be fixed by backend response
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString(),
                createdBy: "",
                updatedBy: "",
                htmlUpdatedBy: "",
                htmlUpdatedDate: new Date().toISOString(),
              };
              draft.push(tempSection);
            }
          )
        );

        try {
          const result = await queryFulfilled;
          // Replace the temporary section with the real one
          dispatch(
            centralHandbookApi.util.updateQueryData(
              "getCentralSections",
              undefined,
              (draft) => {
                const tempIndex = draft.findIndex((s) =>
                  s.id?.startsWith("temp-")
                );
                if (tempIndex !== -1) {
                  draft[tempIndex] = result.data;
                }
              }
            )
          );
        } catch {
          // Revert the optimistic update if the mutation fails
          patchResult.undo();
        }
      },
      invalidatesTags: ["CentralSection"],
    }),

    updateCentralSection: builder.mutation<
      CentralSection,
      UpdateCentralSectionRequest
    >({
      query: (section) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/section`,
        method: "POST",
        body: section,
      }),
      onQueryStarted: async (sectionUpdate, { dispatch, queryFulfilled }) => {
        // Optimistic update for getCentralSections query
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralSections",
            undefined,
            (draft) => {
              const sectionIndex = draft.findIndex(
                (s) => s.id === sectionUpdate.id
              );
              if (sectionIndex !== -1) {
                // Update the section with new data, preserving the type and other section-specific fields
                const existingSection = draft[sectionIndex];
                draft[sectionIndex] = {
                  ...existingSection,
                  // Update fields that can change during a move
                  title: sectionUpdate.title,
                  centralHandbookId: sectionUpdate.centralHandbookId,
                  parentId: sectionUpdate.parentId,
                  html: sectionUpdate.html,
                  sortOrder: sectionUpdate.sortOrder,
                  updatedDate: sectionUpdate.updatedDate,
                  updatedBy: sectionUpdate.updatedBy,
                  htmlUpdatedDate: sectionUpdate.htmlUpdatedDate,
                  htmlUpdatedBy: sectionUpdate.htmlUpdatedBy,
                  titleUpdatedDate: sectionUpdate.titleUpdatedDate,
                  titleUpdatedBy: sectionUpdate.titleUpdatedBy,
                  // Ensure type remains as SECTION
                  type: "SECTION" as const,
                };
              }
            }
          )
        );

        try {
          const { data: updatedSection } = await queryFulfilled;

          // Update with server response data
          dispatch(
            centralHandbookApi.util.updateQueryData(
              "getCentralSections",
              undefined,
              (draft) => {
                const sectionIndex = draft.findIndex(
                  (s) => s.id === updatedSection.id
                );
                if (sectionIndex !== -1) {
                  draft[sectionIndex] = updatedSection;
                }
              }
            )
          );

          // Also update individual section cache if it exists
          dispatch(
            centralHandbookApi.util.updateQueryData(
              "getCentralSectionById",
              {
                handbookId: updatedSection.centralHandbookId,
                sectionId: updatedSection.id!,
              },
              () => updatedSection
            )
          );

          // Refresh all sections to ensure correct tree structure
          dispatch(centralHandbookApi.util.invalidateTags(["CentralSection"]));
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, { id }) => {
        // Only invalidate if there was an error
        return error ? [{ type: "CentralSection", id }] : [];
      },
    }),

    deleteCentralSection: builder.mutation<void, string>({
      query: (sectionId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/section/${sectionId}`,
        method: "DELETE",
      }),
      onQueryStarted: async (sectionId, { dispatch, queryFulfilled }) => {
        // Optimistic update - remove section from list
        const patchResult = dispatch(
          centralHandbookApi.util.updateQueryData(
            "getCentralSections",
            undefined,
            (draft) => {
              return draft.filter((section) => section.id !== sectionId);
            }
          )
        );

        try {
          await queryFulfilled;
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      invalidatesTags: (_result, error, sectionId) => {
        // Only invalidate if there was an error
        return error ? [{ type: "CentralSection", id: sectionId }] : [];
      },
    }),


    getCentralHandbookAccess: builder.query<string[], string>({
      query: (externalOrgId) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/access/${externalOrgId}`,
        method: "GET",
      }),
      providesTags: (_result, _error, externalOrgId) => [
        { type: "CentralHandbook", id: `access-${externalOrgId}` },
      ],
    }),

    saveCentralHandbookAccess: builder.mutation<string[], SaveAccessRequest>({
      query: ({ externalOrgId, handbookIds }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/access/${externalOrgId}`,
        method: "POST",
        body: handbookIds,
      }),
      invalidatesTags: (_result, error, { externalOrgId }) => {
        // Only invalidate if there was an error
        return error
          ? [{ type: "CentralHandbook", id: `access-${externalOrgId}` }]
          : [];
      },
    }),


    getLatestCentralChapterVersion: builder.query<
      PublishedCentralChapter,
      { handbookId: string; chapterId: string }
    >({
      query: ({ handbookId, chapterId }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/${handbookId}/chapter/${chapterId}/latest/`,
        method: "GET",
      }),
      providesTags: (_result, _error, { chapterId }) => [
        { type: "CentralChapter", id: chapterId },
      ],
    }),

    getLatestCentralSectionVersion: builder.query<
      PublishedCentralSection,
      { handbookId: string; sectionId: string }
    >({
      query: ({ handbookId, sectionId }) => ({
        url: `${API_BASE_URLS.HANDBOOKS}/central/${handbookId}/section/${sectionId}/latest/`,
        method: "GET",
      }),
      providesTags: (_result, _error, { sectionId }) => [
        { type: "CentralSection", id: sectionId },
      ],
    }),
  }),
});

export const {
  useGetPublishedCentralHandbooksQuery,
  useGetPublishedCentralHandbookContentQuery,

  useCreateCentralHandbookMutation,
  useUpdateCentralHandbookMutation,
  useGetCentralHandbooksQuery,
  useGetCentralHandbookByIdQuery,
  useGetCentralChaptersQuery,
  useGetCentralChapterByIdQuery,
  useGetCentralSectionsQuery,
  useGetCentralSectionByIdQuery,
  useSortCentralItemsMutation,
  useDeleteCentralHandbookMutation,
  usePublishCentralHandbookMutation,
  useGetCentralHandbookVersionsQuery,
  useGetReadingLinksQuery,
  useGetReadingLinkBySectionIdQuery,
  useCreateReadingLinkMutation,
  useDeleteReadingLinkMutation,
  useGetPendingPublicationsQuery,
  useDiscoverAllPendingPublicationsQuery,
  useCheckPendingPublicationsQuery,
  useCreateCentralChapterMutation,
  useUpdateCentralChapterMutation,
  useDeleteCentralChapterMutation,
  useCreateCentralSectionMutation,
  useUpdateCentralSectionMutation,
  useDeleteCentralSectionMutation,

  useGetCentralHandbookAccessQuery,
  useSaveCentralHandbookAccessMutation,

  useGetLatestCentralChapterVersionQuery,
  useGetLatestCentralSectionVersionQuery,
} = centralHandbookApi;
