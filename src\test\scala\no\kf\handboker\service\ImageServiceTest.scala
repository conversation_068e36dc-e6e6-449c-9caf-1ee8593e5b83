package no.kf.handboker.service

import better.files._
import no.kf.handboker.DefaultTestDI
import no.kf.handboker.config.ImageFolder
import no.kf.handboker.model.Image
import org.scalatest.{BeforeAndAfterAll, FunSuite}
import org.junit.runner.RunWith
import org.scalatest.junit.JUnitRunner
import org.mockito.Mockito._

@RunWith(classOf[JUnitRunner])
class ImageServiceTest extends FunSuite with DefaultTestDI with BeforeAndAfterAll {

  override val componentRegistry = new DefaultTestRegistry {
    override lazy val imageService: ImageService = new ImageServiceImpl
  }

  val imageService = componentRegistry.imageService
  val rootFolder = componentRegistry.settings.settingFor(ImageFolder)

  test("That we can persist, retrieve and delete images") {
    val image = createImage()

    imageService.persistImage(image)

    val expectedContentFromDisk = buildOutputDir(image).lines.foldRight("")(_+_)
    assert(new String(image.file) === expectedContentFromDisk)

    val expectedContentFromService = new String(imageService.retrieveImage(image.createdName).byteArray)
    assert(new String(image.file) === expectedContentFromService)

    assert(imageService.deleteImage(image.createdName))
    assert(buildOutputDir(image).notExists)

  }

  def buildOutputDir(image: Image): File = {
    rootFolder / image.id.take(2) / image.createdName
  }

  def createImage(): Image = {
    Image("png", "image".getBytes)
  }


}
