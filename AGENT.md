# Agent Instructions for KF Handboker

## Build/Test Commands
- **Build all**: `mvn clean install -s settings.xml`
- **Build for production**: `mvn clean install -Pprod -s settings.xml`
- **Run backend only**: `mvn jetty:run -P !frontend -s settings.xml`
- **Run full app**: `mvn jetty:run -s settings.xml`
- **Single test**: `mvn test -Dtest=ClassName#methodName -s settings.xml`
- **Frontend build**: `cd frontend && npm run build`
- **Frontend tests**: `cd frontend && npm test`
- **Frontend dev**: `cd frontend && npm start` (port 3000)

## Architecture
- **Backend**: Scala 2.12/Java 8 with Scalatra REST framework, Maven build
- **Frontend**: React/Redux SPA with Webpack, Node.js
- **Database**: Derby (dev), connection via DbConnectionManager  
- **Search**: Elasticsearch 7.16 via elastic4s client
- **Authentication**: CAS integration with LDAP
- **Deployment**: WAR to Tomcat, Docker support available

## Code Style
- **Scala**: Package structure `no.kf.handboker.*`, trait-based components via ComponentRegistry
- **React**: ES6/JSX, Flow types, Redux/Saga patterns, styled-components for CSS
- **Formatting**: Use `npm run format` for frontend (prettier-eslint)
- **Testing**: ScalaTest for backend, Jest for frontend
- **Imports**: Group by standard library, third-party, then local packages
