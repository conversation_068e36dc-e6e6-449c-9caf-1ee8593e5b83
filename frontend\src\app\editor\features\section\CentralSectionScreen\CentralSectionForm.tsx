import React, { useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import {
  Title,
  Subtitle,
  Button,
  Columns,
  Column,
  Field,
  Label,
  Input,
  Help,
} from "kf-bui";
import { usePrefixedTranslation } from "@/libs/i18n";
import { Wysiwyg, type WysiwygRef } from "../../shared/Wysiwyg";
import { UnsavedChangesModal } from "@/shared/components/UnsavedChangesModal";
import { useUnsavedChangesGuard } from "@/shared/hooks/useUnsavedChangesGuard";
import { FeatureErrorBoundaryWrapper } from "@/shared/components/ErrorBoundary";
import type { CentralSection } from "@/types";

interface CentralSectionFormData {
  title: string;
  html: string;
}

const validationSchema: Yup.ObjectSchema<CentralSectionFormData> =
  Yup.object().shape({
    title: Yup.string().required("Avsnittet må ha en tittel"),
    html: Yup.string().optional().default(""),
  });

interface CentralSectionFormProps {
  centralSection?: CentralSection;
  onSubmit: (values: CentralSectionFormData) => Promise<void>;
  abortLink: string;
  isLoading?: boolean;
}

export const CentralSectionForm: React.FC<CentralSectionFormProps> = ({
  centralSection,
  onSubmit,
  abortLink,
  isLoading = false,
}) => {
  const t = usePrefixedTranslation("editor.containers.EditSection");
  const navigate = useNavigate();

  const [htmlWysiwyg, setHtmlWysiwyg] = useState<WysiwygRef | null>(null);
  const [isWysiwygDirty, setIsWysiwygDirty] = useState(false);

  const {
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
  } = useForm<CentralSectionFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      title: centralSection?.title || "",
      html: centralSection?.html || "",
    },
    mode: "onChange",
  });

  const titleValue = watch("title");

  const { showModal, handleStay, handleLeave, bypassGuard } =
    useUnsavedChangesGuard({
      isDirty: isWysiwygDirty,
    });

  const getWysiwygAndSubmit = async (values: CentralSectionFormData) => {
    if (htmlWysiwyg) {
      try {
        const content = await htmlWysiwyg.uploadImagesAndGetContent();
        setIsWysiwygDirty(false);
        bypassGuard();
        await onSubmit({ ...values, html: content });
      } catch (error) {
        console.error("Error uploading images:", error);
        try {
          const content = htmlWysiwyg.getContent();
          setIsWysiwygDirty(false);
          bypassGuard();
          await onSubmit({ ...values, html: content });
        } catch (fallbackError) {
          console.error("Error getting content:", fallbackError);
          setIsWysiwygDirty(false);
          bypassGuard();
          await onSubmit(values);
        }
      }
    } else {
      setIsWysiwygDirty(false);
      bypassGuard();
      await onSubmit(values);
    }
  };

  const handleCancel = () => {
    navigate(abortLink);
  };

  const wysiwygRef = useCallback((c: WysiwygRef | null) => {
    setHtmlWysiwyg(c);
  }, []);

  const handleWysiwygChange = useCallback(
    (content: string) => {
      setValue("html", content, { shouldValidate: false });
    },
    [setValue]
  );

  return (
    <form onSubmit={handleSubmit(getWysiwygAndSubmit)}>
      <Title>{centralSection ? t("editTitle") : t("createTitle")}</Title>
      {centralSection && <Subtitle>{centralSection.title}</Subtitle>}
      <hr />

      <Field>
        <Label htmlFor="title">{t("titleLabel")}</Label>
        <Input
          id="title"
          autoFocus
          readOnly={isLoading}
          placeholder={t("titleLabel")}
          value={titleValue}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
            setValue("title", e.target.value, { shouldValidate: true })
          }
          required
        />
        {errors.title && <Help color="danger">{errors.title.message}</Help>}
      </Field>

      <Field>
        <Label htmlFor="html">{t("textLabel")}</Label>
        <FeatureErrorBoundaryWrapper featureName="WYSIWYG Editor">
          <Wysiwyg
            id="html"
            name="html"
            value={centralSection?.html || ""}
            ref={wysiwygRef}
            onChange={handleWysiwygChange}
            onDirtyStateChange={setIsWysiwygDirty}
            disabled={isLoading}
          />
        </FeatureErrorBoundaryWrapper>
      </Field>

      <Columns responsive="mobile">
        <Column>
          <Button type="button" onClick={handleCancel} disabled={isLoading}>
            {t("cancelButton")}
          </Button>
        </Column>
        <Column narrow>
          <Button
            disabled={!isValid || isLoading}
            loading={isLoading}
            type="submit"
            color="primary"
          >
            {t("saveButton")}
          </Button>
        </Column>
      </Columns>

      <UnsavedChangesModal
        isOpen={showModal}
        onCancel={handleStay}
        onConfirm={handleLeave}
      />
    </form>
  );
};
