import { useCallback, useEffect, useRef } from "react";
import { useBlocker } from "react-router-dom";

interface UseUnsavedChangesGuardOptions {
  isDirty: boolean;
}

interface UseUnsavedChangesGuardReturn {
  showModal: boolean;
  handleStay: () => void;
  handleLeave: () => void;
  bypassGuard: () => void;
}

export const useUnsavedChangesGuard = ({
  isDirty,
}: UseUnsavedChangesGuardOptions): UseUnsavedChangesGuardReturn => {
  const bypassRef = useRef(false);
  
  const blocker = useBlocker(useCallback(() => isDirty && !bypassRef.current, [isDirty]));

  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (isDirty) {
        event.preventDefault();
        return "Du har ikke lagret endringene dine. Vil du fortsette uten å lagre?";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [isDirty]);

  const handleStay = () => {
    blocker.reset?.();
  };

  const handleLeave = () => {
    blocker.proceed?.();
  };

  const bypassGuard = () => {
    bypassRef.current = true;
  };

  return {
    showModal: blocker.state === "blocked",
    handleStay,
    handleLeave,
    bypassGuard,
  };
};
