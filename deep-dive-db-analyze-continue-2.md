# Deep Dive Database Analysis - Continued Part 2

## 4.2 Query Optimization and Indexing (Continued)

```
│ │                                                                     │   │
│ │ Result: Uses idx_section_handbook + sort                           │   │
│ │ Cost: ~2ms for 100 sections                                        │   │
│ │                                                                     │   │
│ │ EXPLAIN SELECT ls.*, cs.title_updated_date, cs.html_updated_date    │   │
│ │ FROM handbooksection ls                                            │   │
│ │ JOIN central_handbooks.section cs                                  │   │
│ │   ON ls.importedhandbooksection_id = cs.id                        │   │
│ │ WHERE ls.handbook_id = 'hb-123';                                   │   │
│ │                                                                     │   │
│ │ Result: Uses idx_section_handbook + idx_central_section_id         │   │
│ │ Cost: ~5ms for 100 sections with joins                             │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ Performance Monitoring:                                                     │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ -- Slow query identification                                        │   │
│ │ SELECT                                                              │   │
│ │   query_time,                                                       │   │
│ │   lock_time,                                                        │   │
│ │   rows_sent,                                                        │   │
│ │   rows_examined,                                                    │   │
│ │   sql_text                                                          │   │
│ │ FROM mysql.slow_log                                                 │   │
│ │ WHERE query_time > 1.0                                              │   │
│ │ ORDER BY query_time DESC                                            │   │
│ │ LIMIT 10;                                                           │   │
│ │                                                                     │   │
│ │ -- Index usage analysis                                             │   │
│ │ SELECT                                                              │   │
│ │   table_schema,                                                     │   │
│ │   table_name,                                                       │   │
│ │   index_name,                                                       │   │
│ │   cardinality,                                                      │   │
│ │   sub_part,                                                         │   │
│ │   packed,                                                           │   │
│ │   nullable                                                          │   │
│ │ FROM information_schema.statistics                                  │   │
│ │ WHERE table_schema = 'handbooks'                                    │   │
│ │ ORDER BY table_name, seq_in_index;                                  │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 4.3 Caching Strategy Implementation

```
MULTI-LEVEL CACHING ARCHITECTURE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ LEVEL 1: APPLICATION MEMORY CACHE                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Caffeine Cache Configuration:                                       │   │
│ │                                                                     │   │
│ │ val handbookCache: Cache[String, Handbook] = Caffeine.newBuilder() │   │
│ │   .maximumSize(1000)                                               │   │
│ │   .expireAfterWrite(15, TimeUnit.MINUTES)                          │   │
│ │   .expireAfterAccess(5, TimeUnit.MINUTES)                          │   │
│ │   .recordStats()                                                    │   │
│ │   .build()                                                          │   │
│ │                                                                     │   │
│ │ val sectionCache: Cache[String, HandbookSection] =                  │   │
│ │   Caffeine.newBuilder()                                            │   │
│ │     .maximumSize(10000)                                            │   │
│ │     .expireAfterWrite(30, TimeUnit.MINUTES)                        │   │
│ │     .recordStats()                                                  │   │
│ │     .build()                                                        │   │
│ │                                                                     │   │
│ │ Usage Pattern:                                                      │   │
│ │ def getHandbook(id: String): Option[Handbook] = {                   │   │
│ │   Option(handbookCache.getIfPresent(id)) match {                   │   │
│ │     case Some(handbook) =>                                         │   │
│ │       logger.debug(s"Cache hit for handbook $id")                 │   │
│ │       Some(handbook)                                               │   │
│ │     case None =>                                                    │   │
│ │       logger.debug(s"Cache miss for handbook $id")                │   │
│ │       val handbook = loadHandbookFromDatabase(id)                  │   │
│ │       handbook.foreach(h => handbookCache.put(id, h))              │   │
│ │       handbook                                                      │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ LEVEL 2: REDIS DISTRIBUTED CACHE                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Redis Configuration:                                                │   │
│ │                                                                     │   │
│ │ redis {                                                             │   │
│ │   host = "redis.internal.kf.no"                                    │   │
│ │   port = 6379                                                      │   │
│ │   database = 2                                                     │   │
│ │   timeout = 5000                                                   │   │
│ │   pool {                                                            │   │
│ │     maxTotal = 20                                                  │   │
│ │     maxIdle = 10                                                   │   │
│ │     minIdle = 2                                                    │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Key Patterns:                                                       │   │
│ │ • handbook:{org_id}:{handbook_id} → Handbook JSON                  │   │
│ │ • structure:{handbook_id} → Complete tree structure                │   │
│ │ • permissions:{user_email}:{org_id} → User permissions             │   │
│ │ • central_version:{central_id} → Latest central version info       │   │
│ │                                                                     │   │
│ │ Cache-Aside Pattern:                                                │   │
│ │ def getHandbookStructure(handbookId: String): HandbookStructure = { │   │
│ │   val cacheKey = s"structure:$handbookId"                          │   │
│ │                                                                     │   │
│ │   // Try Redis first                                               │   │
│ │   redisClient.get(cacheKey) match {                                │   │
│ │     case Some(json) =>                                             │   │
│ │       Json.parse(json).as[HandbookStructure]                       │   │
│ │     case None =>                                                    │   │
│ │       // Load from database                                        │   │
│ │       val structure = buildHandbookStructure(handbookId)           │   │
│ │       // Cache for 1 hour                                          │   │
│ │       redisClient.setex(cacheKey, 3600, Json.toJson(structure))    │   │
│ │       structure                                                     │   │
│ │   }                                                                 │   │
│ │ }                                                                   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ LEVEL 3: DATABASE QUERY RESULT CACHE                                       │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ MySQL Query Cache (mysql.cnf):                                     │   │
│ │                                                                     │   │
│ │ query_cache_type = 1                                               │   │
│ │ query_cache_size = 256M                                            │   │
│ │ query_cache_limit = 2M                                             │   │
│ │ query_cache_min_res_unit = 4096                                    │   │
│ │                                                                     │   │
│ │ Prepared Statement Cache:                                           │   │
│ │ • Reuse compiled query plans                                       │   │
│ │ • Reduce parsing overhead                                          │   │
│ │ • Improve execution time by 20-30%                                 │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ CACHE INVALIDATION STRATEGY                                                 │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Event-Driven Invalidation:                                         │   │
│ │                                                                     │   │
│ │ def invalidateHandbookCache(handbookId: String): Unit = {           │   │
│ │   // Clear application cache                                       │   │
│ │   handbookCache.invalidate(handbookId)                             │   │
│ │   sectionCache.asMap().keySet().asScala                            │   │
│ │     .filter(_.startsWith(s"$handbookId:"))                         │   │
│ │     .foreach(sectionCache.invalidate)                              │   │
│ │                                                                     │   │
│ │   // Clear Redis cache                                             │   │
│ │   val pattern = s"*:$handbookId*"                                  │   │
│ │   redisClient.keys(pattern).foreach(redisClient.del)               │   │
│ │                                                                     │   │
│ │   // Notify other application instances                            │   │
│ │   publishCacheInvalidationEvent(handbookId)                        │   │
│ │ }                                                                   │   │
│ │                                                                     │   │
│ │ Scheduled Refresh:                                                  │   │
│ │ • Background job every 30 minutes                                  │   │
│ │ • Refresh expiring cache entries                                   │   │
│ │ • Preload frequently accessed content                              │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 5. Data Migration and Versioning

### 5.1 Database Migration Framework

```
MIGRATION SYSTEM ARCHITECTURE
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ MIGRATION EXECUTION FLOW                                                    │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Application Startup:                                                │   │
│ │                                                                     │   │
│ │ 1. Check handboker_meta_data.version                               │   │
│ │ 2. Compare with expected version (currently 25)                    │   │
│ │ 3. If version < expected, run migration scripts                    │   │
│ │ 4. Execute scripts in sequence: 24.sql, 25.sql, etc.              │   │
│ │ 5. Update version number after each successful migration           │   │
│ │                                                                     │   │
│ │ Migration Script Structure:                                         │   │
│ │ ┌─────────────────────────────────────────────────────────────┐   │   │
│ │ │ -- migrate-db-25.sql                                        │   │   │
│ │ │ -- Description: Add chapter update tracking                 │   │   │
│ │ │                                                             │   │   │
│ │ │ -- Add new columns                                          │   │   │
│ │ │ ALTER TABLE central_handbooks.chapter                       │   │   │
│ │ │   ADD updated_date_before_publish BIGINT;                   │   │   │
│ │ │                                                             │   │   │
│ │ │ ALTER TABLE handbookchapter                                 │   │   │
│ │ │   ADD COLUMN local_chapter_updated_date BIGINT;             │   │   │
│ │ │                                                             │   │   │
│ │ │ -- Initialize with existing data                            │   │   │
│ │ │ UPDATE central_handbooks.chapter                            │   │   │
│ │ │ SET updated_date_before_publish = updated_date;             │   │   │
│ │ │                                                             │   │   │
│ │ │ UPDATE handbookchapter                                      │   │   │
│ │ │ SET local_chapter_updated_date = updated_date;              │   │   │
│ │ │                                                             │   │   │
│ │ │ -- Update version                                           │   │   │
│ │ │ UPDATE handboker_meta_data SET version = 25;               │   │   │
│ │ └─────────────────────────────────────────────────────────────┘   │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ ROLLBACK STRATEGY                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Rollback Scripts (rollback-db-25.sql):                             │   │
│ │                                                                     │   │
│ │ -- Remove added columns                                             │   │
│ │ ALTER TABLE central_handbooks.chapter                              │   │
│ │   DROP COLUMN updated_date_before_publish;                         │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbookchapter                                        │   │
│ │   DROP COLUMN local_chapter_updated_date;                          │   │
│ │                                                                     │   │
│ │ -- Revert version                                                   │   │
│ │ UPDATE handboker_meta_data SET version = 24;                       │   │
│ │                                                                     │   │
│ │ Rollback Execution:                                                 │   │
│ │ • Manual process for safety                                        │   │
│ │ • Requires application downtime                                    │   │
│ │ • Full database backup before rollback                             │   │
│ │ • Validation of data integrity after rollback                      │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ MIGRATION TESTING                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Test Environment Process:                                           │   │
│ │                                                                     │   │
│ │ 1. Create copy of production database                              │   │
│ │ 2. Run migration scripts on copy                                   │   │
│ │ 3. Validate data integrity and application functionality           │   │
│ │ 4. Performance test with migrated schema                           │   │
│ │ 5. Test rollback procedure                                          │   │
│ │ 6. Document any issues or performance impacts                      │   │
│ │                                                                     │   │
│ │ Automated Tests:                                                    │   │
│ │ • Unit tests for migration logic                                   │   │
│ │ • Integration tests with migrated schema                           │   │
│ │ • Performance benchmarks before/after migration                    │   │
│ │ • Data consistency validation queries                              │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 5.2 Data Consistency and Integrity

```
DATA INTEGRITY ENFORCEMENT
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ FOREIGN KEY CONSTRAINTS                                                     │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Local Schema Constraints:                                           │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbookchapter                                        │   │
│ │   ADD CONSTRAINT fk_chapter_handbook                               │   │
│ │   FOREIGN KEY (handbook_id) REFERENCES handbook(id)                │   │
│ │   ON DELETE CASCADE ON UPDATE CASCADE;                             │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbookchapter                                        │   │
│ │   ADD CONSTRAINT fk_chapter_parent                                 │   │
│ │   FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)   │   │
│ │   ON DELETE CASCADE ON UPDATE CASCADE;                             │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbooksection                                        │   │
│ │   ADD CONSTRAINT fk_section_handbook                               │   │
│ │   FOREIGN KEY (handbook_id) REFERENCES handbook(id)                │   │
│ │   ON DELETE CASCADE ON UPDATE CASCADE;                             │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbooksection                                        │   │
│ │   ADD CONSTRAINT fk_section_chapter                                │   │
│ │   FOREIGN KEY (parent_chapter_id) REFERENCES handbookchapter(id)   │   │
│ │   ON DELETE CASCADE ON UPDATE CASCADE;                             │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ CHECK CONSTRAINTS                                                           │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Business Rule Enforcement:                                          │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbook                                               │   │
│ │   ADD CONSTRAINT chk_handbook_flags                                │   │
│ │   CHECK (manualmerge IN (0, 1) AND pendingchanges IN (0, 1)       │   │
│ │          AND deleted IN (0, 1));                                   │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbookchapter                                        │   │
│ │   ADD CONSTRAINT chk_chapter_order                                 │   │
│ │   CHECK (orderindex >= 0 AND orderindex <= 999);                  │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbooksection                                        │   │
│ │   ADD CONSTRAINT chk_section_order                                 │   │
│ │   CHECK (orderindex >= 0 AND orderindex <= 999);                  │   │
│ │                                                                     │   │
│ │ ALTER TABLE central_handbooks.section                              │   │
│ │   ADD CONSTRAINT chk_section_status                                │   │
│ │   CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED'));           │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ UNIQUE CONSTRAINTS                                                          │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Prevent Duplicate Data:                                             │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbook                                               │   │
│ │   ADD CONSTRAINT uk_handbook_org_title                             │   │
│ │   UNIQUE (external_org_id, title, deleted);                       │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbookchapter                                        │   │
│ │   ADD CONSTRAINT uk_chapter_handbook_order                         │   │
│ │   UNIQUE (handbook_id, parent_chapter_id, orderindex, deleted);    │   │
│ │                                                                     │   │
│ │ ALTER TABLE handbooksection                                        │   │
│ │   ADD CONSTRAINT uk_section_chapter_order                          │   │
│ │   UNIQUE (parent_chapter_id, orderindex, deleted);                 │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ DATA VALIDATION PROCEDURES                                                  │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Integrity Check Queries:                                            │   │
│ │                                                                     │   │
│ │ -- Orphaned chapters (no parent handbook)                          │   │
│ │ SELECT c.id, c.title, c.handbook_id                                │   │
│ │ FROM handbookchapter c                                             │   │
│ │ LEFT JOIN handbook h ON c.handbook_id = h.id                       │   │
│ │ WHERE h.id IS NULL AND c.deleted = 0;                              │   │
│ │                                                                     │   │
│ │ -- Orphaned sections (no parent chapter)                           │   │
│ │ SELECT s.id, s.title, s.parent_chapter_id                          │   │
│ │ FROM handbooksection s                                             │   │
│ │ LEFT JOIN handbookchapter c ON s.parent_chapter_id = c.id          │   │
│ │ WHERE c.id IS NULL AND s.deleted = 0;                              │   │
│ │                                                                     │   │
│ │ -- Invalid central references                                      │   │
│ │ SELECT h.id, h.title, h.importedhandbook_id                        │   │
│ │ FROM handbook h                                                     │   │
│ │ LEFT JOIN central_handbooks.handbook ch                            │   │
│ │   ON h.importedhandbook_id = ch.central_id                         │   │
│ │ WHERE h.importedhandbook_id IS NOT NULL                            │   │
│ │   AND ch.central_id IS NULL                                        │   │
│ │   AND h.deleted = 0;                                               │   │
│ │                                                                     │   │
│ │ -- Duplicate order indexes                                         │   │
│ │ SELECT parent_chapter_id, orderindex, COUNT(*)                     │   │
│ │ FROM handbooksection                                               │   │
│ │ WHERE deleted = 0                                                   │   │
│ │ GROUP BY parent_chapter_id, orderindex                             │   │
│ │ HAVING COUNT(*) > 1;                                               │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 6. Backup and Recovery Procedures

### 6.1 Backup Strategy

```
COMPREHENSIVE BACKUP STRATEGY
┌─────────────────────────────────────────────────────────────────────────────┐
│                                                                             │
│ DAILY INCREMENTAL BACKUPS                                                   │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ MySQL Backup Script (backup-daily.sh):                             │   │
│ │                                                                     │   │
│ │ #!/bin/bash                                                         │   │
│ │ DATE=$(date +%Y%m%d_%H%M%S)                                        │   │
│ │ BACKUP_DIR="/backup/mysql/daily"                                   │   │
│ │ DB_NAME="handbooks"                                                 │   │
│ │                                                                     │   │
│ │ # Create backup directory                                           │   │
│ │ mkdir -p $BACKUP_DIR                                               │   │
│ │                                                                     │   │
│ │ # Dump database with consistent snapshot                           │   │
│ │ mysqldump --single-transaction \                                   │   │
│ │           --routines \                                             │   │
│ │           --triggers \                                             │   │
│ │           --events \                                               │   │
│ │           --hex-blob \                                             │   │
│ │           --quick \                                                │   │
│ │           --lock-tables=false \                                    │   │
│ │           --databases $DB_NAME \                                   │   │
│ │           > $BACKUP_DIR/handbooks_$DATE.sql                       │   │
│ │                                                                     │   │
│ │ # Compress backup                                                   │   │
│ │ gzip $BACKUP_DIR/handbooks_$DATE.sql                              │   │
│ │                                                                     │   │
│ │ # Verify backup integrity                                          │   │
│ │ gunzip -t $BACKUP_DIR/handbooks_$DATE.sql.gz                      │   │
│ │                                                                     │   │
│ │ # Clean up old backups (keep 30 days)                             │   │
│ │ find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete              │   │
│ │                                                                     │   │
│ │ # Log backup completion                                            │   │
│ │ echo "$(date): Backup completed successfully" >> /var/log/backup.log │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ WEEKLY FULL BACKUPS                                                         │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ Full System Backup (backup-weekly.sh):                             │   │
│ │                                                                     │   │
│ │ #!/bin/bash                                                         │   │
│ │ DATE=$(date +%Y%m%d)                                               │   │
│ │ BACKUP_DIR="/backup/mysql/weekly"                                  │   │
│ │                                                                     │   │
│ │ # Stop application to ensure consistency                           │   │
│ │ systemctl stop handbooks-app                                       │   │
│ │                                                                     │   │
│ │ # Full database backup                                             │   │
│ │ mysqldump --all-databases \                                        │   │
│ │           --single-transaction \                                   │   │
│ │           --routines \                                             │   │
│ │           --triggers \                                             │   │
│ │           --events \                                               │   │
│ │           > $BACKUP_DIR/full_backup_$DATE.sql                     │   │
│ │                                                                     │   │
│ │ # Backup configuration files                                       │   │
│ │ tar -czf $BACKUP_DIR/config_$DATE.tar.gz \                        │   │
│ │     /opt/handbooks/conf/ \                                         │   │
│ │     /etc/nginx/sites-available/handbooks \                        │   │
│ │     /etc/systemd/system/handbooks.service                         │   │
│ │                                                                     │   │
│ │ # Restart application                                              │   │
│ │ systemctl start handbooks-app                                      │   │
│ │                                                                     │   │
│ │ # Compress and encrypt backup                                      │   │
│ │ gzip $BACKUP_DIR/full_backup_$DATE.sql                            │   │
│ │ gpg --cipher-algo AES256 --compress-algo 1 \                      │   │
│ │     --symmetric --output $BACKUP_DIR/full_backup_$DATE.sql.gz.gpg \│   │
│ │     $BACKUP_DIR/full_backup_$DATE.sql.gz                          │   │
│ │                                                                     │   │
│ │ # Remove unencrypted backup                                        │   │
│ │ rm $BACKUP_DIR/full_backup_$DATE.sql.gz                           │   │
│ └─────────────────────────────────────────────────────────────────────┘   │
│                                                                             │
│ OFFSITE BACKUP REPLICATION                                                  │
│ ┌─────────────────────────────────────────────────────────────────────┐   │
│ │ AWS S3 Sync (backup-offsite.sh):                                   │   │
│ │                                                                     │   │
│ │ #!/bin/bash                                                         │   │
│ │ LOCAL_BACKUP="/backup/mysql"                                       │   │
│ │ S3_BUCKET="s3://kf-handbooks-backup"                              │   │
│ │                                                                     │   │
│ │ # Sync daily backups                                               │   │
│ │ aws s3 sync $LOCAL_BACKUP/daily/ $S3_BUCKET/daily/ \              │   │
│ │     --storage-class STANDARD_IA \                                  │   │
│ │     --exclude "*" --include "*.sql.gz"                            │   │
│ │                                                                     │   │
│ │ # Sync weekly backups                                              │   │
│ │ aws s3 sync $LOCAL_BACKUP/weekly/ $S3_BUCKET/weekly/ \            │   │
│ │     --storage-class GLACIER \                                     │   │
│ │     --exclude "*" --include "*.gpg"                               │   │
│ │                                                                     │   │
│ │ # Set lifecycle policy for automatic cleanup                       │   │
│ │ # Daily backups: Delete after 90 days                             │   │
│ │ # Weekly backups: Move to Deep Archive after 1 year              │   │
│
</
</augment_code_snippet>