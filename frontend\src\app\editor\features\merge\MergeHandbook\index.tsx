import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import {
  Button,
  Column,
  Columns,
  Title,
  Subtitle,
  Card,
  Field,
  Label,
  Input,
} from "kf-bui";
import moment from "moment";
import { toast } from "@/shared/components/Toast";
import { Spinner } from "@/shared/components/Spinner";
import { usePrefixedTranslation } from "@/libs/i18n";
import {
  useGetLocalHandbookQuery,
  useSaveLocalHandbookMutation,
} from "@/store/services/handbook/localHandbookApi";
import { useGetPublishedCentralHandbooksQuery } from "@/store/services/handbook/centralHandbookApi";
import type { Handbook } from "@/types";
import type { PublishedCentralHandbook } from "@/store/services/handbook/centralHandbookApi";

function mergeTitle(
  element: Handbook,
  centralElement: PublishedCentralHandbook,
  title: string
): Handbook {
  return {
    ...element,
    title,
    pendingChange: false,
    pendingChangeUpdatedDate: new Date().toISOString(),
    localChange: title !== centralElement.title,
  };
}

export const MergeHandbook = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const t = usePrefixedTranslation("editor.components.MergeHandbookOrChapter");

  const [title, setTitle] = useState<string>("");
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [centralElement, setCentralElement] = useState<
    PublishedCentralHandbook | undefined
  >(undefined);

  const { data: localHandbook } = useGetLocalHandbookQuery(id!, {
    skip: !id,
  });

  const { data: centralHandbooks, error: centralError } =
    useGetPublishedCentralHandbooksQuery();

  const [saveHandbook] = useSaveLocalHandbookMutation();

  // Find the matching central handbook
  useEffect(() => {
    if (localHandbook && centralHandbooks) {
      const matchingCentralHandbook = centralHandbooks.find(
        (h) => h.id === localHandbook.importedHandbookId
      );
      if (matchingCentralHandbook) {
        setCentralElement(matchingCentralHandbook);
      } else if (centralError) {
        toast.error(t("fetchCentralElementFail"));
      }
    }
  }, [localHandbook, centralHandbooks, centralError, t]);

  const handleSave = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!localHandbook || !centralElement) return;

    setIsSaving(true);

    try {
      const merged = mergeTitle(localHandbook, centralElement, title);
      await saveHandbook({
        ...merged,
        title: `${merged.title}`,
      }).unwrap();

      navigate(`/editor/${id}`);
    } catch (error) {
      console.error("Error merging handbook:", error);
      toast.error("Feil ved lagring av håndbok");
    } finally {
      setIsSaving(false);
    }
  };

  if (!localHandbook || !centralElement) {
    return (
      <div className="loading-container">
        <Spinner />
      </div>
    );
  }

  const abortLink = `/editor/${localHandbook.id}`;

  return (
    <>
      <Columns>
        <Column>
          <Title className="text-center">{t("mergeManual")}</Title>
          <Subtitle>{t("mergeManualParagraph")}</Subtitle>
          <hr />
        </Column>
      </Columns>

      <Columns>
        <Column>
          <Card>
            <Card.Header title={t("headerCentral")} />
            <Card.Content>
              <Title as="em" size="5">
                {centralElement.title}
              </Title>
              <div className="pending-timestamp">
                {`${t("lastModified")} ${moment(centralElement.updatedDate).format("DD.MM.YYYY")}`}
              </div>
            </Card.Content>
            <Card.Footer>
              <Card.Footer.Item
                as="a"
                role="button"
                href=""
                onClick={(event) => {
                  event.preventDefault();
                  setTitle(centralElement.title);
                }}
              >
                {t("useCentral")}
              </Card.Footer.Item>
            </Card.Footer>
          </Card>
        </Column>

        <Column>
          <Card>
            <Card.Header title={t("headerExisting")} />
            <Card.Content>
              <Title as="em" size="5">
                {localHandbook.title}
              </Title>
              <div className="pending-timestamp">
                {`${t("lastModified")} ${moment(localHandbook.updatedDate).format("DD.MM.YYYY")}`}
              </div>
            </Card.Content>
            <Card.Footer>
              <Card.Footer.Item
                as="a"
                role="button"
                href=""
                onClick={(event) => {
                  event.preventDefault();
                  setTitle(localHandbook.title);
                }}
              >
                {t("useLocal")}
              </Card.Footer.Item>
            </Card.Footer>
          </Card>
        </Column>
      </Columns>

      <hr />

      <form onSubmit={handleSave}>
        <Field>
          <Label htmlFor="title">Tittel</Label>
          <Input
            id="title"
            placeholder="Tittel"
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setTitle(e.target.value)
            }
            value={title}
            required
            readOnly={isSaving}
          />
        </Field>

        <Columns responsive="mobile">
          <Column>
            <Button as={Link} to={abortLink}>
              Avbryt
            </Button>
          </Column>
          <Column narrow>
            <Button
              disabled={!title}
              loading={isSaving}
              onClick={handleSave}
              color="primary"
              type="submit"
            >
              {t("saveButton")}
            </Button>
          </Column>
        </Columns>
      </form>
    </>
  );
};
