<!doctype html>
<html lang="no" data-theme="light">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- Make the page mobile compatible -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>K<PERSON></title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="shortcut icon" href="/favicon.ico" />

    <!-- Global variables that can be set by the server -->
    <script>
      // These would typically be set by the server in production
      // For development, we'll set defaults
      window.__BASENAME__ = window.__BASENAME__ || "/";

      // Session data for editor app (would be set by server in production)
      // window.__PRELOADED_SESSION_STATE__ = '{"user":{"id":1,"name":"Test User"},"organization":{"id":"test","name":"Test Org"}}';
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
