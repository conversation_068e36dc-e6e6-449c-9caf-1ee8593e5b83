package no.kf.handboker.service.search

import no.kf.handboker.model.SearchResult
import no.kf.handboker.model.local.{Chapter, Handbook, Section}

class SearchMockService extends SearchIndexService with SearchService with SearchIndexBuilderService {

  override def createIndexForOrg(externalOrgId: String): Unit = {}

  override def resetDatabase(): Unit = {}

  override def indexHandbook(handbook: Handbook): Unit = {}

  override def indexChapter(chapter: Chapter, externalOrgId: String): Unit = {}

  override def indexSection(section: Section, externalOrgId: String): Unit = {}

  override def indexDocuments(): Unit = {}

  override def deleteEntriesFromIndex(index: String): Unit = {}

  override def doReindex(externalOrgId: String): Boolean = true

  override def indexDocumentsForExtOrg(externalOrgId: String): Unit = {}

  override def doSearch(externalOrgId: String, query: String, handbookId: Option[String], page: Option[Int]): SearchResult = {
    SearchResult(0, 0, page.getOrElse(1), 10, Iterable.empty)
  }
}
