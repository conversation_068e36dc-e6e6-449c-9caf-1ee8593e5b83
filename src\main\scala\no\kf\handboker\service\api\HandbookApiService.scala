package no.kf.handboker.service.api

import no.kf.handboker.model.api.{TreeStructureChapter, TreeStructureHandbook}
import no.kf.handboker.model.local._
import no.kf.handboker.service.LocalHandbookServiceComponent
import org.joda.time.DateTime

import scala.annotation.tailrec


trait HandbookApiServiceComponent {
  this: LocalHandbookServiceComponent =>

  class HandbookApiServiceImpl extends HandbookApiService {

    override def retrieveHandbooksWithMetadata(externalOrgId: String): List[Handbook] = {
      val handbooks: List[Handbook] = localHandbookService.retrieveHandbooksForExternalOrganization(externalOrgId)
      handbooks
    }

    override def retrieveHandbookTreeStructure(handbookId: String, externalOrgId: String): Option[TreeStructureHandbook] = {
      val handbook = localHandbookService.retrieveHandbook(handbookId)
      val chapters = handbook.map(book => localHandbookService.retrieveChaptersForHandbook(book.id.get)).getOrElse(Nil)
      val sections = handbook.map(book => localHandbookService.retrieveSectionsForHandbook(book.id.get)).getOrElse(Nil)
      val rootChapters = createTreeStructure(chapters, sections)

      val handbookWithChapters = handbook.map(book => {
        constructTreeStructureHandbook(book, rootChapters)
      })
      handbookWithChapters
    }

    override def retrieveChangedEntitiesAfterDateForOrg(externalOrgId: String, from: DateTime): (List[Handbook], List[Chapter], List[Section]) = {
      val handbooks = localHandbookService.retrieveHandbooksForExternalOrganization(externalOrgId)
      val chapters = handbooks.flatMap(handbook => localHandbookService.retrieveChaptersForHandbook(handbook.id.get))
      val sections = handbooks.flatMap(handbook => localHandbookService.retrieveSectionsForHandbook(handbook.id.get))
      val handbooksAfterDate = handbooks.filter(handbook => handbook.updatedDate.exists(updatedDate => updatedDate.isAfter(from.getMillis)))
      val chaptersAfterDate = chapters.filter(chapter => chapter.updatedDate.exists(updatedDate => updatedDate.isAfter(from.getMillis)))
      val sectionsAfterDate = sections.filter(section => section.updatedDate.exists(updatedDate => updatedDate.isAfter(from.getMillis)))

      (handbooksAfterDate, chaptersAfterDate, sectionsAfterDate)
    }

    private def createTreeStructure(chapters: List[Chapter], sections: List[Section]): List[TreeStructureChapter] = {
      /*
       * Complexity: O(n^2). Readability over complexity
       * 1. Find leaf-chapters in remaining chapters (those who aren't parent to anyone)
       * 2. Find potential children in already processed chapters (those with matching parentId to current chapter ID)
       * 4. Add potential children to current chapter's child chapters
       * 5. Add current chapter to processed chapters list
       * 6. Remove current chapter from remaining chapters lsit
       */
      @tailrec
      def builder(remainingChapters: List[TreeStructureChapter], processedChapters: List[TreeStructureChapter]): List[TreeStructureChapter] = {
        if (remainingChapters.isEmpty) {
          processedChapters
        } else {
          val chapterWithoutChildren = remainingChapters.find(chapter => remainingChapters.forall(c => c.parentId != chapter.id))
          val (currentChapterChildren, otherChapterChildren) = processedChapters.partition(chapter => chapterWithoutChildren.exists(_.id == chapter.parentId))
          val chapterWithChildren = chapterWithoutChildren.map(_.copy(chapters = currentChapterChildren))
          val newRemaining = remainingChapters.filterNot(chapter => chapterWithoutChildren.exists(_.id == chapter.id))
          val newChildChapters: List[TreeStructureChapter] = chapterWithChildren.map(_ :: otherChapterChildren).getOrElse(Nil)
          builder(newRemaining, newChildChapters)
        }
      }

      val structureChaptersWithSections = chapters.map(chapter => {
        val chapterSections = sections.filter(_.parentId == chapter.id.get)
        val structureChapter = constructTreeStructureChapter(chapter, chapterSections)
        structureChapter
      })
      builder(structureChaptersWithSections, Nil)
    }

    override def constructTreeStructureHandbook(handbook: Handbook, chapters: List[TreeStructureChapter]): TreeStructureHandbook = {
      TreeStructureHandbook(
        handbook.id,
        handbook.title,
        handbook.importedHandbookId,
        handbook.externalOrgId,
        chapters,
        handbook.localChange,
        handbook.isPublic,
        handbook.pendingChange,
        handbook.pendingChangeUpdatedDate,
        handbook.updatedDate,
        handbook.createdDate,
        handbook.updatedBy,
        handbook.createdBy
      )
    }

    override def constructTreeStructureChapter(chapter: Chapter, sections: List[Section]): TreeStructureChapter = {
      TreeStructureChapter(
        chapter.id,
        chapter.title,
        chapter.importedHandbookChapterId,
        chapter.importedHandbookId,
        chapter.handbookId,
        chapter.parentId,
        chapter.sortOrder,
        Nil,
        sections,
        chapter.localChange,
        chapter.pendingChange,
        chapter.pendingChangeUpdatedDate,
        chapter.updatedDate,
        chapter.createdDate,
        chapter.updatedBy,
        chapter.createdBy
      )
    }
  }
}

trait HandbookApiService {
  def retrieveHandbooksWithMetadata(externalOrgId: String): List[Handbook]
  def retrieveHandbookTreeStructure(handbookId: String, externalOrgId: String): Option[TreeStructureHandbook]
  def retrieveChangedEntitiesAfterDateForOrg(externalOrgId: String, from: DateTime): (List[Handbook], List[Chapter], List[Section])
  def constructTreeStructureHandbook(handbook: Handbook, chapters: List[TreeStructureChapter]): TreeStructureHandbook
  def constructTreeStructureChapter(chapter: Chapter, sections: List[Section]): TreeStructureChapter
}
