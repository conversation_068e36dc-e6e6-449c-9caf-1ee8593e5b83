import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { Container, Section, Title, Subtitle, Select } from "kf-bui";
import { toast } from "@/shared/components/Toast";
import { usePrefixedTranslation } from "@/libs/i18n";
import { useSession } from "@/store/services/session/hooks";
import { SelectCentralHandbooks } from "@/app/editor/features/central";
import {
  useGetCentralHandbooksQuery,
  useGetCentralHandbookAccessQuery,
  useSaveCentralHandbookAccessMutation,
  centralHandbookApi,
} from "@/store/services/handbook/centralHandbookApi";

export const CentralHandbooksAccessPage: React.FC = () => {
  const dispatch = useDispatch();
  const t = usePrefixedTranslation("editor.containers.CentralHandbooksPage");
  const { session } = useSession();

  const [externalOrganization, setExternalOrganization] = useState<string>(
    session?.organization?.id || ""
  );

  const { data: handbooks = [], isLoading: isLoadingHandbooks } =
    useGetCentralHandbooksQuery();

  const { data: access = [], isLoading: isLoadingAccess } =
    useGetCentralHandbookAccessQuery(externalOrganization, {
      skip: !externalOrganization,
    });

  const [saveAccess, { isLoading: isSaving }] =
    useSaveCentralHandbookAccessMutation();

  const handleExternalOrgChange = (orgId: string) => {
    setExternalOrganization(orgId);
  };

  const handleSave = async (ids: string[]) => {
    try {
      await saveAccess({
        externalOrgId: externalOrganization,
        handbookIds: ids,
      }).unwrap();

      toast.success(t("saveSuccess"));

      if (session?.organization?.id === externalOrganization) {
        dispatch(centralHandbookApi.util.invalidateTags(["CentralHandbook"]));
      } else {
        dispatch(
          centralHandbookApi.util.invalidateTags([
            { type: "CentralHandbook", id: `access-${externalOrganization}` },
          ])
        );
      }
    } catch (error) {
      console.error("Failed to save access:", error);
      toast.error(t("saveError"));
    }
  };

  document.title = `${t("title")} - KF Håndbøker`;

  return (
    <Section>
      <Container>
        <Title>{t("title")}</Title>
        <Subtitle>{t("header")}</Subtitle>

        <Select
          aria-label={t("chooseOrganization")}
          onChange={handleExternalOrgChange}
          options={session?.userOrgsWithAccess || []}
          value={externalOrganization}
          placeholder={t("chooseOrganization")}
          labelKey="name"
          valueKey="id"
          simpleValue
        />

        <hr />

        {externalOrganization && (
          <SelectCentralHandbooks
            handbooks={handbooks}
            access={access}
            isSaving={isSaving}
            onSave={handleSave}
            isLoading={isLoadingHandbooks || isLoadingAccess}
          />
        )}
      </Container>
    </Section>
  );
};
