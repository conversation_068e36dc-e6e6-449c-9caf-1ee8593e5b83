package no.kf.handboker.service.handbookdownload

import no.kf.db.TransactionManager
import no.kf.handboker.ProductionRegistry
import no.kf.handboker.config.{AppSettingComponent, VersioningStart, VersioningDeletedStart}
import no.kf.handboker.model.central.{CentralChapter, CentralHandbook, CentralSection}
import no.kf.handboker.model.local.{Chapter, Handbook, Section}
import no.kf.handboker.service.{CentralHandbookServiceComponent, LocalHandbookServiceComponent, LocalHandbookVersionServiceComponent}
import no.kf.handboker.util.section.ImageUtil.{replaceIframes, replaceImageData}
import no.kf.util.Logging
import org.htmlcleaner.{CleanerProperties, HtmlCleaner, SimpleHtmlSerializer}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import scala.util.matching.Regex

import java.io.{StringReader, StringWriter}
import scala.xml.Elem

trait ReportServiceComponent extends TransactionManager {
  this: AppSettingComponent
    with CentralHandbookServiceComponent
    with LocalHandbookServiceComponent
    with LocalHandbookVersionServiceComponent
  =>

  val reportService: ReportService

  class ReportServiceImpl extends ReportService {

    private val dateFormat = "dd.MM.yyyy"

    lazy val versioningStart = DateTime.parse(ProductionRegistry.componentRegistry.settings.settingFor(VersioningStart), DateTimeFormat.forPattern(dateFormat))

    lazy val versioningDeletedStart = DateTime.parse(ProductionRegistry.componentRegistry.settings.settingFor(VersioningDeletedStart), DateTimeFormat.forPattern(dateFormat))

    def cleanHtmlStyles(html: String): String = {
      // Regex to match tables with specific complex styles and attributes
      val complexTablePattern = new Regex("""(?s)(<table[^>]*data-tablestyle\s*=\s*["']MsoTableGrid["'][^>]*data-tablelook\s*=\s*["']1696["'][^>]*>.*?</table>)""")
      // Regex to match <img> tags
      val imgTagPattern = new Regex("""<img\s+[^>]*src\s*=\s*["'][^"']+["'][^>]*>""")
      // Regex to match <p> tags containing only an <img>
      val pWithImgPattern = new Regex("""<p[^>]*>\s*(<img[^>]+>)\s*</p>""")

      // Function to clean and fix a table pasted from a Word document
      def cleanComplexTable(tableHtml: String): String = {
        val cleanedTable = tableHtml.replaceAll("""<table[^>]*>""", """<table style="border-collapse: collapse; width: 100%;" border="1">""")

        val cleanedRows = cleanedTable.replaceAll("""<tr[^>]*>""", """<tr>""")

        val tdPattern = new Regex("""<td[^>]*>""")
        val cleanedCells = tdPattern.replaceAllIn(cleanedRows, (m: Regex.Match) => {
          val tdTag = m.matched

          val widthPattern = """width\s*:\s*\d+(\.\d+)?%""".r
          val borderPattern = """border\s*:\s*\d+\s*px\s*solid\s*[^;"]+""".r
          val backgroundColorPattern = """background-color\s*:\s*[^;"]+""".r
          val spanPattern = """<span[^>]*style\s*=\s*["']([^"']*)["'][^>]*>""".r

          val width = widthPattern.findFirstIn(tdTag).map(_ + ";").getOrElse("width: auto;")
          val border = borderPattern.findFirstIn(tdTag).map(_ + ";").getOrElse("border: 1px solid black;")
          val backgroundColor = backgroundColorPattern.findFirstIn(tdTag).filterNot(_.contains("transparent")).map(_ + ";").getOrElse("")
          val spanStyles = spanPattern.findFirstMatchIn(tdTag).map(_.group(1)).getOrElse("")
          val colorPattern = """color\s*:\s*([^;"]+);?""".r
          val fontSizePattern = """font-size\s*:\s*[^;"]+;?""".r
          val fontFamilyPattern = """font-family\s*:\s*[^;"]+;?""".r

          val spanColor = colorPattern.findFirstMatchIn(spanStyles).map(_.group(1)).map {
            case "transparent" => "color: #000000;"
            case other => s"color: $other;"
          }.getOrElse("")

          val spanFontSize = fontSizePattern.findFirstIn(spanStyles).getOrElse("")
          val spanFontFamily = fontFamilyPattern.findFirstIn(spanStyles).getOrElse("")

          val retainedStyles = List(width, border, backgroundColor, spanColor, spanFontSize, spanFontFamily)
            .filter(_.nonEmpty)
            .mkString(" ")

          s"""<td style="$retainedStyles">"""
        })

        val flattenedContent = cleanedCells
          .replaceAll("""<p[^>]*>""", "")
          .replaceAll("""</p>""", "")
          .replaceAll("""<span[^>]*>""", "")
          .replaceAll("""</span>""", "")

        flattenedContent
      }

      // Function to fix <img> tags for Safari PDF conversion
      def fixImageTags(html: String): String = {
        imgTagPattern.replaceAllIn(html, (m: Regex.Match) => {
          val imgTag = m.matched

          val srcPattern = """src\s*=\s*["']([^"']+)["']""".r
          val widthPattern = """width\s*=\s*["'](\d+)["']""".r
          val heightPattern = """height\s*=\s*["'](\d+)["']""".r

          val src = srcPattern.findFirstMatchIn(imgTag).map(_.group(1)).getOrElse("")
          val width = widthPattern.findFirstMatchIn(imgTag).map(_.group(1)).getOrElse("auto")
          val height = heightPattern.findFirstMatchIn(imgTag).map(_.group(1)).getOrElse("auto")

          val style = s"width: ${width}px; height: ${height}px; object-fit: contain; image-orientation: none;"

          s"""<img src="$src" style="$style" />"""
        })
      }

      // Function to unwrap or replace <p> tags surrounding <img>
      def fixImageWrapping(html: String): String = {
        pWithImgPattern.replaceAllIn(html, (m: Regex.Match) => {
          val imgTag = m.group(1)
          // Replace <p> with <div> or remove the <p> entirely
          // Uncomment one of the following lines based on your preference:
          // imgTag // Remove <p> entirely
          s"""<div>$imgTag</div>""" // Replace <p> with <div>
        })
      }

      // Clean the complex tables
      val cleanedTablesHtml = complexTablePattern.replaceAllIn(html, m => cleanComplexTable(m.matched))

      // Remove the image tags
      val fixedImageTagsHtml = fixImageTags(cleanedTablesHtml)

      // Remove <p> tags wrapping <img>
      val fixedWrappingHtml = fixImageWrapping(fixedImageTagsHtml)

      // Return the cleaned HTML
      fixedWrappingHtml.replaceAll("""\s{2,}""", " ").trim
    }

    override def centralHandbookPDF(centralHandbookId: String, orgName: String, fullName: String): (String, Array[Byte]) = inTransaction {
      val handbookToPrint = centralHandbookService.retrieveCentralHandbook(centralHandbookId).get
      val elem = handbookhHTML(orgName, fullName, ReportType.central)(handbookToPrint)
      // Clean up the generated HTML before converting to PDF
      val cleanedHtml = cleanHtmlStyles(elem.toString)
      log.debug(cleanedHtml.replaceAll("""<img src="[^ ]+" (.*)/>""", """<img $1/>"""))
      (s"Utskrift av ${handbookToPrint.title} ${DateTime.now().toString(dateFormat)}.pdf", PdfConverter.html2Pdf(cleanedHtml))
    }

    override def localHandbookPDF(handbookId: String, orgName: String, fullName: String, time: DateTime): (String, Array[Byte]) = inTransaction {
      val handbookToPrint = localHandbookService.retrieveHandbook(handbookId).get
      val elem = handbookhHTML(orgName, fullName, ReportType.local, Some(time))(handbookToPrint)
      // Clean up the generated HTML before converting to PDF
      val cleanedHtml = cleanHtmlStyles(elem.toString)
      log.debug(cleanedHtml.toString().replaceAll("""<img src="[^ ]+" (.*)/>""", """<img $1/>"""))
      (s"Utskrift av ${handbookToPrint.title} per ${time.toString(dateFormat)} utskriftsdato ${DateTime.now().toString(dateFormat)}.pdf", PdfConverter.html2Pdf(cleanedHtml.toString))
    }

    override def centralHandbookWord(centralHandbookId: String, orgName: String, fullName: String): (String, Array[Byte]) = inTransaction {
      val handbookToPrint = centralHandbookService.retrieveCentralHandbook(centralHandbookId).get
      val elem = handbookhHTML(orgName, fullName, ReportType.central)(handbookToPrint)
      log.debug(elem.toString().replaceAll("""<img src="[^ ]+" (.*)/>""", """<img $1/>"""))
      (s"Utskrift av ${handbookToPrint.title} ${DateTime.now().toString(dateFormat)}.docx", WordConverter.html2Word(elem.toString, fullName))
    }

    override def localHandbookWord(centralHandbookId: String, orgName: String, fullName: String, time: DateTime): (String, Array[Byte]) = inTransaction {
      val handbookToPrint = localHandbookService.retrieveHandbook(centralHandbookId).get
      val elem = handbookhHTML(orgName, fullName, ReportType.local, Some(time))(handbookToPrint)
      (s"Utskrift av ${handbookToPrint.title} per ${time.toString(dateFormat)} utskriftsdato ${DateTime.now().toString(dateFormat)}.docx", WordConverter.html2Word(elem.toString, fullName))
    }

    private def handbookhHTML(orgName: String, fullName: String, reportType: ReportType.Type, time: Option[DateTime] = None)(implicit handbook: PDFHandbook): Elem = inTransaction {
      new HandbookHtmlBuilder(orgName, fullName, reportType, time)(handbook).build()
    }

    private abstract class HtmlBuilder(handbookName: String, dato: Option[DateTime]) {

      def renderUnparsedHtml(html: String) = {
        val cleanedHtml = HtmlCleaner.cleanup(html)
        scala.xml.Unparsed(
          cleanedHtml.replaceAll("<br>", "<br/>")
            .replaceAll("&nbsp;", "&#160;")
            .replaceAll("""<html[^>]*><head></head><body>""", "")
            .replaceAll("""</body></html>""", "")
            .replaceAll("""<!DOCTYPE html>""", "")
        )
      }

      def renderBody(): Elem

      def build(): Elem = {
        val x = 1
        val y = x + 1
        <html lang="nb-NO">
          <head>
            <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
            <meta charset="utf-8"/>
            <style type="text/css">
              {s"""
                @page:nth(1){}

                @page {
                  size: A4;

                  @top-left {
                    font-family: Arial;
                    color: #7B7B7B;
                    content: "$handbookName${if (dato.isDefined) " per " + dato.get.toString(dateFormat) else ""}"
                  }
                  @top-right {
                    font-family: Arial;
                    color: #7B7B7B;
                    content: "${DateTime.now().toString(dateFormat)}"
                  }
                  @bottom-right {
                    font-family: Arial;
                    content: "Side " counter(page) " av " counter(pages);
                  }
                }

                ul {
                  list-style-type: none
                }

                ul.toc a::after {
                    content: leader('.') target-counter(attr(href), page);
                }

                li.content {
                  display: block
                }

                .title {
                  font-size: 26pt;
                  font-family: Georgia;
                }

                .sub-title {
                  width: 100%;
                  font-size: 20pt;
                  font-family: Arial;
                  text-align: left;
                }

                .sub-title-small {
                  width: 100%;
                  font-size: 18pt;
                  font-family: Arial;
                  text-align: left;
                }

                .section-title {
                  width: 100%;
                  font-size: 16pt;
                  font-family: Arial;
                  text-align: left;
                }

                .breadcrumbs {
                  width: inherit;
                  vertical-align: middle;
                  text-align: left;
                  color: #525252;
                  margin-top: 3pt;
                  margin-bottom: 3pt;
                  font-size: 11pt;
                  font-family: Arial;
                }

                .text {
                  font-family: Arial;
                  font-size: 11pt;
                  width: inherit;
                  text-align: left;
                  margin-bottom: 6pt;
                }

                .table {
                  width: 100%;
                  table-layout: fixed;
                  word-wrap: break-word;
                  border: none;
                }

                .single-page{
                  width: 21cm;
                  height: 26.7cm;
                  width: 100%;
                }

                .center{
                  text-align: center;
                }

              """}
            </style>
          </head>{renderBody()}

        </html>
      }
    }

    private class HandbookHtmlBuilder(orgName: String, fullName: String, reportType: ReportType.Type, time: Option[DateTime])(implicit handbook: PDFHandbook) extends HtmlBuilder(handbook.title, time) {
// Table of contents virker for å være veldig buggy i word, i terminal kommer denne linjen ved nedlastning:
// org.docx4j.org.xhtmlrenderer.css-parse WARNING:: (null#inline_style_1) Found function where an identifier was expected at line 1. Skipping @page rule.
//
      def tableOfContents(rootChapter: Option[String] = None)(implicit chapters: List[PDFChapter]): Elem = {
        val currentChapters = sortReportItems(chapters.filter(_.parentId == rootChapter))
        <ul class="toc">
          {
          currentChapters.map(chapter =>{
            val childChapters = chapters.filter(_.parentId == chapter.id)
              <li class="content">
                <a href={s"#${chapter.id.get}"}>{chapter.title}</a>
                {if(childChapters.nonEmpty){tableOfContents(chapter.id)(chapters)}}
              </li>
          })
          }
        </ul>
      }

      def generateHandBook(rootChapter: Option[String] = None, parentChapterString: Option[String] = None, isSubchapter: Boolean = false)(implicit chapters: List[PDFChapter], sections:  List[PDFSection]): List[Elem] = {
        val childChapters = chapters.filter(_.parentId == rootChapter)
        val childSections = sections.filter(_.parentId == rootChapter.getOrElse("noId"))

        val itemList = sortReportItems(childChapters, childSections).flatMap {
          case chapter: PDFChapter =>
            val title = <tr>
              <td class={if (isSubchapter) "sub-title-small" else "sub-title"} id={chapter.id.get}>
                {parentChapterString.getOrElse("") + chapter.title}
              </td>
            </tr>

            List(title) ++ generateHandBook(chapter.id, parentChapterString, isSubchapter = true)(chapters, sections)
          case section: PDFSection =>
            List({
              <tr>
                <td>
                  <div>
                    <p>
                      <div class="section-title">
                        {section.title}
                      </div>
                    </p>{if (section.html.isDefined) renderUnparsedHtml(section.html.get)}
                  </div>
                </td>
              </tr>
            })
        }

        itemList
      }

      override def renderBody(): Elem = {

        val (chapters: List[PDFChapter], sections: List[PDFSection]) = if (reportType == ReportType.central) {
          val sectionData = centralHandbookService.retrieveCentralSections(handbook.id.get)
          val sections: List[PDFSection] = sectionData.map(s => replaceImageData(s)).map(s => replaceIframes(s))
          val chapters: List[PDFChapter] = centralHandbookService.retrieveCentralChapters(handbook.id.get).sortBy(_.sortOrder)
          (chapters, sections)
        } else {
          // Empty handbook if before we started versioning chapters and sections, as agreed with KF
          if (time.get.isBefore(versioningStart)) {
            (List(), List())
          } else {
            val (c, s) = localHandbookService.retrieveChapterAndSectionVersions(handbook.id.get, time)
            val pdfc: List[PDFChapter] = c
            val pdfs: List[PDFSection] = s
            (pdfc, pdfs)
          }
        }

        val handbookTitle: String = if (reportType == ReportType.central) {
          handbook.title
        } else {
          val latestHandbookVersionAtTime = localHandbookService.retrieveHandbookTitleVersion(handbook.id.get, time)
          if (latestHandbookVersionAtTime.nonEmpty) {
            val handbookUpdatedTime = localHandbookService.retrieveHandbook(handbook.id.get)
              .getOrElse(throw new Exception("Handbook not found:  " + handbook.id.get))
              .updatedDate.get
            if (handbookUpdatedTime.getMillis.compareTo(time.get.getMillis) < 0) {
              // Handbook has not been updated after desired time; use current title
              handbook.title
            } else {
              // Handbook has been updated; use relevant version
              latestHandbookVersionAtTime.head.title
            }
          } else {
            // Handbook has never been updated; use current title
            handbook.title
          }
        }

        <body>
          <div class="single-page center">
            <div class ="title">{handbookTitle}</div>
            {if (time.isDefined) <p>Versjonsdato: {time.get.toString(dateFormat)} </p>}
            {if (time.isDefined && time.get.isBefore(versioningDeletedStart)) <p>MERK: Innhold slettet før {versioningDeletedStart.toString(dateFormat)} er ikke med i utskriften.</p>}
            <p>Utskriftsdato: {DateTime.now().toString(dateFormat)}</p>
            <p>{orgName}</p>
            <p>{fullName}</p>
          </div>
          <div class ="sub-title">Innholdsfortegnelse</div>
          <nav>
          {tableOfContents()(chapters)}
          </nav>

          { val content = generateHandBook()(chapters, sections)
            var table = <table class="table" style="width: 100%;"></table>
             content.foreach(item => {
               table = table.copy(child = table.child ++ item)
             })
            table }

        </body>
      }

      private def orderAndTitle(a: Product) = {
        a match {
          case chapter: PDFChapter =>
            (chapter.sortOrder, chapter.title)
          case section: PDFSection =>
            (section.sortOrder, section.title)
        }
      }

      private def sortReportItems(cliste: List[PDFChapter], sliste: List[PDFSection]): List[Product] =
        (cliste ++ sliste)
          .sortWith((a, b) => {
            val (sortOrder1, title1) = orderAndTitle(a)
            val (sortOrder2, title2) = orderAndTitle(b)
            if (sortOrder1.isDefined && sortOrder2.isDefined)
              if (sortOrder1.get == sortOrder2.get) title1 < title2 else sortOrder1.get < sortOrder2.get
            else
              title1 < title2
          })

      private def sortReportItems(cliste: List[PDFChapter]): List[PDFChapter] =
        cliste
          .sortWith((a, b) => {
            val (sortOrder1, title1) = orderAndTitle(a)
            val (sortOrder2, title2) = orderAndTitle(b)
            if (sortOrder1.isDefined && sortOrder2.isDefined)
              if (sortOrder1.get == sortOrder2.get) title1 < title2 else sortOrder1.get < sortOrder2.get
            else
              title1 < title2
          })

    }
  }

  case class PDFHandbook(id: Option[String], title: String)

  case class PDFChapter(id: Option[String], title: String, parentId: Option[String], sortOrder: Option[Int])

  case class PDFSection(id: Option[String], title: String, html: Option[String], parentId: String, sortOrder: Option[Int])

  implicit def centralHandbookToPDFHandbook(h: CentralHandbook): PDFHandbook = PDFHandbook(h.id, h.title)

  implicit def localHandbookToPDFHandbook(h: Handbook): PDFHandbook = PDFHandbook(h.id, h.title)

  implicit def centralChapterListToPDFChapterList(cl: List[CentralChapter]): List[PDFChapter] = cl.map(c => PDFChapter(c.id, c.title, c.parentId, Some(c.sortOrder)))

  implicit def centralSectionListToPDFSectionList(ll: List[CentralSection]): List[PDFSection] = ll.map(s => PDFSection(s.id, s.title, s.html, s.parentId, Some(s.sortOrder)))

  implicit def localChapterListToPDFChapterList(cl: List[Chapter]): List[PDFChapter] = cl.map(c => PDFChapter(if (c.versionOf.isDefined) c.versionOf else c.id, c.title, c.parentId, c.sortOrder))

  implicit def localSectionListToPDFSectionList(ll: List[Section]): List[PDFSection] = ll.map(s => PDFSection(if (s.versionOf.isDefined) s.versionOf else s.id, s.title, s.text, s.parentId, s.sortOrder))

  implicit def centralToLocalSection(cs: CentralSection): Section = Section(cs.id, cs.title, cs.html, None, None, cs.centralHandbookId, cs.parentId, Some(cs.sortOrder))

  object ReportType extends Enumeration {
    type Type = Value
    val local = Value("local")
    val central = Value("central")
  }

  object HtmlCleaner extends Logging {
    //    private val htmlCleanerProperties = {
    //      val props = new CleanerProperties
    //      //Not sure if it breaks the word converter, but we don't need it.
    //      props.setOmitXmlDeclaration(true)
    //      props
    //    }
    //
    //    def cleanup(input: String): String = {
    //      def clean(in: String, cleaner: HtmlCleaner, serializer: SimpleHtmlSerializer) =  {
    //        val cleanTree = cleaner.clean(new StringReader(in))
    //        val stringWriter = new StringWriter(in.size)
    //        serializer.write(cleanTree, stringWriter, "utf-8")
    //        stringWriter.toString
    //      }
    //      log.trace("Html cleanup input: " + input)
    //      val pass = clean(input, new HtmlCleaner(htmlCleanerProperties), new SimpleHtmlSerializer(htmlCleanerProperties))
    //      log.trace("Html cleanup pass1: " + pass)
    //      pass
    //    }
    private val htmlCleanerProperties1 = {
      val props = new CleanerProperties
      //Not sure if it breaks the word converter, but we don't need it.
      props.setOmitXmlDeclaration(true)
      //Word converter does not like oslash and the like, but it does like Numeric Character Representations.
      //To achieve this, setTranslateSpecialEntities, setAdvancedXmlEscape and setTransResCharsToNCR must all be explicitly set.
      //And then we need to do two passes. This is a bug with htmlcleaner, as far as I've been able to determine - may want to check
      //if versions > 2.18 fix this.
      props.setTranslateSpecialEntities(false)
      props.setTransSpecialEntitiesToNCR(true)
      props.setAdvancedXmlEscape(true)
      props.setTransResCharsToNCR(true)
      props.setRecognizeUnicodeChars(false)
      props.setNamespacesAware(false)
      props.setOmitUnknownTags(true)
      props.setAllowHtmlInsideAttributes(true)
      props
    }
    private val htmlCleaner1 = new HtmlCleaner(htmlCleanerProperties1)
    private val htmlSerializer1 = new SimpleHtmlSerializer(htmlCleanerProperties1)
    private val htmlCleanerProperties2 = {
      val props = new CleanerProperties
      //Not sure if it breaks the word converter, but we don't need it.
      props.setOmitXmlDeclaration(true)
      //Word converter does not like oslash and the like, but it does like Numeric Character Representations.
      //To achieve this, setTranslateSpecialEntities, setAdvancedXmlEscape and setTransResCharsToNCR must all be explicitly set.
      //And then we need to do two passes. This is a bug with htmlcleaner, as far as I've been able to determine - may want to check
      //if versions > 2.18 fix this.
      props.setTranslateSpecialEntities(true)
      props.setTransSpecialEntitiesToNCR(true)
      props.setAdvancedXmlEscape(true)
      props.setTransResCharsToNCR(true)
      props.setRecognizeUnicodeChars(false)
      props.setNamespacesAware(false)
      props.setOmitUnknownTags(true)
      props.setAllowHtmlInsideAttributes(true)
      props
    }
    private val htmlCleaner2 = new HtmlCleaner(htmlCleanerProperties2)
    private val htmlSerializer2 = new SimpleHtmlSerializer(htmlCleanerProperties2)

    def cleanup(input: String): String = {
      def clean(in: String, cleaner: HtmlCleaner, serializer: SimpleHtmlSerializer) = {
        val cleanTree = cleaner.clean(new StringReader(in))
        val stringWriter = new StringWriter(in.size)
        serializer.write(cleanTree, stringWriter, "utf-8")
        stringWriter.toString
      }
      //Two passes due to bug in htmlcleaner 2.18 regarding direct translation of unicode to NCR. See also comment in htmlCleanerProperties definition
      log.debug("Html cleanup input: " + input)
      val pass1 = clean(input, htmlCleaner1, htmlSerializer1)
      log.debug("Html cleanup pass1: " + pass1)
      val pass2 = clean(pass1, htmlCleaner2, htmlSerializer2)
      log.debug("Html cleanup pass2: " + pass2)
      pass2
    }
  }

}

trait ReportService {
  def centralHandbookPDF(handbookId: String, orgName: String, fullName: String): (String, Array[Byte])

  def localHandbookPDF(handbookId: String, orgName: String, fullName: String, time: DateTime): (String, Array[Byte])

  def centralHandbookWord(handbookId: String, orgName: String, fullName: String): (String, Array[Byte])

  def localHandbookWord(handbookId: String, orgName: String, fullName: String, time: DateTime): (String, Array[Byte])
}

